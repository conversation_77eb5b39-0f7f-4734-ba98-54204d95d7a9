#
# Module manifest for module 'Microsoft.Graph.Identity.Governance'
#
# Generated by: Microsoft Corporation
#
# Generated on: 7/9/2025
#

@{

# Script module or binary module file associated with this manifest.
RootModule = './Microsoft.Graph.Identity.Governance.psm1'

# Version number of this module.
ModuleVersion = '2.29.0'

# Supported PSEditions
CompatiblePSEditions = 'Core', 'Desktop'

# ID used to uniquely identify this module
GUID = '530fc574-049c-42cc-810e-8835853204b7'

# Author of this module
Author = 'Microsoft Corporation'

# Company or vendor of this module
CompanyName = 'Microsoft Corporation'

# Copyright statement for this module
Copyright = 'Microsoft Corporation. All rights reserved.'

# Description of the functionality provided by this module
Description = 'Microsoft Graph PowerShell Cmdlets'

# Minimum version of the PowerShell engine required by this module
PowerShellVersion = '5.1'

# Name of the PowerShell host required by this module
# PowerShellHostName = ''

# Minimum version of the PowerShell host required by this module
# PowerShellHostVersion = ''

# Minimum version of Microsoft .NET Framework required by this module. This prerequisite is valid for the PowerShell Desktop edition only.
DotNetFrameworkVersion = '4.7.2'

# Minimum version of the common language runtime (CLR) required by this module. This prerequisite is valid for the PowerShell Desktop edition only.
# ClrVersion = ''

# Processor architecture (None, X86, Amd64) required by this module
# ProcessorArchitecture = ''

# Modules that must be imported into the global environment prior to importing this module
RequiredModules = @(@{ModuleName = 'Microsoft.Graph.Authentication'; RequiredVersion = '2.29.0'; })

# Assemblies that must be loaded prior to importing this module
RequiredAssemblies = './bin/Microsoft.Graph.Identity.Governance.private.dll'

# Script files (.ps1) that are run in the caller's environment prior to importing this module.
# ScriptsToProcess = @()

# Type files (.ps1xml) to be loaded when importing this module
# TypesToProcess = @()

# Format files (.ps1xml) to be loaded when importing this module
FormatsToProcess = './Microsoft.Graph.Identity.Governance.format.ps1xml'

# Modules to import as nested modules of the module specified in RootModule/ModuleToProcess
# NestedModules = @()

# Functions to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no functions to export.
FunctionsToExport = 'Add-MgIdentityGovernanceAccessReviewDefinitionInstanceDecision', 
               'Get-MgAgreement', 'Get-MgAgreementAcceptance', 
               'Get-MgAgreementAcceptanceCount', 'Get-MgAgreementFile', 
               'Get-MgAgreementFileCount', 'Get-MgAgreementFileLocalization', 
               'Get-MgAgreementFileLocalizationCount', 
               'Get-MgAgreementFileLocalizationVersion', 
               'Get-MgAgreementFileLocalizationVersionCount', 
               'Get-MgAgreementFileVersion', 'Get-MgAgreementFileVersionCount', 
               'Get-MgEntitlementManagementAccessPackage', 
               'Get-MgEntitlementManagementAccessPackageApplicablePolicyRequirement', 
               'Get-MgEntitlementManagementAccessPackageAssignmentApprovalCount', 
               'Get-MgEntitlementManagementAccessPackageAssignmentApprovalStage', 
               'Get-MgEntitlementManagementAccessPackageAssignmentApprovalStageCount', 
               'Get-MgEntitlementManagementAccessPackageAssignmentPolicy', 
               'Get-MgEntitlementManagementAccessPackageCatalog', 
               'Get-MgEntitlementManagementAccessPackageCount', 
               'Get-MgEntitlementManagementAccessPackageIncompatibleAccessPackage', 
               'Get-MgEntitlementManagementAccessPackageIncompatibleAccessPackageByRef', 
               'Get-MgEntitlementManagementAccessPackageIncompatibleGroup', 
               'Get-MgEntitlementManagementAccessPackageIncompatibleGroupByRef', 
               'Get-MgEntitlementManagementAccessPackageIncompatibleWith', 
               'Get-MgEntitlementManagementAssignment', 
               'Get-MgEntitlementManagementAssignmentAdditional', 
               'Get-MgEntitlementManagementAssignmentCount', 
               'Get-MgEntitlementManagementAssignmentPolicy', 
               'Get-MgEntitlementManagementAssignmentPolicyAccessPackage', 
               'Get-MgEntitlementManagementAssignmentPolicyCatalog', 
               'Get-MgEntitlementManagementAssignmentPolicyCount', 
               'Get-MgEntitlementManagementAssignmentPolicyCustomExtensionStageSetting', 
               'Get-MgEntitlementManagementAssignmentPolicyCustomExtensionStageSettingCount', 
               'Get-MgEntitlementManagementAssignmentPolicyCustomExtensionStageSettingCustomExtension', 
               'Get-MgEntitlementManagementAssignmentPolicyQuestion', 
               'Get-MgEntitlementManagementAssignmentPolicyQuestionCount', 
               'Get-MgEntitlementManagementAssignmentRequest', 
               'Get-MgEntitlementManagementAssignmentRequestCount', 
               'Get-MgEntitlementManagementCatalog', 
               'Get-MgEntitlementManagementCatalogAccessPackageCount', 
               'Get-MgEntitlementManagementCatalogCount', 
               'Get-MgEntitlementManagementCatalogCustomWorkflowExtension', 
               'Get-MgEntitlementManagementCatalogCustomWorkflowExtensionCount', 
               'Get-MgEntitlementManagementCatalogResource', 
               'Get-MgEntitlementManagementCatalogResourceCount', 
               'Get-MgEntitlementManagementCatalogResourceEnvironment', 
               'Get-MgEntitlementManagementCatalogResourceRole', 
               'Get-MgEntitlementManagementCatalogResourceRoleCount', 
               'Get-MgEntitlementManagementCatalogResourceRoleResource', 
               'Get-MgEntitlementManagementCatalogResourceRoleResourceEnvironment', 
               'Get-MgEntitlementManagementCatalogResourceRoleResourceScope', 
               'Get-MgEntitlementManagementCatalogResourceRoleResourceScopeCount', 
               'Get-MgEntitlementManagementCatalogResourceRoleResourceScopeResource', 
               'Get-MgEntitlementManagementCatalogResourceRoleResourceScopeResourceEnvironment', 
               'Get-MgEntitlementManagementCatalogResourceRoleResourceScopeResourceRole', 
               'Get-MgEntitlementManagementCatalogResourceRoleResourceScopeResourceRoleCount', 
               'Get-MgEntitlementManagementCatalogResourceScope', 
               'Get-MgEntitlementManagementCatalogResourceScopeCount', 
               'Get-MgEntitlementManagementCatalogResourceScopeResource', 
               'Get-MgEntitlementManagementCatalogResourceScopeResourceEnvironment', 
               'Get-MgEntitlementManagementCatalogResourceScopeResourceRole', 
               'Get-MgEntitlementManagementCatalogResourceScopeResourceRoleCount', 
               'Get-MgEntitlementManagementCatalogResourceScopeResourceRoleResource', 
               'Get-MgEntitlementManagementCatalogResourceScopeResourceRoleResourceEnvironment', 
               'Get-MgEntitlementManagementCatalogResourceScopeResourceRoleResourceScope', 
               'Get-MgEntitlementManagementCatalogResourceScopeResourceRoleResourceScopeCount', 
               'Get-MgEntitlementManagementConnectedOrganization', 
               'Get-MgEntitlementManagementConnectedOrganizationCount', 
               'Get-MgEntitlementManagementConnectedOrganizationExternalSponsor', 
               'Get-MgEntitlementManagementConnectedOrganizationExternalSponsorByRef', 
               'Get-MgEntitlementManagementConnectedOrganizationExternalSponsorCount', 
               'Get-MgEntitlementManagementConnectedOrganizationInternalSponsor', 
               'Get-MgEntitlementManagementConnectedOrganizationInternalSponsorByRef', 
               'Get-MgEntitlementManagementConnectedOrganizationInternalSponsorCount', 
               'Get-MgEntitlementManagementResource', 
               'Get-MgEntitlementManagementResourceCount', 
               'Get-MgEntitlementManagementResourceEnvironment', 
               'Get-MgEntitlementManagementResourceEnvironmentCount', 
               'Get-MgEntitlementManagementResourceEnvironmentResource', 
               'Get-MgEntitlementManagementResourceEnvironmentResourceCount', 
               'Get-MgEntitlementManagementResourceEnvironmentResourceRole', 
               'Get-MgEntitlementManagementResourceEnvironmentResourceRoleCount', 
               'Get-MgEntitlementManagementResourceEnvironmentResourceRoleResource', 
               'Get-MgEntitlementManagementResourceEnvironmentResourceRoleResourceEnvironment', 
               'Get-MgEntitlementManagementResourceEnvironmentResourceRoleResourceScope', 
               'Get-MgEntitlementManagementResourceEnvironmentResourceRoleResourceScopeCount', 
               'Get-MgEntitlementManagementResourceEnvironmentResourceRoleResourceScopeResource', 
               'Get-MgEntitlementManagementResourceEnvironmentResourceRoleResourceScopeResourceEnvironment', 
               'Get-MgEntitlementManagementResourceEnvironmentResourceScope', 
               'Get-MgEntitlementManagementResourceEnvironmentResourceScopeCount', 
               'Get-MgEntitlementManagementResourceEnvironmentResourceScopeResource', 
               'Get-MgEntitlementManagementResourceEnvironmentResourceScopeResourceEnvironment', 
               'Get-MgEntitlementManagementResourceEnvironmentResourceScopeResourceRole', 
               'Get-MgEntitlementManagementResourceEnvironmentResourceScopeResourceRoleCount', 
               'Get-MgEntitlementManagementResourceEnvironmentResourceScopeResourceRoleResource', 
               'Get-MgEntitlementManagementResourceEnvironmentResourceScopeResourceRoleResourceEnvironment', 
               'Get-MgEntitlementManagementResourceRequest', 
               'Get-MgEntitlementManagementResourceRequestCatalog', 
               'Get-MgEntitlementManagementResourceRequestCatalogAccessPackage', 
               'Get-MgEntitlementManagementResourceRequestCatalogAccessPackageCount', 
               'Get-MgEntitlementManagementResourceRequestCatalogCustomWorkflowExtension', 
               'Get-MgEntitlementManagementResourceRequestCatalogCustomWorkflowExtensionCount', 
               'Get-MgEntitlementManagementResourceRequestCatalogResource', 
               'Get-MgEntitlementManagementResourceRequestCatalogResourceCount', 
               'Get-MgEntitlementManagementResourceRequestCatalogResourceEnvironment', 
               'Get-MgEntitlementManagementResourceRequestCatalogResourceRole', 
               'Get-MgEntitlementManagementResourceRequestCatalogResourceRoleCount', 
               'Get-MgEntitlementManagementResourceRequestCatalogResourceRoleResource', 
               'Get-MgEntitlementManagementResourceRequestCatalogResourceRoleResourceEnvironment', 
               'Get-MgEntitlementManagementResourceRequestCatalogResourceRoleResourceScope', 
               'Get-MgEntitlementManagementResourceRequestCatalogResourceRoleResourceScopeCount', 
               'Get-MgEntitlementManagementResourceRequestCatalogResourceRoleResourceScopeResource', 
               'Get-MgEntitlementManagementResourceRequestCatalogResourceRoleResourceScopeResourceEnvironment', 
               'Get-MgEntitlementManagementResourceRequestCatalogResourceRoleResourceScopeResourceRole', 
               'Get-MgEntitlementManagementResourceRequestCatalogResourceRoleResourceScopeResourceRoleCount', 
               'Get-MgEntitlementManagementResourceRequestCatalogResourceScope', 
               'Get-MgEntitlementManagementResourceRequestCatalogResourceScopeCount', 
               'Get-MgEntitlementManagementResourceRequestCatalogResourceScopeResource', 
               'Get-MgEntitlementManagementResourceRequestCatalogResourceScopeResourceEnvironment', 
               'Get-MgEntitlementManagementResourceRequestCatalogResourceScopeResourceRole', 
               'Get-MgEntitlementManagementResourceRequestCatalogResourceScopeResourceRoleCount', 
               'Get-MgEntitlementManagementResourceRequestCatalogResourceScopeResourceRoleResource', 
               'Get-MgEntitlementManagementResourceRequestCatalogResourceScopeResourceRoleResourceEnvironment', 
               'Get-MgEntitlementManagementResourceRequestCatalogResourceScopeResourceRoleResourceScope', 
               'Get-MgEntitlementManagementResourceRequestCatalogResourceScopeResourceRoleResourceScopeCount', 
               'Get-MgEntitlementManagementResourceRequestCount', 
               'Get-MgEntitlementManagementResourceRequestResource', 
               'Get-MgEntitlementManagementResourceRequestResourceEnvironment', 
               'Get-MgEntitlementManagementResourceRequestResourceRole', 
               'Get-MgEntitlementManagementResourceRequestResourceRoleCount', 
               'Get-MgEntitlementManagementResourceRequestResourceRoleResource', 
               'Get-MgEntitlementManagementResourceRequestResourceRoleResourceEnvironment', 
               'Get-MgEntitlementManagementResourceRequestResourceRoleResourceScope', 
               'Get-MgEntitlementManagementResourceRequestResourceRoleResourceScopeCount', 
               'Get-MgEntitlementManagementResourceRequestResourceRoleResourceScopeResource', 
               'Get-MgEntitlementManagementResourceRequestResourceRoleResourceScopeResourceEnvironment', 
               'Get-MgEntitlementManagementResourceRequestResourceScope', 
               'Get-MgEntitlementManagementResourceRequestResourceScopeCount', 
               'Get-MgEntitlementManagementResourceRequestResourceScopeResource', 
               'Get-MgEntitlementManagementResourceRequestResourceScopeResourceEnvironment', 
               'Get-MgEntitlementManagementResourceRequestResourceScopeResourceRole', 
               'Get-MgEntitlementManagementResourceRequestResourceScopeResourceRoleCount', 
               'Get-MgEntitlementManagementResourceRequestResourceScopeResourceRoleResource', 
               'Get-MgEntitlementManagementResourceRequestResourceScopeResourceRoleResourceEnvironment', 
               'Get-MgEntitlementManagementResourceRole', 
               'Get-MgEntitlementManagementResourceRoleCount', 
               'Get-MgEntitlementManagementResourceRoleResource', 
               'Get-MgEntitlementManagementResourceRoleResourceEnvironment', 
               'Get-MgEntitlementManagementResourceRoleResourceScope', 
               'Get-MgEntitlementManagementResourceRoleResourceScopeCount', 
               'Get-MgEntitlementManagementResourceRoleResourceScopeResource', 
               'Get-MgEntitlementManagementResourceRoleResourceScopeResourceEnvironment', 
               'Get-MgEntitlementManagementResourceRoleScope', 
               'Get-MgEntitlementManagementResourceRoleScopeCount', 
               'Get-MgEntitlementManagementResourceRoleScopeResource', 
               'Get-MgEntitlementManagementResourceRoleScopeResourceEnvironment', 
               'Get-MgEntitlementManagementResourceRoleScopeResourceRole', 
               'Get-MgEntitlementManagementResourceRoleScopeResourceRoleCount', 
               'Get-MgEntitlementManagementResourceRoleScopeResourceRoleResource', 
               'Get-MgEntitlementManagementResourceRoleScopeResourceRoleResourceEnvironment', 
               'Get-MgEntitlementManagementResourceRoleScopeResourceRoleResourceScope', 
               'Get-MgEntitlementManagementResourceRoleScopeResourceRoleResourceScopeCount', 
               'Get-MgEntitlementManagementResourceRoleScopeResourceScope', 
               'Get-MgEntitlementManagementResourceRoleScopeResourceScopeCount', 
               'Get-MgEntitlementManagementResourceRoleScopeRole', 
               'Get-MgEntitlementManagementResourceRoleScopeRoleResource', 
               'Get-MgEntitlementManagementResourceRoleScopeRoleResourceEnvironment', 
               'Get-MgEntitlementManagementResourceRoleScopeRoleResourceRole', 
               'Get-MgEntitlementManagementResourceRoleScopeRoleResourceRoleCount', 
               'Get-MgEntitlementManagementResourceRoleScopeRoleResourceScope', 
               'Get-MgEntitlementManagementResourceRoleScopeRoleResourceScopeCount', 
               'Get-MgEntitlementManagementResourceRoleScopeRoleResourceScopeResource', 
               'Get-MgEntitlementManagementResourceRoleScopeRoleResourceScopeResourceEnvironment', 
               'Get-MgEntitlementManagementResourceRoleScopeRoleResourceScopeResourceRole', 
               'Get-MgEntitlementManagementResourceRoleScopeRoleResourceScopeResourceRoleCount', 
               'Get-MgEntitlementManagementResourceScope', 
               'Get-MgEntitlementManagementResourceScopeCount', 
               'Get-MgEntitlementManagementResourceScopeResource', 
               'Get-MgEntitlementManagementResourceScopeResourceEnvironment', 
               'Get-MgEntitlementManagementResourceScopeResourceRole', 
               'Get-MgEntitlementManagementResourceScopeResourceRoleCount', 
               'Get-MgEntitlementManagementResourceScopeResourceRoleResource', 
               'Get-MgEntitlementManagementResourceScopeResourceRoleResourceEnvironment', 
               'Get-MgEntitlementManagementSetting', 
               'Get-MgIdentityGovernanceAccessReviewDefinition', 
               'Get-MgIdentityGovernanceAccessReviewDefinitionCount', 
               'Get-MgIdentityGovernanceAccessReviewDefinitionInstance', 
               'Get-MgIdentityGovernanceAccessReviewDefinitionInstanceContactedReviewer', 
               'Get-MgIdentityGovernanceAccessReviewDefinitionInstanceContactedReviewerCount', 
               'Get-MgIdentityGovernanceAccessReviewDefinitionInstanceCount', 
               'Get-MgIdentityGovernanceAccessReviewDefinitionInstanceDecision', 
               'Get-MgIdentityGovernanceAccessReviewDefinitionInstanceDecisionCount', 
               'Get-MgIdentityGovernanceAccessReviewDefinitionInstanceDecisionInsight', 
               'Get-MgIdentityGovernanceAccessReviewDefinitionInstanceDecisionInsightCount', 
               'Get-MgIdentityGovernanceAccessReviewDefinitionInstanceStage', 
               'Get-MgIdentityGovernanceAccessReviewDefinitionInstanceStageCount', 
               'Get-MgIdentityGovernanceAccessReviewDefinitionInstanceStageDecision', 
               'Get-MgIdentityGovernanceAccessReviewDefinitionInstanceStageDecisionCount', 
               'Get-MgIdentityGovernanceAccessReviewDefinitionInstanceStageDecisionInsight', 
               'Get-MgIdentityGovernanceAccessReviewHistoryDefinition', 
               'Get-MgIdentityGovernanceAccessReviewHistoryDefinitionCount', 
               'Get-MgIdentityGovernanceAccessReviewHistoryDefinitionInstance', 
               'Get-MgIdentityGovernanceAccessReviewHistoryDefinitionInstanceCount', 
               'Get-MgIdentityGovernanceAppConsentRequest', 
               'Get-MgIdentityGovernanceAppConsentRequestCount', 
               'Get-MgIdentityGovernanceAppConsentRequestUserConsentRequest', 
               'Get-MgIdentityGovernanceAppConsentRequestUserConsentRequestApproval', 
               'Get-MgIdentityGovernanceAppConsentRequestUserConsentRequestApprovalStage', 
               'Get-MgIdentityGovernanceAppConsentRequestUserConsentRequestApprovalStageCount', 
               'Get-MgIdentityGovernanceAppConsentRequestUserConsentRequestCount', 
               'Get-MgIdentityGovernanceLifecycleWorkflow', 
               'Get-MgIdentityGovernanceLifecycleWorkflowCount', 
               'Get-MgIdentityGovernanceLifecycleWorkflowCreatedBy', 
               'Get-MgIdentityGovernanceLifecycleWorkflowCreatedByMailboxSetting', 
               'Get-MgIdentityGovernanceLifecycleWorkflowCreatedByServiceProvisioningError', 
               'Get-MgIdentityGovernanceLifecycleWorkflowCreatedByServiceProvisioningErrorCount', 
               'Get-MgIdentityGovernanceLifecycleWorkflowCustomTaskExtension', 
               'Get-MgIdentityGovernanceLifecycleWorkflowCustomTaskExtensionCount', 
               'Get-MgIdentityGovernanceLifecycleWorkflowCustomTaskExtensionCreatedBy', 
               'Get-MgIdentityGovernanceLifecycleWorkflowCustomTaskExtensionCreatedByMailboxSetting', 
               'Get-MgIdentityGovernanceLifecycleWorkflowCustomTaskExtensionCreatedByServiceProvisioningError', 
               'Get-MgIdentityGovernanceLifecycleWorkflowCustomTaskExtensionCreatedByServiceProvisioningErrorCount', 
               'Get-MgIdentityGovernanceLifecycleWorkflowCustomTaskExtensionLastModifiedBy', 
               'Get-MgIdentityGovernanceLifecycleWorkflowCustomTaskExtensionLastModifiedByMailboxSetting', 
               'Get-MgIdentityGovernanceLifecycleWorkflowCustomTaskExtensionLastModifiedByServiceProvisioningError', 
               'Get-MgIdentityGovernanceLifecycleWorkflowCustomTaskExtensionLastModifiedByServiceProvisioningErrorCount', 
               'Get-MgIdentityGovernanceLifecycleWorkflowDeletedItem', 
               'Get-MgIdentityGovernanceLifecycleWorkflowDeletedItemWorkflow', 
               'Get-MgIdentityGovernanceLifecycleWorkflowDeletedItemWorkflowCount', 
               'Get-MgIdentityGovernanceLifecycleWorkflowDeletedItemWorkflowCreatedBy', 
               'Get-MgIdentityGovernanceLifecycleWorkflowDeletedItemWorkflowExecutionScope', 
               'Get-MgIdentityGovernanceLifecycleWorkflowDeletedItemWorkflowLastModifiedBy', 
               'Get-MgIdentityGovernanceLifecycleWorkflowDeletedItemWorkflowRun', 
               'Get-MgIdentityGovernanceLifecycleWorkflowDeletedItemWorkflowTask', 
               'Get-MgIdentityGovernanceLifecycleWorkflowDeletedItemWorkflowTaskReport', 
               'Get-MgIdentityGovernanceLifecycleWorkflowDeletedItemWorkflowUserProcessingResult', 
               'Get-MgIdentityGovernanceLifecycleWorkflowDeletedItemWorkflowVersion', 
               'Get-MgIdentityGovernanceLifecycleWorkflowExecutionScope', 
               'Get-MgIdentityGovernanceLifecycleWorkflowExecutionScopeCount', 
               'Get-MgIdentityGovernanceLifecycleWorkflowInsight', 
               'Get-MgIdentityGovernanceLifecycleWorkflowLastModifiedBy', 
               'Get-MgIdentityGovernanceLifecycleWorkflowLastModifiedByMailboxSetting', 
               'Get-MgIdentityGovernanceLifecycleWorkflowLastModifiedByServiceProvisioningError', 
               'Get-MgIdentityGovernanceLifecycleWorkflowLastModifiedByServiceProvisioningErrorCount', 
               'Get-MgIdentityGovernanceLifecycleWorkflowRun', 
               'Get-MgIdentityGovernanceLifecycleWorkflowRunCount', 
               'Get-MgIdentityGovernanceLifecycleWorkflowRunTaskProcessingResult', 
               'Get-MgIdentityGovernanceLifecycleWorkflowRunTaskProcessingResultCount', 
               'Get-MgIdentityGovernanceLifecycleWorkflowRunTaskProcessingResultSubject', 
               'Get-MgIdentityGovernanceLifecycleWorkflowRunTaskProcessingResultSubjectMailboxSetting', 
               'Get-MgIdentityGovernanceLifecycleWorkflowRunTaskProcessingResultSubjectServiceProvisioningError', 
               'Get-MgIdentityGovernanceLifecycleWorkflowRunTaskProcessingResultSubjectServiceProvisioningErrorCount', 
               'Get-MgIdentityGovernanceLifecycleWorkflowRunTaskProcessingResultTask', 
               'Get-MgIdentityGovernanceLifecycleWorkflowRunUserProcessingResult', 
               'Get-MgIdentityGovernanceLifecycleWorkflowRunUserProcessingResultCount', 
               'Get-MgIdentityGovernanceLifecycleWorkflowRunUserProcessingResultSubject', 
               'Get-MgIdentityGovernanceLifecycleWorkflowRunUserProcessingResultSubjectMailboxSetting', 
               'Get-MgIdentityGovernanceLifecycleWorkflowRunUserProcessingResultSubjectServiceProvisioningError', 
               'Get-MgIdentityGovernanceLifecycleWorkflowRunUserProcessingResultSubjectServiceProvisioningErrorCount', 
               'Get-MgIdentityGovernanceLifecycleWorkflowRunUserProcessingResultTaskProcessingResult', 
               'Get-MgIdentityGovernanceLifecycleWorkflowSetting', 
               'Get-MgIdentityGovernanceLifecycleWorkflowTask', 
               'Get-MgIdentityGovernanceLifecycleWorkflowTaskCount', 
               'Get-MgIdentityGovernanceLifecycleWorkflowTaskDefinition', 
               'Get-MgIdentityGovernanceLifecycleWorkflowTaskDefinitionCount', 
               'Get-MgIdentityGovernanceLifecycleWorkflowTaskProcessingResult', 
               'Get-MgIdentityGovernanceLifecycleWorkflowTaskProcessingResultCount', 
               'Get-MgIdentityGovernanceLifecycleWorkflowTaskProcessingResultSubject', 
               'Get-MgIdentityGovernanceLifecycleWorkflowTaskProcessingResultSubjectMailboxSetting', 
               'Get-MgIdentityGovernanceLifecycleWorkflowTaskProcessingResultSubjectServiceProvisioningError', 
               'Get-MgIdentityGovernanceLifecycleWorkflowTaskProcessingResultSubjectServiceProvisioningErrorCount', 
               'Get-MgIdentityGovernanceLifecycleWorkflowTaskProcessingResultTask', 
               'Get-MgIdentityGovernanceLifecycleWorkflowTaskReport', 
               'Get-MgIdentityGovernanceLifecycleWorkflowTaskReportCount', 
               'Get-MgIdentityGovernanceLifecycleWorkflowTaskReportTask', 
               'Get-MgIdentityGovernanceLifecycleWorkflowTaskReportTaskDefinition', 
               'Get-MgIdentityGovernanceLifecycleWorkflowTaskReportTaskProcessingResult', 
               'Get-MgIdentityGovernanceLifecycleWorkflowTaskReportTaskProcessingResultCount', 
               'Get-MgIdentityGovernanceLifecycleWorkflowTaskReportTaskProcessingResultSubject', 
               'Get-MgIdentityGovernanceLifecycleWorkflowTaskReportTaskProcessingResultSubjectMailboxSetting', 
               'Get-MgIdentityGovernanceLifecycleWorkflowTaskReportTaskProcessingResultSubjectServiceProvisioningError', 
               'Get-MgIdentityGovernanceLifecycleWorkflowTaskReportTaskProcessingResultSubjectServiceProvisioningErrorCount', 
               'Get-MgIdentityGovernanceLifecycleWorkflowTaskReportTaskProcessingResultTask', 
               'Get-MgIdentityGovernanceLifecycleWorkflowTemplate', 
               'Get-MgIdentityGovernanceLifecycleWorkflowTemplateCount', 
               'Get-MgIdentityGovernanceLifecycleWorkflowTemplateTask', 
               'Get-MgIdentityGovernanceLifecycleWorkflowTemplateTaskCount', 
               'Get-MgIdentityGovernanceLifecycleWorkflowTemplateTaskProcessingResult', 
               'Get-MgIdentityGovernanceLifecycleWorkflowTemplateTaskProcessingResultCount', 
               'Get-MgIdentityGovernanceLifecycleWorkflowTemplateTaskProcessingResultSubject', 
               'Get-MgIdentityGovernanceLifecycleWorkflowTemplateTaskProcessingResultSubjectMailboxSetting', 
               'Get-MgIdentityGovernanceLifecycleWorkflowTemplateTaskProcessingResultSubjectServiceProvisioningError', 
               'Get-MgIdentityGovernanceLifecycleWorkflowTemplateTaskProcessingResultSubjectServiceProvisioningErrorCount', 
               'Get-MgIdentityGovernanceLifecycleWorkflowTemplateTaskProcessingResultTask', 
               'Get-MgIdentityGovernanceLifecycleWorkflowUserProcessingResult', 
               'Get-MgIdentityGovernanceLifecycleWorkflowUserProcessingResultCount', 
               'Get-MgIdentityGovernanceLifecycleWorkflowUserProcessingResultSubject', 
               'Get-MgIdentityGovernanceLifecycleWorkflowUserProcessingResultSubjectMailboxSetting', 
               'Get-MgIdentityGovernanceLifecycleWorkflowUserProcessingResultSubjectServiceProvisioningError', 
               'Get-MgIdentityGovernanceLifecycleWorkflowUserProcessingResultSubjectServiceProvisioningErrorCount', 
               'Get-MgIdentityGovernanceLifecycleWorkflowUserProcessingResultTaskProcessingResult', 
               'Get-MgIdentityGovernanceLifecycleWorkflowVersion', 
               'Get-MgIdentityGovernanceLifecycleWorkflowVersionCount', 
               'Get-MgIdentityGovernanceLifecycleWorkflowVersionCreatedBy', 
               'Get-MgIdentityGovernanceLifecycleWorkflowVersionCreatedByMailboxSetting', 
               'Get-MgIdentityGovernanceLifecycleWorkflowVersionCreatedByServiceProvisioningError', 
               'Get-MgIdentityGovernanceLifecycleWorkflowVersionCreatedByServiceProvisioningErrorCount', 
               'Get-MgIdentityGovernanceLifecycleWorkflowVersionLastModifiedBy', 
               'Get-MgIdentityGovernanceLifecycleWorkflowVersionLastModifiedByMailboxSetting', 
               'Get-MgIdentityGovernanceLifecycleWorkflowVersionLastModifiedByServiceProvisioningError', 
               'Get-MgIdentityGovernanceLifecycleWorkflowVersionLastModifiedByServiceProvisioningErrorCount', 
               'Get-MgIdentityGovernanceLifecycleWorkflowVersionTask', 
               'Get-MgIdentityGovernanceLifecycleWorkflowVersionTaskCount', 
               'Get-MgIdentityGovernanceLifecycleWorkflowVersionTaskProcessingResult', 
               'Get-MgIdentityGovernanceLifecycleWorkflowVersionTaskProcessingResultCount', 
               'Get-MgIdentityGovernanceLifecycleWorkflowVersionTaskProcessingResultSubject', 
               'Get-MgIdentityGovernanceLifecycleWorkflowVersionTaskProcessingResultSubjectMailboxSetting', 
               'Get-MgIdentityGovernanceLifecycleWorkflowVersionTaskProcessingResultSubjectServiceProvisioningError', 
               'Get-MgIdentityGovernanceLifecycleWorkflowVersionTaskProcessingResultSubjectServiceProvisioningErrorCount', 
               'Get-MgIdentityGovernanceLifecycleWorkflowVersionTaskProcessingResultTask', 
               'Get-MgIdentityGovernancePrivilegedAccess', 
               'Get-MgIdentityGovernancePrivilegedAccessGroup', 
               'Get-MgIdentityGovernancePrivilegedAccessGroupAssignmentApproval', 
               'Get-MgIdentityGovernancePrivilegedAccessGroupAssignmentApprovalCount', 
               'Get-MgIdentityGovernancePrivilegedAccessGroupAssignmentApprovalStage', 
               'Get-MgIdentityGovernancePrivilegedAccessGroupAssignmentApprovalStageCount', 
               'Get-MgIdentityGovernancePrivilegedAccessGroupAssignmentSchedule', 
               'Get-MgIdentityGovernancePrivilegedAccessGroupAssignmentScheduleActivatedUsing', 
               'Get-MgIdentityGovernancePrivilegedAccessGroupAssignmentScheduleCount', 
               'Get-MgIdentityGovernancePrivilegedAccessGroupAssignmentScheduleGroup', 
               'Get-MgIdentityGovernancePrivilegedAccessGroupAssignmentScheduleGroupServiceProvisioningError', 
               'Get-MgIdentityGovernancePrivilegedAccessGroupAssignmentScheduleGroupServiceProvisioningErrorCount', 
               'Get-MgIdentityGovernancePrivilegedAccessGroupAssignmentScheduleInstance', 
               'Get-MgIdentityGovernancePrivilegedAccessGroupAssignmentScheduleInstanceActivatedUsing', 
               'Get-MgIdentityGovernancePrivilegedAccessGroupAssignmentScheduleInstanceCount', 
               'Get-MgIdentityGovernancePrivilegedAccessGroupAssignmentScheduleInstanceGroup', 
               'Get-MgIdentityGovernancePrivilegedAccessGroupAssignmentScheduleInstanceGroupServiceProvisioningError', 
               'Get-MgIdentityGovernancePrivilegedAccessGroupAssignmentScheduleInstanceGroupServiceProvisioningErrorCount', 
               'Get-MgIdentityGovernancePrivilegedAccessGroupAssignmentScheduleInstancePrincipal', 
               'Get-MgIdentityGovernancePrivilegedAccessGroupAssignmentSchedulePrincipal', 
               'Get-MgIdentityGovernancePrivilegedAccessGroupAssignmentScheduleRequest', 
               'Get-MgIdentityGovernancePrivilegedAccessGroupAssignmentScheduleRequestActivatedUsing', 
               'Get-MgIdentityGovernancePrivilegedAccessGroupAssignmentScheduleRequestCount', 
               'Get-MgIdentityGovernancePrivilegedAccessGroupAssignmentScheduleRequestGroup', 
               'Get-MgIdentityGovernancePrivilegedAccessGroupAssignmentScheduleRequestGroupServiceProvisioningError', 
               'Get-MgIdentityGovernancePrivilegedAccessGroupAssignmentScheduleRequestGroupServiceProvisioningErrorCount', 
               'Get-MgIdentityGovernancePrivilegedAccessGroupAssignmentScheduleRequestPrincipal', 
               'Get-MgIdentityGovernancePrivilegedAccessGroupAssignmentScheduleRequestTargetSchedule', 
               'Get-MgIdentityGovernancePrivilegedAccessGroupEligibilitySchedule', 
               'Get-MgIdentityGovernancePrivilegedAccessGroupEligibilityScheduleCount', 
               'Get-MgIdentityGovernancePrivilegedAccessGroupEligibilityScheduleGroup', 
               'Get-MgIdentityGovernancePrivilegedAccessGroupEligibilityScheduleGroupServiceProvisioningError', 
               'Get-MgIdentityGovernancePrivilegedAccessGroupEligibilityScheduleGroupServiceProvisioningErrorCount', 
               'Get-MgIdentityGovernancePrivilegedAccessGroupEligibilityScheduleInstance', 
               'Get-MgIdentityGovernancePrivilegedAccessGroupEligibilityScheduleInstanceCount', 
               'Get-MgIdentityGovernancePrivilegedAccessGroupEligibilityScheduleInstanceGroup', 
               'Get-MgIdentityGovernancePrivilegedAccessGroupEligibilityScheduleInstanceGroupServiceProvisioningError', 
               'Get-MgIdentityGovernancePrivilegedAccessGroupEligibilityScheduleInstanceGroupServiceProvisioningErrorCount', 
               'Get-MgIdentityGovernancePrivilegedAccessGroupEligibilityScheduleInstancePrincipal', 
               'Get-MgIdentityGovernancePrivilegedAccessGroupEligibilitySchedulePrincipal', 
               'Get-MgIdentityGovernancePrivilegedAccessGroupEligibilityScheduleRequest', 
               'Get-MgIdentityGovernancePrivilegedAccessGroupEligibilityScheduleRequestCount', 
               'Get-MgIdentityGovernancePrivilegedAccessGroupEligibilityScheduleRequestGroup', 
               'Get-MgIdentityGovernancePrivilegedAccessGroupEligibilityScheduleRequestGroupServiceProvisioningError', 
               'Get-MgIdentityGovernancePrivilegedAccessGroupEligibilityScheduleRequestGroupServiceProvisioningErrorCount', 
               'Get-MgIdentityGovernancePrivilegedAccessGroupEligibilityScheduleRequestPrincipal', 
               'Get-MgIdentityGovernancePrivilegedAccessGroupEligibilityScheduleRequestTargetSchedule', 
               'Get-MgIdentityGovernanceTermsOfUseAgreement', 
               'Get-MgIdentityGovernanceTermsOfUseAgreementAcceptance', 
               'Get-MgIdentityGovernanceTermsOfUseAgreementAcceptanceCount', 
               'Get-MgIdentityGovernanceTermsOfUseAgreementCount', 
               'Get-MgIdentityGovernanceTermsOfUseAgreementFile', 
               'Get-MgIdentityGovernanceTermsOfUseAgreementFileCount', 
               'Get-MgIdentityGovernanceTermsOfUseAgreementFileLocalization', 
               'Get-MgIdentityGovernanceTermsOfUseAgreementFileLocalizationCount', 
               'Get-MgIdentityGovernanceTermsOfUseAgreementFileLocalizationVersion', 
               'Get-MgIdentityGovernanceTermsOfUseAgreementFileLocalizationVersionCount', 
               'Get-MgIdentityGovernanceTermsOfUseAgreementFileVersion', 
               'Get-MgIdentityGovernanceTermsOfUseAgreementFileVersionCount', 
               'Get-MgRoleManagementDirectory', 
               'Get-MgRoleManagementDirectoryResourceNamespace', 
               'Get-MgRoleManagementDirectoryResourceNamespaceCount', 
               'Get-MgRoleManagementDirectoryResourceNamespaceResourceAction', 
               'Get-MgRoleManagementDirectoryResourceNamespaceResourceActionCount', 
               'Get-MgRoleManagementDirectoryRoleAssignment', 
               'Get-MgRoleManagementDirectoryRoleAssignmentAppScope', 
               'Get-MgRoleManagementDirectoryRoleAssignmentCount', 
               'Get-MgRoleManagementDirectoryRoleAssignmentDirectoryScope', 
               'Get-MgRoleManagementDirectoryRoleAssignmentPrincipal', 
               'Get-MgRoleManagementDirectoryRoleAssignmentRoleDefinition', 
               'Get-MgRoleManagementDirectoryRoleAssignmentSchedule', 
               'Get-MgRoleManagementDirectoryRoleAssignmentScheduleActivatedUsing', 
               'Get-MgRoleManagementDirectoryRoleAssignmentScheduleAppScope', 
               'Get-MgRoleManagementDirectoryRoleAssignmentScheduleCount', 
               'Get-MgRoleManagementDirectoryRoleAssignmentScheduleDirectoryScope', 
               'Get-MgRoleManagementDirectoryRoleAssignmentScheduleInstance', 
               'Get-MgRoleManagementDirectoryRoleAssignmentScheduleInstanceActivatedUsing', 
               'Get-MgRoleManagementDirectoryRoleAssignmentScheduleInstanceAppScope', 
               'Get-MgRoleManagementDirectoryRoleAssignmentScheduleInstanceCount', 
               'Get-MgRoleManagementDirectoryRoleAssignmentScheduleInstanceDirectoryScope', 
               'Get-MgRoleManagementDirectoryRoleAssignmentScheduleInstancePrincipal', 
               'Get-MgRoleManagementDirectoryRoleAssignmentScheduleInstanceRoleDefinition', 
               'Get-MgRoleManagementDirectoryRoleAssignmentSchedulePrincipal', 
               'Get-MgRoleManagementDirectoryRoleAssignmentScheduleRequest', 
               'Get-MgRoleManagementDirectoryRoleAssignmentScheduleRequestActivatedUsing', 
               'Get-MgRoleManagementDirectoryRoleAssignmentScheduleRequestAppScope', 
               'Get-MgRoleManagementDirectoryRoleAssignmentScheduleRequestCount', 
               'Get-MgRoleManagementDirectoryRoleAssignmentScheduleRequestDirectoryScope', 
               'Get-MgRoleManagementDirectoryRoleAssignmentScheduleRequestPrincipal', 
               'Get-MgRoleManagementDirectoryRoleAssignmentScheduleRequestRoleDefinition', 
               'Get-MgRoleManagementDirectoryRoleAssignmentScheduleRequestTargetSchedule', 
               'Get-MgRoleManagementDirectoryRoleAssignmentScheduleRoleDefinition', 
               'Get-MgRoleManagementDirectoryRoleDefinition', 
               'Get-MgRoleManagementDirectoryRoleDefinitionCount', 
               'Get-MgRoleManagementDirectoryRoleDefinitionInheritPermissionFrom', 
               'Get-MgRoleManagementDirectoryRoleDefinitionInheritPermissionFromCount', 
               'Get-MgRoleManagementDirectoryRoleEligibilitySchedule', 
               'Get-MgRoleManagementDirectoryRoleEligibilityScheduleAppScope', 
               'Get-MgRoleManagementDirectoryRoleEligibilityScheduleCount', 
               'Get-MgRoleManagementDirectoryRoleEligibilityScheduleDirectoryScope', 
               'Get-MgRoleManagementDirectoryRoleEligibilityScheduleInstance', 
               'Get-MgRoleManagementDirectoryRoleEligibilityScheduleInstanceAppScope', 
               'Get-MgRoleManagementDirectoryRoleEligibilityScheduleInstanceCount', 
               'Get-MgRoleManagementDirectoryRoleEligibilityScheduleInstanceDirectoryScope', 
               'Get-MgRoleManagementDirectoryRoleEligibilityScheduleInstancePrincipal', 
               'Get-MgRoleManagementDirectoryRoleEligibilityScheduleInstanceRoleDefinition', 
               'Get-MgRoleManagementDirectoryRoleEligibilitySchedulePrincipal', 
               'Get-MgRoleManagementDirectoryRoleEligibilityScheduleRequest', 
               'Get-MgRoleManagementDirectoryRoleEligibilityScheduleRequestAppScope', 
               'Get-MgRoleManagementDirectoryRoleEligibilityScheduleRequestCount', 
               'Get-MgRoleManagementDirectoryRoleEligibilityScheduleRequestDirectoryScope', 
               'Get-MgRoleManagementDirectoryRoleEligibilityScheduleRequestPrincipal', 
               'Get-MgRoleManagementDirectoryRoleEligibilityScheduleRequestRoleDefinition', 
               'Get-MgRoleManagementDirectoryRoleEligibilityScheduleRequestTargetSchedule', 
               'Get-MgRoleManagementDirectoryRoleEligibilityScheduleRoleDefinition', 
               'Get-MgRoleManagementEntitlementManagement', 
               'Get-MgRoleManagementEntitlementManagementResourceNamespace', 
               'Get-MgRoleManagementEntitlementManagementResourceNamespaceCount', 
               'Get-MgRoleManagementEntitlementManagementResourceNamespaceResourceAction', 
               'Get-MgRoleManagementEntitlementManagementResourceNamespaceResourceActionCount', 
               'Get-MgRoleManagementEntitlementManagementRoleAssignment', 
               'Get-MgRoleManagementEntitlementManagementRoleAssignmentAppScope', 
               'Get-MgRoleManagementEntitlementManagementRoleAssignmentCount', 
               'Get-MgRoleManagementEntitlementManagementRoleAssignmentDirectoryScope', 
               'Get-MgRoleManagementEntitlementManagementRoleAssignmentPrincipal', 
               'Get-MgRoleManagementEntitlementManagementRoleAssignmentRoleDefinition', 
               'Get-MgRoleManagementEntitlementManagementRoleAssignmentSchedule', 
               'Get-MgRoleManagementEntitlementManagementRoleAssignmentScheduleActivatedUsing', 
               'Get-MgRoleManagementEntitlementManagementRoleAssignmentScheduleAppScope', 
               'Get-MgRoleManagementEntitlementManagementRoleAssignmentScheduleCount', 
               'Get-MgRoleManagementEntitlementManagementRoleAssignmentScheduleDirectoryScope', 
               'Get-MgRoleManagementEntitlementManagementRoleAssignmentScheduleInstance', 
               'Get-MgRoleManagementEntitlementManagementRoleAssignmentScheduleInstanceActivatedUsing', 
               'Get-MgRoleManagementEntitlementManagementRoleAssignmentScheduleInstanceAppScope', 
               'Get-MgRoleManagementEntitlementManagementRoleAssignmentScheduleInstanceCount', 
               'Get-MgRoleManagementEntitlementManagementRoleAssignmentScheduleInstanceDirectoryScope', 
               'Get-MgRoleManagementEntitlementManagementRoleAssignmentScheduleInstancePrincipal', 
               'Get-MgRoleManagementEntitlementManagementRoleAssignmentScheduleInstanceRoleDefinition', 
               'Get-MgRoleManagementEntitlementManagementRoleAssignmentSchedulePrincipal', 
               'Get-MgRoleManagementEntitlementManagementRoleAssignmentScheduleRequest', 
               'Get-MgRoleManagementEntitlementManagementRoleAssignmentScheduleRequestActivatedUsing', 
               'Get-MgRoleManagementEntitlementManagementRoleAssignmentScheduleRequestAppScope', 
               'Get-MgRoleManagementEntitlementManagementRoleAssignmentScheduleRequestCount', 
               'Get-MgRoleManagementEntitlementManagementRoleAssignmentScheduleRequestDirectoryScope', 
               'Get-MgRoleManagementEntitlementManagementRoleAssignmentScheduleRequestPrincipal', 
               'Get-MgRoleManagementEntitlementManagementRoleAssignmentScheduleRequestRoleDefinition', 
               'Get-MgRoleManagementEntitlementManagementRoleAssignmentScheduleRequestTargetSchedule', 
               'Get-MgRoleManagementEntitlementManagementRoleAssignmentScheduleRoleDefinition', 
               'Get-MgRoleManagementEntitlementManagementRoleDefinition', 
               'Get-MgRoleManagementEntitlementManagementRoleDefinitionCount', 
               'Get-MgRoleManagementEntitlementManagementRoleDefinitionInheritPermissionFrom', 
               'Get-MgRoleManagementEntitlementManagementRoleDefinitionInheritPermissionFromCount', 
               'Get-MgRoleManagementEntitlementManagementRoleEligibilitySchedule', 
               'Get-MgRoleManagementEntitlementManagementRoleEligibilityScheduleAppScope', 
               'Get-MgRoleManagementEntitlementManagementRoleEligibilityScheduleCount', 
               'Get-MgRoleManagementEntitlementManagementRoleEligibilityScheduleDirectoryScope', 
               'Get-MgRoleManagementEntitlementManagementRoleEligibilityScheduleInstance', 
               'Get-MgRoleManagementEntitlementManagementRoleEligibilityScheduleInstanceAppScope', 
               'Get-MgRoleManagementEntitlementManagementRoleEligibilityScheduleInstanceCount', 
               'Get-MgRoleManagementEntitlementManagementRoleEligibilityScheduleInstanceDirectoryScope', 
               'Get-MgRoleManagementEntitlementManagementRoleEligibilityScheduleInstancePrincipal', 
               'Get-MgRoleManagementEntitlementManagementRoleEligibilityScheduleInstanceRoleDefinition', 
               'Get-MgRoleManagementEntitlementManagementRoleEligibilitySchedulePrincipal', 
               'Get-MgRoleManagementEntitlementManagementRoleEligibilityScheduleRequest', 
               'Get-MgRoleManagementEntitlementManagementRoleEligibilityScheduleRequestAppScope', 
               'Get-MgRoleManagementEntitlementManagementRoleEligibilityScheduleRequestCount', 
               'Get-MgRoleManagementEntitlementManagementRoleEligibilityScheduleRequestDirectoryScope', 
               'Get-MgRoleManagementEntitlementManagementRoleEligibilityScheduleRequestPrincipal', 
               'Get-MgRoleManagementEntitlementManagementRoleEligibilityScheduleRequestRoleDefinition', 
               'Get-MgRoleManagementEntitlementManagementRoleEligibilityScheduleRequestTargetSchedule', 
               'Get-MgRoleManagementEntitlementManagementRoleEligibilityScheduleRoleDefinition', 
               'Get-MgUserAgreementAcceptance', 
               'Get-MgUserAgreementAcceptanceCount', 
               'Initialize-MgIdentityGovernanceLifecycleWorkflow', 
               'Initialize-MgIdentityGovernanceLifecycleWorkflowDeletedItemWorkflow', 
               'Invoke-MgAcceptIdentityGovernanceAccessReviewDefinitionInstanceRecommendation', 
               'Invoke-MgBatchIdentityGovernanceAccessReviewDefinitionInstanceRecordDecision', 
               'Invoke-MgFilterEntitlementManagementAccessPackageAssignmentApprovalByCurrentUser', 
               'Invoke-MgFilterEntitlementManagementAccessPackageByCurrentUser', 
               'Invoke-MgFilterEntitlementManagementAssignmentByCurrentUser', 
               'Invoke-MgFilterEntitlementManagementAssignmentRequestByCurrentUser', 
               'Invoke-MgFilterIdentityGovernanceAccessReviewDefinitionByCurrentUser', 
               'Invoke-MgFilterIdentityGovernanceAccessReviewDefinitionInstanceByCurrentUser', 
               'Invoke-MgFilterIdentityGovernanceAccessReviewDefinitionInstanceDecisionByCurrentUser', 
               'Invoke-MgFilterIdentityGovernanceAccessReviewDefinitionInstanceStageByCurrentUser', 
               'Invoke-MgFilterIdentityGovernanceAccessReviewDefinitionInstanceStageDecisionByCurrentUser', 
               'Invoke-MgFilterIdentityGovernanceAppConsentRequestByCurrentUser', 
               'Invoke-MgFilterIdentityGovernanceAppConsentRequestUserConsentRequestByCurrentUser', 
               'Invoke-MgFilterIdentityGovernancePrivilegedAccessGroupAssignmentApprovalByCurrentUser', 
               'Invoke-MgFilterIdentityGovernancePrivilegedAccessGroupAssignmentScheduleByCurrentUser', 
               'Invoke-MgFilterIdentityGovernancePrivilegedAccessGroupAssignmentScheduleInstanceByCurrentUser', 
               'Invoke-MgFilterIdentityGovernancePrivilegedAccessGroupAssignmentScheduleRequestByCurrentUser', 
               'Invoke-MgFilterIdentityGovernancePrivilegedAccessGroupEligibilityScheduleByCurrentUser', 
               'Invoke-MgFilterIdentityGovernancePrivilegedAccessGroupEligibilityScheduleInstanceByCurrentUser', 
               'Invoke-MgFilterIdentityGovernancePrivilegedAccessGroupEligibilityScheduleRequestByCurrentUser', 
               'Invoke-MgFilterRoleManagementDirectoryRoleAssignmentScheduleByCurrentUser', 
               'Invoke-MgFilterRoleManagementDirectoryRoleAssignmentScheduleInstanceByCurrentUser', 
               'Invoke-MgFilterRoleManagementDirectoryRoleAssignmentScheduleRequestByCurrentUser', 
               'Invoke-MgFilterRoleManagementDirectoryRoleEligibilityScheduleByCurrentUser', 
               'Invoke-MgFilterRoleManagementDirectoryRoleEligibilityScheduleInstanceByCurrentUser', 
               'Invoke-MgFilterRoleManagementDirectoryRoleEligibilityScheduleRequestByCurrentUser', 
               'Invoke-MgFilterRoleManagementEntitlementManagementRoleAssignmentScheduleByCurrentUser', 
               'Invoke-MgFilterRoleManagementEntitlementManagementRoleAssignmentScheduleInstanceByCurrentUser', 
               'Invoke-MgFilterRoleManagementEntitlementManagementRoleAssignmentScheduleRequestByCurrentUser', 
               'Invoke-MgFilterRoleManagementEntitlementManagementRoleEligibilityScheduleByCurrentUser', 
               'Invoke-MgFilterRoleManagementEntitlementManagementRoleEligibilityScheduleInstanceByCurrentUser', 
               'Invoke-MgFilterRoleManagementEntitlementManagementRoleEligibilityScheduleRequestByCurrentUser', 
               'Invoke-MgGraphIdentityGovernanceLifecycleWorkflowInsight', 
               'Invoke-MgSummaryIdentityGovernanceLifecycleWorkflowRun', 
               'Invoke-MgSummaryIdentityGovernanceLifecycleWorkflowRunUserProcessingResult', 
               'Invoke-MgSummaryIdentityGovernanceLifecycleWorkflowTaskReport', 
               'Invoke-MgSummaryIdentityGovernanceLifecycleWorkflowUserProcessingResult', 
               'Invoke-MgTopIdentityGovernanceLifecycleWorkflowInsightTaskProcessedSummary', 
               'Invoke-MgTopIdentityGovernanceLifecycleWorkflowInsightWorkflowProcessedSummary', 
               'Invoke-MgWorkflowIdentityGovernanceLifecycleWorkflowInsightProcessedSummary', 
               'New-MgAgreement', 'New-MgAgreementAcceptance', 'New-MgAgreementFile', 
               'New-MgAgreementFileLocalization', 
               'New-MgAgreementFileLocalizationVersion', 
               'New-MgAgreementFileVersion', 
               'New-MgEntitlementManagementAccessPackage', 
               'New-MgEntitlementManagementAccessPackageAssignmentApprovalStage', 
               'New-MgEntitlementManagementAccessPackageAssignmentPolicy', 
               'New-MgEntitlementManagementAccessPackageIncompatibleAccessPackageByRef', 
               'New-MgEntitlementManagementAccessPackageIncompatibleGroupByRef', 
               'New-MgEntitlementManagementAccessPackageResourceRoleScope', 
               'New-MgEntitlementManagementAssignment', 
               'New-MgEntitlementManagementAssignmentPolicy', 
               'New-MgEntitlementManagementAssignmentPolicyCustomExtensionStageSetting', 
               'New-MgEntitlementManagementAssignmentPolicyQuestion', 
               'New-MgEntitlementManagementAssignmentRequest', 
               'New-MgEntitlementManagementCatalog', 
               'New-MgEntitlementManagementCatalogCustomWorkflowExtension', 
               'New-MgEntitlementManagementCatalogResource', 
               'New-MgEntitlementManagementCatalogResourceRole', 
               'New-MgEntitlementManagementCatalogResourceRoleResourceScope', 
               'New-MgEntitlementManagementCatalogResourceRoleResourceScopeResourceRole', 
               'New-MgEntitlementManagementCatalogResourceScope', 
               'New-MgEntitlementManagementCatalogResourceScopeResourceRole', 
               'New-MgEntitlementManagementCatalogResourceScopeResourceRoleResourceScope', 
               'New-MgEntitlementManagementConnectedOrganization', 
               'New-MgEntitlementManagementConnectedOrganizationExternalSponsorByRef', 
               'New-MgEntitlementManagementConnectedOrganizationInternalSponsorByRef', 
               'New-MgEntitlementManagementResource', 
               'New-MgEntitlementManagementResourceEnvironment', 
               'New-MgEntitlementManagementResourceEnvironmentResource', 
               'New-MgEntitlementManagementResourceEnvironmentResourceRole', 
               'New-MgEntitlementManagementResourceEnvironmentResourceRoleResourceScope', 
               'New-MgEntitlementManagementResourceEnvironmentResourceScope', 
               'New-MgEntitlementManagementResourceEnvironmentResourceScopeResourceRole', 
               'New-MgEntitlementManagementResourceRequest', 
               'New-MgEntitlementManagementResourceRequestCatalogCustomWorkflowExtension', 
               'New-MgEntitlementManagementResourceRequestCatalogResource', 
               'New-MgEntitlementManagementResourceRequestCatalogResourceRole', 
               'New-MgEntitlementManagementResourceRequestCatalogResourceRoleResourceScope', 
               'New-MgEntitlementManagementResourceRequestCatalogResourceRoleResourceScopeResourceRole', 
               'New-MgEntitlementManagementResourceRequestCatalogResourceScope', 
               'New-MgEntitlementManagementResourceRequestCatalogResourceScopeResourceRole', 
               'New-MgEntitlementManagementResourceRequestCatalogResourceScopeResourceRoleResourceScope', 
               'New-MgEntitlementManagementResourceRequestResourceRole', 
               'New-MgEntitlementManagementResourceRequestResourceRoleResourceScope', 
               'New-MgEntitlementManagementResourceRequestResourceScope', 
               'New-MgEntitlementManagementResourceRequestResourceScopeResourceRole', 
               'New-MgEntitlementManagementResourceRole', 
               'New-MgEntitlementManagementResourceRoleResourceScope', 
               'New-MgEntitlementManagementResourceRoleScope', 
               'New-MgEntitlementManagementResourceRoleScopeResourceRole', 
               'New-MgEntitlementManagementResourceRoleScopeResourceRoleResourceScope', 
               'New-MgEntitlementManagementResourceRoleScopeResourceScope', 
               'New-MgEntitlementManagementResourceRoleScopeRoleResourceRole', 
               'New-MgEntitlementManagementResourceRoleScopeRoleResourceScope', 
               'New-MgEntitlementManagementResourceRoleScopeRoleResourceScopeResourceRole', 
               'New-MgEntitlementManagementResourceScope', 
               'New-MgEntitlementManagementResourceScopeResourceRole', 
               'New-MgIdentityGovernanceAccessReviewDefinition', 
               'New-MgIdentityGovernanceAccessReviewDefinitionInstance', 
               'New-MgIdentityGovernanceAccessReviewDefinitionInstanceContactedReviewer', 
               'New-MgIdentityGovernanceAccessReviewDefinitionInstanceDecision', 
               'New-MgIdentityGovernanceAccessReviewDefinitionInstanceDecisionInsight', 
               'New-MgIdentityGovernanceAccessReviewDefinitionInstanceStage', 
               'New-MgIdentityGovernanceAccessReviewDefinitionInstanceStageDecision', 
               'New-MgIdentityGovernanceAccessReviewDefinitionInstanceStageDecisionInsight', 
               'New-MgIdentityGovernanceAccessReviewHistoryDefinition', 
               'New-MgIdentityGovernanceAccessReviewHistoryDefinitionInstance', 
               'New-MgIdentityGovernanceAccessReviewHistoryDefinitionInstanceDownloadUri', 
               'New-MgIdentityGovernanceAppConsentRequest', 
               'New-MgIdentityGovernanceAppConsentRequestUserConsentRequest', 
               'New-MgIdentityGovernanceAppConsentRequestUserConsentRequestApprovalStage', 
               'New-MgIdentityGovernanceLifecycleWorkflow', 
               'New-MgIdentityGovernanceLifecycleWorkflowCustomTaskExtension', 
               'New-MgIdentityGovernanceLifecycleWorkflowDeletedItemWorkflowNewVersion', 
               'New-MgIdentityGovernanceLifecycleWorkflowDeletedItemWorkflowTask', 
               'New-MgIdentityGovernanceLifecycleWorkflowNewVersion', 
               'New-MgIdentityGovernanceLifecycleWorkflowTask', 
               'New-MgIdentityGovernanceLifecycleWorkflowVersionTask', 
               'New-MgIdentityGovernancePrivilegedAccessGroupAssignmentApproval', 
               'New-MgIdentityGovernancePrivilegedAccessGroupAssignmentApprovalStage', 
               'New-MgIdentityGovernancePrivilegedAccessGroupAssignmentSchedule', 
               'New-MgIdentityGovernancePrivilegedAccessGroupAssignmentScheduleInstance', 
               'New-MgIdentityGovernancePrivilegedAccessGroupAssignmentScheduleRequest', 
               'New-MgIdentityGovernancePrivilegedAccessGroupEligibilitySchedule', 
               'New-MgIdentityGovernancePrivilegedAccessGroupEligibilityScheduleInstance', 
               'New-MgIdentityGovernancePrivilegedAccessGroupEligibilityScheduleRequest', 
               'New-MgIdentityGovernanceTermsOfUseAgreement', 
               'New-MgIdentityGovernanceTermsOfUseAgreementAcceptance', 
               'New-MgIdentityGovernanceTermsOfUseAgreementFile', 
               'New-MgIdentityGovernanceTermsOfUseAgreementFileLocalization', 
               'New-MgIdentityGovernanceTermsOfUseAgreementFileLocalizationVersion', 
               'New-MgIdentityGovernanceTermsOfUseAgreementFileVersion', 
               'New-MgRoleManagementDirectoryResourceNamespace', 
               'New-MgRoleManagementDirectoryResourceNamespaceResourceAction', 
               'New-MgRoleManagementDirectoryRoleAssignment', 
               'New-MgRoleManagementDirectoryRoleAssignmentSchedule', 
               'New-MgRoleManagementDirectoryRoleAssignmentScheduleInstance', 
               'New-MgRoleManagementDirectoryRoleAssignmentScheduleRequest', 
               'New-MgRoleManagementDirectoryRoleDefinition', 
               'New-MgRoleManagementDirectoryRoleDefinitionInheritPermissionFrom', 
               'New-MgRoleManagementDirectoryRoleEligibilitySchedule', 
               'New-MgRoleManagementDirectoryRoleEligibilityScheduleInstance', 
               'New-MgRoleManagementDirectoryRoleEligibilityScheduleRequest', 
               'New-MgRoleManagementEntitlementManagementResourceNamespace', 
               'New-MgRoleManagementEntitlementManagementResourceNamespaceResourceAction', 
               'New-MgRoleManagementEntitlementManagementRoleAssignment', 
               'New-MgRoleManagementEntitlementManagementRoleAssignmentSchedule', 
               'New-MgRoleManagementEntitlementManagementRoleAssignmentScheduleInstance', 
               'New-MgRoleManagementEntitlementManagementRoleAssignmentScheduleRequest', 
               'New-MgRoleManagementEntitlementManagementRoleDefinition', 
               'New-MgRoleManagementEntitlementManagementRoleDefinitionInheritPermissionFrom', 
               'New-MgRoleManagementEntitlementManagementRoleEligibilitySchedule', 
               'New-MgRoleManagementEntitlementManagementRoleEligibilityScheduleInstance', 
               'New-MgRoleManagementEntitlementManagementRoleEligibilityScheduleRequest', 
               'Remove-MgAgreement', 'Remove-MgAgreementAcceptance', 
               'Remove-MgAgreementFile', 'Remove-MgAgreementFileLocalization', 
               'Remove-MgAgreementFileLocalizationVersion', 
               'Remove-MgAgreementFileVersion', 
               'Remove-MgEntitlementManagementAccessPackage', 
               'Remove-MgEntitlementManagementAccessPackageAssignmentApproval', 
               'Remove-MgEntitlementManagementAccessPackageAssignmentApprovalStage', 
               'Remove-MgEntitlementManagementAccessPackageAssignmentPolicy', 
               'Remove-MgEntitlementManagementAccessPackageIncompatibleAccessPackageByRef', 
               'Remove-MgEntitlementManagementAccessPackageIncompatibleGroupByRef', 
               'Remove-MgEntitlementManagementAccessPackageResourceRoleScope', 
               'Remove-MgEntitlementManagementAssignment', 
               'Remove-MgEntitlementManagementAssignmentPolicy', 
               'Remove-MgEntitlementManagementAssignmentPolicyCustomExtensionStageSetting', 
               'Remove-MgEntitlementManagementAssignmentPolicyQuestion', 
               'Remove-MgEntitlementManagementAssignmentRequest', 
               'Remove-MgEntitlementManagementCatalog', 
               'Remove-MgEntitlementManagementCatalogCustomWorkflowExtension', 
               'Remove-MgEntitlementManagementCatalogResource', 
               'Remove-MgEntitlementManagementCatalogResourceRole', 
               'Remove-MgEntitlementManagementCatalogResourceRoleResource', 
               'Remove-MgEntitlementManagementCatalogResourceRoleResourceScope', 
               'Remove-MgEntitlementManagementCatalogResourceRoleResourceScopeResource', 
               'Remove-MgEntitlementManagementCatalogResourceRoleResourceScopeResourceRole', 
               'Remove-MgEntitlementManagementCatalogResourceScope', 
               'Remove-MgEntitlementManagementCatalogResourceScopeResource', 
               'Remove-MgEntitlementManagementCatalogResourceScopeResourceRole', 
               'Remove-MgEntitlementManagementCatalogResourceScopeResourceRoleResource', 
               'Remove-MgEntitlementManagementCatalogResourceScopeResourceRoleResourceScope', 
               'Remove-MgEntitlementManagementConnectedOrganization', 
               'Remove-MgEntitlementManagementConnectedOrganizationExternalSponsorDirectoryObjectByRef', 
               'Remove-MgEntitlementManagementConnectedOrganizationInternalSponsorDirectoryObjectByRef', 
               'Remove-MgEntitlementManagementResource', 
               'Remove-MgEntitlementManagementResourceEnvironment', 
               'Remove-MgEntitlementManagementResourceEnvironmentResource', 
               'Remove-MgEntitlementManagementResourceEnvironmentResourceRole', 
               'Remove-MgEntitlementManagementResourceEnvironmentResourceRoleResource', 
               'Remove-MgEntitlementManagementResourceEnvironmentResourceRoleResourceScope', 
               'Remove-MgEntitlementManagementResourceEnvironmentResourceRoleResourceScopeResource', 
               'Remove-MgEntitlementManagementResourceEnvironmentResourceScope', 
               'Remove-MgEntitlementManagementResourceEnvironmentResourceScopeResource', 
               'Remove-MgEntitlementManagementResourceEnvironmentResourceScopeResourceRole', 
               'Remove-MgEntitlementManagementResourceEnvironmentResourceScopeResourceRoleResource', 
               'Remove-MgEntitlementManagementResourceRequest', 
               'Remove-MgEntitlementManagementResourceRequestCatalog', 
               'Remove-MgEntitlementManagementResourceRequestCatalogCustomWorkflowExtension', 
               'Remove-MgEntitlementManagementResourceRequestCatalogResource', 
               'Remove-MgEntitlementManagementResourceRequestCatalogResourceRole', 
               'Remove-MgEntitlementManagementResourceRequestCatalogResourceRoleResource', 
               'Remove-MgEntitlementManagementResourceRequestCatalogResourceRoleResourceScope', 
               'Remove-MgEntitlementManagementResourceRequestCatalogResourceRoleResourceScopeResource', 
               'Remove-MgEntitlementManagementResourceRequestCatalogResourceRoleResourceScopeResourceRole', 
               'Remove-MgEntitlementManagementResourceRequestCatalogResourceScope', 
               'Remove-MgEntitlementManagementResourceRequestCatalogResourceScopeResource', 
               'Remove-MgEntitlementManagementResourceRequestCatalogResourceScopeResourceRole', 
               'Remove-MgEntitlementManagementResourceRequestCatalogResourceScopeResourceRoleResource', 
               'Remove-MgEntitlementManagementResourceRequestCatalogResourceScopeResourceRoleResourceScope', 
               'Remove-MgEntitlementManagementResourceRequestResource', 
               'Remove-MgEntitlementManagementResourceRequestResourceRole', 
               'Remove-MgEntitlementManagementResourceRequestResourceRoleResource', 
               'Remove-MgEntitlementManagementResourceRequestResourceRoleResourceScope', 
               'Remove-MgEntitlementManagementResourceRequestResourceRoleResourceScopeResource', 
               'Remove-MgEntitlementManagementResourceRequestResourceScope', 
               'Remove-MgEntitlementManagementResourceRequestResourceScopeResource', 
               'Remove-MgEntitlementManagementResourceRequestResourceScopeResourceRole', 
               'Remove-MgEntitlementManagementResourceRequestResourceScopeResourceRoleResource', 
               'Remove-MgEntitlementManagementResourceRole', 
               'Remove-MgEntitlementManagementResourceRoleResource', 
               'Remove-MgEntitlementManagementResourceRoleResourceScope', 
               'Remove-MgEntitlementManagementResourceRoleResourceScopeResource', 
               'Remove-MgEntitlementManagementResourceRoleScope', 
               'Remove-MgEntitlementManagementResourceRoleScopeResource', 
               'Remove-MgEntitlementManagementResourceRoleScopeResourceRole', 
               'Remove-MgEntitlementManagementResourceRoleScopeResourceRoleResource', 
               'Remove-MgEntitlementManagementResourceRoleScopeResourceRoleResourceScope', 
               'Remove-MgEntitlementManagementResourceRoleScopeResourceScope', 
               'Remove-MgEntitlementManagementResourceRoleScopeRole', 
               'Remove-MgEntitlementManagementResourceRoleScopeRoleResource', 
               'Remove-MgEntitlementManagementResourceRoleScopeRoleResourceRole', 
               'Remove-MgEntitlementManagementResourceRoleScopeRoleResourceScope', 
               'Remove-MgEntitlementManagementResourceRoleScopeRoleResourceScopeResource', 
               'Remove-MgEntitlementManagementResourceRoleScopeRoleResourceScopeResourceRole', 
               'Remove-MgEntitlementManagementResourceScope', 
               'Remove-MgEntitlementManagementResourceScopeResource', 
               'Remove-MgEntitlementManagementResourceScopeResourceRole', 
               'Remove-MgEntitlementManagementResourceScopeResourceRoleResource', 
               'Remove-MgIdentityGovernanceAccessReviewDefinition', 
               'Remove-MgIdentityGovernanceAccessReviewDefinitionInstance', 
               'Remove-MgIdentityGovernanceAccessReviewDefinitionInstanceContactedReviewer', 
               'Remove-MgIdentityGovernanceAccessReviewDefinitionInstanceDecision', 
               'Remove-MgIdentityGovernanceAccessReviewDefinitionInstanceDecisionInsight', 
               'Remove-MgIdentityGovernanceAccessReviewDefinitionInstanceStage', 
               'Remove-MgIdentityGovernanceAccessReviewDefinitionInstanceStageDecision', 
               'Remove-MgIdentityGovernanceAccessReviewDefinitionInstanceStageDecisionInsight', 
               'Remove-MgIdentityGovernanceAccessReviewHistoryDefinition', 
               'Remove-MgIdentityGovernanceAccessReviewHistoryDefinitionInstance', 
               'Remove-MgIdentityGovernanceAppConsentRequest', 
               'Remove-MgIdentityGovernanceAppConsentRequestUserConsentRequest', 
               'Remove-MgIdentityGovernanceAppConsentRequestUserConsentRequestApproval', 
               'Remove-MgIdentityGovernanceAppConsentRequestUserConsentRequestApprovalStage', 
               'Remove-MgIdentityGovernanceLifecycleWorkflow', 
               'Remove-MgIdentityGovernanceLifecycleWorkflowCustomTaskExtension', 
               'Remove-MgIdentityGovernanceLifecycleWorkflowDeletedItem', 
               'Remove-MgIdentityGovernanceLifecycleWorkflowDeletedItemWorkflow', 
               'Remove-MgIdentityGovernanceLifecycleWorkflowDeletedItemWorkflowTask', 
               'Remove-MgIdentityGovernanceLifecycleWorkflowInsight', 
               'Remove-MgIdentityGovernanceLifecycleWorkflowTask', 
               'Remove-MgIdentityGovernanceLifecycleWorkflowVersionTask', 
               'Remove-MgIdentityGovernancePrivilegedAccess', 
               'Remove-MgIdentityGovernancePrivilegedAccessGroup', 
               'Remove-MgIdentityGovernancePrivilegedAccessGroupAssignmentApproval', 
               'Remove-MgIdentityGovernancePrivilegedAccessGroupAssignmentApprovalStage', 
               'Remove-MgIdentityGovernancePrivilegedAccessGroupAssignmentSchedule', 
               'Remove-MgIdentityGovernancePrivilegedAccessGroupAssignmentScheduleInstance', 
               'Remove-MgIdentityGovernancePrivilegedAccessGroupAssignmentScheduleRequest', 
               'Remove-MgIdentityGovernancePrivilegedAccessGroupEligibilitySchedule', 
               'Remove-MgIdentityGovernancePrivilegedAccessGroupEligibilityScheduleInstance', 
               'Remove-MgIdentityGovernancePrivilegedAccessGroupEligibilityScheduleRequest', 
               'Remove-MgIdentityGovernanceTermsOfUseAgreement', 
               'Remove-MgIdentityGovernanceTermsOfUseAgreementAcceptance', 
               'Remove-MgIdentityGovernanceTermsOfUseAgreementFile', 
               'Remove-MgIdentityGovernanceTermsOfUseAgreementFileLocalization', 
               'Remove-MgIdentityGovernanceTermsOfUseAgreementFileLocalizationVersion', 
               'Remove-MgIdentityGovernanceTermsOfUseAgreementFileVersion', 
               'Remove-MgRoleManagementDirectory', 
               'Remove-MgRoleManagementDirectoryResourceNamespace', 
               'Remove-MgRoleManagementDirectoryResourceNamespaceResourceAction', 
               'Remove-MgRoleManagementDirectoryRoleAssignment', 
               'Remove-MgRoleManagementDirectoryRoleAssignmentAppScope', 
               'Remove-MgRoleManagementDirectoryRoleAssignmentSchedule', 
               'Remove-MgRoleManagementDirectoryRoleAssignmentScheduleInstance', 
               'Remove-MgRoleManagementDirectoryRoleAssignmentScheduleRequest', 
               'Remove-MgRoleManagementDirectoryRoleDefinition', 
               'Remove-MgRoleManagementDirectoryRoleDefinitionInheritPermissionFrom', 
               'Remove-MgRoleManagementDirectoryRoleEligibilitySchedule', 
               'Remove-MgRoleManagementDirectoryRoleEligibilityScheduleInstance', 
               'Remove-MgRoleManagementDirectoryRoleEligibilityScheduleRequest', 
               'Remove-MgRoleManagementEntitlementManagement', 
               'Remove-MgRoleManagementEntitlementManagementResourceNamespace', 
               'Remove-MgRoleManagementEntitlementManagementResourceNamespaceResourceAction', 
               'Remove-MgRoleManagementEntitlementManagementRoleAssignment', 
               'Remove-MgRoleManagementEntitlementManagementRoleAssignmentAppScope', 
               'Remove-MgRoleManagementEntitlementManagementRoleAssignmentSchedule', 
               'Remove-MgRoleManagementEntitlementManagementRoleAssignmentScheduleInstance', 
               'Remove-MgRoleManagementEntitlementManagementRoleAssignmentScheduleRequest', 
               'Remove-MgRoleManagementEntitlementManagementRoleDefinition', 
               'Remove-MgRoleManagementEntitlementManagementRoleDefinitionInheritPermissionFrom', 
               'Remove-MgRoleManagementEntitlementManagementRoleEligibilitySchedule', 
               'Remove-MgRoleManagementEntitlementManagementRoleEligibilityScheduleInstance', 
               'Remove-MgRoleManagementEntitlementManagementRoleEligibilityScheduleRequest', 
               'Reset-MgIdentityGovernanceAccessReviewDefinitionInstanceDecision', 
               'Restore-MgIdentityGovernanceLifecycleWorkflow', 
               'Restore-MgIdentityGovernanceLifecycleWorkflowDeletedItemWorkflow', 
               'Resume-MgEntitlementManagementAssignmentRequest', 
               'Resume-MgIdentityGovernanceLifecycleWorkflowRunTaskProcessingResult', 
               'Resume-MgIdentityGovernanceLifecycleWorkflowTaskProcessingResult', 
               'Resume-MgIdentityGovernanceLifecycleWorkflowTaskReportTaskProcessingResult', 
               'Resume-MgIdentityGovernanceLifecycleWorkflowTemplateTaskProcessingResult', 
               'Resume-MgIdentityGovernanceLifecycleWorkflowVersionTaskProcessingResult', 
               'Send-MgIdentityGovernanceAccessReviewDefinitionInstanceReminder', 
               'Set-MgEntitlementManagementAssignmentPolicy', 
               'Set-MgIdentityGovernanceAccessReviewDefinition', 
               'Stop-MgEntitlementManagementAssignmentRequest', 
               'Stop-MgIdentityGovernanceAccessReviewDefinition', 
               'Stop-MgIdentityGovernanceAccessReviewDefinitionInstance', 
               'Stop-MgIdentityGovernanceAccessReviewDefinitionInstanceStage', 
               'Stop-MgIdentityGovernancePrivilegedAccessGroupAssignmentScheduleRequest', 
               'Stop-MgIdentityGovernancePrivilegedAccessGroupEligibilityScheduleRequest', 
               'Stop-MgRoleManagementDirectoryRoleAssignmentScheduleRequest', 
               'Stop-MgRoleManagementDirectoryRoleEligibilityScheduleRequest', 
               'Stop-MgRoleManagementEntitlementManagementRoleAssignmentScheduleRequest', 
               'Stop-MgRoleManagementEntitlementManagementRoleEligibilityScheduleRequest', 
               'Update-MgAgreement', 'Update-MgAgreementAcceptance', 
               'Update-MgAgreementFile', 'Update-MgAgreementFileLocalization', 
               'Update-MgAgreementFileLocalizationVersion', 
               'Update-MgAgreementFileVersion', 
               'Update-MgEntitlementManagementAccessPackage', 
               'Update-MgEntitlementManagementAccessPackageAssignmentApproval', 
               'Update-MgEntitlementManagementAccessPackageAssignmentApprovalStage', 
               'Update-MgEntitlementManagementAccessPackageAssignmentPolicy', 
               'Update-MgEntitlementManagementAccessPackageResourceRoleScope', 
               'Update-MgEntitlementManagementAssignment', 
               'Update-MgEntitlementManagementAssignmentPolicyCustomExtensionStageSetting', 
               'Update-MgEntitlementManagementAssignmentPolicyQuestion', 
               'Update-MgEntitlementManagementAssignmentRequest', 
               'Update-MgEntitlementManagementCatalog', 
               'Update-MgEntitlementManagementCatalogCustomWorkflowExtension', 
               'Update-MgEntitlementManagementCatalogResource', 
               'Update-MgEntitlementManagementCatalogResourceRole', 
               'Update-MgEntitlementManagementCatalogResourceRoleResource', 
               'Update-MgEntitlementManagementCatalogResourceRoleResourceScope', 
               'Update-MgEntitlementManagementCatalogResourceRoleResourceScopeResource', 
               'Update-MgEntitlementManagementCatalogResourceRoleResourceScopeResourceRole', 
               'Update-MgEntitlementManagementCatalogResourceScope', 
               'Update-MgEntitlementManagementCatalogResourceScopeResource', 
               'Update-MgEntitlementManagementCatalogResourceScopeResourceRole', 
               'Update-MgEntitlementManagementCatalogResourceScopeResourceRoleResource', 
               'Update-MgEntitlementManagementCatalogResourceScopeResourceRoleResourceScope', 
               'Update-MgEntitlementManagementConnectedOrganization', 
               'Update-MgEntitlementManagementResource', 
               'Update-MgEntitlementManagementResourceEnvironment', 
               'Update-MgEntitlementManagementResourceEnvironmentResource', 
               'Update-MgEntitlementManagementResourceEnvironmentResourceRole', 
               'Update-MgEntitlementManagementResourceEnvironmentResourceRoleResource', 
               'Update-MgEntitlementManagementResourceEnvironmentResourceRoleResourceScope', 
               'Update-MgEntitlementManagementResourceEnvironmentResourceRoleResourceScopeResource', 
               'Update-MgEntitlementManagementResourceEnvironmentResourceScope', 
               'Update-MgEntitlementManagementResourceEnvironmentResourceScopeResource', 
               'Update-MgEntitlementManagementResourceEnvironmentResourceScopeResourceRole', 
               'Update-MgEntitlementManagementResourceEnvironmentResourceScopeResourceRoleResource', 
               'Update-MgEntitlementManagementResourceRequest', 
               'Update-MgEntitlementManagementResourceRequestCatalog', 
               'Update-MgEntitlementManagementResourceRequestCatalogCustomWorkflowExtension', 
               'Update-MgEntitlementManagementResourceRequestCatalogResource', 
               'Update-MgEntitlementManagementResourceRequestCatalogResourceRole', 
               'Update-MgEntitlementManagementResourceRequestCatalogResourceRoleResource', 
               'Update-MgEntitlementManagementResourceRequestCatalogResourceRoleResourceScope', 
               'Update-MgEntitlementManagementResourceRequestCatalogResourceRoleResourceScopeResource', 
               'Update-MgEntitlementManagementResourceRequestCatalogResourceRoleResourceScopeResourceRole', 
               'Update-MgEntitlementManagementResourceRequestCatalogResourceScope', 
               'Update-MgEntitlementManagementResourceRequestCatalogResourceScopeResource', 
               'Update-MgEntitlementManagementResourceRequestCatalogResourceScopeResourceRole', 
               'Update-MgEntitlementManagementResourceRequestCatalogResourceScopeResourceRoleResource', 
               'Update-MgEntitlementManagementResourceRequestCatalogResourceScopeResourceRoleResourceScope', 
               'Update-MgEntitlementManagementResourceRequestResource', 
               'Update-MgEntitlementManagementResourceRequestResourceRole', 
               'Update-MgEntitlementManagementResourceRequestResourceRoleResource', 
               'Update-MgEntitlementManagementResourceRequestResourceRoleResourceScope', 
               'Update-MgEntitlementManagementResourceRequestResourceRoleResourceScopeResource', 
               'Update-MgEntitlementManagementResourceRequestResourceScope', 
               'Update-MgEntitlementManagementResourceRequestResourceScopeResource', 
               'Update-MgEntitlementManagementResourceRequestResourceScopeResourceRole', 
               'Update-MgEntitlementManagementResourceRequestResourceScopeResourceRoleResource', 
               'Update-MgEntitlementManagementResourceRole', 
               'Update-MgEntitlementManagementResourceRoleResource', 
               'Update-MgEntitlementManagementResourceRoleResourceScope', 
               'Update-MgEntitlementManagementResourceRoleResourceScopeResource', 
               'Update-MgEntitlementManagementResourceRoleScope', 
               'Update-MgEntitlementManagementResourceRoleScopeResource', 
               'Update-MgEntitlementManagementResourceRoleScopeResourceRole', 
               'Update-MgEntitlementManagementResourceRoleScopeResourceRoleResource', 
               'Update-MgEntitlementManagementResourceRoleScopeResourceRoleResourceScope', 
               'Update-MgEntitlementManagementResourceRoleScopeResourceScope', 
               'Update-MgEntitlementManagementResourceRoleScopeRole', 
               'Update-MgEntitlementManagementResourceRoleScopeRoleResource', 
               'Update-MgEntitlementManagementResourceRoleScopeRoleResourceRole', 
               'Update-MgEntitlementManagementResourceRoleScopeRoleResourceScope', 
               'Update-MgEntitlementManagementResourceRoleScopeRoleResourceScopeResource', 
               'Update-MgEntitlementManagementResourceRoleScopeRoleResourceScopeResourceRole', 
               'Update-MgEntitlementManagementResourceScope', 
               'Update-MgEntitlementManagementResourceScopeResource', 
               'Update-MgEntitlementManagementResourceScopeResourceRole', 
               'Update-MgEntitlementManagementResourceScopeResourceRoleResource', 
               'Update-MgEntitlementManagementSetting', 
               'Update-MgIdentityGovernanceAccessReviewDefinitionInstance', 
               'Update-MgIdentityGovernanceAccessReviewDefinitionInstanceContactedReviewer', 
               'Update-MgIdentityGovernanceAccessReviewDefinitionInstanceDecision', 
               'Update-MgIdentityGovernanceAccessReviewDefinitionInstanceDecisionInsight', 
               'Update-MgIdentityGovernanceAccessReviewDefinitionInstanceStage', 
               'Update-MgIdentityGovernanceAccessReviewDefinitionInstanceStageDecision', 
               'Update-MgIdentityGovernanceAccessReviewDefinitionInstanceStageDecisionInsight', 
               'Update-MgIdentityGovernanceAccessReviewHistoryDefinition', 
               'Update-MgIdentityGovernanceAccessReviewHistoryDefinitionInstance', 
               'Update-MgIdentityGovernanceAppConsentRequest', 
               'Update-MgIdentityGovernanceAppConsentRequestUserConsentRequest', 
               'Update-MgIdentityGovernanceAppConsentRequestUserConsentRequestApproval', 
               'Update-MgIdentityGovernanceAppConsentRequestUserConsentRequestApprovalStage', 
               'Update-MgIdentityGovernanceLifecycleWorkflow', 
               'Update-MgIdentityGovernanceLifecycleWorkflowCreatedByMailboxSetting', 
               'Update-MgIdentityGovernanceLifecycleWorkflowCustomTaskExtension', 
               'Update-MgIdentityGovernanceLifecycleWorkflowCustomTaskExtensionCreatedByMailboxSetting', 
               'Update-MgIdentityGovernanceLifecycleWorkflowCustomTaskExtensionLastModifiedByMailboxSetting', 
               'Update-MgIdentityGovernanceLifecycleWorkflowDeletedItemWorkflowTask', 
               'Update-MgIdentityGovernanceLifecycleWorkflowInsight', 
               'Update-MgIdentityGovernanceLifecycleWorkflowLastModifiedByMailboxSetting', 
               'Update-MgIdentityGovernanceLifecycleWorkflowRunTaskProcessingResultSubjectMailboxSetting', 
               'Update-MgIdentityGovernanceLifecycleWorkflowRunUserProcessingResultSubjectMailboxSetting', 
               'Update-MgIdentityGovernanceLifecycleWorkflowSetting', 
               'Update-MgIdentityGovernanceLifecycleWorkflowTask', 
               'Update-MgIdentityGovernanceLifecycleWorkflowTaskProcessingResultSubjectMailboxSetting', 
               'Update-MgIdentityGovernanceLifecycleWorkflowTaskReportTaskProcessingResultSubjectMailboxSetting', 
               'Update-MgIdentityGovernanceLifecycleWorkflowTemplateTaskProcessingResultSubjectMailboxSetting', 
               'Update-MgIdentityGovernanceLifecycleWorkflowUserProcessingResultSubjectMailboxSetting', 
               'Update-MgIdentityGovernanceLifecycleWorkflowVersionCreatedByMailboxSetting', 
               'Update-MgIdentityGovernanceLifecycleWorkflowVersionLastModifiedByMailboxSetting', 
               'Update-MgIdentityGovernanceLifecycleWorkflowVersionTask', 
               'Update-MgIdentityGovernanceLifecycleWorkflowVersionTaskProcessingResultSubjectMailboxSetting', 
               'Update-MgIdentityGovernancePrivilegedAccess', 
               'Update-MgIdentityGovernancePrivilegedAccessGroup', 
               'Update-MgIdentityGovernancePrivilegedAccessGroupAssignmentApproval', 
               'Update-MgIdentityGovernancePrivilegedAccessGroupAssignmentApprovalStage', 
               'Update-MgIdentityGovernancePrivilegedAccessGroupAssignmentSchedule', 
               'Update-MgIdentityGovernancePrivilegedAccessGroupAssignmentScheduleInstance', 
               'Update-MgIdentityGovernancePrivilegedAccessGroupAssignmentScheduleRequest', 
               'Update-MgIdentityGovernancePrivilegedAccessGroupEligibilitySchedule', 
               'Update-MgIdentityGovernancePrivilegedAccessGroupEligibilityScheduleInstance', 
               'Update-MgIdentityGovernancePrivilegedAccessGroupEligibilityScheduleRequest', 
               'Update-MgIdentityGovernanceTermsOfUseAgreement', 
               'Update-MgIdentityGovernanceTermsOfUseAgreementAcceptance', 
               'Update-MgIdentityGovernanceTermsOfUseAgreementFile', 
               'Update-MgIdentityGovernanceTermsOfUseAgreementFileLocalization', 
               'Update-MgIdentityGovernanceTermsOfUseAgreementFileLocalizationVersion', 
               'Update-MgIdentityGovernanceTermsOfUseAgreementFileVersion', 
               'Update-MgRoleManagementDirectory', 
               'Update-MgRoleManagementDirectoryResourceNamespace', 
               'Update-MgRoleManagementDirectoryResourceNamespaceResourceAction', 
               'Update-MgRoleManagementDirectoryRoleAssignment', 
               'Update-MgRoleManagementDirectoryRoleAssignmentAppScope', 
               'Update-MgRoleManagementDirectoryRoleAssignmentSchedule', 
               'Update-MgRoleManagementDirectoryRoleAssignmentScheduleInstance', 
               'Update-MgRoleManagementDirectoryRoleAssignmentScheduleRequest', 
               'Update-MgRoleManagementDirectoryRoleDefinition', 
               'Update-MgRoleManagementDirectoryRoleDefinitionInheritPermissionFrom', 
               'Update-MgRoleManagementDirectoryRoleEligibilitySchedule', 
               'Update-MgRoleManagementDirectoryRoleEligibilityScheduleInstance', 
               'Update-MgRoleManagementDirectoryRoleEligibilityScheduleRequest', 
               'Update-MgRoleManagementEntitlementManagement', 
               'Update-MgRoleManagementEntitlementManagementResourceNamespace', 
               'Update-MgRoleManagementEntitlementManagementResourceNamespaceResourceAction', 
               'Update-MgRoleManagementEntitlementManagementRoleAssignment', 
               'Update-MgRoleManagementEntitlementManagementRoleAssignmentAppScope', 
               'Update-MgRoleManagementEntitlementManagementRoleAssignmentSchedule', 
               'Update-MgRoleManagementEntitlementManagementRoleAssignmentScheduleInstance', 
               'Update-MgRoleManagementEntitlementManagementRoleAssignmentScheduleRequest', 
               'Update-MgRoleManagementEntitlementManagementRoleDefinition', 
               'Update-MgRoleManagementEntitlementManagementRoleDefinitionInheritPermissionFrom', 
               'Update-MgRoleManagementEntitlementManagementRoleEligibilitySchedule', 
               'Update-MgRoleManagementEntitlementManagementRoleEligibilityScheduleInstance', 
               'Update-MgRoleManagementEntitlementManagementRoleEligibilityScheduleRequest'

# Cmdlets to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no cmdlets to export.
CmdletsToExport = @()

# Variables to export from this module
# VariablesToExport = @()

# Aliases to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no aliases to export.
AliasesToExport = 
               'Remove-MgEntitlementManagementConnectedOrganizationExternalSponsorByRef', 
               'Remove-MgEntitlementManagementConnectedOrganizationInternalSponsorByRef'

# DSC resources to export from this module
# DscResourcesToExport = @()

# List of all modules packaged with this module
# ModuleList = @()

# List of all files packaged with this module
# FileList = @()

# Private data to pass to the module specified in RootModule/ModuleToProcess. This may also contain a PSData hashtable with additional module metadata used by PowerShell.
PrivateData = @{

    PSData = @{

        # Tags applied to this module. These help with module discovery in online galleries.
        Tags = 'Microsoft','Office365','Graph','PowerShell','PSModule','PSIncludes_Cmdlet'

        # A URL to the license for this module.
        LicenseUri = 'https://aka.ms/devservicesagreement'

        # A URL to the main website for this project.
        ProjectUri = 'https://github.com/microsoftgraph/msgraph-sdk-powershell'

        # A URL to an icon representing this module.
        IconUri = 'https://raw.githubusercontent.com/microsoftgraph/msgraph-sdk-powershell/dev/docs/images/graph_color256.png'

        # ReleaseNotes of this module
        ReleaseNotes = 'See https://aka.ms/GraphPowerShell-Release.'

        # Prerelease string of this module
        # Prerelease = ''

        # Flag to indicate whether the module requires explicit user acceptance for install/update/save
        # RequireLicenseAcceptance = $false

        # External dependent modules of this module
        # ExternalModuleDependencies = @()

    } # End of PSData hashtable

 } # End of PrivateData hashtable

# HelpInfo URI of this module
# HelpInfoURI = ''

# Default prefix for commands exported from this module. Override the default prefix using Import-Module -Prefix.
# DefaultCommandPrefix = ''

}


# SIG # Begin signature block
# MIIoKgYJKoZIhvcNAQcCoIIoGzCCKBcCAQExDzANBglghkgBZQMEAgEFADB5Bgor
# BgEEAYI3AgEEoGswaTA0BgorBgEEAYI3AgEeMCYCAwEAAAQQH8w7YFlLCE63JNLG
# KX7zUQIBAAIBAAIBAAIBAAIBADAxMA0GCWCGSAFlAwQCAQUABCAwIP5cYvhYpRSX
# A/fk1Gax7ClFQzUdlqOlv2lLSR+jtqCCDXYwggX0MIID3KADAgECAhMzAAAEBGx0
# Bv9XKydyAAAAAAQEMA0GCSqGSIb3DQEBCwUAMH4xCzAJBgNVBAYTAlVTMRMwEQYD
# VQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNy
# b3NvZnQgQ29ycG9yYXRpb24xKDAmBgNVBAMTH01pY3Jvc29mdCBDb2RlIFNpZ25p
# bmcgUENBIDIwMTEwHhcNMjQwOTEyMjAxMTE0WhcNMjUwOTExMjAxMTE0WjB0MQsw
# CQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9u
# ZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMR4wHAYDVQQDExVNaWNy
# b3NvZnQgQ29ycG9yYXRpb24wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIB
# AQC0KDfaY50MDqsEGdlIzDHBd6CqIMRQWW9Af1LHDDTuFjfDsvna0nEuDSYJmNyz
# NB10jpbg0lhvkT1AzfX2TLITSXwS8D+mBzGCWMM/wTpciWBV/pbjSazbzoKvRrNo
# DV/u9omOM2Eawyo5JJJdNkM2d8qzkQ0bRuRd4HarmGunSouyb9NY7egWN5E5lUc3
# a2AROzAdHdYpObpCOdeAY2P5XqtJkk79aROpzw16wCjdSn8qMzCBzR7rvH2WVkvF
# HLIxZQET1yhPb6lRmpgBQNnzidHV2Ocxjc8wNiIDzgbDkmlx54QPfw7RwQi8p1fy
# 4byhBrTjv568x8NGv3gwb0RbAgMBAAGjggFzMIIBbzAfBgNVHSUEGDAWBgorBgEE
# AYI3TAgBBggrBgEFBQcDAzAdBgNVHQ4EFgQU8huhNbETDU+ZWllL4DNMPCijEU4w
# RQYDVR0RBD4wPKQ6MDgxHjAcBgNVBAsTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEW
# MBQGA1UEBRMNMjMwMDEyKzUwMjkyMzAfBgNVHSMEGDAWgBRIbmTlUAXTgqoXNzci
# tW2oynUClTBUBgNVHR8ETTBLMEmgR6BFhkNodHRwOi8vd3d3Lm1pY3Jvc29mdC5j
# b20vcGtpb3BzL2NybC9NaWNDb2RTaWdQQ0EyMDExXzIwMTEtMDctMDguY3JsMGEG
# CCsGAQUFBwEBBFUwUzBRBggrBgEFBQcwAoZFaHR0cDovL3d3dy5taWNyb3NvZnQu
# Y29tL3BraW9wcy9jZXJ0cy9NaWNDb2RTaWdQQ0EyMDExXzIwMTEtMDctMDguY3J0
# MAwGA1UdEwEB/wQCMAAwDQYJKoZIhvcNAQELBQADggIBAIjmD9IpQVvfB1QehvpC
# Ge7QeTQkKQ7j3bmDMjwSqFL4ri6ae9IFTdpywn5smmtSIyKYDn3/nHtaEn0X1NBj
# L5oP0BjAy1sqxD+uy35B+V8wv5GrxhMDJP8l2QjLtH/UglSTIhLqyt8bUAqVfyfp
# h4COMRvwwjTvChtCnUXXACuCXYHWalOoc0OU2oGN+mPJIJJxaNQc1sjBsMbGIWv3
# cmgSHkCEmrMv7yaidpePt6V+yPMik+eXw3IfZ5eNOiNgL1rZzgSJfTnvUqiaEQ0X
# dG1HbkDv9fv6CTq6m4Ty3IzLiwGSXYxRIXTxT4TYs5VxHy2uFjFXWVSL0J2ARTYL
# E4Oyl1wXDF1PX4bxg1yDMfKPHcE1Ijic5lx1KdK1SkaEJdto4hd++05J9Bf9TAmi
# u6EK6C9Oe5vRadroJCK26uCUI4zIjL/qG7mswW+qT0CW0gnR9JHkXCWNbo8ccMk1
# sJatmRoSAifbgzaYbUz8+lv+IXy5GFuAmLnNbGjacB3IMGpa+lbFgih57/fIhamq
# 5VhxgaEmn/UjWyr+cPiAFWuTVIpfsOjbEAww75wURNM1Imp9NJKye1O24EspEHmb
# DmqCUcq7NqkOKIG4PVm3hDDED/WQpzJDkvu4FrIbvyTGVU01vKsg4UfcdiZ0fQ+/
# V0hf8yrtq9CkB8iIuk5bBxuPMIIHejCCBWKgAwIBAgIKYQ6Q0gAAAAAAAzANBgkq
# hkiG9w0BAQsFADCBiDELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldhc2hpbmd0b24x
# EDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlv
# bjEyMDAGA1UEAxMpTWljcm9zb2Z0IFJvb3QgQ2VydGlmaWNhdGUgQXV0aG9yaXR5
# IDIwMTEwHhcNMTEwNzA4MjA1OTA5WhcNMjYwNzA4MjEwOTA5WjB+MQswCQYDVQQG
# EwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwG
# A1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSgwJgYDVQQDEx9NaWNyb3NvZnQg
# Q29kZSBTaWduaW5nIFBDQSAyMDExMIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIIC
# CgKCAgEAq/D6chAcLq3YbqqCEE00uvK2WCGfQhsqa+laUKq4BjgaBEm6f8MMHt03
# a8YS2AvwOMKZBrDIOdUBFDFC04kNeWSHfpRgJGyvnkmc6Whe0t+bU7IKLMOv2akr
# rnoJr9eWWcpgGgXpZnboMlImEi/nqwhQz7NEt13YxC4Ddato88tt8zpcoRb0Rrrg
# OGSsbmQ1eKagYw8t00CT+OPeBw3VXHmlSSnnDb6gE3e+lD3v++MrWhAfTVYoonpy
# 4BI6t0le2O3tQ5GD2Xuye4Yb2T6xjF3oiU+EGvKhL1nkkDstrjNYxbc+/jLTswM9
# sbKvkjh+0p2ALPVOVpEhNSXDOW5kf1O6nA+tGSOEy/S6A4aN91/w0FK/jJSHvMAh
# dCVfGCi2zCcoOCWYOUo2z3yxkq4cI6epZuxhH2rhKEmdX4jiJV3TIUs+UsS1Vz8k
# A/DRelsv1SPjcF0PUUZ3s/gA4bysAoJf28AVs70b1FVL5zmhD+kjSbwYuER8ReTB
# w3J64HLnJN+/RpnF78IcV9uDjexNSTCnq47f7Fufr/zdsGbiwZeBe+3W7UvnSSmn
# Eyimp31ngOaKYnhfsi+E11ecXL93KCjx7W3DKI8sj0A3T8HhhUSJxAlMxdSlQy90
# lfdu+HggWCwTXWCVmj5PM4TasIgX3p5O9JawvEagbJjS4NaIjAsCAwEAAaOCAe0w
# ggHpMBAGCSsGAQQBgjcVAQQDAgEAMB0GA1UdDgQWBBRIbmTlUAXTgqoXNzcitW2o
# ynUClTAZBgkrBgEEAYI3FAIEDB4KAFMAdQBiAEMAQTALBgNVHQ8EBAMCAYYwDwYD
# VR0TAQH/BAUwAwEB/zAfBgNVHSMEGDAWgBRyLToCMZBDuRQFTuHqp8cx0SOJNDBa
# BgNVHR8EUzBRME+gTaBLhklodHRwOi8vY3JsLm1pY3Jvc29mdC5jb20vcGtpL2Ny
# bC9wcm9kdWN0cy9NaWNSb29DZXJBdXQyMDExXzIwMTFfMDNfMjIuY3JsMF4GCCsG
# AQUFBwEBBFIwUDBOBggrBgEFBQcwAoZCaHR0cDovL3d3dy5taWNyb3NvZnQuY29t
# L3BraS9jZXJ0cy9NaWNSb29DZXJBdXQyMDExXzIwMTFfMDNfMjIuY3J0MIGfBgNV
# HSAEgZcwgZQwgZEGCSsGAQQBgjcuAzCBgzA/BggrBgEFBQcCARYzaHR0cDovL3d3
# dy5taWNyb3NvZnQuY29tL3BraW9wcy9kb2NzL3ByaW1hcnljcHMuaHRtMEAGCCsG
# AQUFBwICMDQeMiAdAEwAZQBnAGEAbABfAHAAbwBsAGkAYwB5AF8AcwB0AGEAdABl
# AG0AZQBuAHQALiAdMA0GCSqGSIb3DQEBCwUAA4ICAQBn8oalmOBUeRou09h0ZyKb
# C5YR4WOSmUKWfdJ5DJDBZV8uLD74w3LRbYP+vj/oCso7v0epo/Np22O/IjWll11l
# hJB9i0ZQVdgMknzSGksc8zxCi1LQsP1r4z4HLimb5j0bpdS1HXeUOeLpZMlEPXh6
# I/MTfaaQdION9MsmAkYqwooQu6SpBQyb7Wj6aC6VoCo/KmtYSWMfCWluWpiW5IP0
# wI/zRive/DvQvTXvbiWu5a8n7dDd8w6vmSiXmE0OPQvyCInWH8MyGOLwxS3OW560
# STkKxgrCxq2u5bLZ2xWIUUVYODJxJxp/sfQn+N4sOiBpmLJZiWhub6e3dMNABQam
# ASooPoI/E01mC8CzTfXhj38cbxV9Rad25UAqZaPDXVJihsMdYzaXht/a8/jyFqGa
# J+HNpZfQ7l1jQeNbB5yHPgZ3BtEGsXUfFL5hYbXw3MYbBL7fQccOKO7eZS/sl/ah
# XJbYANahRr1Z85elCUtIEJmAH9AAKcWxm6U/RXceNcbSoqKfenoi+kiVH6v7RyOA
# 9Z74v2u3S5fi63V4GuzqN5l5GEv/1rMjaHXmr/r8i+sLgOppO6/8MO0ETI7f33Vt
# Y5E90Z1WTk+/gFcioXgRMiF670EKsT/7qMykXcGhiJtXcVZOSEXAQsmbdlsKgEhr
# /Xmfwb1tbWrJUnMTDXpQzTGCGgowghoGAgEBMIGVMH4xCzAJBgNVBAYTAlVTMRMw
# EQYDVQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVN
# aWNyb3NvZnQgQ29ycG9yYXRpb24xKDAmBgNVBAMTH01pY3Jvc29mdCBDb2RlIFNp
# Z25pbmcgUENBIDIwMTECEzMAAAQEbHQG/1crJ3IAAAAABAQwDQYJYIZIAWUDBAIB
# BQCgga4wGQYJKoZIhvcNAQkDMQwGCisGAQQBgjcCAQQwHAYKKwYBBAGCNwIBCzEO
# MAwGCisGAQQBgjcCARUwLwYJKoZIhvcNAQkEMSIEIKyRyLH8n55OPyF4lgA3rQMG
# 4v54Wn6YSxl6MDjRbe2DMEIGCisGAQQBgjcCAQwxNDAyoBSAEgBNAGkAYwByAG8A
# cwBvAGYAdKEagBhodHRwOi8vd3d3Lm1pY3Jvc29mdC5jb20wDQYJKoZIhvcNAQEB
# BQAEggEAdlUNjsR5Jsfh5wFkooMiVOP4czL4dUqpZUgFcSBiscQDvnuBUET7640d
# Z4gFwYZI8bYB8BMURPFn9GwIiVFYoPmhn4D3fIyWWDyXrNOMyNiI6/ZZ66nnWCDF
# OKDXi9uregDz18qfKWRP0cnqI5Uu3UZbpj6wXI13bJitNTA22t+DiZz3/Q+mjGEm
# +60OyzrSa0XIAtNt5Fsihy18KG2Y8YEIh0GKmGvLSGbp/e0e0+7wjOM70iHJC9xo
# SNEvhWzbka5GUe2HNaC//zbBk+12pvOPRa0D/I4Fd69AeSPexqke/6yNOD1V7xe6
# y3drfuN8zgeAQg4XhNdFPfbCiw1VeqGCF5QwgheQBgorBgEEAYI3AwMBMYIXgDCC
# F3wGCSqGSIb3DQEHAqCCF20wghdpAgEDMQ8wDQYJYIZIAWUDBAIBBQAwggFSBgsq
# hkiG9w0BCRABBKCCAUEEggE9MIIBOQIBAQYKKwYBBAGEWQoDATAxMA0GCWCGSAFl
# AwQCAQUABCBPsoTmyqkZ7m/IK8fZwadfb7ImCpOQI5yUk2w3cyZLfAIGaErf2xFY
# GBMyMDI1MDcwOTExMDcyNS41NzVaMASAAgH0oIHRpIHOMIHLMQswCQYDVQQGEwJV
# UzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwGA1UE
# ChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSUwIwYDVQQLExxNaWNyb3NvZnQgQW1l
# cmljYSBPcGVyYXRpb25zMScwJQYDVQQLEx5uU2hpZWxkIFRTUyBFU046QTAwMC0w
# NUUwLUQ5NDcxJTAjBgNVBAMTHE1pY3Jvc29mdCBUaW1lLVN0YW1wIFNlcnZpY2Wg
# ghHqMIIHIDCCBQigAwIBAgITMwAAAgh4nVhdksfZUgABAAACCDANBgkqhkiG9w0B
# AQsFADB8MQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UE
# BxMHUmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSYwJAYD
# VQQDEx1NaWNyb3NvZnQgVGltZS1TdGFtcCBQQ0EgMjAxMDAeFw0yNTAxMzAxOTQy
# NTNaFw0yNjA0MjIxOTQyNTNaMIHLMQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2Fz
# aGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENv
# cnBvcmF0aW9uMSUwIwYDVQQLExxNaWNyb3NvZnQgQW1lcmljYSBPcGVyYXRpb25z
# MScwJQYDVQQLEx5uU2hpZWxkIFRTUyBFU046QTAwMC0wNUUwLUQ5NDcxJTAjBgNV
# BAMTHE1pY3Jvc29mdCBUaW1lLVN0YW1wIFNlcnZpY2UwggIiMA0GCSqGSIb3DQEB
# AQUAA4ICDwAwggIKAoICAQC1y3AI5lIz3Ip1nK5BMUUbGRsjSnCz/VGs33zvY0Ne
# shsPgfld3/Z3/3dS8WKBLlDlosmXJOZlFSiNXUd6DTJxA9ik/ZbCdWJ78LKjbN3t
# FkX2c6RRpRMpA8sq/oBbRryP3c8Q/gxpJAKHHz8cuSn7ewfCLznNmxqliTk3Q5LH
# qz2PjeYKD/dbKMBT2TAAWAvum4z/HXIJ6tFdGoNV4WURZswCSt6ROwaqQ1oAYGvE
# ndH+DXZq1+bHsgvcPNCdTSIpWobQiJS/UKLiR02KNCqB4I9yajFTSlnMIEMz/Ni5
# 38oGI64phcvNpUe2+qaKWHZ8d4T1KghvRmSSF4YF5DNEJbxaCUwsy7nULmsFnTaO
# jVOoTFWWfWXvBuOKkBcQKWGKvrki976j4x+5ezAP36fq3u6dHRJTLZAu4dEuOooU
# 3+kMZr+RBYWjTHQCKV+yZ1ST0eGkbHXoA2lyyRDlNjBQcoeZIxWCZts/d3+nf1ji
# SLN6f6wdHaUz0ADwOTQ/aEo1IC85eFePvyIKaxFJkGU2Mqa6Xzq3qCq5tokIHtjh
# ogsrEgfDKTeFXTtdhl1IPtLcCfMcWOGGAXosVUU7G948F6W96424f2VHD8L3FoyA
# I9+r4zyIQUmqiESzuQWeWpTTjFYwCmgXaGOuSDV8cNOVQB6IPzPneZhVTjwxbAZl
# aQIDAQABo4IBSTCCAUUwHQYDVR0OBBYEFKMx4vfOqcUTgYOVB9f18/mhegFNMB8G
# A1UdIwQYMBaAFJ+nFV0AXmJdg/Tl0mWnG1M1GelyMF8GA1UdHwRYMFYwVKBSoFCG
# Tmh0dHA6Ly93d3cubWljcm9zb2Z0LmNvbS9wa2lvcHMvY3JsL01pY3Jvc29mdCUy
# MFRpbWUtU3RhbXAlMjBQQ0ElMjAyMDEwKDEpLmNybDBsBggrBgEFBQcBAQRgMF4w
# XAYIKwYBBQUHMAKGUGh0dHA6Ly93d3cubWljcm9zb2Z0LmNvbS9wa2lvcHMvY2Vy
# dHMvTWljcm9zb2Z0JTIwVGltZS1TdGFtcCUyMFBDQSUyMDIwMTAoMSkuY3J0MAwG
# A1UdEwEB/wQCMAAwFgYDVR0lAQH/BAwwCgYIKwYBBQUHAwgwDgYDVR0PAQH/BAQD
# AgeAMA0GCSqGSIb3DQEBCwUAA4ICAQBRszKJKwAfswqdaQPFiaYB/ZNAYWDa040X
# TcQsCaCua5nsG1IslYaSpH7miTLr6eQEqXczZoqeOa/xvDnMGifGNda0CHbQwtpn
# IhsutrKO2jhjEaGwlJgOMql21r7Ik6XnBza0e3hBOu4UBkMl/LEX+AURt7i7+RTN
# sGN0cXPwPSbTFE+9z7WagGbY9pwUo/NxkGJseqGCQ/9K2VMU74bw5e7+8IGUhM2x
# spJPqnSeHPhYmcB0WclOxcVIfj/ZuQvworPbTEEYDVCzSN37c0yChPMY7FJ+HGFB
# NJxwd5lKIr7GYfq8a0gOiC2ljGYlc4rt4cCed1XKg83f0l9aUVimWBYXtfNebhpf
# r6Lc3jD8NgsrDhzt0WgnIdnTZCi7jxjsIBilH99pY5/h6bQcLKK/E6KCP9E1YN78
# fLaOXkXMyO6xLrvQZ+uCSi1hdTufFC7oSB/CU5RbfIVHXG0j1o2n1tne4eCbNfKq
# UPTE31tNbWBR23Yiy0r3kQmHeYE1GLbL4pwknqaip1BRn6WIUMJtgncawEN33f8A
# YGZ4a3NnHopzGVV6neffGVag4Tduy+oy1YF+shChoXdMqfhPWFpHe3uJGT4GJEiN
# s4+28a/wHUuF+aRaR0cN5P7XlOwU1360iUCJtQdvKQaNAwGI29KOwS3QGriR9F2j
# OGPUAlpeEzCCB3EwggVZoAMCAQICEzMAAAAVxedrngKbSZkAAAAAABUwDQYJKoZI
# hvcNAQELBQAwgYgxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNoaW5ndG9uMRAw
# DgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNyb3NvZnQgQ29ycG9yYXRpb24x
# MjAwBgNVBAMTKU1pY3Jvc29mdCBSb290IENlcnRpZmljYXRlIEF1dGhvcml0eSAy
# MDEwMB4XDTIxMDkzMDE4MjIyNVoXDTMwMDkzMDE4MzIyNVowfDELMAkGA1UEBhMC
# VVMxEzARBgNVBAgTCldhc2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNV
# BAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEmMCQGA1UEAxMdTWljcm9zb2Z0IFRp
# bWUtU3RhbXAgUENBIDIwMTAwggIiMA0GCSqGSIb3DQEBAQUAA4ICDwAwggIKAoIC
# AQDk4aZM57RyIQt5osvXJHm9DtWC0/3unAcH0qlsTnXIyjVX9gF/bErg4r25Phdg
# M/9cT8dm95VTcVrifkpa/rg2Z4VGIwy1jRPPdzLAEBjoYH1qUoNEt6aORmsHFPPF
# dvWGUNzBRMhxXFExN6AKOG6N7dcP2CZTfDlhAnrEqv1yaa8dq6z2Nr41JmTamDu6
# GnszrYBbfowQHJ1S/rboYiXcag/PXfT+jlPP1uyFVk3v3byNpOORj7I5LFGc6XBp
# Dco2LXCOMcg1KL3jtIckw+DJj361VI/c+gVVmG1oO5pGve2krnopN6zL64NF50Zu
# yjLVwIYwXE8s4mKyzbnijYjklqwBSru+cakXW2dg3viSkR4dPf0gz3N9QZpGdc3E
# XzTdEonW/aUgfX782Z5F37ZyL9t9X4C626p+Nuw2TPYrbqgSUei/BQOj0XOmTTd0
# lBw0gg/wEPK3Rxjtp+iZfD9M269ewvPV2HM9Q07BMzlMjgK8QmguEOqEUUbi0b1q
# GFphAXPKZ6Je1yh2AuIzGHLXpyDwwvoSCtdjbwzJNmSLW6CmgyFdXzB0kZSU2LlQ
# +QuJYfM2BjUYhEfb3BvR/bLUHMVr9lxSUV0S2yW6r1AFemzFER1y7435UsSFF5PA
# PBXbGjfHCBUYP3irRbb1Hode2o+eFnJpxq57t7c+auIurQIDAQABo4IB3TCCAdkw
# EgYJKwYBBAGCNxUBBAUCAwEAATAjBgkrBgEEAYI3FQIEFgQUKqdS/mTEmr6CkTxG
# NSnPEP8vBO4wHQYDVR0OBBYEFJ+nFV0AXmJdg/Tl0mWnG1M1GelyMFwGA1UdIARV
# MFMwUQYMKwYBBAGCN0yDfQEBMEEwPwYIKwYBBQUHAgEWM2h0dHA6Ly93d3cubWlj
# cm9zb2Z0LmNvbS9wa2lvcHMvRG9jcy9SZXBvc2l0b3J5Lmh0bTATBgNVHSUEDDAK
# BggrBgEFBQcDCDAZBgkrBgEEAYI3FAIEDB4KAFMAdQBiAEMAQTALBgNVHQ8EBAMC
# AYYwDwYDVR0TAQH/BAUwAwEB/zAfBgNVHSMEGDAWgBTV9lbLj+iiXGJo0T2UkFvX
# zpoYxDBWBgNVHR8ETzBNMEugSaBHhkVodHRwOi8vY3JsLm1pY3Jvc29mdC5jb20v
# cGtpL2NybC9wcm9kdWN0cy9NaWNSb29DZXJBdXRfMjAxMC0wNi0yMy5jcmwwWgYI
# KwYBBQUHAQEETjBMMEoGCCsGAQUFBzAChj5odHRwOi8vd3d3Lm1pY3Jvc29mdC5j
# b20vcGtpL2NlcnRzL01pY1Jvb0NlckF1dF8yMDEwLTA2LTIzLmNydDANBgkqhkiG
# 9w0BAQsFAAOCAgEAnVV9/Cqt4SwfZwExJFvhnnJL/Klv6lwUtj5OR2R4sQaTlz0x
# M7U518JxNj/aZGx80HU5bbsPMeTCj/ts0aGUGCLu6WZnOlNN3Zi6th542DYunKmC
# VgADsAW+iehp4LoJ7nvfam++Kctu2D9IdQHZGN5tggz1bSNU5HhTdSRXud2f8449
# xvNo32X2pFaq95W2KFUn0CS9QKC/GbYSEhFdPSfgQJY4rPf5KYnDvBewVIVCs/wM
# nosZiefwC2qBwoEZQhlSdYo2wh3DYXMuLGt7bj8sCXgU6ZGyqVvfSaN0DLzskYDS
# PeZKPmY7T7uG+jIa2Zb0j/aRAfbOxnT99kxybxCrdTDFNLB62FD+CljdQDzHVG2d
# Y3RILLFORy3BFARxv2T5JL5zbcqOCb2zAVdJVGTZc9d/HltEAY5aGZFrDZ+kKNxn
# GSgkujhLmm77IVRrakURR6nxt67I6IleT53S0Ex2tVdUCbFpAUR+fKFhbHP+Crvs
# QWY9af3LwUFJfn6Tvsv4O+S3Fb+0zj6lMVGEvL8CwYKiexcdFYmNcP7ntdAoGokL
# jzbaukz5m/8K6TT4JDVnK+ANuOaMmdbhIurwJ0I9JZTmdHRbatGePu1+oDEzfbzL
# 6Xu/OHBE0ZDxyKs6ijoIYn/ZcGNTTY3ugm2lBRDBcQZqELQdVTNYs6FwZvKhggNN
# MIICNQIBATCB+aGB0aSBzjCByzELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldhc2hp
# bmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBDb3Jw
# b3JhdGlvbjElMCMGA1UECxMcTWljcm9zb2Z0IEFtZXJpY2EgT3BlcmF0aW9uczEn
# MCUGA1UECxMeblNoaWVsZCBUU1MgRVNOOkEwMDAtMDVFMC1EOTQ3MSUwIwYDVQQD
# ExxNaWNyb3NvZnQgVGltZS1TdGFtcCBTZXJ2aWNloiMKAQEwBwYFKw4DAhoDFQCN
# kvu0NKcSjdYKyrhJZcsyXOUTNKCBgzCBgKR+MHwxCzAJBgNVBAYTAlVTMRMwEQYD
# VQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNy
# b3NvZnQgQ29ycG9yYXRpb24xJjAkBgNVBAMTHU1pY3Jvc29mdCBUaW1lLVN0YW1w
# IFBDQSAyMDEwMA0GCSqGSIb3DQEBCwUAAgUA7BhLyDAiGA8yMDI1MDcwOTAyMDA0
# MFoYDzIwMjUwNzEwMDIwMDQwWjB0MDoGCisGAQQBhFkKBAExLDAqMAoCBQDsGEvI
# AgEAMAcCAQACAicsMAcCAQACAhIvMAoCBQDsGZ1IAgEAMDYGCisGAQQBhFkKBAIx
# KDAmMAwGCisGAQQBhFkKAwKgCjAIAgEAAgMHoSChCjAIAgEAAgMBhqAwDQYJKoZI
# hvcNAQELBQADggEBADnXGmdDdtuw5MGwOaf96lcG8iq3aoRiQjZhOpVbGtu9gArj
# t+uv2sUqBZjSzFlwiX1PTSAE3S8UCYzWWLeDPWmFxjIV04oimy5NgmVOGDAv2lvR
# iCOG3Vzm317mUdC82WJTGwIYgkrC7ZU5hkXuBODWKwGCuiZ1m+lrCk7NPUg4WXLv
# HEn6PU82GrpnaKTOqd+mRx3x8LUGua3J5JD6EoCtSKPWRMjfd6pHuD/FfCHx1m6i
# M43oGVpeeCfG443+Rz9RZmfKX+f7N1pIFqBid4bYrv14PcsfJe0t9GZ5NyA5n8R+
# bO0E0hNfYg2EuhrMUU82bexalXDjwJeg/kbOVFsxggQNMIIECQIBATCBkzB8MQsw
# CQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9u
# ZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSYwJAYDVQQDEx1NaWNy
# b3NvZnQgVGltZS1TdGFtcCBQQ0EgMjAxMAITMwAAAgh4nVhdksfZUgABAAACCDAN
# BglghkgBZQMEAgEFAKCCAUowGgYJKoZIhvcNAQkDMQ0GCyqGSIb3DQEJEAEEMC8G
# CSqGSIb3DQEJBDEiBCDSgqMwHU7qOr8s3C4n27mVK9REn+K+AL5ARLenKsF/VTCB
# +gYLKoZIhvcNAQkQAi8xgeowgecwgeQwgb0EII//jm8JHa2W1O9778t9+Ft2Z5Nm
# KqttPk6Q+9RRpmepMIGYMIGApH4wfDELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldh
# c2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBD
# b3Jwb3JhdGlvbjEmMCQGA1UEAxMdTWljcm9zb2Z0IFRpbWUtU3RhbXAgUENBIDIw
# MTACEzMAAAIIeJ1YXZLH2VIAAQAAAggwIgQgPL73O+knfnt2bAvd1AuD2lC7DQdu
# 5l6OGM83s86H4zEwDQYJKoZIhvcNAQELBQAEggIAPlIxZXPJye2hg20+S7XSZjb4
# ZWSvAL65qkghQq8lxN4LTbk3IpWO8XduS7h800esbnskSr2hr7elDT7SkI+SsVVL
# 9T4l5XNJyrFuQuzhV1TWtzksY31uk1lG6rcY7GT7q4jL6BxR5lTkYD7F2phTsySs
# AftVREgX4lcrIqwtmwZwr9ayN/nsu3rOcY2doVU3bTLtMmp/TYKuR+UDmZnuamSQ
# jvUOVUg4DhNvQ8a5ebIYz/rQFXpwuWo1mDLjGX0UV3VaAemWNgPL03ojUChxJfY+
# sJalEWhsQsY9MmWhjHfKjMdu8WrhPeMxyu6mw1os8cnsczxjOtm6zpUqBRQTAK4L
# J4I+QU74VIfpaxSRwKCNCRUBV/4P7vLulm/ngWnoocBesWZnKwNbVusa4fRkjZQq
# IhEb2NZ/DP0fnCWk8kvRdeMbi1OjGZKdJpaEDaV2fh1tX/FyjGyyDKBhKW3RvRRV
# VE7uq/NIlI/KKPG2TTrAXsixlVvWEXOrJ0hi2spmis1DQUDcBXR4YWU9TL9BqcO7
# IbzStzm26c6g1SDfWi9IH/V/OA/peyd4KpLjX0/BR0Osltggt3wsgnaYw9s6YyG6
# 1Pi33x2q98DhDxsFHppdbRmy694vyliCtV916qSprnHDXjfkCCMx3BFaOfYWHD/5
# 8+KpBK0gz4jj3kUkORw=
# SIG # End signature block
