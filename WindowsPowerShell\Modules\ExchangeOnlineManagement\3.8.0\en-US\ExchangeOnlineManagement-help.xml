﻿<?xml version="1.0" encoding="utf-8"?>
<helpItems schema="maml" xmlns="http://msh">
	<command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
		<command:details>
			<command:name>Connect-ExchangeOnline</command:name>
			<command:verb>Connect</command:verb>
			<command:noun>ExchangeOnline</command:noun>
			<maml:description>
				<maml:para>This cmdlet is available only in the Exchange Online PowerShell module. For more information, see About the Exchange Online PowerShell module (https://aka.ms/exov3-module).</maml:para>
				<maml:para>Use the Connect-ExchangeOnline cmdlet in the Exchange Online PowerShell module to connect to Exchange Online PowerShell or standalone Exchange Online Protection PowerShell using modern authentication. This cmdlet works for accounts with or without multi-factor authentication (MFA).</maml:para>
				<maml:para>To connect to Security &amp; Compliance PowerShell, use the Connect-IPPSSession (https://learn.microsoft.com/powershell/module/exchange/connect-ippssession)cmdlet.</maml:para>
				<maml:para>For information about the parameter sets in the Syntax section below, see Exchange cmdlet syntax (https://learn.microsoft.com/powershell/exchange/exchange-cmdlet-syntax).</maml:para>
			</maml:description>
		</command:details>
		<maml:description>
			<maml:para>This cmdlet creates a PowerShell connection to your Exchange Online organization.</maml:para>
			<maml:para>Connect commands will likely fail if the profile path of the account that you used to connect contains special PowerShell characters (for example, `$`). The workaround is to connect using a different account that doesn't have special characters in the profile path.</maml:para>
		</maml:description>
		<command:syntax>
			<command:syntaxItem>
				<maml:name>Connect-ExchangeOnline</maml:name>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="0" aliases="none">
					<maml:name>ConnectionUri</maml:name>
					<maml:description>
						<maml:para>Note : If you use the ExchangeEnvironmentName parameter, you don't need to use the AzureADAuthorizationEndpointUri or ConnectionUri parameters.</maml:para>
						<maml:para>The ConnectionUri parameter specifies the connection endpoint for the PowerShell session. The following Exchange Online PowerShell environments and related values are supported:</maml:para>
						<maml:para>- Microsoft 365 or Microsoft 365 GCC: Don't use this parameter. The required value is `https://outlook.office365.com/powershell-liveid/`, but that's also the default value, so you don't need to use this parameter.</maml:para>
						<maml:para>- Office 365 Germany: `https://outlook.office.de/PowerShell-LiveID`</maml:para>
						<maml:para>- Office 365 operated by 21Vianet: `https://partner.outlook.cn/PowerShell`</maml:para>
						<maml:para>- Microsoft 365 GCC High: `https://outlook.office365.us/powershell-liveID`</maml:para>
						<maml:para>- Microsoft 365 DoD: `https://webmail.apps.mil/powershell-liveID`</maml:para>
					</maml:description>
					<command:parameterValue required="true" variableLength="false">String</command:parameterValue>
					<dev:type>
						<maml:name>String</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>None</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="1" aliases="none">
					<maml:name>AzureADAuthorizationEndpointUri</maml:name>
					<maml:description>
						<maml:para>Note : If you use the ExchangeEnvironmentName parameter, you don't need to use the AzureADAuthorizationEndpointUri or ConnectionUri parameters.</maml:para>
						<maml:para>The AzureADAuthorizationEndpointUri parameter specifies the Microsoft Entra Authorization endpoint that can issue OAuth2 access tokens. The following Exchange Online PowerShell environments and related values are supported:</maml:para>
						<maml:para>- Microsoft 365 or Microsoft 365 GCC: Don't use this parameter. The required value is `https://login.microsoftonline.com/common`, but that's also the default value, so you don't need to use this parameter.</maml:para>
						<maml:para>- Office 365 Germany: `https://login.microsoftonline.de/common`</maml:para>
						<maml:para>- Microsoft 365 GCC High or Microsoft 365 DoD: `https://login.microsoftonline.us/common`</maml:para>
						<maml:para></maml:para>
						<maml:para>If you use the UserPrincipalName parameter, you don't need to use the AzureADAuthorizationEndpointUri parameter for MFA or federated users in environments that normally require it (UserPrincipalName or AzureADAuthorizationEndpointUri is required; OK to use both). Note : MFA authentication or federated authentication isn't available in Office 365 operated by 21Vianet.</maml:para>
					</maml:description>
					<command:parameterValue required="true" variableLength="false">String</command:parameterValue>
					<dev:type>
						<maml:name>String</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>None</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="2" aliases="none">
					<maml:name>ExchangeEnvironmentName</maml:name>
					<maml:description>
						<maml:para>The ExchangeEnvironmentName specifies the Exchange Online environment and eliminates the need to use the AzureADAuthorizationEndpointUri and ConnectionUri parameters. The following Exchange Online PowerShell environments are supported:</maml:para>
						<maml:para>- Microsoft 365 or Microsoft 365 GCC: Don't use this parameter. The required value is `O365Default`, but that's also the default value, so you don't need to use this parameter.</maml:para>
						<maml:para>- Office 365 Germany: `O365GermanyCloud`</maml:para>
						<maml:para>- Office 365 operated by 21Vianet: `O365China`</maml:para>
						<maml:para>- Microsoft 365 GCC High: `O365USGovGCCHigh`</maml:para>
						<maml:para>- Microsoft 365 DoD: `O365USGovDoD`</maml:para>
					</maml:description>
					<command:parameterValue required="true" variableLength="false">ExchangeEnvironment</command:parameterValue>
					<dev:type>
						<maml:name>ExchangeEnvironment</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>O365Default</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="3" aliases="none">
					<maml:name>PSSessionOption</maml:name>
					<maml:description>
						<maml:para>Note : This parameter doesn't work in REST API connections.</maml:para>
						<maml:para>The PSSessionOption parameter specifies the PowerShell session options to use in your connection to Exchange Online. This parameter works only if you also use the UseRPSSession switch in the same command.</maml:para>
						<maml:para>Store the output of the New-PSSessionOption (https://learn.microsoft.com/powershell/module/microsoft.powershell.core/new-pssessionoption) command in a variable (for example, `$PSOptions = New-PSSessionOption &lt;Settings&gt;`), and use the variable name as the value for this parameter (for example, `$PSOptions`).</maml:para>
					</maml:description>
					<command:parameterValue required="true" variableLength="false">PSSessionOption</command:parameterValue>
					<dev:type>
						<maml:name>PSSessionOption</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>None</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="4" aliases="none">
					<maml:name>DelegatedOrganization</maml:name>
					<maml:description>
						<maml:para>The DelegatedOrganization parameter specifies the customer organization that you want to manage. A valid value for this parameter is the primary .onmicrosoft.com domain or tenant ID of the customer organization.</maml:para>
						<maml:para>This parameter works only if the customer organization has agreed to your delegated management via the CSP program.</maml:para>
						<maml:para>After you successfully authenticate, the cmdlets in this session are mapped to the customer organization, and all operations in this session are done on the customer organization.</maml:para>
					</maml:description>
					<command:parameterValue required="true" variableLength="false">String</command:parameterValue>
					<dev:type>
						<maml:name>String</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>None</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="5" aliases="none">
					<maml:name>Prefix</maml:name>
					<maml:description>
						<maml:para>The Prefix parameter specifies a text value to add to the names of Exchange Online PowerShell cmdlets when you connect. For example, Get-InboundConnector becomes Get-ContosoInboundConnector when you use the value Contoso for this parameter.</maml:para>
						<maml:para>- The Prefix value can't contain spaces or special characters like underscores or asterisks.</maml:para>
						<maml:para>- You can't use the Prefix value EXO. That value is reserved for the nine exclusive Get-EXO\ * cmdlets that are built into the module.</maml:para>
						<maml:para>- The Prefix parameter affects only imported Exchange Online cmdlet names. It doesn't affect the names of cmdlets that are built into the module (for example, Disconnect-ExchangeOnline).</maml:para>
					</maml:description>
					<command:parameterValue required="true" variableLength="false">String</command:parameterValue>
					<dev:type>
						<maml:name>String</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>None</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="6" aliases="none">
					<maml:name>CommandName</maml:name>
					<maml:description>
						<maml:para>The CommandName parameter specifies the comma separated list of commands to import into the session. Use this parameter for applications or scripts that use a specific set of cmdlets. Reducing the number of cmdlets in the session helps improve performance and reduces the memory footprint of the application or script.</maml:para>
					</maml:description>
					<command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
					<dev:type>
						<maml:name>String[]</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>None</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="7" aliases="none">
					<maml:name>FormatTypeName</maml:name>
					<maml:description>
						<maml:para>The FormatTypeName parameter specifies the output format of the cmdlet.</maml:para>
					</maml:description>
					<command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
					<dev:type>
						<maml:name>String[]</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>None</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
					<maml:name>AccessToken</maml:name>
					<maml:description>
						<maml:para>Note : This parameter is available in version 3.1.0 or later of the module.</maml:para>
						<maml:para>The AccessToken parameter specifies the OAuth JSON Web Token (JWT) that's used to connect to Exchange Online.</maml:para>
						<maml:para>Depending on the type of access token, you need to use this parameter with the Organization, DelegatedOrganization, or UserPrincipalName parameters.</maml:para>
					</maml:description>
					<command:parameterValue required="true" variableLength="false">String</command:parameterValue>
					<dev:type>
						<maml:name>String</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>None</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
					<maml:name>AppId</maml:name>
					<maml:description>
						<maml:para>The AppId parameter specifies the application ID of the service principal that's used in certificate based authentication (CBA). A valid value is the GUID of the application ID (service principal). For example, `36ee4c6c-0812-40a2-b820-b22ebd02bce3`.</maml:para>
						<maml:para>For more information, see App-only authentication for unattended scripts in the Exchange Online PowerShell module (https://aka.ms/exo-cba).</maml:para>
					</maml:description>
					<command:parameterValue required="true" variableLength="false">String</command:parameterValue>
					<dev:type>
						<maml:name>String</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>None</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
					<maml:name>BypassMailboxAnchoring</maml:name>
					<maml:description>
						<maml:para>The BypassMailboxAnchoring switch bypasses the use of the mailbox anchoring hint. You don't need to specify a value with this switch.</maml:para>
					</maml:description>
					<dev:type>
						<maml:name>SwitchParameter</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>False</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
					<maml:name>Certificate</maml:name>
					<maml:description>
						<maml:para>The Certificate parameter specifies the certificate that's used for certificate-based authentication (CBA). A valid value is the X509Certificate2 object value of the certificate.</maml:para>
						<maml:para>Don't use this parameter with the CertificateFilePath or CertificateThumbprint parameters.</maml:para>
						<maml:para>For more information about CBA, see App-only authentication for unattended scripts in the Exchange Online PowerShell module (https://aka.ms/exo-cba).</maml:para>
					</maml:description>
					<command:parameterValue required="true" variableLength="false">X509Certificate2</command:parameterValue>
					<dev:type>
						<maml:name>X509Certificate2</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>None</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
					<maml:name>CertificateFilePath</maml:name>
					<maml:description>
						<maml:para>The CertificateFilePath parameter specifies the certificate that's used for CBA. A valid value is the complete public path to the certificate file. Use the CertificatePassword parameter with this parameter.</maml:para>
						<maml:para>Don't use this parameter with the Certificate or CertificateThumbprint parameters.</maml:para>
						<maml:para>For more information about CBA, see App-only authentication for unattended scripts in the Exchange Online PowerShell module (https://aka.ms/exo-cba).</maml:para>
					</maml:description>
					<command:parameterValue required="true" variableLength="false">String</command:parameterValue>
					<dev:type>
						<maml:name>String</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>None</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
					<maml:name>CertificatePassword</maml:name>
					<maml:description>
						<maml:para>The CertificatePassword parameter specifies the password that's required to open the certificate file when you use the CertificateFilePath parameter to identify the certificate that's used for CBA.</maml:para>
						<maml:para>You can use the following methods as a value for this parameter:</maml:para>
						<maml:para>- `(ConvertTo-SecureString -String '&lt;password&gt;' -AsPlainText -Force)`.</maml:para>
						<maml:para>- Before you run this command, store the password as a variable (for example, `$password = Read-Host "Enter password" -AsSecureString`), and then use the variable (`$password`) for the value.</maml:para>
						<maml:para>- `(Get-Credential).password` to be prompted to enter the password securely when you run this command.</maml:para>
						<maml:para></maml:para>
						<maml:para>For more information about CBA, see App-only authentication for unattended scripts in the Exchange Online PowerShell module (https://aka.ms/exo-cba). Note : Using a ConvertTo-SecureString command to store the password of the certificate locally defeats the purpose of a secure connection method for automation scenarios. Using a Get-Credential command to prompt you for the password of the certificate securely isn't ideal for automation scenarios. In other words, there's really no automated and secure way to connect using a local certificate.</maml:para>
					</maml:description>
					<command:parameterValue required="true" variableLength="false">SecureString</command:parameterValue>
					<dev:type>
						<maml:name>SecureString</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>None</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
					<maml:name>CertificateThumbprint</maml:name>
					<maml:description>
						<maml:para>The CertificateThumbprint parameter specifies the certificate that's used for CBA. A valid value is the thumbprint value of the certificate. For example, `83213AEAC56D61C97AEE5C1528F4AC5EBA7321C1`.</maml:para>
						<maml:para>Don't use this parameter with the Certificate or CertificateFilePath parameters. Note : The CertificateThumbprint parameter is supported only in Microsoft Windows.</maml:para>
						<maml:para>For more information about CBA, see App-only authentication for unattended scripts in the Exchange Online PowerShell module (https://aka.ms/exo-cba).</maml:para>
					</maml:description>
					<command:parameterValue required="true" variableLength="false">String</command:parameterValue>
					<dev:type>
						<maml:name>String</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>None</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
					<maml:name>Credential</maml:name>
					<maml:description>
						<maml:para>The Credential parameter specifies the username and password that's used to connect to Exchange Online PowerShell. Typically, you use this parameter in scripts or when you need to provide different credentials that have the required permissions. Don't use this parameter for accounts that use multi-factor authentication (MFA).</maml:para>
						<maml:para>Before you run the Connect-ExchangeOnline command, store the username and password in a variable (for example, `$UserCredential = Get-Credential`). Then, use the variable name (`$UserCredential`) for this parameter.</maml:para>
						<maml:para>After the Connect-ExchangeOnline command is complete, the password key in the variable is emptied.</maml:para>
						<maml:para>To specify the password for a certificate file, don't use this parameter; use the CertificatePassword parameter instead.</maml:para>
					</maml:description>
					<command:parameterValue required="true" variableLength="false">PSCredential</command:parameterValue>
					<dev:type>
						<maml:name>PSCredential</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>None</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
					<maml:name>Device</maml:name>
					<maml:description>
						<maml:para>Note : This parameter is available in version 2.0.4 or later of the module, and only in PowerShell 7.</maml:para>
						<maml:para>The Device switch is typically used on computers without web browsers. You don't need to specify a value with this switch.</maml:para>
						<maml:para>Using this switch results in an on-screen message that contains the URL &lt;https://microsoft.com/devicelogin&gt; and a unique code. On any other device with a web browser and internet access, open the URL, enter the unique code, and enter your credentials in the subsequent pages.</maml:para>
						<maml:para>If your login was successful, the PowerShell connection continues.</maml:para>
					</maml:description>
					<dev:type>
						<maml:name>SwitchParameter</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>False</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
					<maml:name>DisableWAM</maml:name>
					<maml:description>
						<maml:para>Note : This parameter is available in version 3.7.2-Preview1 or later of the module.</maml:para>
						<maml:para>The DisableWAM switch disables Web Account Manager (WAM). You don't need to specify a value with this switch.</maml:para>
						<maml:para>Starting in version 3.7.0, WAM is enabled by default when connecting to Exchange Online. If you encounter WAM-related issues during sign in, you can use this switch to disable WAM.</maml:para>
					</maml:description>
					<dev:type>
						<maml:name>SwitchParameter</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>False</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
					<maml:name>EnableErrorReporting</maml:name>
					<maml:description>
						<maml:para>The EnableErrorReporting switch specifies whether to enable error reporting. You don't need to specify a value with this switch.</maml:para>
					</maml:description>
					<dev:type>
						<maml:name>SwitchParameter</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>False</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
					<maml:name>InlineCredential</maml:name>
					<maml:description>
						<maml:para>Note : This parameter is available in version 2.0.4 or later of the module, and only in PowerShell 7.</maml:para>
						<maml:para>The InlineCredential switch specifies whether to pass credentials directly in the Windows PowerShell window. You don't need to specify a value with this switch.</maml:para>
						<maml:para>This switch is similar to the Credential parameter, but with added security. The InlineCredential switch doesn't require you to store the credentials locally in the script, and you can enter credentials directly in an interactive PowerShell session.</maml:para>
						<maml:para>This switch does not work with accounts that use MFA.</maml:para>
					</maml:description>
					<dev:type>
						<maml:name>SwitchParameter</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>False</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
					<maml:name>LoadCmdletHelp</maml:name>
					<maml:description>
						<maml:para>Note : This parameter is available in version 3.7.0-Preview1 or later of the module.</maml:para>
						<maml:para>The LoadCmdletHelp switch downloads cmdlet help files for the Get-Help cmdlet in REST API connections. You don't need to specify a value with this switch.</maml:para>
						<maml:para>Starting in v3.7.0-Preview1, help files for the command line aren't downloaded by default. Use this switch to download the files for cmdlet help at the command line. Tip : This parameter replaces the SkipLoadingCmdletHelp parameter. The SkipLoadingCmdletHelp parameter is no longer required and no longer works, because cmdlet help files are no longer downloaded by default.</maml:para>
					</maml:description>
					<dev:type>
						<maml:name>SwitchParameter</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>False</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
					<maml:name>LogDirectoryPath</maml:name>
					<maml:description>
						<maml:para>The LogDirectoryPath parameter specifies the location of the log files. The default location is `%TMP%\EXOCmdletTelemetry\EXOCmdletTelemetry-yyyymmdd-hhmmss.csv`.</maml:para>
						<maml:para>If you specify a custom location and filename that contains spaces, enclose the value in quotation marks (").</maml:para>
					</maml:description>
					<command:parameterValue required="true" variableLength="false">String</command:parameterValue>
					<dev:type>
						<maml:name>String</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>None</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
					<maml:name>LogLevel</maml:name>
					<maml:description>
						<maml:para>The LogLevel parameter specifies the logging level. Valid values are Default and All.</maml:para>
					</maml:description>
					<command:parameterValue required="true" variableLength="false">String</command:parameterValue>
					<dev:type>
						<maml:name>String</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>None</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
					<maml:name>ManagedIdentity</maml:name>
					<maml:description>
						<maml:para>The ManagedIdentity switch specifies that you're using managed identity to connect. You don't need to specify a value with this switch.</maml:para>
						<maml:para>Managed identity connections are currently supported for the following types of Azure resources:</maml:para>
						<maml:para>- Azure Automation runbooks</maml:para>
						<maml:para>- Azure Virtual Machines</maml:para>
						<maml:para>- Azure Virtual Machine Scale Sets</maml:para>
						<maml:para>- Azure Functions</maml:para>
						<maml:para></maml:para>
						<maml:para>You must use this switch with the Organization parameter.</maml:para>
						<maml:para>For user-assigned managed identity, you must also use this switch with the ManagedIdentityAccountId parameter.</maml:para>
						<maml:para>For more information about connecting with managed identity, see Use Azure managed identities to connect to Exchange Online PowerShell (https://learn.microsoft.com/powershell/exchange/connect-exo-powershell-managed-identity).</maml:para>
					</maml:description>
					<dev:type>
						<maml:name>SwitchParameter</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>False</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
					<maml:name>ManagedIdentityAccountId</maml:name>
					<maml:description>
						<maml:para>The ManagedIdentityAccountId parameter specifies the user-assigned managed identity that you're using to connect. A valid value for this parameter is the application ID (GUID) of the service principal that corresponds to the user-assigned managed identity in Azure.</maml:para>
						<maml:para>You must use this parameter with the Organization parameter and the ManagedIdentity switch.</maml:para>
						<maml:para>For more information about connecting with managed identity, see Use Azure managed identities to connect to Exchange Online PowerShell (https://learn.microsoft.com/powershell/exchange/connect-exo-powershell-managed-identity).</maml:para>
					</maml:description>
					<command:parameterValue required="true" variableLength="false">String</command:parameterValue>
					<dev:type>
						<maml:name>String</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>None</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
					<maml:name>Organization</maml:name>
					<maml:description>
						<maml:para>The Organization parameter specifies the organization when you connect using CBA or managed identity. A valid value for this parameter is the primary .onmicrosoft.com domain or tenant ID of the organization.</maml:para>
						<maml:para>For more information about connecting with CBA, see App-only authentication for unattended scripts in the Exchange Online PowerShell module (https://aka.ms/exo-cba).</maml:para>
						<maml:para>For more information about connecting with managed identity, see Use Azure managed identities to connect to Exchange Online PowerShell (https://learn.microsoft.com/powershell/exchange/connect-exo-powershell-managed-identity).</maml:para>
					</maml:description>
					<command:parameterValue required="true" variableLength="false">String</command:parameterValue>
					<dev:type>
						<maml:name>String</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>None</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
					<maml:name>PageSize</maml:name>
					<maml:description>
						<maml:para>The PageSize parameter specifies the maximum number of entries per page. Valid input for this parameter is an integer between 1 and 1000. The default value is 1000.</maml:para>
					</maml:description>
					<command:parameterValue required="true" variableLength="false">UInt32</command:parameterValue>
					<dev:type>
						<maml:name>UInt32</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>None</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
					<maml:name>ShowBanner</maml:name>
					<maml:description>
						<maml:para>The ShowBanner switch shows or hides the banner message that's displayed when you run Connect-ExchangeOnline. You don't need to specify a value with this switch.</maml:para>
						<maml:para>- To show the banner, you don't need to use this switch (the banner is displayed by default).</maml:para>
						<maml:para>- To hide the banner, use this exact syntax: `-ShowBanner:$false`.</maml:para>
					</maml:description>
					<dev:type>
						<maml:name>SwitchParameter</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>$true</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
					<maml:name>ShowProgress</maml:name>
					<maml:description>
						<maml:para>The ShowProgress parameter specifies whether to show or hide the progress bar of imported cmdlets when you connect. Valid values are:</maml:para>
						<maml:para>- $true: The progress bar is displayed. This is the default value.</maml:para>
						<maml:para>- $false: Currently, this value has no effect.</maml:para>
					</maml:description>
					<command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
					<dev:type>
						<maml:name>Boolean</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>None</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
					<maml:name>SigningCertificate</maml:name>
					<maml:description>
						<maml:para>Note : This parameter is available in version 3.2.0 or later of the module.</maml:para>
						<maml:para>The SigningCertificate parameter specifies the client certificate that's used to sign the format files (*.Format.ps1xml) or script module files (.psm1) in the temporary module that Connect-ExchangeOnline creates.</maml:para>
						<maml:para>A valid value for this parameter is a variable that contains the certificate, or a command or expression that gets the certificate.</maml:para>
						<maml:para>To find the certificate, use the Get-PfxCertificate cmdlet in the Microsoft.PowerShell.Security module or use the Get-ChildItem cmdlet in the certificate (Cert:) drive. If the certificate isn't valid or doesn't have sufficient authority, the command will fail.</maml:para>
					</maml:description>
					<command:parameterValue required="true" variableLength="false">X509Certificate2</command:parameterValue>
					<dev:type>
						<maml:name>X509Certificate2</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>None</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
					<maml:name>SkipLoadingCmdletHelp</maml:name>
					<maml:description>
						<maml:para>Note : This parameter is available in version 3.3.0 or later of the module.</maml:para>
						<maml:para>In version 3.7.0-Preview1 or later, this parameter is replaced by the LoadCmdletHelp parameter. The SkipLoadingCmdletHelp parameter is no longer required and no longer does anything, because cmdlet help files are no longer downloaded by default. Eventually, this parameter will be retired, so remove it from any scripts.</maml:para>
						<maml:para>The SkipLoadingCmdletHelp switch prevents downloading the cmdlet help files for the Get-Help cmdlet in REST API connections. You don't need to specify a value with this switch.</maml:para>
						<maml:para>When you use this switch, you don't get local help files for any cmdlet at the command line.</maml:para>
						<maml:para>This switch doesn't work with the UseRPSSession switch.</maml:para>
					</maml:description>
					<dev:type>
						<maml:name>SwitchParameter</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>False</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
					<maml:name>SkipLoadingFormatData</maml:name>
					<maml:description>
						<maml:para>The SkipLoadingFormatData switch prevents downloading the format data for REST API connections. You don't need to specify a value with this switch.</maml:para>
						<maml:para>When you use this switch, the output of any Exchange cmdlet will be unformatted.</maml:para>
						<maml:para>Use this switch to avoid errors when connecting to Exchange Online PowerShell from within a Windows service or the Windows PowerShell SDK.</maml:para>
						<maml:para>This switch doesn't work with the UseRPSSession switch.</maml:para>
					</maml:description>
					<dev:type>
						<maml:name>SwitchParameter</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>False</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
					<maml:name>TrackPerformance</maml:name>
					<maml:description>
						<maml:para>The TrackPerformance parameter measures additional events (for example, CPU load and memory consumed). Valid values are:</maml:para>
						<maml:para>- $true: Performance tracking is enabled.</maml:para>
						<maml:para>- $false: Performance tracking is disabled. This is the default value.</maml:para>
						<maml:para></maml:para>
						<maml:para>This parameter works only when logging is enabled.</maml:para>
					</maml:description>
					<command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
					<dev:type>
						<maml:name>Boolean</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>None</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
					<maml:name>UseMultithreading</maml:name>
					<maml:description>
						<maml:para>The UseMultithreading parameter specifies whether to disable or enable multi-threading in the Exchange Online PowerShell module. Valid values are:</maml:para>
						<maml:para>- $true: Enable multi-threading. This is the default value.</maml:para>
						<maml:para>- $false: Disable multi-threading. This value will degrade the performance of the nine exclusive Get-EXO\ * cmdlets in the module.</maml:para>
					</maml:description>
					<command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
					<dev:type>
						<maml:name>Boolean</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>None</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
					<maml:name>UserPrincipalName</maml:name>
					<maml:description>
						<maml:para>The UserPrincipalName parameter specifies the account that you want to use to connect (for example, `<EMAIL>`). Using this parameter allows you to skip entering a username in the modern authentication credentials prompt (you're prompted to enter a password).</maml:para>
						<maml:para>If you use the UserPrincipalName parameter, you don't need to use the AzureADAuthorizationEndpointUri parameter for MFA or federated users in environments that normally require it (UserPrincipalName or AzureADAuthorizationEndpointUri is required; OK to use both).</maml:para>
					</maml:description>
					<command:parameterValue required="true" variableLength="false">String</command:parameterValue>
					<dev:type>
						<maml:name>String</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>None</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
					<maml:name>UseRPSSession</maml:name>
					<maml:description>
						<maml:para>Note : Remote PowerShell connections to Exchange Online PowerShell are deprecated. For more information, see Deprecation of Remote PowerShell in Exchange Online (https://techcommunity.microsoft.com/t5/exchange-team-blog/deprecation-of-remote-powershell-in-exchange-online-re-enabling/ba-p/3779692).</maml:para>
						<maml:para>The UseRPSSession switch allows you to connect to Exchange Online PowerShell using traditional remote PowerShell access to all cmdlets. You don't need to specify a value with this switch.</maml:para>
						<maml:para>This switch requires that Basic authentication is enabled in WinRM on the local computer. For more information, see Turn on Basic authentication in WinRM (https://aka.ms/exov3-module#turn-on-basic-authentication-in-winrm).</maml:para>
						<maml:para>If you don't use this switch, REST API mode is used for the connection, so Basic authentication in WinRM isn't required.</maml:para>
					</maml:description>
					<dev:type>
						<maml:name>SwitchParameter</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>False</dev:defaultValue>
				</command:parameter>
			</command:syntaxItem>
		</command:syntax>
		<command:parameters>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="0" aliases="none">
				<maml:name>ConnectionUri</maml:name>
				<maml:description>
					<maml:para>Note : If you use the ExchangeEnvironmentName parameter, you don't need to use the AzureADAuthorizationEndpointUri or ConnectionUri parameters.</maml:para>
					<maml:para>The ConnectionUri parameter specifies the connection endpoint for the PowerShell session. The following Exchange Online PowerShell environments and related values are supported:</maml:para>
					<maml:para>- Microsoft 365 or Microsoft 365 GCC: Don't use this parameter. The required value is `https://outlook.office365.com/powershell-liveid/`, but that's also the default value, so you don't need to use this parameter.</maml:para>
					<maml:para>- Office 365 Germany: `https://outlook.office.de/PowerShell-LiveID`</maml:para>
					<maml:para>- Office 365 operated by 21Vianet: `https://partner.outlook.cn/PowerShell`</maml:para>
					<maml:para>- Microsoft 365 GCC High: `https://outlook.office365.us/powershell-liveID`</maml:para>
					<maml:para>- Microsoft 365 DoD: `https://webmail.apps.mil/powershell-liveID`</maml:para>
				</maml:description>
				<command:parameterValue required="true" variableLength="false">String</command:parameterValue>
				<dev:type>
					<maml:name>String</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>None</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="1" aliases="none">
				<maml:name>AzureADAuthorizationEndpointUri</maml:name>
				<maml:description>
					<maml:para>Note : If you use the ExchangeEnvironmentName parameter, you don't need to use the AzureADAuthorizationEndpointUri or ConnectionUri parameters.</maml:para>
					<maml:para>The AzureADAuthorizationEndpointUri parameter specifies the Microsoft Entra Authorization endpoint that can issue OAuth2 access tokens. The following Exchange Online PowerShell environments and related values are supported:</maml:para>
					<maml:para>- Microsoft 365 or Microsoft 365 GCC: Don't use this parameter. The required value is `https://login.microsoftonline.com/common`, but that's also the default value, so you don't need to use this parameter.</maml:para>
					<maml:para>- Office 365 Germany: `https://login.microsoftonline.de/common`</maml:para>
					<maml:para>- Microsoft 365 GCC High or Microsoft 365 DoD: `https://login.microsoftonline.us/common`</maml:para>
					<maml:para></maml:para>
					<maml:para>If you use the UserPrincipalName parameter, you don't need to use the AzureADAuthorizationEndpointUri parameter for MFA or federated users in environments that normally require it (UserPrincipalName or AzureADAuthorizationEndpointUri is required; OK to use both). Note : MFA authentication or federated authentication isn't available in Office 365 operated by 21Vianet.</maml:para>
				</maml:description>
				<command:parameterValue required="true" variableLength="false">String</command:parameterValue>
				<dev:type>
					<maml:name>String</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>None</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="2" aliases="none">
				<maml:name>ExchangeEnvironmentName</maml:name>
				<maml:description>
					<maml:para>The ExchangeEnvironmentName specifies the Exchange Online environment and eliminates the need to use the AzureADAuthorizationEndpointUri and ConnectionUri parameters. The following Exchange Online PowerShell environments are supported:</maml:para>
					<maml:para>- Microsoft 365 or Microsoft 365 GCC: Don't use this parameter. The required value is `O365Default`, but that's also the default value, so you don't need to use this parameter.</maml:para>
					<maml:para>- Office 365 Germany: `O365GermanyCloud`</maml:para>
					<maml:para>- Office 365 operated by 21Vianet: `O365China`</maml:para>
					<maml:para>- Microsoft 365 GCC High: `O365USGovGCCHigh`</maml:para>
					<maml:para>- Microsoft 365 DoD: `O365USGovDoD`</maml:para>
				</maml:description>
				<command:parameterValue required="true" variableLength="false">ExchangeEnvironment</command:parameterValue>
				<dev:type>
					<maml:name>ExchangeEnvironment</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>O365Default</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="3" aliases="none">
				<maml:name>PSSessionOption</maml:name>
				<maml:description>
					<maml:para>Note : This parameter doesn't work in REST API connections.</maml:para>
					<maml:para>The PSSessionOption parameter specifies the PowerShell session options to use in your connection to Exchange Online. This parameter works only if you also use the UseRPSSession switch in the same command.</maml:para>
					<maml:para>Store the output of the New-PSSessionOption (https://learn.microsoft.com/powershell/module/microsoft.powershell.core/new-pssessionoption) command in a variable (for example, `$PSOptions = New-PSSessionOption &lt;Settings&gt;`), and use the variable name as the value for this parameter (for example, `$PSOptions`).</maml:para>
				</maml:description>
				<command:parameterValue required="true" variableLength="false">PSSessionOption</command:parameterValue>
				<dev:type>
					<maml:name>PSSessionOption</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>None</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="4" aliases="none">
				<maml:name>DelegatedOrganization</maml:name>
				<maml:description>
					<maml:para>The DelegatedOrganization parameter specifies the customer organization that you want to manage. A valid value for this parameter is the primary .onmicrosoft.com domain or tenant ID of the customer organization.</maml:para>
					<maml:para>This parameter works only if the customer organization has agreed to your delegated management via the CSP program.</maml:para>
					<maml:para>After you successfully authenticate, the cmdlets in this session are mapped to the customer organization, and all operations in this session are done on the customer organization.</maml:para>
				</maml:description>
				<command:parameterValue required="true" variableLength="false">String</command:parameterValue>
				<dev:type>
					<maml:name>String</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>None</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="5" aliases="none">
				<maml:name>Prefix</maml:name>
				<maml:description>
					<maml:para>The Prefix parameter specifies a text value to add to the names of Exchange Online PowerShell cmdlets when you connect. For example, Get-InboundConnector becomes Get-ContosoInboundConnector when you use the value Contoso for this parameter.</maml:para>
					<maml:para>- The Prefix value can't contain spaces or special characters like underscores or asterisks.</maml:para>
					<maml:para>- You can't use the Prefix value EXO. That value is reserved for the nine exclusive Get-EXO\ * cmdlets that are built into the module.</maml:para>
					<maml:para>- The Prefix parameter affects only imported Exchange Online cmdlet names. It doesn't affect the names of cmdlets that are built into the module (for example, Disconnect-ExchangeOnline).</maml:para>
				</maml:description>
				<command:parameterValue required="true" variableLength="false">String</command:parameterValue>
				<dev:type>
					<maml:name>String</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>None</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="6" aliases="none">
				<maml:name>CommandName</maml:name>
				<maml:description>
					<maml:para>The CommandName parameter specifies the comma separated list of commands to import into the session. Use this parameter for applications or scripts that use a specific set of cmdlets. Reducing the number of cmdlets in the session helps improve performance and reduces the memory footprint of the application or script.</maml:para>
				</maml:description>
				<command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
				<dev:type>
					<maml:name>String[]</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>None</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="7" aliases="none">
				<maml:name>FormatTypeName</maml:name>
				<maml:description>
					<maml:para>The FormatTypeName parameter specifies the output format of the cmdlet.</maml:para>
				</maml:description>
				<command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
				<dev:type>
					<maml:name>String[]</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>None</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
				<maml:name>AccessToken</maml:name>
				<maml:description>
					<maml:para>Note : This parameter is available in version 3.1.0 or later of the module.</maml:para>
					<maml:para>The AccessToken parameter specifies the OAuth JSON Web Token (JWT) that's used to connect to Exchange Online.</maml:para>
					<maml:para>Depending on the type of access token, you need to use this parameter with the Organization, DelegatedOrganization, or UserPrincipalName parameters.</maml:para>
				</maml:description>
				<command:parameterValue required="true" variableLength="false">String</command:parameterValue>
				<dev:type>
					<maml:name>String</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>None</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
				<maml:name>AppId</maml:name>
				<maml:description>
					<maml:para>The AppId parameter specifies the application ID of the service principal that's used in certificate based authentication (CBA). A valid value is the GUID of the application ID (service principal). For example, `36ee4c6c-0812-40a2-b820-b22ebd02bce3`.</maml:para>
					<maml:para>For more information, see App-only authentication for unattended scripts in the Exchange Online PowerShell module (https://aka.ms/exo-cba).</maml:para>
				</maml:description>
				<command:parameterValue required="true" variableLength="false">String</command:parameterValue>
				<dev:type>
					<maml:name>String</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>None</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
				<maml:name>BypassMailboxAnchoring</maml:name>
				<maml:description>
					<maml:para>The BypassMailboxAnchoring switch bypasses the use of the mailbox anchoring hint. You don't need to specify a value with this switch.</maml:para>
				</maml:description>
				<command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
				<dev:type>
					<maml:name>SwitchParameter</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>False</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
				<maml:name>Certificate</maml:name>
				<maml:description>
					<maml:para>The Certificate parameter specifies the certificate that's used for certificate-based authentication (CBA). A valid value is the X509Certificate2 object value of the certificate.</maml:para>
					<maml:para>Don't use this parameter with the CertificateFilePath or CertificateThumbprint parameters.</maml:para>
					<maml:para>For more information about CBA, see App-only authentication for unattended scripts in the Exchange Online PowerShell module (https://aka.ms/exo-cba).</maml:para>
				</maml:description>
				<command:parameterValue required="true" variableLength="false">X509Certificate2</command:parameterValue>
				<dev:type>
					<maml:name>X509Certificate2</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>None</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
				<maml:name>CertificateFilePath</maml:name>
				<maml:description>
					<maml:para>The CertificateFilePath parameter specifies the certificate that's used for CBA. A valid value is the complete public path to the certificate file. Use the CertificatePassword parameter with this parameter.</maml:para>
					<maml:para>Don't use this parameter with the Certificate or CertificateThumbprint parameters.</maml:para>
					<maml:para>For more information about CBA, see App-only authentication for unattended scripts in the Exchange Online PowerShell module (https://aka.ms/exo-cba).</maml:para>
				</maml:description>
				<command:parameterValue required="true" variableLength="false">String</command:parameterValue>
				<dev:type>
					<maml:name>String</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>None</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
				<maml:name>CertificatePassword</maml:name>
				<maml:description>
					<maml:para>The CertificatePassword parameter specifies the password that's required to open the certificate file when you use the CertificateFilePath parameter to identify the certificate that's used for CBA.</maml:para>
					<maml:para>You can use the following methods as a value for this parameter:</maml:para>
					<maml:para>- `(ConvertTo-SecureString -String '&lt;password&gt;' -AsPlainText -Force)`.</maml:para>
					<maml:para>- Before you run this command, store the password as a variable (for example, `$password = Read-Host "Enter password" -AsSecureString`), and then use the variable (`$password`) for the value.</maml:para>
					<maml:para>- `(Get-Credential).password` to be prompted to enter the password securely when you run this command.</maml:para>
					<maml:para></maml:para>
					<maml:para>For more information about CBA, see App-only authentication for unattended scripts in the Exchange Online PowerShell module (https://aka.ms/exo-cba). Note : Using a ConvertTo-SecureString command to store the password of the certificate locally defeats the purpose of a secure connection method for automation scenarios. Using a Get-Credential command to prompt you for the password of the certificate securely isn't ideal for automation scenarios. In other words, there's really no automated and secure way to connect using a local certificate.</maml:para>
				</maml:description>
				<command:parameterValue required="true" variableLength="false">SecureString</command:parameterValue>
				<dev:type>
					<maml:name>SecureString</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>None</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
				<maml:name>CertificateThumbprint</maml:name>
				<maml:description>
					<maml:para>The CertificateThumbprint parameter specifies the certificate that's used for CBA. A valid value is the thumbprint value of the certificate. For example, `83213AEAC56D61C97AEE5C1528F4AC5EBA7321C1`.</maml:para>
					<maml:para>Don't use this parameter with the Certificate or CertificateFilePath parameters. Note : The CertificateThumbprint parameter is supported only in Microsoft Windows.</maml:para>
					<maml:para>For more information about CBA, see App-only authentication for unattended scripts in the Exchange Online PowerShell module (https://aka.ms/exo-cba).</maml:para>
				</maml:description>
				<command:parameterValue required="true" variableLength="false">String</command:parameterValue>
				<dev:type>
					<maml:name>String</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>None</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
				<maml:name>Credential</maml:name>
				<maml:description>
					<maml:para>The Credential parameter specifies the username and password that's used to connect to Exchange Online PowerShell. Typically, you use this parameter in scripts or when you need to provide different credentials that have the required permissions. Don't use this parameter for accounts that use multi-factor authentication (MFA).</maml:para>
					<maml:para>Before you run the Connect-ExchangeOnline command, store the username and password in a variable (for example, `$UserCredential = Get-Credential`). Then, use the variable name (`$UserCredential`) for this parameter.</maml:para>
					<maml:para>After the Connect-ExchangeOnline command is complete, the password key in the variable is emptied.</maml:para>
					<maml:para>To specify the password for a certificate file, don't use this parameter; use the CertificatePassword parameter instead.</maml:para>
				</maml:description>
				<command:parameterValue required="true" variableLength="false">PSCredential</command:parameterValue>
				<dev:type>
					<maml:name>PSCredential</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>None</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
				<maml:name>Device</maml:name>
				<maml:description>
					<maml:para>Note : This parameter is available in version 2.0.4 or later of the module, and only in PowerShell 7.</maml:para>
					<maml:para>The Device switch is typically used on computers without web browsers. You don't need to specify a value with this switch.</maml:para>
					<maml:para>Using this switch results in an on-screen message that contains the URL &lt;https://microsoft.com/devicelogin&gt; and a unique code. On any other device with a web browser and internet access, open the URL, enter the unique code, and enter your credentials in the subsequent pages.</maml:para>
					<maml:para>If your login was successful, the PowerShell connection continues.</maml:para>
				</maml:description>
				<command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
				<dev:type>
					<maml:name>SwitchParameter</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>False</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
				<maml:name>DisableWAM</maml:name>
				<maml:description>
					<maml:para>Note : This parameter is available in version 3.7.2-Preview1 or later of the module.</maml:para>
					<maml:para>The DisableWAM switch disables Web Account Manager (WAM). You don't need to specify a value with this switch.</maml:para>
					<maml:para>Starting in version 3.7.0, WAM is enabled by default when connecting to Exchange Online. If you encounter WAM-related issues during sign in, you can use this switch to disable WAM.</maml:para>
				</maml:description>
				<command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
				<dev:type>
					<maml:name>SwitchParameter</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>False</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
				<maml:name>EnableErrorReporting</maml:name>
				<maml:description>
					<maml:para>The EnableErrorReporting switch specifies whether to enable error reporting. You don't need to specify a value with this switch.</maml:para>
				</maml:description>
				<command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
				<dev:type>
					<maml:name>SwitchParameter</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>False</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
				<maml:name>InlineCredential</maml:name>
				<maml:description>
					<maml:para>Note : This parameter is available in version 2.0.4 or later of the module, and only in PowerShell 7.</maml:para>
					<maml:para>The InlineCredential switch specifies whether to pass credentials directly in the Windows PowerShell window. You don't need to specify a value with this switch.</maml:para>
					<maml:para>This switch is similar to the Credential parameter, but with added security. The InlineCredential switch doesn't require you to store the credentials locally in the script, and you can enter credentials directly in an interactive PowerShell session.</maml:para>
					<maml:para>This switch does not work with accounts that use MFA.</maml:para>
				</maml:description>
				<command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
				<dev:type>
					<maml:name>SwitchParameter</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>False</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
				<maml:name>LoadCmdletHelp</maml:name>
				<maml:description>
					<maml:para>Note : This parameter is available in version 3.7.0-Preview1 or later of the module.</maml:para>
					<maml:para>The LoadCmdletHelp switch downloads cmdlet help files for the Get-Help cmdlet in REST API connections. You don't need to specify a value with this switch.</maml:para>
					<maml:para>Starting in v3.7.0-Preview1, help files for the command line aren't downloaded by default. Use this switch to download the files for cmdlet help at the command line. Tip : This parameter replaces the SkipLoadingCmdletHelp parameter. The SkipLoadingCmdletHelp parameter is no longer required and no longer works, because cmdlet help files are no longer downloaded by default.</maml:para>
				</maml:description>
				<command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
				<dev:type>
					<maml:name>SwitchParameter</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>False</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
				<maml:name>LogDirectoryPath</maml:name>
				<maml:description>
					<maml:para>The LogDirectoryPath parameter specifies the location of the log files. The default location is `%TMP%\EXOCmdletTelemetry\EXOCmdletTelemetry-yyyymmdd-hhmmss.csv`.</maml:para>
					<maml:para>If you specify a custom location and filename that contains spaces, enclose the value in quotation marks (").</maml:para>
				</maml:description>
				<command:parameterValue required="true" variableLength="false">String</command:parameterValue>
				<dev:type>
					<maml:name>String</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>None</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
				<maml:name>LogLevel</maml:name>
				<maml:description>
					<maml:para>The LogLevel parameter specifies the logging level. Valid values are Default and All.</maml:para>
				</maml:description>
				<command:parameterValue required="true" variableLength="false">String</command:parameterValue>
				<dev:type>
					<maml:name>String</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>None</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
				<maml:name>ManagedIdentity</maml:name>
				<maml:description>
					<maml:para>The ManagedIdentity switch specifies that you're using managed identity to connect. You don't need to specify a value with this switch.</maml:para>
					<maml:para>Managed identity connections are currently supported for the following types of Azure resources:</maml:para>
					<maml:para>- Azure Automation runbooks</maml:para>
					<maml:para>- Azure Virtual Machines</maml:para>
					<maml:para>- Azure Virtual Machine Scale Sets</maml:para>
					<maml:para>- Azure Functions</maml:para>
					<maml:para></maml:para>
					<maml:para>You must use this switch with the Organization parameter.</maml:para>
					<maml:para>For user-assigned managed identity, you must also use this switch with the ManagedIdentityAccountId parameter.</maml:para>
					<maml:para>For more information about connecting with managed identity, see Use Azure managed identities to connect to Exchange Online PowerShell (https://learn.microsoft.com/powershell/exchange/connect-exo-powershell-managed-identity).</maml:para>
				</maml:description>
				<command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
				<dev:type>
					<maml:name>SwitchParameter</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>False</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
				<maml:name>ManagedIdentityAccountId</maml:name>
				<maml:description>
					<maml:para>The ManagedIdentityAccountId parameter specifies the user-assigned managed identity that you're using to connect. A valid value for this parameter is the application ID (GUID) of the service principal that corresponds to the user-assigned managed identity in Azure.</maml:para>
					<maml:para>You must use this parameter with the Organization parameter and the ManagedIdentity switch.</maml:para>
					<maml:para>For more information about connecting with managed identity, see Use Azure managed identities to connect to Exchange Online PowerShell (https://learn.microsoft.com/powershell/exchange/connect-exo-powershell-managed-identity).</maml:para>
				</maml:description>
				<command:parameterValue required="true" variableLength="false">String</command:parameterValue>
				<dev:type>
					<maml:name>String</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>None</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
				<maml:name>Organization</maml:name>
				<maml:description>
					<maml:para>The Organization parameter specifies the organization when you connect using CBA or managed identity. A valid value for this parameter is the primary .onmicrosoft.com domain or tenant ID of the organization.</maml:para>
					<maml:para>For more information about connecting with CBA, see App-only authentication for unattended scripts in the Exchange Online PowerShell module (https://aka.ms/exo-cba).</maml:para>
					<maml:para>For more information about connecting with managed identity, see Use Azure managed identities to connect to Exchange Online PowerShell (https://learn.microsoft.com/powershell/exchange/connect-exo-powershell-managed-identity).</maml:para>
				</maml:description>
				<command:parameterValue required="true" variableLength="false">String</command:parameterValue>
				<dev:type>
					<maml:name>String</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>None</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
				<maml:name>PageSize</maml:name>
				<maml:description>
					<maml:para>The PageSize parameter specifies the maximum number of entries per page. Valid input for this parameter is an integer between 1 and 1000. The default value is 1000.</maml:para>
				</maml:description>
				<command:parameterValue required="true" variableLength="false">UInt32</command:parameterValue>
				<dev:type>
					<maml:name>UInt32</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>None</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
				<maml:name>ShowBanner</maml:name>
				<maml:description>
					<maml:para>The ShowBanner switch shows or hides the banner message that's displayed when you run Connect-ExchangeOnline. You don't need to specify a value with this switch.</maml:para>
					<maml:para>- To show the banner, you don't need to use this switch (the banner is displayed by default).</maml:para>
					<maml:para>- To hide the banner, use this exact syntax: `-ShowBanner:$false`.</maml:para>
				</maml:description>
				<command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
				<dev:type>
					<maml:name>SwitchParameter</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>$true</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
				<maml:name>ShowProgress</maml:name>
				<maml:description>
					<maml:para>The ShowProgress parameter specifies whether to show or hide the progress bar of imported cmdlets when you connect. Valid values are:</maml:para>
					<maml:para>- $true: The progress bar is displayed. This is the default value.</maml:para>
					<maml:para>- $false: Currently, this value has no effect.</maml:para>
				</maml:description>
				<command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
				<dev:type>
					<maml:name>Boolean</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>None</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
				<maml:name>SigningCertificate</maml:name>
				<maml:description>
					<maml:para>Note : This parameter is available in version 3.2.0 or later of the module.</maml:para>
					<maml:para>The SigningCertificate parameter specifies the client certificate that's used to sign the format files (*.Format.ps1xml) or script module files (.psm1) in the temporary module that Connect-ExchangeOnline creates.</maml:para>
					<maml:para>A valid value for this parameter is a variable that contains the certificate, or a command or expression that gets the certificate.</maml:para>
					<maml:para>To find the certificate, use the Get-PfxCertificate cmdlet in the Microsoft.PowerShell.Security module or use the Get-ChildItem cmdlet in the certificate (Cert:) drive. If the certificate isn't valid or doesn't have sufficient authority, the command will fail.</maml:para>
				</maml:description>
				<command:parameterValue required="true" variableLength="false">X509Certificate2</command:parameterValue>
				<dev:type>
					<maml:name>X509Certificate2</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>None</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
				<maml:name>SkipLoadingCmdletHelp</maml:name>
				<maml:description>
					<maml:para>Note : This parameter is available in version 3.3.0 or later of the module.</maml:para>
					<maml:para>In version 3.7.0-Preview1 or later, this parameter is replaced by the LoadCmdletHelp parameter. The SkipLoadingCmdletHelp parameter is no longer required and no longer does anything, because cmdlet help files are no longer downloaded by default. Eventually, this parameter will be retired, so remove it from any scripts.</maml:para>
					<maml:para>The SkipLoadingCmdletHelp switch prevents downloading the cmdlet help files for the Get-Help cmdlet in REST API connections. You don't need to specify a value with this switch.</maml:para>
					<maml:para>When you use this switch, you don't get local help files for any cmdlet at the command line.</maml:para>
					<maml:para>This switch doesn't work with the UseRPSSession switch.</maml:para>
				</maml:description>
				<command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
				<dev:type>
					<maml:name>SwitchParameter</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>False</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
				<maml:name>SkipLoadingFormatData</maml:name>
				<maml:description>
					<maml:para>The SkipLoadingFormatData switch prevents downloading the format data for REST API connections. You don't need to specify a value with this switch.</maml:para>
					<maml:para>When you use this switch, the output of any Exchange cmdlet will be unformatted.</maml:para>
					<maml:para>Use this switch to avoid errors when connecting to Exchange Online PowerShell from within a Windows service or the Windows PowerShell SDK.</maml:para>
					<maml:para>This switch doesn't work with the UseRPSSession switch.</maml:para>
				</maml:description>
				<command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
				<dev:type>
					<maml:name>SwitchParameter</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>False</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
				<maml:name>TrackPerformance</maml:name>
				<maml:description>
					<maml:para>The TrackPerformance parameter measures additional events (for example, CPU load and memory consumed). Valid values are:</maml:para>
					<maml:para>- $true: Performance tracking is enabled.</maml:para>
					<maml:para>- $false: Performance tracking is disabled. This is the default value.</maml:para>
					<maml:para></maml:para>
					<maml:para>This parameter works only when logging is enabled.</maml:para>
				</maml:description>
				<command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
				<dev:type>
					<maml:name>Boolean</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>None</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
				<maml:name>UseMultithreading</maml:name>
				<maml:description>
					<maml:para>The UseMultithreading parameter specifies whether to disable or enable multi-threading in the Exchange Online PowerShell module. Valid values are:</maml:para>
					<maml:para>- $true: Enable multi-threading. This is the default value.</maml:para>
					<maml:para>- $false: Disable multi-threading. This value will degrade the performance of the nine exclusive Get-EXO\ * cmdlets in the module.</maml:para>
				</maml:description>
				<command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
				<dev:type>
					<maml:name>Boolean</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>None</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
				<maml:name>UserPrincipalName</maml:name>
				<maml:description>
					<maml:para>The UserPrincipalName parameter specifies the account that you want to use to connect (for example, `<EMAIL>`). Using this parameter allows you to skip entering a username in the modern authentication credentials prompt (you're prompted to enter a password).</maml:para>
					<maml:para>If you use the UserPrincipalName parameter, you don't need to use the AzureADAuthorizationEndpointUri parameter for MFA or federated users in environments that normally require it (UserPrincipalName or AzureADAuthorizationEndpointUri is required; OK to use both).</maml:para>
				</maml:description>
				<command:parameterValue required="true" variableLength="false">String</command:parameterValue>
				<dev:type>
					<maml:name>String</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>None</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
				<maml:name>UseRPSSession</maml:name>
				<maml:description>
					<maml:para>Note : Remote PowerShell connections to Exchange Online PowerShell are deprecated. For more information, see Deprecation of Remote PowerShell in Exchange Online (https://techcommunity.microsoft.com/t5/exchange-team-blog/deprecation-of-remote-powershell-in-exchange-online-re-enabling/ba-p/3779692).</maml:para>
					<maml:para>The UseRPSSession switch allows you to connect to Exchange Online PowerShell using traditional remote PowerShell access to all cmdlets. You don't need to specify a value with this switch.</maml:para>
					<maml:para>This switch requires that Basic authentication is enabled in WinRM on the local computer. For more information, see Turn on Basic authentication in WinRM (https://aka.ms/exov3-module#turn-on-basic-authentication-in-winrm).</maml:para>
					<maml:para>If you don't use this switch, REST API mode is used for the connection, so Basic authentication in WinRM isn't required.</maml:para>
				</maml:description>
				<command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
				<dev:type>
					<maml:name>SwitchParameter</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>False</dev:defaultValue>
			</command:parameter>
		</command:parameters>
		<command:inputTypes />
		<command:returnValues />
		<maml:alertSet>
			<maml:alert>
				<maml:para></maml:para>
			</maml:alert>
		</maml:alertSet>
		<command:examples>
			<command:example>
				<maml:title>-------------------------- Example 1 --------------------------</maml:title>
				<dev:code>Connect-ExchangeOnline -UserPrincipalName <EMAIL></dev:code>
				<dev:remarks>
					<maml:para>This example connects to Exchange Online PowerShell using modern authentication, with or without multi-factor authentication (MFA). The connection uses REST API mode and doesn't require Basic authentication to be enabled in WinRM on the local computer.</maml:para>
				</dev:remarks>
			</command:example>
			<command:example>
				<maml:title>-------------------------- Example 2 --------------------------</maml:title>
				<dev:code>Connect-ExchangeOnline -AppId &lt;%App_id%&gt; -CertificateThumbprint &lt;%Thumbprint string of certificate%&gt; -Organization "contoso.onmicrosoft.com"</dev:code>
				<dev:remarks>
					<maml:para>This example connects to Exchange Online PowerShell in an unattended scripting scenario using a certificate thumbprint.</maml:para>
				</dev:remarks>
			</command:example>
			<command:example>
				<maml:title>-------------------------- Example 3 --------------------------</maml:title>
				<dev:code>Connect-ExchangeOnline -AppId &lt;%App_id%&gt; -Certificate &lt;%X509Certificate2 object%&gt; -Organization "contoso.onmicrosoft.com"</dev:code>
				<dev:remarks>
					<maml:para>This example connects to Exchange Online PowerShell in an unattended scripting scenario using a certificate file. This method is best suited for scenarios where the certificate is stored in remote machines and fetched at runtime. For example, the certificate is stored in the Azure Key Vault.</maml:para>
				</dev:remarks>
			</command:example>
			<command:example>
				<maml:title>-------------------------- Example 4 --------------------------</maml:title>
				<dev:code>Connect-ExchangeOnline -Device</dev:code>
				<dev:remarks>
					<maml:para>In PowerShell 7.0.3 or later using version 2.0.4 or later of the module, this example connects to Exchange Online PowerShell in interactive scripting scenarios on computers that don't have web browsers.</maml:para>
					<maml:para>The command returns a URL and unique code that's tied to the session. You need to open the URL in a browser on any computer, and then enter the unique code. After you complete the login in the web browser, the session in the Powershell 7 window is authenticated via the regular Microsoft Entra authentication flow, and the Exchange Online cmdlets are imported after few seconds.</maml:para>
				</dev:remarks>
			</command:example>
			<command:example>
				<maml:title>-------------------------- Example 6 --------------------------</maml:title>
				<dev:code>Connect-ExchangeOnline -InlineCredential</dev:code>
				<dev:remarks>
					<maml:para>In PowerShell 7.0.3 or later using version 2.0.4 or later of the module, this example connects to Exchange Online PowerShell in interactive scripting scenarios by passing credentials directly in the PowerShell window.</maml:para>
				</dev:remarks>
			</command:example>
		</command:examples>
		<command:relatedLinks>
			<maml:navigationLink>
				<maml:linkText>Online Version:</maml:linkText>
				<maml:uri>https://learn.microsoft.com/powershell/module/exchange/connect-exchangeonline</maml:uri>
			</maml:navigationLink>
		</command:relatedLinks>
	</command:command>
	<command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
		<command:details>
			<command:name>Connect-IPPSSession</command:name>
			<command:verb>Connect</command:verb>
			<command:noun>IPPSSession</command:noun>
			<maml:description>
				<maml:para>This cmdlet is available only in the Exchange Online PowerShell module. For more information, see About the Exchange Online PowerShell module (https://aka.ms/exov3-module).</maml:para>
				<maml:para>Use the Connect-IPPSSession cmdlet in the Exchange Online PowerShell module to connect to Security &amp; Compliance PowerShell using modern authentication. The cmdlet works for MFA or non-MFA enabled accounts. Note : Version 3.2.0 or later of the module supports REST API mode for virtually all Security &amp; Compliance PowerShell cmdlets (Basic authentication in WinRM on the local computer isn't required for REST API mode). For more information, see Prerequisites for the Exchange Online PowerShell module (https://learn.microsoft.com/powershell/exchange/exchange-online-powershell-v2#prerequisites-for-the-exchange-online-powershell-module).</maml:para>
				<maml:para>For information about the parameter sets in the Syntax section below, see Exchange cmdlet syntax (https://learn.microsoft.com/powershell/exchange/exchange-cmdlet-syntax).</maml:para>
			</maml:description>
		</command:details>
		<maml:description>
			<maml:para>For detailed connection instructions, including prerequisites, see Connect to Security &amp; Compliance PowerShell (https://learn.microsoft.com/powershell/exchange/connect-to-scc-powershell).</maml:para>
		</maml:description>
		<command:syntax>
			<command:syntaxItem>
				<maml:name>Connect-IPPSSession</maml:name>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="0" aliases="none">
					<maml:name>ConnectionUri</maml:name>
					<maml:description>
						<maml:para>The ConnectionUri parameter specifies the connection endpoint for the PowerShell session. The following PowerShell environments and related values are supported:</maml:para>
						<maml:para>- Security &amp; Compliance PowerShell in Microsoft 365 or Microsoft 365 GCC: Don't use this parameter. The required value is `https://ps.compliance.protection.outlook.com/powershell-liveid/`, but that's also the default value, so you don't need to use this parameter.</maml:para>
						<maml:para>- Security &amp; Compliance PowerShell in Office 365 operated by 21Vianet: `https://ps.compliance.protection.partner.outlook.cn/powershell-liveid`</maml:para>
						<maml:para>- Security &amp; Compliance PowerShell in Microsoft GCC High: `https://ps.compliance.protection.office365.us/powershell-liveid/`</maml:para>
						<maml:para>- Security &amp; Compliance PowerShell in Microsoft DoD: `https://l5.ps.compliance.protection.office365.us/powershell-liveid/`</maml:para>
					</maml:description>
					<command:parameterValue required="true" variableLength="false">String</command:parameterValue>
					<dev:type>
						<maml:name>String</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>None</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="1" aliases="none">
					<maml:name>AzureADAuthorizationEndpointUri</maml:name>
					<maml:description>
						<maml:para>The AzureADAuthorizationEndpointUri parameter specifies the Microsoft Entra Authorization endpoint that can issue OAuth2 access tokens. The following PowerShell environments and related values are supported:</maml:para>
						<maml:para>- Security &amp; Compliance PowerShell in Microsoft 365 or Microsoft 365 GCC: Don't use this parameter. The required value is `https://login.microsoftonline.com/common`, but that's also the default value, so you don't need to use this parameter.</maml:para>
						<maml:para>- Security &amp; Compliance PowerShell in Office 365 operated by 21Vianet: `https://login.chinacloudapi.cn/common`</maml:para>
						<maml:para>- Security &amp; Compliance PowerShell in Microsoft GCC High or Microsoft DoD: `https://login.microsoftonline.us/common`</maml:para>
						<maml:para></maml:para>
						<maml:para>If you use the UserPrincipalName parameter, you don't need to use the AzureADAuthorizationEndpointUri parameter for MFA or federated users in environments that normally require it (UserPrincipalName or AzureADAuthorizationEndpointUri is required; OK to use both).</maml:para>
					</maml:description>
					<command:parameterValue required="true" variableLength="false">String</command:parameterValue>
					<dev:type>
						<maml:name>String</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>None</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="2" aliases="none">
					<maml:name>DelegatedOrganization</maml:name>
					<maml:description>
						<maml:para>The DelegatedOrganization parameter specifies the customer organization that you want to manage (for example, contosoelectronics.onmicrosoft.com). This parameter works only if the customer organization has agreed to your delegated management via the CSP program.</maml:para>
						<maml:para>After you successfully authenticate, the cmdlets in this session are mapped to the customer organization, and all operations in this session are done on the customer organization. Notes :</maml:para>
						<maml:para>- Use the primary .onmicrosoft.com domain of the delegated organization for the value of this parameter.</maml:para>
						<maml:para>- You must use the AzureADAuthorizationEndpointUri parameter with this parameter.</maml:para>
					</maml:description>
					<command:parameterValue required="true" variableLength="false">String</command:parameterValue>
					<dev:type>
						<maml:name>String</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>None</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="3" aliases="none">
					<maml:name>PSSessionOption</maml:name>
					<maml:description>
						<maml:para>Note : This parameter doesn't work in REST API connections.</maml:para>
						<maml:para>The PSSessionOption parameter specifies the remote PowerShell session options to use in your connection to Security &amp; Compliance PowerShell. This parameter works only if you also use the UseRPSSession switch in the same command.</maml:para>
						<maml:para>Store the output of the New-PSSessionOption (https://learn.microsoft.com/powershell/module/microsoft.powershell.core/new-pssessionoption) command in a variable (for example, `$PSOptions = New-PSSessionOption &lt;Settings&gt;`), and use the variable name as the value for this parameter (for example, `$PSOptions`).</maml:para>
					</maml:description>
					<command:parameterValue required="true" variableLength="false">PSSessionOption</command:parameterValue>
					<dev:type>
						<maml:name>PSSessionOption</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>None</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="4" aliases="none">
					<maml:name>Prefix</maml:name>
					<maml:description>
						<maml:para>The Prefix parameter specifies a text value to add to the names of Security &amp; Compliance PowerShell cmdlets when you connect. For example, Get-ComplianceCase becomes Get-ContosoComplianceCase when you use the value Contoso for this parameter.</maml:para>
						<maml:para>- The Prefix value can't contain spaces or special characters like underscores or asterisks.</maml:para>
						<maml:para>- You can't use the Prefix value EXO. That value is reserved for the nine exclusive Get-EXO\ * cmdlets that are built into the module.</maml:para>
						<maml:para>- The Prefix parameter affects only imported Security &amp; Compliance cmdlet names. It doesn't affect the names of cmdlets that are built into the module (for example, Disconnect-ExchangeOnline).</maml:para>
					</maml:description>
					<command:parameterValue required="true" variableLength="false">String</command:parameterValue>
					<dev:type>
						<maml:name>String</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>None</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="5" aliases="none">
					<maml:name>CommandName</maml:name>
					<maml:description>
						<maml:para>The CommandName parameter specifies the comma separated list of commands to import into the session. Use this parameter for applications or scripts that use a specific set of cmdlets. Reducing the number of cmdlets in the session helps improve performance and reduces the memory footprint of the application or script.</maml:para>
					</maml:description>
					<command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
					<dev:type>
						<maml:name>String[]</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>None</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="6" aliases="none">
					<maml:name>FormatTypeName</maml:name>
					<maml:description>
						<maml:para>The FormatTypeName parameter specifies the output format of the cmdlet.</maml:para>
					</maml:description>
					<command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
					<dev:type>
						<maml:name>String[]</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>None</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
					<maml:name>AccessToken</maml:name>
					<maml:description>
						<maml:para>Note : This parameter is available in version 3.8.0-Preview1 or later of the module.</maml:para>
						<maml:para>The AccessToken parameter specifies the OAuth JSON Web Token (JWT) that's used to connect to Security and Compliance PowerShell.</maml:para>
						<maml:para>Depending on the type of access token, you need to use this parameter with the Organization, DelegatedOrganization, or UserPrincipalName parameters.</maml:para>
					</maml:description>
					<command:parameterValue required="true" variableLength="false">String</command:parameterValue>
					<dev:type>
						<maml:name>String</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>None</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
					<maml:name>AppId</maml:name>
					<maml:description>
						<maml:para>The AppId parameter specifies the application ID of the service principal that's used in certificate based authentication (CBA). A valid value is the GUID of the application ID (service principal). For example, `36ee4c6c-0812-40a2-b820-b22ebd02bce3`.</maml:para>
						<maml:para>For more information, see App-only authentication for unattended scripts in the Exchange Online PowerShell module (https://aka.ms/exo-cba).</maml:para>
					</maml:description>
					<command:parameterValue required="true" variableLength="false">String</command:parameterValue>
					<dev:type>
						<maml:name>String</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>None</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
					<maml:name>BypassMailboxAnchoring</maml:name>
					<maml:description>
						<maml:para>The BypassMailboxAnchoring switch bypasses the use of the mailbox anchoring hint. You don't need to specify a value with this switch.</maml:para>
					</maml:description>
					<dev:type>
						<maml:name>SwitchParameter</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>False</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
					<maml:name>Certificate</maml:name>
					<maml:description>
						<maml:para>The Certificate parameter specifies the certificate that's used for certificate-based authentication (CBA). A valid value is the X509Certificate2 object value of the certificate.</maml:para>
						<maml:para>Don't use this parameter with the CertificateFilePath or CertificateThumbprint parameters.</maml:para>
						<maml:para>For more information about CBA, see App-only authentication for unattended scripts in the Exchange Online PowerShell module (https://aka.ms/exo-cba).</maml:para>
					</maml:description>
					<command:parameterValue required="true" variableLength="false">X509Certificate2</command:parameterValue>
					<dev:type>
						<maml:name>X509Certificate2</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>None</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
					<maml:name>CertificateFilePath</maml:name>
					<maml:description>
						<maml:para>The CertificateFilePath parameter specifies the certificate that's used for CBA. A valid value is the complete public path to the certificate file. Use the CertificatePassword parameter with this parameter.</maml:para>
						<maml:para>Don't use this parameter with the Certificate or CertificateThumbprint parameters.</maml:para>
						<maml:para>For more information about CBA, see App-only authentication for unattended scripts in the Exchange Online PowerShell module (https://aka.ms/exo-cba).</maml:para>
					</maml:description>
					<command:parameterValue required="true" variableLength="false">String</command:parameterValue>
					<dev:type>
						<maml:name>String</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>None</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
					<maml:name>CertificatePassword</maml:name>
					<maml:description>
						<maml:para>The CertificatePassword parameter specifies the password that's required to open the certificate file when you use the CertificateFilePath parameter to identify the certificate that's used for CBA.</maml:para>
						<maml:para>You can use the following methods as a value for this parameter:</maml:para>
						<maml:para>- `(ConvertTo-SecureString -String '&lt;password&gt;' -AsPlainText -Force)`.</maml:para>
						<maml:para>- Before you run this command, store the password as a variable (for example, `$password = Read-Host "Enter password" -AsSecureString`), and then use the variable (`$password`) for the value.</maml:para>
						<maml:para>- `(Get-Credential).password` to be prompted to enter the password securely when you run this command.</maml:para>
						<maml:para></maml:para>
						<maml:para>For more information about CBA, see App-only authentication for unattended scripts in the Exchange Online PowerShell module (https://aka.ms/exo-cba). Note : Using a ConvertTo-SecureString command to store the password of the certificate locally defeats the purpose of a secure connection method for automation scenarios. Using a Get-Credential command to prompt you for the password of the certificate securely isn't ideal for automation scenarios. In other words, there's really no automated and secure way to connect using a local certificate.</maml:para>
					</maml:description>
					<command:parameterValue required="true" variableLength="false">SecureString</command:parameterValue>
					<dev:type>
						<maml:name>SecureString</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>None</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
					<maml:name>CertificateThumbprint</maml:name>
					<maml:description>
						<maml:para>The CertificateThumbprint parameter specifies the certificate that's used for CBA. A valid value is the thumbprint value of the certificate. For example, `83213AEAC56D61C97AEE5C1528F4AC5EBA7321C1`.</maml:para>
						<maml:para>Don't use this parameter with the Certificate or CertificateFilePath parameters. Note : The CertificateThumbprint parameter is supported only in Microsoft Windows.</maml:para>
						<maml:para>For more information about CBA, see App-only authentication for unattended scripts in the Exchange Online PowerShell module (https://aka.ms/exo-cba).</maml:para>
					</maml:description>
					<command:parameterValue required="true" variableLength="false">String</command:parameterValue>
					<dev:type>
						<maml:name>String</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>None</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
					<maml:name>Credential</maml:name>
					<maml:description>
						<maml:para>The Credential parameter specifies the username and password that's used to connect to Exchange Online PowerShell. Typically, you use this parameter in scripts or when you need to provide different credentials that have the required permissions. Don't use this parameter for accounts that use multi-factor authentication (MFA).</maml:para>
						<maml:para>Before you run the Connect-IPPSSession command, store the username and password in a variable (for example, `$UserCredential = Get-Credential`). Then, use the variable name (`$UserCredential`) for this parameter.</maml:para>
						<maml:para>After the Connect-IPPSSession command is complete, the password key in the variable is emptied.</maml:para>
					</maml:description>
					<command:parameterValue required="true" variableLength="false">PSCredential</command:parameterValue>
					<dev:type>
						<maml:name>PSCredential</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>None</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
					<maml:name>Organization</maml:name>
					<maml:description>
						<maml:para>The Organization parameter specifies the organization when you connect using CBA. You must use the primary .onmicrosoft.com domain of the organization for the value of this parameter.</maml:para>
						<maml:para>For more information about CBA, see App-only authentication for unattended scripts in the Exchange Online PowerShell module (https://aka.ms/exo-cba).</maml:para>
					</maml:description>
					<command:parameterValue required="true" variableLength="false">String</command:parameterValue>
					<dev:type>
						<maml:name>String</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>None</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
					<maml:name>UserPrincipalName</maml:name>
					<maml:description>
						<maml:para>The UserPrincipalName parameter specifies the account that you want to use to connect (for example, <EMAIL>). Using this parameter allows you to skip entering a username in the modern authentication credentials prompt (you're prompted to enter a password).</maml:para>
						<maml:para>If you use the UserPrincipalName parameter, you don't need to use the AzureADAuthorizationEndpointUri parameter for MFA or federated users in environments that normally require it (UserPrincipalName or AzureADAuthorizationEndpointUri is required; OK to use both).</maml:para>
					</maml:description>
					<command:parameterValue required="true" variableLength="false">String</command:parameterValue>
					<dev:type>
						<maml:name>String</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>None</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
					<maml:name>UseRPSSession</maml:name>
					<maml:description>
						<maml:para>This parameter is available in version 3.2.0 or later of the module. Note : Remote PowerShell connections to Security &amp; Compliance are deprecated. For more information, see Deprecation of Remote PowerShell in Security and Compliance PowerShell (https://techcommunity.microsoft.com/t5/exchange-team-blog/deprecation-of-remote-powershell-rps-protocol-in-security-and/ba-p/3815432).</maml:para>
						<maml:para>The UseRPSSession switch allows you to connect to Security &amp; Compliance PowerShell using traditional remote PowerShell access to all cmdlets. You don't need to specify a value with this switch.</maml:para>
						<maml:para>This switch requires that Basic authentication is enabled in WinRM on the local computer. For more information, see Turn on Basic authentication in WinRM (https://aka.ms/exov3-module#turn-on-basic-authentication-in-winrm).</maml:para>
						<maml:para>If you don't use this switch, Basic authentication in WinRM is not required.</maml:para>
					</maml:description>
					<dev:type>
						<maml:name>SwitchParameter</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>False</dev:defaultValue>
				</command:parameter>
			</command:syntaxItem>
		</command:syntax>
		<command:parameters>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="0" aliases="none">
				<maml:name>ConnectionUri</maml:name>
				<maml:description>
					<maml:para>The ConnectionUri parameter specifies the connection endpoint for the PowerShell session. The following PowerShell environments and related values are supported:</maml:para>
					<maml:para>- Security &amp; Compliance PowerShell in Microsoft 365 or Microsoft 365 GCC: Don't use this parameter. The required value is `https://ps.compliance.protection.outlook.com/powershell-liveid/`, but that's also the default value, so you don't need to use this parameter.</maml:para>
					<maml:para>- Security &amp; Compliance PowerShell in Office 365 operated by 21Vianet: `https://ps.compliance.protection.partner.outlook.cn/powershell-liveid`</maml:para>
					<maml:para>- Security &amp; Compliance PowerShell in Microsoft GCC High: `https://ps.compliance.protection.office365.us/powershell-liveid/`</maml:para>
					<maml:para>- Security &amp; Compliance PowerShell in Microsoft DoD: `https://l5.ps.compliance.protection.office365.us/powershell-liveid/`</maml:para>
				</maml:description>
				<command:parameterValue required="true" variableLength="false">String</command:parameterValue>
				<dev:type>
					<maml:name>String</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>None</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="1" aliases="none">
				<maml:name>AzureADAuthorizationEndpointUri</maml:name>
				<maml:description>
					<maml:para>The AzureADAuthorizationEndpointUri parameter specifies the Microsoft Entra Authorization endpoint that can issue OAuth2 access tokens. The following PowerShell environments and related values are supported:</maml:para>
					<maml:para>- Security &amp; Compliance PowerShell in Microsoft 365 or Microsoft 365 GCC: Don't use this parameter. The required value is `https://login.microsoftonline.com/common`, but that's also the default value, so you don't need to use this parameter.</maml:para>
					<maml:para>- Security &amp; Compliance PowerShell in Office 365 operated by 21Vianet: `https://login.chinacloudapi.cn/common`</maml:para>
					<maml:para>- Security &amp; Compliance PowerShell in Microsoft GCC High or Microsoft DoD: `https://login.microsoftonline.us/common`</maml:para>
					<maml:para></maml:para>
					<maml:para>If you use the UserPrincipalName parameter, you don't need to use the AzureADAuthorizationEndpointUri parameter for MFA or federated users in environments that normally require it (UserPrincipalName or AzureADAuthorizationEndpointUri is required; OK to use both).</maml:para>
				</maml:description>
				<command:parameterValue required="true" variableLength="false">String</command:parameterValue>
				<dev:type>
					<maml:name>String</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>None</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="2" aliases="none">
				<maml:name>DelegatedOrganization</maml:name>
				<maml:description>
					<maml:para>The DelegatedOrganization parameter specifies the customer organization that you want to manage (for example, contosoelectronics.onmicrosoft.com). This parameter works only if the customer organization has agreed to your delegated management via the CSP program.</maml:para>
					<maml:para>After you successfully authenticate, the cmdlets in this session are mapped to the customer organization, and all operations in this session are done on the customer organization. Notes :</maml:para>
					<maml:para>- Use the primary .onmicrosoft.com domain of the delegated organization for the value of this parameter.</maml:para>
					<maml:para>- You must use the AzureADAuthorizationEndpointUri parameter with this parameter.</maml:para>
				</maml:description>
				<command:parameterValue required="true" variableLength="false">String</command:parameterValue>
				<dev:type>
					<maml:name>String</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>None</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="3" aliases="none">
				<maml:name>PSSessionOption</maml:name>
				<maml:description>
					<maml:para>Note : This parameter doesn't work in REST API connections.</maml:para>
					<maml:para>The PSSessionOption parameter specifies the remote PowerShell session options to use in your connection to Security &amp; Compliance PowerShell. This parameter works only if you also use the UseRPSSession switch in the same command.</maml:para>
					<maml:para>Store the output of the New-PSSessionOption (https://learn.microsoft.com/powershell/module/microsoft.powershell.core/new-pssessionoption) command in a variable (for example, `$PSOptions = New-PSSessionOption &lt;Settings&gt;`), and use the variable name as the value for this parameter (for example, `$PSOptions`).</maml:para>
				</maml:description>
				<command:parameterValue required="true" variableLength="false">PSSessionOption</command:parameterValue>
				<dev:type>
					<maml:name>PSSessionOption</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>None</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="4" aliases="none">
				<maml:name>Prefix</maml:name>
				<maml:description>
					<maml:para>The Prefix parameter specifies a text value to add to the names of Security &amp; Compliance PowerShell cmdlets when you connect. For example, Get-ComplianceCase becomes Get-ContosoComplianceCase when you use the value Contoso for this parameter.</maml:para>
					<maml:para>- The Prefix value can't contain spaces or special characters like underscores or asterisks.</maml:para>
					<maml:para>- You can't use the Prefix value EXO. That value is reserved for the nine exclusive Get-EXO\ * cmdlets that are built into the module.</maml:para>
					<maml:para>- The Prefix parameter affects only imported Security &amp; Compliance cmdlet names. It doesn't affect the names of cmdlets that are built into the module (for example, Disconnect-ExchangeOnline).</maml:para>
				</maml:description>
				<command:parameterValue required="true" variableLength="false">String</command:parameterValue>
				<dev:type>
					<maml:name>String</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>None</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="5" aliases="none">
				<maml:name>CommandName</maml:name>
				<maml:description>
					<maml:para>The CommandName parameter specifies the comma separated list of commands to import into the session. Use this parameter for applications or scripts that use a specific set of cmdlets. Reducing the number of cmdlets in the session helps improve performance and reduces the memory footprint of the application or script.</maml:para>
				</maml:description>
				<command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
				<dev:type>
					<maml:name>String[]</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>None</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="6" aliases="none">
				<maml:name>FormatTypeName</maml:name>
				<maml:description>
					<maml:para>The FormatTypeName parameter specifies the output format of the cmdlet.</maml:para>
				</maml:description>
				<command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
				<dev:type>
					<maml:name>String[]</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>None</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
				<maml:name>AccessToken</maml:name>
				<maml:description>
					<maml:para>Note : This parameter is available in version 3.8.0-Preview1 or later of the module.</maml:para>
					<maml:para>The AccessToken parameter specifies the OAuth JSON Web Token (JWT) that's used to connect to Security and Compliance PowerShell.</maml:para>
					<maml:para>Depending on the type of access token, you need to use this parameter with the Organization, DelegatedOrganization, or UserPrincipalName parameters.</maml:para>
				</maml:description>
				<command:parameterValue required="true" variableLength="false">String</command:parameterValue>
				<dev:type>
					<maml:name>String</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>None</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
				<maml:name>AppId</maml:name>
				<maml:description>
					<maml:para>The AppId parameter specifies the application ID of the service principal that's used in certificate based authentication (CBA). A valid value is the GUID of the application ID (service principal). For example, `36ee4c6c-0812-40a2-b820-b22ebd02bce3`.</maml:para>
					<maml:para>For more information, see App-only authentication for unattended scripts in the Exchange Online PowerShell module (https://aka.ms/exo-cba).</maml:para>
				</maml:description>
				<command:parameterValue required="true" variableLength="false">String</command:parameterValue>
				<dev:type>
					<maml:name>String</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>None</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
				<maml:name>BypassMailboxAnchoring</maml:name>
				<maml:description>
					<maml:para>The BypassMailboxAnchoring switch bypasses the use of the mailbox anchoring hint. You don't need to specify a value with this switch.</maml:para>
				</maml:description>
				<command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
				<dev:type>
					<maml:name>SwitchParameter</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>False</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
				<maml:name>Certificate</maml:name>
				<maml:description>
					<maml:para>The Certificate parameter specifies the certificate that's used for certificate-based authentication (CBA). A valid value is the X509Certificate2 object value of the certificate.</maml:para>
					<maml:para>Don't use this parameter with the CertificateFilePath or CertificateThumbprint parameters.</maml:para>
					<maml:para>For more information about CBA, see App-only authentication for unattended scripts in the Exchange Online PowerShell module (https://aka.ms/exo-cba).</maml:para>
				</maml:description>
				<command:parameterValue required="true" variableLength="false">X509Certificate2</command:parameterValue>
				<dev:type>
					<maml:name>X509Certificate2</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>None</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
				<maml:name>CertificateFilePath</maml:name>
				<maml:description>
					<maml:para>The CertificateFilePath parameter specifies the certificate that's used for CBA. A valid value is the complete public path to the certificate file. Use the CertificatePassword parameter with this parameter.</maml:para>
					<maml:para>Don't use this parameter with the Certificate or CertificateThumbprint parameters.</maml:para>
					<maml:para>For more information about CBA, see App-only authentication for unattended scripts in the Exchange Online PowerShell module (https://aka.ms/exo-cba).</maml:para>
				</maml:description>
				<command:parameterValue required="true" variableLength="false">String</command:parameterValue>
				<dev:type>
					<maml:name>String</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>None</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
				<maml:name>CertificatePassword</maml:name>
				<maml:description>
					<maml:para>The CertificatePassword parameter specifies the password that's required to open the certificate file when you use the CertificateFilePath parameter to identify the certificate that's used for CBA.</maml:para>
					<maml:para>You can use the following methods as a value for this parameter:</maml:para>
					<maml:para>- `(ConvertTo-SecureString -String '&lt;password&gt;' -AsPlainText -Force)`.</maml:para>
					<maml:para>- Before you run this command, store the password as a variable (for example, `$password = Read-Host "Enter password" -AsSecureString`), and then use the variable (`$password`) for the value.</maml:para>
					<maml:para>- `(Get-Credential).password` to be prompted to enter the password securely when you run this command.</maml:para>
					<maml:para></maml:para>
					<maml:para>For more information about CBA, see App-only authentication for unattended scripts in the Exchange Online PowerShell module (https://aka.ms/exo-cba). Note : Using a ConvertTo-SecureString command to store the password of the certificate locally defeats the purpose of a secure connection method for automation scenarios. Using a Get-Credential command to prompt you for the password of the certificate securely isn't ideal for automation scenarios. In other words, there's really no automated and secure way to connect using a local certificate.</maml:para>
				</maml:description>
				<command:parameterValue required="true" variableLength="false">SecureString</command:parameterValue>
				<dev:type>
					<maml:name>SecureString</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>None</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
				<maml:name>CertificateThumbprint</maml:name>
				<maml:description>
					<maml:para>The CertificateThumbprint parameter specifies the certificate that's used for CBA. A valid value is the thumbprint value of the certificate. For example, `83213AEAC56D61C97AEE5C1528F4AC5EBA7321C1`.</maml:para>
					<maml:para>Don't use this parameter with the Certificate or CertificateFilePath parameters. Note : The CertificateThumbprint parameter is supported only in Microsoft Windows.</maml:para>
					<maml:para>For more information about CBA, see App-only authentication for unattended scripts in the Exchange Online PowerShell module (https://aka.ms/exo-cba).</maml:para>
				</maml:description>
				<command:parameterValue required="true" variableLength="false">String</command:parameterValue>
				<dev:type>
					<maml:name>String</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>None</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
				<maml:name>Credential</maml:name>
				<maml:description>
					<maml:para>The Credential parameter specifies the username and password that's used to connect to Exchange Online PowerShell. Typically, you use this parameter in scripts or when you need to provide different credentials that have the required permissions. Don't use this parameter for accounts that use multi-factor authentication (MFA).</maml:para>
					<maml:para>Before you run the Connect-IPPSSession command, store the username and password in a variable (for example, `$UserCredential = Get-Credential`). Then, use the variable name (`$UserCredential`) for this parameter.</maml:para>
					<maml:para>After the Connect-IPPSSession command is complete, the password key in the variable is emptied.</maml:para>
				</maml:description>
				<command:parameterValue required="true" variableLength="false">PSCredential</command:parameterValue>
				<dev:type>
					<maml:name>PSCredential</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>None</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
				<maml:name>Organization</maml:name>
				<maml:description>
					<maml:para>The Organization parameter specifies the organization when you connect using CBA. You must use the primary .onmicrosoft.com domain of the organization for the value of this parameter.</maml:para>
					<maml:para>For more information about CBA, see App-only authentication for unattended scripts in the Exchange Online PowerShell module (https://aka.ms/exo-cba).</maml:para>
				</maml:description>
				<command:parameterValue required="true" variableLength="false">String</command:parameterValue>
				<dev:type>
					<maml:name>String</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>None</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
				<maml:name>UserPrincipalName</maml:name>
				<maml:description>
					<maml:para>The UserPrincipalName parameter specifies the account that you want to use to connect (for example, <EMAIL>). Using this parameter allows you to skip entering a username in the modern authentication credentials prompt (you're prompted to enter a password).</maml:para>
					<maml:para>If you use the UserPrincipalName parameter, you don't need to use the AzureADAuthorizationEndpointUri parameter for MFA or federated users in environments that normally require it (UserPrincipalName or AzureADAuthorizationEndpointUri is required; OK to use both).</maml:para>
				</maml:description>
				<command:parameterValue required="true" variableLength="false">String</command:parameterValue>
				<dev:type>
					<maml:name>String</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>None</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
				<maml:name>UseRPSSession</maml:name>
				<maml:description>
					<maml:para>This parameter is available in version 3.2.0 or later of the module. Note : Remote PowerShell connections to Security &amp; Compliance are deprecated. For more information, see Deprecation of Remote PowerShell in Security and Compliance PowerShell (https://techcommunity.microsoft.com/t5/exchange-team-blog/deprecation-of-remote-powershell-rps-protocol-in-security-and/ba-p/3815432).</maml:para>
					<maml:para>The UseRPSSession switch allows you to connect to Security &amp; Compliance PowerShell using traditional remote PowerShell access to all cmdlets. You don't need to specify a value with this switch.</maml:para>
					<maml:para>This switch requires that Basic authentication is enabled in WinRM on the local computer. For more information, see Turn on Basic authentication in WinRM (https://aka.ms/exov3-module#turn-on-basic-authentication-in-winrm).</maml:para>
					<maml:para>If you don't use this switch, Basic authentication in WinRM is not required.</maml:para>
				</maml:description>
				<command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
				<dev:type>
					<maml:name>SwitchParameter</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>False</dev:defaultValue>
			</command:parameter>
		</command:parameters>
		<command:inputTypes />
		<command:returnValues />
		<maml:alertSet>
			<maml:alert>
				<maml:para></maml:para>
			</maml:alert>
		</maml:alertSet>
		<command:examples>
			<command:example>
				<maml:title>-------------------------- Example 1 --------------------------</maml:title>
				<dev:code>Connect-IPPSSession -UserPrincipalName <EMAIL></dev:code>
				<dev:remarks>
					<maml:para>This example connects to Security &amp; Compliance PowerShell using the specified account and modern authentication, with or without MFA. In v3.2.0 or later of the module, we're connecting in REST API mode, so Basic authentication in WinRM isn't required on the local computer.</maml:para>
				</dev:remarks>
			</command:example>
			<command:example>
				<maml:title>-------------------------- Example 2 --------------------------</maml:title>
				<dev:code>Connect-IPPSSession -UserPrincipalName <EMAIL> -UseRPSSession</dev:code>
				<dev:remarks>
					<maml:para>This example connects to Security &amp; Compliance PowerShell using the specified account and modern authentication, with or without MFA. In v3.2.0 or later of the module, we're connecting in remote PowerShell mode, so Basic authentication in WinRM is required on the local computer.</maml:para>
				</dev:remarks>
			</command:example>
			<command:example>
				<maml:title>-------------------------- Example 3 --------------------------</maml:title>
				<dev:code>Connect-IPPSSession -AppId &lt;%App_id%&gt; -CertificateThumbprint &lt;%Thumbprint string of certificate%&gt; -Organization "contoso.onmicrosoft.com"</dev:code>
				<dev:remarks>
					<maml:para>This example connects to Security &amp; Compliance PowerShell in an unattended scripting scenario using a certificate thumbprint.</maml:para>
				</dev:remarks>
			</command:example>
			<command:example>
				<maml:title>-------------------------- Example 4 --------------------------</maml:title>
				<dev:code>Connect-IPPSSession -AppId &lt;%App_id%&gt; -Certificate &lt;%X509Certificate2 object%&gt; -Organization "contoso.onmicrosoft.com"</dev:code>
				<dev:remarks>
					<maml:para>This example connects to Security &amp; Compliance PowerShell in an unattended scripting scenario using a certificate file. This method is best suited for scenarios where the certificate is stored in remote machines and fetched at runtime. For example, the certificate is stored in the Azure Key Vault.</maml:para>
				</dev:remarks>
			</command:example>
		</command:examples>
		<command:relatedLinks>
			<maml:navigationLink>
				<maml:linkText>Online Version:</maml:linkText>
				<maml:uri>https://learn.microsoft.com/powershell/module/exchange/connect-ippssession</maml:uri>
			</maml:navigationLink>
		</command:relatedLinks>
	</command:command>
	<command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
		<command:details>
			<command:name>Disconnect-ExchangeOnline</command:name>
			<command:verb>Disconnect</command:verb>
			<command:noun>ExchangeOnline</command:noun>
			<maml:description>
				<maml:para>This cmdlet is available only in the Exchange Online PowerShell module. For more information, see About the Exchange Online PowerShell module (https://aka.ms/exov3-module).</maml:para>
				<maml:para>Use the Disconnect-ExchangeOnline cmdlet in the Exchange Online PowerShell module to disconnect the connections that you created using the Connect-ExchangeOnline or Connect-IPPSSession cmdlets.</maml:para>
				<maml:para>For information about the parameter sets in the Syntax section below, see Exchange cmdlet syntax (https://learn.microsoft.com/powershell/exchange/exchange-cmdlet-syntax).</maml:para>
			</maml:description>
		</command:details>
		<maml:description>
			<maml:para>This cmdlet is the counterpart to the Connect-ExchangeOnline and Connect-IPPSSession cmdlets.</maml:para>
			<maml:para>This cmdlet disconnects any connections and clears the cache. After a successful disconnect, you can't successfully run any cmdlets for your organization.</maml:para>
			<maml:para>Disconnect commands will likely fail if the profile path of the account that you used to connect contains special PowerShell characters (for example, `$`). The workaround is to connect using a different account that doesn't have special characters in the profile path.</maml:para>
		</maml:description>
		<command:syntax>
			<command:syntaxItem>
				<maml:name>Disconnect-ExchangeOnline</maml:name>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="cf">
					<maml:name>Confirm</maml:name>
					<maml:description>
						<maml:para>The Confirm switch specifies whether to show or hide the confirmation prompt. How this switch affects the cmdlet depends on if the cmdlet requires confirmation before proceeding.</maml:para>
						<maml:para>- Destructive cmdlets (for example, Remove-* cmdlets) have a built-in pause that forces you to acknowledge the command before proceeding. For these cmdlets, you can skip the confirmation prompt by using this exact syntax: `-Confirm:$false`.</maml:para>
						<maml:para>- Most other cmdlets (for example, New-\ and Set-\ cmdlets) don't have a built-in pause. For these cmdlets, specifying the Confirm switch without a value introduces a pause that forces you acknowledge the command before proceeding.</maml:para>
					</maml:description>
					<dev:type>
						<maml:name>SwitchParameter</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>False</dev:defaultValue>
				</command:parameter>
				<command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
					<maml:name>ConnectionId</maml:name>
					<maml:description>
						<maml:para>Note : This parameter is available in version 3.2.0 or later of the module.</maml:para>
						<maml:para>The ConnectionId parameter specifies the REST API connections to disconnect by ConnectionId. ConnectionId is a GUID value in the output of the Get-ConnectionInformation cmdlet that uniquely identifies a connection, even if you have multiple connections open. You can specify multiple ConnectionId values separated by commas.</maml:para>
						<maml:para>Don't use this parameter with the ModulePrefix parameter.</maml:para>
					</maml:description>
					<command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
					<dev:type>
						<maml:name>String[]</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>None</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="wi">
					<maml:name>WhatIf</maml:name>
					<maml:description>
						<maml:para>The WhatIf switch simulates the actions of the command. You can use this switch to view the changes that would occur without actually applying those changes. You don't need to specify a value with this switch.</maml:para>
					</maml:description>
					<dev:type>
						<maml:name>SwitchParameter</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>False</dev:defaultValue>
				</command:parameter>
			</command:syntaxItem>
			<command:syntaxItem>
				<maml:name>Disconnect-ExchangeOnline</maml:name>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="cf">
					<maml:name>Confirm</maml:name>
					<maml:description>
						<maml:para>The Confirm switch specifies whether to show or hide the confirmation prompt. How this switch affects the cmdlet depends on if the cmdlet requires confirmation before proceeding.</maml:para>
						<maml:para>- Destructive cmdlets (for example, Remove-* cmdlets) have a built-in pause that forces you to acknowledge the command before proceeding. For these cmdlets, you can skip the confirmation prompt by using this exact syntax: `-Confirm:$false`.</maml:para>
						<maml:para>- Most other cmdlets (for example, New-\ and Set-\ cmdlets) don't have a built-in pause. For these cmdlets, specifying the Confirm switch without a value introduces a pause that forces you acknowledge the command before proceeding.</maml:para>
					</maml:description>
					<dev:type>
						<maml:name>SwitchParameter</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>False</dev:defaultValue>
				</command:parameter>
				<command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
					<maml:name>ModulePrefix</maml:name>
					<maml:description>
						<maml:para>Note : This parameter is available in version 3.2.0 or later of the module.</maml:para>
						<maml:para>The ModulePrefix parameter specifies the REST API connections to disconnect by ModulePrefix. When you use the Prefix parameter with the Connect-ExchangeOnline cmdlet, the specified text is added to the names of all Exchange Online cmdlets (for example, Get-InboundConnector becomes Get-ContosoInboundConnector). The ModulePrefix value is visible in the output of the Get-ConnectionInformation cmdlet. You can specify multiple ModulePrefix values separated by commas.</maml:para>
						<maml:para>Don't use this parameter with the ConnectionId parameter.</maml:para>
					</maml:description>
					<command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
					<dev:type>
						<maml:name>String[]</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>None</dev:defaultValue>
				</command:parameter>
				<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="wi">
					<maml:name>WhatIf</maml:name>
					<maml:description>
						<maml:para>The WhatIf switch simulates the actions of the command. You can use this switch to view the changes that would occur without actually applying those changes. You don't need to specify a value with this switch.</maml:para>
					</maml:description>
					<dev:type>
						<maml:name>SwitchParameter</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>False</dev:defaultValue>
				</command:parameter>
			</command:syntaxItem>
		</command:syntax>
		<command:parameters>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="cf">
				<maml:name>Confirm</maml:name>
				<maml:description>
					<maml:para>The Confirm switch specifies whether to show or hide the confirmation prompt. How this switch affects the cmdlet depends on if the cmdlet requires confirmation before proceeding.</maml:para>
					<maml:para>- Destructive cmdlets (for example, Remove-* cmdlets) have a built-in pause that forces you to acknowledge the command before proceeding. For these cmdlets, you can skip the confirmation prompt by using this exact syntax: `-Confirm:$false`.</maml:para>
					<maml:para>- Most other cmdlets (for example, New-\ and Set-\ cmdlets) don't have a built-in pause. For these cmdlets, specifying the Confirm switch without a value introduces a pause that forces you acknowledge the command before proceeding.</maml:para>
				</maml:description>
				<command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
				<dev:type>
					<maml:name>SwitchParameter</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>False</dev:defaultValue>
			</command:parameter>
			<command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
				<maml:name>ConnectionId</maml:name>
				<maml:description>
					<maml:para>Note : This parameter is available in version 3.2.0 or later of the module.</maml:para>
					<maml:para>The ConnectionId parameter specifies the REST API connections to disconnect by ConnectionId. ConnectionId is a GUID value in the output of the Get-ConnectionInformation cmdlet that uniquely identifies a connection, even if you have multiple connections open. You can specify multiple ConnectionId values separated by commas.</maml:para>
					<maml:para>Don't use this parameter with the ModulePrefix parameter.</maml:para>
				</maml:description>
				<command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
				<dev:type>
					<maml:name>String[]</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>None</dev:defaultValue>
			</command:parameter>
			<command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
				<maml:name>ModulePrefix</maml:name>
				<maml:description>
					<maml:para>Note : This parameter is available in version 3.2.0 or later of the module.</maml:para>
					<maml:para>The ModulePrefix parameter specifies the REST API connections to disconnect by ModulePrefix. When you use the Prefix parameter with the Connect-ExchangeOnline cmdlet, the specified text is added to the names of all Exchange Online cmdlets (for example, Get-InboundConnector becomes Get-ContosoInboundConnector). The ModulePrefix value is visible in the output of the Get-ConnectionInformation cmdlet. You can specify multiple ModulePrefix values separated by commas.</maml:para>
					<maml:para>Don't use this parameter with the ConnectionId parameter.</maml:para>
				</maml:description>
				<command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
				<dev:type>
					<maml:name>String[]</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>None</dev:defaultValue>
			</command:parameter>
			<command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="wi">
				<maml:name>WhatIf</maml:name>
				<maml:description>
					<maml:para>The WhatIf switch simulates the actions of the command. You can use this switch to view the changes that would occur without actually applying those changes. You don't need to specify a value with this switch.</maml:para>
				</maml:description>
				<command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
				<dev:type>
					<maml:name>SwitchParameter</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>False</dev:defaultValue>
			</command:parameter>
		</command:parameters>
		<command:inputTypes />
		<command:returnValues />
		<maml:alertSet>
			<maml:alert>
				<maml:para></maml:para>
			</maml:alert>
		</maml:alertSet>
		<command:examples>
			<command:example>
				<maml:title>-------------------------- Example 1 --------------------------</maml:title>
				<dev:code>Disconnect-ExchangeOnline</dev:code>
				<dev:remarks>
					<maml:para>This example asks for confirmation before disconnecting from Exchange Online PowerShell or Security &amp; Compliance PowerShell.</maml:para>
				</dev:remarks>
			</command:example>
			<command:example>
				<maml:title>-------------------------- Example 2 --------------------------</maml:title>
				<dev:code>Disconnect-ExchangeOnline -Confirm:$false</dev:code>
				<dev:remarks>
					<maml:para>This example silently disconnects from Exchange Online PowerShell or Security &amp; Compliance PowerShell without a confirmation prompt or any notification text.</maml:para>
				</dev:remarks>
			</command:example>
			<command:example>
				<maml:title>-------------------------- Example 3 --------------------------</maml:title>
				<dev:code>Disconnect-ExchangeOnline -ConnectionId 1a9e45e8-e7ec-498f-9ac3-0504e987fa85</dev:code>
				<dev:remarks>
					<maml:para>This example disconnects the REST-based Exchange Online PowerShell connection with the specified ConnectionId value. Any other remote PowerShell connections to Exchange Online PowerShell or Security &amp; Compliance PowerShell in the same Windows PowerShell window are also disconnected.</maml:para>
				</dev:remarks>
			</command:example>
			<command:example>
				<maml:title>-------------------------- Example 4 --------------------------</maml:title>
				<dev:code>Disconnect-ExchangeOnline -ModulePrefix Contoso,Fabrikam</dev:code>
				<dev:remarks>
					<maml:para>This example disconnects the REST-based Exchange Online PowerShell connections that are using the specified prefix values. Any other remote PowerShell connections to Exchange Online PowerShell or Security &amp; Compliance PowerShell in the same Windows PowerShell window are also disconnected.</maml:para>
				</dev:remarks>
			</command:example>
		</command:examples>
		<command:relatedLinks>
			<maml:navigationLink>
				<maml:linkText>Online Version:</maml:linkText>
				<maml:uri>https://learn.microsoft.com/powershell/module/exchange/disconnect-exchangeonline</maml:uri>
			</maml:navigationLink>
		</command:relatedLinks>
	</command:command>
</helpItems>