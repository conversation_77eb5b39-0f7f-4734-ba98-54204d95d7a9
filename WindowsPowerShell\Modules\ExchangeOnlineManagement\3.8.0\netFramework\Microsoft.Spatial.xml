<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Spatial</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Spatial.ActionOnDispose">
            <summary>
            This class is responsible for executing an action the first time dispose is called on it.
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.ActionOnDispose.action">
            <summary>The action to be executed on dispose</summary>
        </member>
        <member name="M:Microsoft.Spatial.ActionOnDispose.#ctor(System.Action)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Spatial.ActionOnDispose" /> class.
            </summary>
            <param name="action">the action to be execute on dispose</param>
        </member>
        <member name="M:Microsoft.Spatial.ActionOnDispose.Dispose">
            <summary>
            The dispose method of the IDisposable interface
            </summary>
        </member>
        <member name="T:Microsoft.Spatial.CompositeKey`2">
            <summary>
            A key consisting of multiple fields
            </summary>
            <typeparam name="T1">The type of the first field.</typeparam>
            <typeparam name="T2">The type of the second field.</typeparam>
        </member>
        <member name="F:Microsoft.Spatial.CompositeKey`2.first">
            <summary>
            The first field
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.CompositeKey`2.second">
            <summary>
            The second field
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.CompositeKey`2.#ctor(`0,`1)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Spatial.CompositeKey`2"/> class.
            </summary>
            <param name="first">The first.</param>
            <param name="second">The second.</param>
        </member>
        <member name="M:Microsoft.Spatial.CompositeKey`2.op_Equality(Microsoft.Spatial.CompositeKey{`0,`1},Microsoft.Spatial.CompositeKey{`0,`1})">
            <summary>
            Implements the operator ==.
            </summary>
            <param name="left">The left.</param>
            <param name="right">The right.</param>
            <returns>
            The result of the operator.
            </returns>
        </member>
        <member name="M:Microsoft.Spatial.CompositeKey`2.op_Inequality(Microsoft.Spatial.CompositeKey{`0,`1},Microsoft.Spatial.CompositeKey{`0,`1})">
            <summary>
            Implements the operator !=.
            </summary>
            <param name="left">The left.</param>
            <param name="right">The right.</param>
            <returns>
            The result of the operator.
            </returns>
        </member>
        <member name="M:Microsoft.Spatial.CompositeKey`2.Equals(Microsoft.Spatial.CompositeKey{`0,`1})">
            <summary>
            Indicates whether the current object is equal to another object of the same type.
            </summary>
            <param name="other">An object to compare with this object.</param>
            <returns>
            true if the current object is equal to the <paramref name="other"/> parameter; otherwise, false.
            </returns>
        </member>
        <member name="M:Microsoft.Spatial.CompositeKey`2.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object"/> is equal to this instance.
            </summary>
            <param name="obj">The <see cref="T:System.Object"/> to compare with this instance.</param>
            <returns>
              <c>true</c> if the specified <see cref="T:System.Object"/> is equal to this instance; otherwise, <c>false</c>.
            </returns>
            <exception cref="T:System.NullReferenceException">The <paramref name="obj"/> parameter is null.</exception>
        </member>
        <member name="M:Microsoft.Spatial.CompositeKey`2.GetHashCode">
            <summary>
            Returns a hash code for this instance.
            </summary>
            <returns>
            A hash code for this instance, suitable for use in hashing algorithms and data structures like a hash table.
            </returns>
        </member>
        <member name="T:Microsoft.Spatial.CoordinateSystem">
            <summary>
              Coordinate System Reference
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.CoordinateSystem.DefaultGeometry">
            <summary>
              Default Geometry Reference
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.CoordinateSystem.DefaultGeography">
            <summary>
              Default Geography Reference (SRID 4326, WGS84)
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.CoordinateSystem.References">
            <summary>
              List of registered references
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.CoordinateSystem.referencesLock">
            <summary>
              A lock object for the References static dict
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.CoordinateSystem.topology">
            <summary>
              The shape of the space that this coordinate system measures.
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.CoordinateSystem.#cctor">
            <summary>Initializes a static instance of the <see cref="T:Microsoft.Spatial.CoordinateSystem" /> class.</summary>
        </member>
        <member name="M:Microsoft.Spatial.CoordinateSystem.#ctor(System.Int32,System.String,Microsoft.Spatial.CoordinateSystem.Topology)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.Spatial.CoordinateSystem" /> class.</summary>
            <param name = "epsgId">The coordinate system ID, according to the EPSG</param>
            <param name = "name">The Name of the system</param>
            <param name = "topology">The topology of this coordinate system</param>
        </member>
        <member name="T:Microsoft.Spatial.CoordinateSystem.Topology">
            <summary>
              The shapes of the spaces measured by coordinate systems.
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.CoordinateSystem.Topology.Geography">
            <summary>
              Ellipsoidal coordinates
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.CoordinateSystem.Topology.Geometry">
            <summary>
              Planar coordinates
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.CoordinateSystem.EpsgId">
            <summary>Gets the coordinate system ID according to the EPSG, or NULL if this is not an EPSG coordinate system.</summary>
            <returns>The coordinate system ID according to the EPSG.</returns>
        </member>
        <member name="P:Microsoft.Spatial.CoordinateSystem.Id">
            <summary>Gets the coordinate system Id, no matter what scheme is used.</summary>
            <returns>The coordinate system Id.</returns>
        </member>
        <member name="P:Microsoft.Spatial.CoordinateSystem.Name">
            <summary>Gets the Name of the Reference.</summary>
            <returns>The Name of the Reference.</returns>
        </member>
        <member name="M:Microsoft.Spatial.CoordinateSystem.Geography(System.Nullable{System.Int32})">
            <summary>Gets or creates a Geography coordinate system with the ID, or the default if null is given.</summary>
            <returns>The coordinate system.</returns>
            <param name="epsgId">The coordinate system id, according to the EPSG. Null indicates the default should be returned.</param>
        </member>
        <member name="M:Microsoft.Spatial.CoordinateSystem.Geometry(System.Nullable{System.Int32})">
            <summary>Gets or creates a Geometry coordinate system with the ID, or the default if null is given.</summary>
            <returns>The coordinate system.</returns>
            <param name="epsgId">The coordinate system id, according to the EPSG. Null indicates the default should be returned.</param>
        </member>
        <member name="M:Microsoft.Spatial.CoordinateSystem.ToString">
            <summary>Displays the coordinate system for debugging.</summary>
            <returns>The coordinate system, for debugging.</returns>
        </member>
        <member name="M:Microsoft.Spatial.CoordinateSystem.ToWktId">
            <summary>Displays a string that can be used with extended WKT.</summary>
            <returns>String representation in the form of SRID=#;</returns>
        </member>
        <member name="M:Microsoft.Spatial.CoordinateSystem.Equals(System.Object)">
            <summary>Indicates the Equals overload.</summary>
            <returns>True if equal.</returns>
            <param name="obj">The other CoordinateSystem.</param>
        </member>
        <member name="M:Microsoft.Spatial.CoordinateSystem.Equals(Microsoft.Spatial.CoordinateSystem)">
            <summary>Indicates the Equals overload.</summary>
            <returns>True if equal.</returns>
            <param name="other">The other CoordinateSystem.</param>
        </member>
        <member name="M:Microsoft.Spatial.CoordinateSystem.GetHashCode">
            <summary>Returns a hash code for this instance.</summary>
            <returns>A hash code for this instance, suitable for use in hashing algorithms and data structures like a hash table.</returns>
        </member>
        <member name="M:Microsoft.Spatial.CoordinateSystem.TopologyIs(Microsoft.Spatial.CoordinateSystem.Topology)">
            <summary>
              For tests only. Identifies whether the coordinate system is of the designated topology.
            </summary>
            <param name = "expected">The expected topology.</param>
            <returns>True if this coordinate system is of the expected topology.</returns>
        </member>
        <member name="M:Microsoft.Spatial.CoordinateSystem.GetOrCreate(System.Int32,Microsoft.Spatial.CoordinateSystem.Topology)">
            <summary>
              Get or create a CoordinateSystem with ID
            </summary>
            <param name = "epsgId">The SRID</param>
            <param name = "topology">The topology.</param>
            <returns>
              A CoordinateSystem object
            </returns>
        </member>
        <member name="M:Microsoft.Spatial.CoordinateSystem.AddRef(Microsoft.Spatial.CoordinateSystem)">
            <summary>
              Remember this coordinate system in the references dictionary.
            </summary>
            <param name = "coords">The coords.</param>
        </member>
        <member name="M:Microsoft.Spatial.CoordinateSystem.KeyFor(System.Int32,Microsoft.Spatial.CoordinateSystem.Topology)">
            <summary>
              Gets the key for a coordinate system
            </summary>
            <param name = "epsgId">ID</param>
            <param name = "topology">topology</param>
            <returns>The key to use with the references dict.</returns>
        </member>
        <member name="T:Microsoft.Spatial.DataServicesSpatialImplementation">
            <summary>
            Class responsible for knowing how to create the Geography and Geometry builders for
            the data services implementation of Spatial types
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.DataServicesSpatialImplementation.Operations">
            <summary>
            Property used to register Spatial operations implementation.
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.DataServicesSpatialImplementation.CreateBuilder">
            <summary>
            Creates a SpatialBuilder for this implementation
            </summary>
            <returns>
            The SpatialBuilder created.
            </returns>
        </member>
        <member name="M:Microsoft.Spatial.DataServicesSpatialImplementation.CreateGmlFormatter">
            <summary>
            Creates a GmlFormatter for this implementation
            </summary>
            <returns>The GmlFormatter created.</returns>
        </member>
        <member name="M:Microsoft.Spatial.DataServicesSpatialImplementation.CreateGeoJsonObjectFormatter">
            <summary>
            Creates a GeoJsonObjectFormatter for this implementation
            </summary>
            <returns>The GeoJsonObjectFormatter created.</returns>
        </member>
        <member name="M:Microsoft.Spatial.DataServicesSpatialImplementation.CreateWellKnownTextSqlFormatter">
            <summary>
            Creates a WellKnownTextSqlFormatter for this implementation
            </summary>
            <returns>The WellKnownTextSqlFormatter created.</returns>
        </member>
        <member name="M:Microsoft.Spatial.DataServicesSpatialImplementation.CreateWellKnownTextSqlFormatter(System.Boolean)">
            <summary>
            Creates a WellKnownTextSqlFormatter for this implementation
            </summary>
            <param name="allowOnlyTwoDimensions">Controls the writing and reading of the Z and M dimension</param>
            <returns>
            The WellKnownTextSqlFormatter created.
            </returns>
        </member>
        <member name="M:Microsoft.Spatial.DataServicesSpatialImplementation.CreateValidator">
            <summary>
            Creates a SpatialValidator for this implementation
            </summary>
            <returns>The SpatialValidator created.</returns>
        </member>
        <member name="T:Microsoft.Spatial.DebugUtils">
            <summary>
            Dummy class for code that is shared with ODataLib.
            The ODataLib version of this class has an implementation, but this version is just provided
            so that we don't have to conditionally compile all references to it in the shared code.
            Since it is debug-only anyway, there is no harm in leaving this no-op version so that the shared code is cleaner.
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.DebugUtils.CheckNoExternalCallers">
            <summary>
            Dummy method to allow shared code to compile.
            </summary>
        </member>
        <member name="T:Microsoft.Spatial.DrawBoth">
            <summary>
            Base class to create a unified set of handlers for Geometry and Geography
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.DrawBoth.GeographyPipeline">
            <summary>
            Gets the draw geography.
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.DrawBoth.GeometryPipeline">
            <summary>
            Gets the draw geometry.
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.DrawBoth.op_Implicit(Microsoft.Spatial.DrawBoth)~Microsoft.Spatial.SpatialPipeline">
            <summary>
            Performs an implicit conversion from <see cref="T:Microsoft.Spatial.DrawBoth"/> to <see cref="T:Microsoft.Spatial.SpatialPipeline"/>.
            </summary>
            <param name="both">The instance to convert.</param>
            <returns>
            The result of the conversion.
            </returns>
        </member>
        <member name="M:Microsoft.Spatial.DrawBoth.OnLineTo(Microsoft.Spatial.GeographyPosition)">
            <summary>
            Draw a point in the specified coordinate
            </summary>
            <param name="position">Next position</param>
            <returns>The position to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Spatial.DrawBoth.OnLineTo(Microsoft.Spatial.GeometryPosition)">
            <summary>
            Draw a point in the specified coordinate
            </summary>
            <param name="position">Next position</param>
            <returns>The position to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Spatial.DrawBoth.OnBeginFigure(Microsoft.Spatial.GeographyPosition)">
            <summary>
            Begin drawing a figure
            </summary>
            <param name="position">Next position</param>
            <returns>The position to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Spatial.DrawBoth.OnBeginFigure(Microsoft.Spatial.GeometryPosition)">
            <summary>
            Begin drawing a figure
            </summary>
            <param name="position">Next position</param>
            <returns>The position to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Spatial.DrawBoth.OnBeginGeography(Microsoft.Spatial.SpatialType)">
            <summary>
            Begin drawing a spatial object
            </summary>
            <param name="type">The spatial type of the object</param>
            <returns>The type to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Spatial.DrawBoth.OnBeginGeometry(Microsoft.Spatial.SpatialType)">
            <summary>
            Begin drawing a spatial object
            </summary>
            <param name="type">The spatial type of the object</param>
            <returns>The type to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Spatial.DrawBoth.OnEndFigure">
            <summary>
            Ends the current figure
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.DrawBoth.OnEndGeography">
            <summary>
            Ends the current spatial object
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.DrawBoth.OnEndGeometry">
            <summary>
            Ends the current spatial object
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.DrawBoth.OnReset">
            <summary>
            Setup the pipeline for reuse
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.DrawBoth.OnSetCoordinateSystem(Microsoft.Spatial.CoordinateSystem)">
            <summary>
            Set the coordinate system
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <returns>the coordinate system to be passed down the pipeline</returns>
        </member>
        <member name="T:Microsoft.Spatial.DrawBoth.DrawGeographyInput">
            <summary>
            This class is responsible for taking the calls to DrawGeography and delegating them to the unified
            handlers
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.DrawBoth.DrawGeographyInput.both">
            <summary>
            the DrawBoth instance that should be delegated to
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.DrawBoth.DrawGeographyInput.#ctor(Microsoft.Spatial.DrawBoth)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Spatial.DrawBoth.DrawGeographyInput"/> class.
            </summary>
            <param name="both">The both.</param>
        </member>
        <member name="M:Microsoft.Spatial.DrawBoth.DrawGeographyInput.LineTo(Microsoft.Spatial.GeographyPosition)">
            <summary>
            Draw a point in the specified coordinate
            </summary>
            <param name="position">Next position</param>
        </member>
        <member name="M:Microsoft.Spatial.DrawBoth.DrawGeographyInput.BeginFigure(Microsoft.Spatial.GeographyPosition)">
            <summary>
            Begin drawing a figure
            </summary>
            <param name="position">Next position</param>
        </member>
        <member name="M:Microsoft.Spatial.DrawBoth.DrawGeographyInput.BeginGeography(Microsoft.Spatial.SpatialType)">
            <summary>
            Begin drawing a spatial object
            </summary>
            <param name="type">The spatial type of the object</param>
        </member>
        <member name="M:Microsoft.Spatial.DrawBoth.DrawGeographyInput.EndFigure">
            <summary>
            Ends the current figure
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.DrawBoth.DrawGeographyInput.EndGeography">
            <summary>
            Ends the current spatial object
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.DrawBoth.DrawGeographyInput.Reset">
            <summary>
            Setup the pipeline for reuse
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.DrawBoth.DrawGeographyInput.SetCoordinateSystem(Microsoft.Spatial.CoordinateSystem)">
            <summary>
            Set the coordinate system
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
        </member>
        <member name="T:Microsoft.Spatial.DrawBoth.DrawGeometryInput">
            <summary>
            This class is responsible for taking the calls to DrawGeometry and delegating them to the unified
            handlers
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.DrawBoth.DrawGeometryInput.both">
            <summary>
            the DrawBoth instance that should be delegated to
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.DrawBoth.DrawGeometryInput.#ctor(Microsoft.Spatial.DrawBoth)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Spatial.DrawBoth.DrawGeometryInput"/> class.
            </summary>
            <param name="both">The both.</param>
        </member>
        <member name="M:Microsoft.Spatial.DrawBoth.DrawGeometryInput.LineTo(Microsoft.Spatial.GeometryPosition)">
            <summary>
            Draw a point in the specified coordinate
            </summary>
            <param name="position">Next position</param>
        </member>
        <member name="M:Microsoft.Spatial.DrawBoth.DrawGeometryInput.BeginFigure(Microsoft.Spatial.GeometryPosition)">
            <summary>
            Begin drawing a figure
            </summary>
            <param name="position">Next position</param>
        </member>
        <member name="M:Microsoft.Spatial.DrawBoth.DrawGeometryInput.BeginGeometry(Microsoft.Spatial.SpatialType)">
            <summary>
            Begin drawing a spatial object
            </summary>
            <param name="type">The spatial type of the object</param>
        </member>
        <member name="M:Microsoft.Spatial.DrawBoth.DrawGeometryInput.EndFigure">
            <summary>
            Ends the current figure
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.DrawBoth.DrawGeometryInput.EndGeometry">
            <summary>
            Ends the current spatial object
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.DrawBoth.DrawGeometryInput.Reset">
            <summary>
            Setup the pipeline for reuse
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.DrawBoth.DrawGeometryInput.SetCoordinateSystem(Microsoft.Spatial.CoordinateSystem)">
            <summary>
            Set the coordinate system
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
        </member>
        <member name="T:Microsoft.Spatial.ExtensionMethods">
            <summary>
              Extension methods for TextWriter
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.ExtensionMethods.WriteRoundtrippable(System.IO.TextWriter,System.Double)">
            <summary>
              Write a double to a TextWriter ensuring that the value will be roundtrippable through double.parse
            </summary>
            <param name = "writer">the writer</param>
            <param name = "d">the double value to be written</param>
        </member>
        <member name="M:Microsoft.Spatial.ExtensionMethods.IfValidReturningNullable``2(``0,System.Func{``0,``1})">
            <summary>
            If the arg is non-null, evaluate the op. Otherwise, propagate the null.
            </summary>
            <typeparam name="TArg">The type of the arg.</typeparam>
            <typeparam name="TResult">The type of the result.</typeparam>
            <param name="arg">The arg.</param>
            <param name="op">The op.</param>
            <returns>op(arg) if arg is non-null; null if arg is null.</returns>
        </member>
        <member name="M:Microsoft.Spatial.ExtensionMethods.IfValid``2(``0,System.Func{``0,``1})">
            <summary>
            If the arg is non-null, evaluate the op. Otherwise, propagate the null.
            </summary>
            <typeparam name="TArg">The type of the arg.</typeparam>
            <typeparam name="TResult">The type of the result.</typeparam>
            <param name="arg">The arg.</param>
            <param name="op">The op.</param>
            <returns>op(arg) if arg is non-null; null if arg is null.</returns>
        </member>
        <member name="T:Microsoft.Spatial.FormatterExtensions">
            <summary>Represents the extensions to formatters.</summary>
        </member>
        <member name="M:Microsoft.Spatial.FormatterExtensions.Write(Microsoft.Spatial.SpatialFormatter{System.IO.TextReader,System.IO.TextWriter},Microsoft.Spatial.ISpatial)">
            <summary>Writes the specified formatter.</summary>
            <returns>A string value of the formatted object.</returns>
            <param name="formatter">The formatter.</param>
            <param name="spatial">The spatial object.</param>
        </member>
        <member name="M:Microsoft.Spatial.FormatterExtensions.Write(Microsoft.Spatial.SpatialFormatter{System.Xml.XmlReader,System.Xml.XmlWriter},Microsoft.Spatial.ISpatial)">
            <summary>Writes the specified formatter.</summary>
            <returns>A string value of the formatted object.</returns>
            <param name="formatter">The formatter.</param>
            <param name="spatial">The spatial object.</param>
        </member>
        <member name="T:Microsoft.Spatial.ForwardingSegment">
            <summary>
            This is a forwarding transform pipe segment
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.ForwardingSegment.SpatialPipelineNoOp">
            <summary>
            The singleton NoOp implementation of the DrawGeography
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.ForwardingSegment.current">
            <summary>
            The current drawspatial that will be called and whose results will be forwarded to the
            next segment
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.ForwardingSegment.next">
            <summary>
            The SpatialPipeline to forward the calls to
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.ForwardingSegment.geographyForwarder">
            <summary>
            the cached GeographyForwarder for this instance
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.ForwardingSegment.geometryForwarder">
            <summary>
            the cached GeometryForwarder for this instance
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.ForwardingSegment.#ctor(Microsoft.Spatial.SpatialPipeline)">
            <summary>
            Constructs a new SpatialPipeline segment
            </summary>
            <param name="current">The DrawSpatial to draw to before calling next.</param>
        </member>
        <member name="M:Microsoft.Spatial.ForwardingSegment.#ctor(Microsoft.Spatial.GeographyPipeline,Microsoft.Spatial.GeometryPipeline)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Spatial.ForwardingSegment"/> class.
            </summary>
            <param name="currentGeography">The current geography.</param>
            <param name="currentGeometry">The current geometry.</param>
        </member>
        <member name="P:Microsoft.Spatial.ForwardingSegment.GeographyPipeline">
            <summary>
            Gets the geography.
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.ForwardingSegment.GeometryPipeline">
            <summary>
            Gets the geometry.
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.ForwardingSegment.NextDrawGeography">
            <summary>
            The next geography sink in  the pipeline
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.ForwardingSegment.NextDrawGeometry">
            <summary>
            The next geometry sink in the pipeline
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.ForwardingSegment.ChainTo(Microsoft.Spatial.SpatialPipeline)">
            <summary>
            Add the next pipeline
            </summary>
            <param name="destination">the next pipeline</param>
            <returns>The last pipesegment in the chain, usually the one just created</returns>
        </member>
        <member name="M:Microsoft.Spatial.ForwardingSegment.DoAction(System.Action,System.Action,System.Action,System.Action)">
            <summary>
            Run one action on a pipeline
            </summary>
            <param name="handler">what to do at this stage of the pipeline</param>
            <param name="handlerReset">The handler reset.</param>
            <param name="delegation">what the rest of the pipeline should do</param>
            <param name="delegationReset">The delegation reset.</param>
        </member>
        <member name="M:Microsoft.Spatial.ForwardingSegment.DoAction``1(System.Action{``0},System.Action,System.Action{``0},System.Action,``0)">
            <summary>
            Run one action on a pipeline
            </summary>
            <typeparam name="T">The type taken and returned by the transform style methods.</typeparam>
            <param name="handler">what to do at this stage of the pipeline</param>
            <param name="handlerReset">The handler reset.</param>
            <param name="delegation">what the rest of the pipeline should do</param>
            <param name="delegationReset">The delegation reset.</param>
            <param name="argument">The argument to pass to both this node and the rest of the pipeline</param>
        </member>
        <member name="T:Microsoft.Spatial.ForwardingSegment.GeographyForwarder">
            <summary>
            The forwarding implementation of DrawGeography
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.ForwardingSegment.GeographyForwarder.segment">
            <summary>
            The ForwardingSegment instance that this pipe is
            associated with
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.ForwardingSegment.GeographyForwarder.#ctor(Microsoft.Spatial.ForwardingSegment)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Spatial.ForwardingSegment.GeographyForwarder"/> class.
            </summary>
            <param name="segment">The segment.</param>
        </member>
        <member name="P:Microsoft.Spatial.ForwardingSegment.GeographyForwarder.Current">
            <summary>
            Gets the current DrawGeography from the associated ForwardingSegment instance
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.ForwardingSegment.GeographyForwarder.Next">
            <summary>
            Gets the next GeographyPipeline from the associated ForwardingSegment instance
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.ForwardingSegment.GeographyForwarder.SetCoordinateSystem(Microsoft.Spatial.CoordinateSystem)">
            <summary>
            Set the system reference to be used by this run of the pipeline
            </summary>
            <param name="coordinateSystem">the coordinate reference system</param>
        </member>
        <member name="M:Microsoft.Spatial.ForwardingSegment.GeographyForwarder.BeginGeography(Microsoft.Spatial.SpatialType)">
            <summary>
            start processing Geography data
            </summary>
            <param name="type">the sort of Geography data being processed</param>
        </member>
        <member name="M:Microsoft.Spatial.ForwardingSegment.GeographyForwarder.EndGeography">
            <summary>
            finish processing Geography data
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.ForwardingSegment.GeographyForwarder.BeginFigure(Microsoft.Spatial.GeographyPosition)">
            <summary>
            Begin drawing a Geography figure
            </summary>
            <param name="position">Next position</param>
        </member>
        <member name="M:Microsoft.Spatial.ForwardingSegment.GeographyForwarder.EndFigure">
            <summary>
            Finish drawing a Geography figure
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.ForwardingSegment.GeographyForwarder.LineTo(Microsoft.Spatial.GeographyPosition)">
            <summary>
            Continue drawing a Geography figure
            </summary>
            <param name="position">Next position</param>
        </member>
        <member name="M:Microsoft.Spatial.ForwardingSegment.GeographyForwarder.Reset">
            <summary>
            Reset the pipeline
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.ForwardingSegment.GeographyForwarder.DoAction``1(System.Action{``0},System.Action{``0},``0)">
            <summary>
            Run one action on a Geography pipeline
            </summary>
            <typeparam name="T">The type taken and returned by the transform style methods.</typeparam>
            <param name="handler">what to do at this stage of the pipeline</param>
            <param name="delegation">what the rest of the pipeline should do</param>
            <param name="argument">The argument to pass to both this node and the rest of the pipeline</param>
        </member>
        <member name="M:Microsoft.Spatial.ForwardingSegment.GeographyForwarder.DoAction(System.Action,System.Action)">
            <summary>
            Run one action on a Geography pipeline
            </summary>
            <param name="handler">what to do at this stage of the pipeline</param>
            <param name="delegation">what the rest of the pipeline should do</param>
        </member>
        <member name="T:Microsoft.Spatial.ForwardingSegment.GeometryForwarder">
            <summary>
            The forwarding implementation of DrawGeography
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.ForwardingSegment.GeometryForwarder.segment">
            <summary>
            The ForwardingSegment instance that this pipe is
            associated with
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.ForwardingSegment.GeometryForwarder.#ctor(Microsoft.Spatial.ForwardingSegment)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Spatial.ForwardingSegment.GeometryForwarder"/> class.
            </summary>
            <param name="segment">The segment.</param>
        </member>
        <member name="P:Microsoft.Spatial.ForwardingSegment.GeometryForwarder.Current">
            <summary>
            Gets the current DrawGeometry from the associated ForwardingSegment instance
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.ForwardingSegment.GeometryForwarder.Next">
            <summary>
            Gets the next GeometryPipeline from the associated ForwardingSegment instance
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.ForwardingSegment.GeometryForwarder.SetCoordinateSystem(Microsoft.Spatial.CoordinateSystem)">
            <summary>
            Set the system reference to be used by this run of the pipeline
            </summary>
            <param name="coordinateSystem">the coordinate reference system</param>
        </member>
        <member name="M:Microsoft.Spatial.ForwardingSegment.GeometryForwarder.BeginGeometry(Microsoft.Spatial.SpatialType)">
            <summary>
            start processing Geometry data
            </summary>
            <param name="type">the sort of Geometry data being processed</param>
        </member>
        <member name="M:Microsoft.Spatial.ForwardingSegment.GeometryForwarder.EndGeometry">
            <summary>
            finish processing Geometry data
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.ForwardingSegment.GeometryForwarder.BeginFigure(Microsoft.Spatial.GeometryPosition)">
            <summary>
            Begin drawing a Geometry figure
            </summary>
            <param name="position">Next position</param>
        </member>
        <member name="M:Microsoft.Spatial.ForwardingSegment.GeometryForwarder.EndFigure">
            <summary>
            Finish drawing a Geometry figure
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.ForwardingSegment.GeometryForwarder.LineTo(Microsoft.Spatial.GeometryPosition)">
            <summary>
            Continue drawing a Geometry figure
            </summary>
            <param name="position">Next position</param>
        </member>
        <member name="M:Microsoft.Spatial.ForwardingSegment.GeometryForwarder.Reset">
            <summary>
            Reset the pipeline
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.ForwardingSegment.GeometryForwarder.DoAction``1(System.Action{``0},System.Action{``0},``0)">
            <summary>
            Run one action on a Geography pipeline
            </summary>
            <typeparam name="T">The type taken and returned by the transform style methods.</typeparam>
            <param name="handler">what to do at this stage of the pipeline</param>
            <param name="delegation">what the rest of the pipeline should do</param>
            <param name="argument">The argument to pass to both this node and the rest of the pipeline</param>
        </member>
        <member name="M:Microsoft.Spatial.ForwardingSegment.GeometryForwarder.DoAction(System.Action,System.Action)">
            <summary>
            Run one action on a Geography pipeline
            </summary>
            <param name="handler">what to do at this stage of the pipeline</param>
            <param name="delegation">what the rest of the pipeline should do</param>
        </member>
        <member name="T:Microsoft.Spatial.ForwardingSegment.NoOpGeographyPipeline">
            <summary>
            A noop implementation of DrawGeography
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.ForwardingSegment.NoOpGeographyPipeline.LineTo(Microsoft.Spatial.GeographyPosition)">
            <summary>
            Draw a point in the specified coordinate
            </summary>
            <param name="position">Next position</param>
        </member>
        <member name="M:Microsoft.Spatial.ForwardingSegment.NoOpGeographyPipeline.BeginFigure(Microsoft.Spatial.GeographyPosition)">
            <summary>
            Begin drawing a figure
            </summary>
            <param name="position">Next position</param>
        </member>
        <member name="M:Microsoft.Spatial.ForwardingSegment.NoOpGeographyPipeline.BeginGeography(Microsoft.Spatial.SpatialType)">
            <summary>
            Begin drawing a spatial object
            </summary>
            <param name="type">The spatial type of the object</param>
        </member>
        <member name="M:Microsoft.Spatial.ForwardingSegment.NoOpGeographyPipeline.EndFigure">
            <summary>
            Ends the current figure
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.ForwardingSegment.NoOpGeographyPipeline.EndGeography">
            <summary>
            Ends the current spatial object
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.ForwardingSegment.NoOpGeographyPipeline.Reset">
            <summary>
            Setup the pipeline for reuse
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.ForwardingSegment.NoOpGeographyPipeline.SetCoordinateSystem(Microsoft.Spatial.CoordinateSystem)">
            <summary>
            Set the coordinate system
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
        </member>
        <member name="T:Microsoft.Spatial.ForwardingSegment.NoOpGeometryPipeline">
            <summary>
            a noop implementation of DrawGeometry
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.ForwardingSegment.NoOpGeometryPipeline.LineTo(Microsoft.Spatial.GeometryPosition)">
            <summary>
            Draw a point in the specified coordinate
            </summary>
            <param name="position">Next position</param>
        </member>
        <member name="M:Microsoft.Spatial.ForwardingSegment.NoOpGeometryPipeline.BeginFigure(Microsoft.Spatial.GeometryPosition)">
            <summary>
            Begin drawing a figure
            </summary>
            <param name="position">Next position</param>
        </member>
        <member name="M:Microsoft.Spatial.ForwardingSegment.NoOpGeometryPipeline.BeginGeometry(Microsoft.Spatial.SpatialType)">
            <summary>
            Begin drawing a spatial object
            </summary>
            <param name="type">The spatial type of the object</param>
        </member>
        <member name="M:Microsoft.Spatial.ForwardingSegment.NoOpGeometryPipeline.EndFigure">
            <summary>
            Ends the current figure
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.ForwardingSegment.NoOpGeometryPipeline.EndGeometry">
            <summary>
            Ends the current spatial object
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.ForwardingSegment.NoOpGeometryPipeline.Reset">
            <summary>
            Setup the pipeline for reuse
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.ForwardingSegment.NoOpGeometryPipeline.SetCoordinateSystem(Microsoft.Spatial.CoordinateSystem)">
            <summary>
            Set the coordinate system
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
        </member>
        <member name="T:Microsoft.Spatial.Geography">
            <summary>Represents a base class of geography shapes.</summary>
        </member>
        <member name="F:Microsoft.Spatial.Geography.creator">
            <summary>
            The implementation that created this instance
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.Geography.coordinateSystem">
            <summary>
            The CoordinateSystem of this geography
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.Geography.#ctor(Microsoft.Spatial.CoordinateSystem,Microsoft.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.Spatial.Geography" /> class.</summary>
            <param name="coordinateSystem">The coordinate system of this geography.</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="P:Microsoft.Spatial.Geography.CoordinateSystem">
            <summary>Gets the coordinate system of the geography.</summary>
            <returns>The coordinate system of the geography.</returns>
        </member>
        <member name="P:Microsoft.Spatial.Geography.IsEmpty">
            <summary>Gets a value that indicates whether the geography is empty.</summary>
            <returns>true if the geography is empty; otherwise, false.</returns>
        </member>
        <member name="P:Microsoft.Spatial.Geography.Creator">
            <summary>
            Gets the implementation that created this instance.
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.Geography.SendTo(Microsoft.Spatial.GeographyPipeline)">
            <summary>Sends the current spatial object to the given pipeline.</summary>
            <param name="chain">The spatial pipeline.</param>
        </member>
        <member name="M:Microsoft.Spatial.Geography.ComputeHashCodeFor``1(Microsoft.Spatial.CoordinateSystem,System.Collections.Generic.IEnumerable{``0})">
            <summary>
            Computes the hashcode for the given CoordinateSystem and the fields
            </summary>
            <typeparam name="T">Spatial type instances or doubles for base types (Geography/Geometry types).</typeparam>
            <param name="coords">CoordinateSystem instance.</param>
            <param name="fields">Spatial type instances or doubles for base types (Geography/Geometry types).</param>
            <returns>hashcode for the CoordinateSystem instance and Spatial type instances.</returns>
        </member>
        <member name="M:Microsoft.Spatial.Geography.BaseEquals(Microsoft.Spatial.Geography)">
            <summary>
            Check for basic equality due to emptiness, nullness, referential equality and difference in coordinate system
            </summary>
            <param name="other">The other geography</param>
            <returns>Boolean value indicating equality, or null to indicate inconclusion</returns>
        </member>
        <member name="T:Microsoft.Spatial.GeographyBuilderImplementation">
            <summary>
            Builder for Geography types
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeographyBuilderImplementation.builder">
            <summary>
            The tree builder
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeographyBuilderImplementation.#ctor(Microsoft.Spatial.SpatialImplementation)">
            <summary>
            Constructor
            </summary>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="E:Microsoft.Spatial.GeographyBuilderImplementation.ProduceGeography">
            <summary>
            Fires when the provider constructs a geography object.
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.GeographyBuilderImplementation.ConstructedGeography">
            <summary>
            Constructed Geography
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeographyBuilderImplementation.LineTo(Microsoft.Spatial.GeographyPosition)">
            <summary>
            Draw a point in the specified coordinate
            </summary>
            <param name="position">Next position</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyBuilderImplementation.BeginFigure(Microsoft.Spatial.GeographyPosition)">
            <summary>
            Begin drawing a figure
            </summary>
            <param name="position">Next position</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyBuilderImplementation.BeginGeography(Microsoft.Spatial.SpatialType)">
            <summary>
            Begin drawing a spatial object
            </summary>
            <param name="type">The spatial type of the object</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyBuilderImplementation.EndFigure">
            <summary>
            Ends the current figure
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeographyBuilderImplementation.EndGeography">
            <summary>
            Ends the current spatial object
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeographyBuilderImplementation.Reset">
            <summary>
            Setup the pipeline for reuse
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeographyBuilderImplementation.SetCoordinateSystem(Microsoft.Spatial.CoordinateSystem)">
            <summary>
            Set the coordinate system
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
        </member>
        <member name="T:Microsoft.Spatial.GeographyBuilderImplementation.GeographyTreeBuilder">
            <summary>
            Geography Tree Builder
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeographyBuilderImplementation.GeographyTreeBuilder.creator">
            <summary>
            The implementation that created this instance.
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeographyBuilderImplementation.GeographyTreeBuilder.currentCoordinateSystem">
            <summary>
            CoordinateSystem for the building geography
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeographyBuilderImplementation.GeographyTreeBuilder.#ctor(Microsoft.Spatial.SpatialImplementation)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Spatial.GeographyBuilderImplementation.GeographyTreeBuilder"/> class.
            </summary>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyBuilderImplementation.GeographyTreeBuilder.SetCoordinateSystem(System.Nullable{System.Int32})">
            <summary>
            Set the coordinate system based on the given ID
            </summary>
            <param name="epsgId">The coordinate system ID to set. Null indicates the default should be used</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyBuilderImplementation.GeographyTreeBuilder.CreatePoint(System.Boolean,System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Create a new instance of Point
            </summary>
            <param name="isEmpty">Whether the point is empty</param>
            <param name="x">X</param>
            <param name="y">Y</param>
            <param name="z">Z</param>
            <param name="m">M</param>
            <returns>A new instance of point</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyBuilderImplementation.GeographyTreeBuilder.CreateShapeInstance(Microsoft.Spatial.SpatialType,System.Collections.Generic.IEnumerable{Microsoft.Spatial.Geography})">
            <summary>
            Create a new instance of T
            </summary>
            <param name="type">The spatial type to create</param>
            <param name="spatialData">The arguments</param>
            <returns>A new instance of T</returns>
        </member>
        <member name="T:Microsoft.Spatial.GeographyCollection">
            <summary>Represents the collection of geographies.</summary>
        </member>
        <member name="M:Microsoft.Spatial.GeographyCollection.#ctor(Microsoft.Spatial.CoordinateSystem,Microsoft.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.Spatial.GeographyCollection" /> class.</summary>
            <param name="coordinateSystem">The coordinate system of this geography collection.</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="P:Microsoft.Spatial.GeographyCollection.Geographies">
            <summary>Gets the collection of geographies.</summary>
            <returns>The collection of geographies.</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyCollection.Equals(Microsoft.Spatial.GeographyCollection)">
            <summary>Determines whether this instance and another specified geography instance have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="other">The geography to compare to this instance.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyCollection.Equals(System.Object)">
            <summary>Determines whether this instance and the specified object have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="obj">The object to compare to this instance.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyCollection.GetHashCode">
            <summary>Gets the hash code.</summary>
            <returns>The hash code.</returns>
        </member>
        <member name="T:Microsoft.Spatial.GeographyCollectionImplementation">
            <summary>
            Geography Collection
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeographyCollectionImplementation.geographyArray">
            <summary>
            Collection of geography instances
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeographyCollectionImplementation.#ctor(Microsoft.Spatial.CoordinateSystem,Microsoft.Spatial.SpatialImplementation,Microsoft.Spatial.Geography[])">
            <summary>
            Constructor
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <param name="creator">The implementation that created this instance.</param>
            <param name="geography">Collection of geography instances</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyCollectionImplementation.#ctor(Microsoft.Spatial.SpatialImplementation,Microsoft.Spatial.Geography[])">
            <summary>
            Constructor
            </summary>
            <param name="creator">The implementation that created this instance.</param>
            <param name="geography">Collection of geography instances</param>
        </member>
        <member name="P:Microsoft.Spatial.GeographyCollectionImplementation.IsEmpty">
            <summary>
            Is Geography Collection Empty
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.GeographyCollectionImplementation.Geographies">
            <summary>
            Geographies
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeographyCollectionImplementation.SendTo(Microsoft.Spatial.GeographyPipeline)">
            <summary>
            Sends the current spatial object to the given pipeline
            </summary>
            <param name="pipeline">The spatial pipeline</param>
        </member>
        <member name="T:Microsoft.Spatial.GeographyCurve">
            <summary>Represents the curve of geography.</summary>
        </member>
        <member name="M:Microsoft.Spatial.GeographyCurve.#ctor(Microsoft.Spatial.CoordinateSystem,Microsoft.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.Spatial.GeographyCurve" /> class.</summary>
            <param name="coordinateSystem">The coordinate system of this geography curve.</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="T:Microsoft.Spatial.GeographyFactory">
            <summary>
            Geography Factory
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFactory.Point(Microsoft.Spatial.CoordinateSystem,System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Create a Geography Point
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <param name="latitude">The latitude value</param>
            <param name="longitude">The longitude value</param>
            <param name="z">The Z value</param>
            <param name="m">The M value</param>
            <returns>A Geography Point Factory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFactory.Point(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Create a Geography Point
            </summary>
            <param name="latitude">The latitude value</param>
            <param name="longitude">The longitude value</param>
            <param name="z">The Z value</param>
            <param name="m">The M value</param>
            <returns>A Geography Point Factory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFactory.Point(Microsoft.Spatial.CoordinateSystem,System.Double,System.Double)">
            <summary>
            Create a Geography Point
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <param name="latitude">The latitude value</param>
            <param name="longitude">The longitude value</param>
            <returns>A Geography Point Factory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFactory.Point(System.Double,System.Double)">
            <summary>
            Create a Geography Point
            </summary>
            <param name="latitude">The latitude value</param>
            <param name="longitude">The longitude value</param>
            <returns>A Geography Point Factory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFactory.Point(Microsoft.Spatial.CoordinateSystem)">
            <summary>
            Create a factory with an empty Geography Point
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <returns>A Geography Point Factory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFactory.Point">
            <summary>
            Create a factory with an empty Geography Point
            </summary>
            <returns>A Geography Point Factory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFactory.MultiPoint(Microsoft.Spatial.CoordinateSystem)">
            <summary>
            Create a Geography MultiPoint
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <returns>A Geography MultiPoint Factory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFactory.MultiPoint">
            <summary>
            Create a Geography MultiPoint
            </summary>
            <returns>A Geography MultiPoint Factory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFactory.LineString(Microsoft.Spatial.CoordinateSystem,System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Create a Geography LineString with a starting position
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <param name="latitude">The latitude value</param>
            <param name="longitude">The longitude value</param>
            <param name="z">The Z value</param>
            <param name="m">The M value</param>
            <returns>A Geography LineString Factory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFactory.LineString(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Create a Geography LineString with a starting position
            </summary>
            <param name="latitude">The latitude value</param>
            <param name="longitude">The longitude value</param>
            <param name="z">The Z value</param>
            <param name="m">The M value</param>
            <returns>A Geography LineString Factory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFactory.LineString(Microsoft.Spatial.CoordinateSystem,System.Double,System.Double)">
            <summary>
            Create a Geography LineString with a starting position
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <param name="latitude">The latitude value</param>
            <param name="longitude">The longitude value</param>
            <returns>A Geography LineString Factory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFactory.LineString(System.Double,System.Double)">
            <summary>
            Create a Geography LineString with a starting position
            </summary>
            <param name="latitude">The latitude value</param>
            <param name="longitude">The longitude value</param>
            <returns>A Geography LineString Factory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFactory.LineString(Microsoft.Spatial.CoordinateSystem)">
            <summary>
            Create an empty Geography LineString
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <returns>A Geography LineString Factory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFactory.LineString">
            <summary>
            Create an empty Geography LineString
            </summary>
            <returns>A Geography LineString Factory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFactory.MultiLineString(Microsoft.Spatial.CoordinateSystem)">
            <summary>
            Create a Geography MultiLineString
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <returns>A Geography MultiLineString Factory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFactory.MultiLineString">
            <summary>
            Create a Geography MultiLineString
            </summary>
            <returns>A Geography MultiLineString Factory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFactory.Polygon(Microsoft.Spatial.CoordinateSystem)">
            <summary>
            Create a Geography Polygon
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <returns>A Geography Polygon Factory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFactory.Polygon">
            <summary>
            Create a Geography Polygon
            </summary>
            <returns>A Geography Polygon Factory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFactory.MultiPolygon(Microsoft.Spatial.CoordinateSystem)">
            <summary>
            Create a Geography MultiPolygon
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <returns>A Geography MultiPolygon Factory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFactory.MultiPolygon">
            <summary>
            Create a Geography MultiPolygon
            </summary>
            <returns>A Geography MultiPolygon Factory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFactory.Collection(Microsoft.Spatial.CoordinateSystem)">
            <summary>
            Create a Geography Collection
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <returns>A Geography Collection Factory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFactory.Collection">
            <summary>
            Create a Geography Collection
            </summary>
            <returns>A Geography Collection Factory</returns>
        </member>
        <member name="T:Microsoft.Spatial.GeographyFactory`1">
            <summary>
            Geography Spatial Factory
            </summary>
            <typeparam name="T">The target type</typeparam>
        </member>
        <member name="F:Microsoft.Spatial.GeographyFactory`1.provider">
            <summary>
            The provider of the built type
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeographyFactory`1.buildChain">
            <summary>
            The chain to build through
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFactory`1.#ctor(Microsoft.Spatial.CoordinateSystem)">
            <summary>
            Initializes a new instance of the GeographyFactory class
            </summary>
            <param name="coordinateSystem">The coordinate system</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFactory`1.op_Implicit(Microsoft.Spatial.GeographyFactory{`0})~`0">
            <summary>
            Using implicit cast to trigger the Finalize call
            </summary>
            <param name="factory">The factory</param>
            <returns>The built instance of the target type</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFactory`1.Point(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Start a new Point
            </summary>
            <param name="latitude">The latitude value</param>
            <param name="longitude">The longitude value</param>
            <param name="z">The Z value</param>
            <param name="m">The M Value</param>
            <returns>The current instance of GeographyFactory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFactory`1.Point(System.Double,System.Double)">
            <summary>
            Start a new Point
            </summary>
            <param name="latitude">The latitude value</param>
            <param name="longitude">The longitude value</param>
            <returns>The current instance of GeographyFactory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFactory`1.Point">
            <summary>
            Start a new empty Point
            </summary>
            <returns>The current instance of GeographyFactory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFactory`1.LineString(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Start a new LineString
            </summary>
            <param name="latitude">The latitude value</param>
            <param name="longitude">The longitude value</param>
            <param name="z">The Z value</param>
            <param name="m">The M Value</param>
            <returns>The current instance of GeographyFactory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFactory`1.LineString(System.Double,System.Double)">
            <summary>
            Start a new LineString
            </summary>
            <param name="latitude">The latitude value</param>
            <param name="longitude">The longitude value</param>
            <returns>The current instance of GeographyFactory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFactory`1.LineString">
            <summary>
            Start a new empty LineString
            </summary>
            <returns>The current instance of GeographyFactory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFactory`1.Polygon">
            <summary>
            Start a new Polygon
            </summary>
            <returns>The current instance of GeographyFactory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFactory`1.MultiPoint">
            <summary>
            Start a new MultiPoint
            </summary>
            <returns>The current instance of GeographyFactory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFactory`1.MultiLineString">
            <summary>
            Start a new MultiLineString
            </summary>
            <returns>The current instance of GeographyFactory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFactory`1.MultiPolygon">
            <summary>
            Start a new MultiPolygon
            </summary>
            <returns>The current instance of GeographyFactory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFactory`1.Collection">
            <summary>
            Start a new Collection
            </summary>
            <returns>The current instance of GeographyFactory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFactory`1.Ring(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Start a new Polygon Ring
            </summary>
            <param name="latitude">The latitude value</param>
            <param name="longitude">The longitude value</param>
            <param name="z">The Z value</param>
            <param name="m">The M Value</param>
            <returns>The current instance of GeographyFactory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFactory`1.Ring(System.Double,System.Double)">
            <summary>
            Start a new Polygon Ring
            </summary>
            <param name="latitude">The latitude value</param>
            <param name="longitude">The longitude value</param>
            <returns>The current instance of GeographyFactory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFactory`1.LineTo(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Add a new point in the current line figure
            </summary>
            <param name="latitude">The latitude value</param>
            <param name="longitude">The longitude value</param>
            <param name="z">The Z value</param>
            <param name="m">The M Value</param>
            <returns>The current instance of GeographyFactory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFactory`1.LineTo(System.Double,System.Double)">
            <summary>
            Add a new point in the current line figure
            </summary>
            <param name="latitude">The latitude value</param>
            <param name="longitude">The longitude value</param>
            <returns>The current instance of GeographyFactory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFactory`1.Build">
            <summary>
            Finish the current geography
            </summary>
            <returns>The constructed instance</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFactory`1.BeginGeo(Microsoft.Spatial.SpatialType)">
            <summary>
            Begin a new geography
            </summary>
            <param name="type">The spatial type</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFactory`1.BeginFigure(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Begin drawing a figure
            TODO: longitude and latitude should be swapped !!! per ABNF.
            </summary>
            <param name="latitude">X or Latitude Coordinate</param>
            <param name="longitude">Y or Longitude Coordinate</param>
            <param name="z">Z Coordinate</param>
            <param name="m">M Coordinate</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFactory`1.AddLine(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Draw a point in the specified coordinate
            </summary>
            <param name="latitude">X or Latitude Coordinate</param>
            <param name="longitude">Y or Longitude Coordinate</param>
            <param name="z">Z Coordinate</param>
            <param name="m">M Coordinate</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFactory`1.EndFigure">
            <summary>
            Ends the figure set on the current node
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFactory`1.EndGeo">
            <summary>
            Ends the current spatial object
            </summary>
        </member>
        <member name="T:Microsoft.Spatial.GeographyFullGlobe">
            <summary>Represents the full globe of geography.</summary>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFullGlobe.#ctor(Microsoft.Spatial.CoordinateSystem,Microsoft.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.Spatial.GeographyFullGlobe" /> class.</summary>
            <param name="coordinateSystem">The coordinate system of this instance.</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFullGlobe.Equals(Microsoft.Spatial.GeographyFullGlobe)">
            <summary>Determines whether this instance and another specified geography instance have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="other">The geography to compare to this instance.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFullGlobe.Equals(System.Object)">
            <summary>Determines whether this instance and the specified object have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="obj">The object to compare to this instance.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFullGlobe.GetHashCode">
            <summary>Gets the hash code.</summary>
            <returns>The hash code.</returns>
        </member>
        <member name="T:Microsoft.Spatial.GeographyFullGlobeImplementation">
            <summary>
            Implementation of FullGlobe
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFullGlobeImplementation.#ctor(Microsoft.Spatial.CoordinateSystem,Microsoft.Spatial.SpatialImplementation)">
            <summary>
            Constructor
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFullGlobeImplementation.#ctor(Microsoft.Spatial.SpatialImplementation)">
            <summary>
            Constructor
            </summary>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="P:Microsoft.Spatial.GeographyFullGlobeImplementation.IsEmpty">
            <summary>
            Is FullGlobe empty
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeographyFullGlobeImplementation.SendTo(Microsoft.Spatial.GeographyPipeline)">
            <summary>
            Sends the spatial geography object to the given sink
            </summary>
            <param name="pipeline">The spatial pipeline</param>
        </member>
        <member name="T:Microsoft.Spatial.GeographyHelperMethods">
            <summary>
            Helper methods for the geography type.
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeographyHelperMethods.SendFigure(Microsoft.Spatial.GeographyLineString,Microsoft.Spatial.GeographyPipeline)">
            <summary>
            Sends the current spatial object to the given pipeline with a figure that represents this LineString
            </summary>
            <param name="lineString">GeographyLineString instance to serialize.</param>
            <param name="pipeline">The pipeline to populate to</param>
        </member>
        <member name="T:Microsoft.Spatial.GeographyLineString">
            <summary>Represents a geography line string consist of an array of geo points.</summary>
        </member>
        <member name="M:Microsoft.Spatial.GeographyLineString.#ctor(Microsoft.Spatial.CoordinateSystem,Microsoft.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.Spatial.GeographyLineString" /> class.</summary>
            <param name="coordinateSystem">The coordinate system of this instance.</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="P:Microsoft.Spatial.GeographyLineString.Points">
            <summary>Gets the point list.</summary>
            <returns>The point list.</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyLineString.Equals(Microsoft.Spatial.GeographyLineString)">
            <summary>Determines whether this instance and another specified geography instance have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="other">The geography to compare to this instance.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyLineString.Equals(System.Object)">
            <summary>Determines whether this instance and the specified object have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="obj">The object to compare to this instance.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyLineString.GetHashCode">
            <summary>Gets the hash code.</summary>
            <returns>The hash code.</returns>
        </member>
        <member name="T:Microsoft.Spatial.GeographyLineStringImplementation">
            <summary>
            A Geography linestring consist of an array of GeoPoints
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeographyLineStringImplementation.points">
            <summary>
            Points array
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeographyLineStringImplementation.#ctor(Microsoft.Spatial.CoordinateSystem,Microsoft.Spatial.SpatialImplementation,Microsoft.Spatial.GeographyPoint[])">
            <summary>
            Constructor
            </summary>
            <param name="coordinateSystem">CoordinateSystem</param>
            <param name="creator">The implementation that created this instance.</param>
            <param name="points">The point list</param>
        </member>
        <member name="P:Microsoft.Spatial.GeographyLineStringImplementation.IsEmpty">
            <summary>
            Is LineString Empty
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.GeographyLineStringImplementation.Points">
            <summary>
            Point list
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeographyLineStringImplementation.SendTo(Microsoft.Spatial.GeographyPipeline)">
            <summary>
            Sends the current spatial object to the given sink
            </summary>
            <param name="pipeline">The spatial pipeline</param>
        </member>
        <member name="T:Microsoft.Spatial.GeographyMultiCurve">
            <summary>Represents the multi-curve of geography.</summary>
        </member>
        <member name="M:Microsoft.Spatial.GeographyMultiCurve.#ctor(Microsoft.Spatial.CoordinateSystem,Microsoft.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.Spatial.GeographyMultiCurve" /> class.</summary>
            <param name="coordinateSystem">The coordinate system of this instance.</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="T:Microsoft.Spatial.GeographyMultiLineString">
            <summary>Represents the multi-line string of geography.</summary>
        </member>
        <member name="M:Microsoft.Spatial.GeographyMultiLineString.#ctor(Microsoft.Spatial.CoordinateSystem,Microsoft.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.Spatial.GeographyMultiLineString" /> class.</summary>
            <param name="coordinateSystem">The coordinate system of this instance.</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="P:Microsoft.Spatial.GeographyMultiLineString.LineStrings">
            <summary>Gets the line strings.</summary>
            <returns>A collection of line strings.</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyMultiLineString.Equals(Microsoft.Spatial.GeographyMultiLineString)">
            <summary>Determines whether this instance and another specified geography instance have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="other">The geography to compare to this instance.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyMultiLineString.Equals(System.Object)">
            <summary>Determines whether this instance and the specified object have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="obj">The object to compare to this instance.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyMultiLineString.GetHashCode">
            <summary>Gets the hash code.</summary>
            <returns>The hash code.</returns>
        </member>
        <member name="T:Microsoft.Spatial.GeographyMultiLineStringImplementation">
            <summary>
            Geography Multi-LineString
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeographyMultiLineStringImplementation.lineStrings">
            <summary>
            Line Strings
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeographyMultiLineStringImplementation.#ctor(Microsoft.Spatial.CoordinateSystem,Microsoft.Spatial.SpatialImplementation,Microsoft.Spatial.GeographyLineString[])">
            <summary>
            Constructor
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <param name="creator">The implementation that created this instance.</param>
            <param name="lineStrings">Line Strings</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyMultiLineStringImplementation.#ctor(Microsoft.Spatial.SpatialImplementation,Microsoft.Spatial.GeographyLineString[])">
            <summary>
            Constructor
            </summary>
            <param name="creator">The implementation that created this instance.</param>
            <param name="lineStrings">Line Strings</param>
        </member>
        <member name="P:Microsoft.Spatial.GeographyMultiLineStringImplementation.IsEmpty">
            <summary>
            Is MultiLineString Empty
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.GeographyMultiLineStringImplementation.Geographies">
            <summary>
            Geographies
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.GeographyMultiLineStringImplementation.LineStrings">
            <summary>
            Line Strings
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeographyMultiLineStringImplementation.SendTo(Microsoft.Spatial.GeographyPipeline)">
            <summary>
            Sends the current spatial object to the given sink
            </summary>
            <param name="pipeline">The spatial pipeline</param>
        </member>
        <member name="T:Microsoft.Spatial.GeographyMultiPoint">
            <summary>Represents the multi-point of geography.</summary>
        </member>
        <member name="M:Microsoft.Spatial.GeographyMultiPoint.#ctor(Microsoft.Spatial.CoordinateSystem,Microsoft.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.Spatial.GeographyMultiPoint" /> class.</summary>
            <param name="coordinateSystem">The coordinate system of this instance.</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="P:Microsoft.Spatial.GeographyMultiPoint.Points">
            <summary>Gets a collection of points.</summary>
            <returns>A collection of points.</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyMultiPoint.Equals(Microsoft.Spatial.GeographyMultiPoint)">
            <summary>Determines whether this instance and another specified geography instance have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="other">The geography to compare to this instance.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyMultiPoint.Equals(System.Object)">
            <summary>Determines whether this instance and the specified object have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="obj">The object to compare to this instance.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyMultiPoint.GetHashCode">
            <summary>Gets the hash code.</summary>
            <returns>The hash code.</returns>
        </member>
        <member name="T:Microsoft.Spatial.GeographyMultiPointImplementation">
            <summary>
            Geography Multi-Point
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeographyMultiPointImplementation.points">
            <summary>
            Points
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeographyMultiPointImplementation.#ctor(Microsoft.Spatial.CoordinateSystem,Microsoft.Spatial.SpatialImplementation,Microsoft.Spatial.GeographyPoint[])">
            <summary>
            Constructor
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <param name="creator">The implementation that created this instance.</param>
            <param name="points">Points</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyMultiPointImplementation.#ctor(Microsoft.Spatial.SpatialImplementation,Microsoft.Spatial.GeographyPoint[])">
            <summary>
            Constructor
            </summary>
            <param name="creator">The implementation that created this instance.</param>
            <param name="points">Points</param>
        </member>
        <member name="P:Microsoft.Spatial.GeographyMultiPointImplementation.IsEmpty">
            <summary>
            Is MultiPoint Empty
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.GeographyMultiPointImplementation.Geographies">
            <summary>
            Geography
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.GeographyMultiPointImplementation.Points">
            <summary>
            Points
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeographyMultiPointImplementation.SendTo(Microsoft.Spatial.GeographyPipeline)">
            <summary>
            Sends the current spatial object to the given sink
            </summary>
            <param name="pipeline">The spatial pipeline</param>
        </member>
        <member name="T:Microsoft.Spatial.GeographyMultiPolygon">
            <summary>Represents the multi-polygon of geography.</summary>
        </member>
        <member name="M:Microsoft.Spatial.GeographyMultiPolygon.#ctor(Microsoft.Spatial.CoordinateSystem,Microsoft.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.Spatial.GeographyMultiPolygon" /> class.</summary>
            <param name="coordinateSystem">The coordinate system of this instance.</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="P:Microsoft.Spatial.GeographyMultiPolygon.Polygons">
            <summary>Gets a collection of polygons.</summary>
            <returns>A collection of polygons.</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyMultiPolygon.Equals(Microsoft.Spatial.GeographyMultiPolygon)">
            <summary>Determines whether this instance and another specified geography instance have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="other">The geography to compare to this instance.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyMultiPolygon.Equals(System.Object)">
            <summary>Determines whether this instance and the specified object have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="obj">The object to compare to this instance.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyMultiPolygon.GetHashCode">
            <summary>Gets the hash code.</summary>
            <returns>The hash code.</returns>
        </member>
        <member name="T:Microsoft.Spatial.GeographyMultiPolygonImplementation">
            <summary>
            Geography Multi-Polygon
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeographyMultiPolygonImplementation.polygons">
            <summary>
            Polygons
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeographyMultiPolygonImplementation.#ctor(Microsoft.Spatial.CoordinateSystem,Microsoft.Spatial.SpatialImplementation,Microsoft.Spatial.GeographyPolygon[])">
            <summary>
            Constructor
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <param name="creator">The implementation that created this instance.</param>
            <param name="polygons">Polygons</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyMultiPolygonImplementation.#ctor(Microsoft.Spatial.SpatialImplementation,Microsoft.Spatial.GeographyPolygon[])">
            <summary>
            Constructor
            </summary>
            <param name="creator">The implementation that created this instance.</param>
            <param name="polygons">Polygons</param>
        </member>
        <member name="P:Microsoft.Spatial.GeographyMultiPolygonImplementation.IsEmpty">
            <summary>
            Is MultiPolygon Empty
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.GeographyMultiPolygonImplementation.Geographies">
            <summary>
            Geographies
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.GeographyMultiPolygonImplementation.Polygons">
            <summary>
            Polygons
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeographyMultiPolygonImplementation.SendTo(Microsoft.Spatial.GeographyPipeline)">
            <summary>
            Sends the current spatial object to the given sink
            </summary>
            <param name="pipeline">The spatial pipeline</param>
        </member>
        <member name="T:Microsoft.Spatial.GeographyMultiSurface">
            <summary>Represents the multi-surface of geography.</summary>
        </member>
        <member name="M:Microsoft.Spatial.GeographyMultiSurface.#ctor(Microsoft.Spatial.CoordinateSystem,Microsoft.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.Spatial.GeographyMultiSurface" /> class.</summary>
            <param name="coordinateSystem">The coordinate system of this instance.</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="T:Microsoft.Spatial.GeographyOperationsExtensions">
            <summary>
              Extension methods for the Geography operations
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeographyOperationsExtensions.Distance(Microsoft.Spatial.Geography,Microsoft.Spatial.Geography)">
            <summary>Determines the distance of the geography.</summary>
            <returns>The operation result.</returns>
            <param name="operand1">The first operand.</param>
            <param name="operand2">The second operand.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyOperationsExtensions.Length(Microsoft.Spatial.Geography)">
            <summary>Determines the Length of the geography LineString.</summary>
            <returns>The operation result.</returns>
            <param name="operand">The LineString operand.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyOperationsExtensions.Intersects(Microsoft.Spatial.Geography,Microsoft.Spatial.Geography)">
            <summary>Determines if geography point and polygon will intersect.</summary>
            <returns>The operation result.</returns>
            <param name="operand1">The first operand.</param>
            <param name="operand2">The second operand.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyOperationsExtensions.OperationsFor(Microsoft.Spatial.Geography[])">
            <summary>
            Finds the ops instance registered for the operands.
            </summary>
            <param name="operands">The operands.</param>
            <returns>The ops value, or null if any operand is null</returns>
        </member>
        <member name="T:Microsoft.Spatial.GeographyPipeline">
            <summary>Represents the pipeline of geography.</summary>
        </member>
        <member name="M:Microsoft.Spatial.GeographyPipeline.BeginGeography(Microsoft.Spatial.SpatialType)">
            <summary>Begins drawing a spatial object.</summary>
            <param name="type">The spatial type of the object.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyPipeline.BeginFigure(Microsoft.Spatial.GeographyPosition)">
            <summary>Begins drawing a figure.</summary>
            <param name="position">The position of the figure.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyPipeline.LineTo(Microsoft.Spatial.GeographyPosition)">
            <summary>Draws a point in the specified coordinate.</summary>
            <param name="position">The position of the line.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyPipeline.EndFigure">
            <summary>Ends the current figure.</summary>
        </member>
        <member name="M:Microsoft.Spatial.GeographyPipeline.EndGeography">
            <summary>Ends the current spatial object.</summary>
        </member>
        <member name="M:Microsoft.Spatial.GeographyPipeline.SetCoordinateSystem(Microsoft.Spatial.CoordinateSystem)">
            <summary>Sets the coordinate system.</summary>
            <param name="coordinateSystem">The coordinate system to set.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyPipeline.Reset">
            <summary>Resets the pipeline.</summary>
        </member>
        <member name="T:Microsoft.Spatial.GeographyPoint">
            <summary>Represents a geography point.</summary>
        </member>
        <member name="M:Microsoft.Spatial.GeographyPoint.#ctor(Microsoft.Spatial.CoordinateSystem,Microsoft.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.Spatial.GeographyPoint" /> class.</summary>
            <param name="coordinateSystem">The coordinate system of this instance.</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="P:Microsoft.Spatial.GeographyPoint.Latitude">
            <summary>Gets the latitude.</summary>
            <returns>The latitude.</returns>
        </member>
        <member name="P:Microsoft.Spatial.GeographyPoint.Longitude">
            <summary>Gets the longitude.</summary>
            <returns>The longitude.</returns>
        </member>
        <member name="P:Microsoft.Spatial.GeographyPoint.Z">
            <summary>Gets the nullable Z.</summary>
            <returns>The nullable Z.</returns>
            <remarks>Z is the altitude portion of position.</remarks>
        </member>
        <member name="P:Microsoft.Spatial.GeographyPoint.M">
            <summary>Gets the nullable M.</summary>
            <returns>The nullable M.</returns>
            <remarks>M is the arbitrary measure associated with a position.</remarks>
        </member>
        <member name="M:Microsoft.Spatial.GeographyPoint.Create(System.Double,System.Double)">
            <summary>Creates a geography point using the specified latitude and longitude.</summary>
            <returns>The geography point that was created.</returns>
            <param name="latitude">The latitude.</param>
            <param name="longitude">The longitude.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyPoint.Create(System.Double,System.Double,System.Nullable{System.Double})">
            <summary>Creates a geography point using the specified latitude, longitude and dimension.</summary>
            <returns>The geography point that was created.</returns>
            <param name="latitude">The latitude.</param>
            <param name="longitude">The longitude.</param>
            <param name="z">The z dimension.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyPoint.Create(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>Creates a geography point using the specified latitude, longitude and dimensions.</summary>
            <returns>The geography point that was created.</returns>
            <param name="latitude">The latitude.</param>
            <param name="longitude">The longitude.</param>
            <param name="z">The z dimension.</param>
            <param name="m">The m dimension.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyPoint.Create(Microsoft.Spatial.CoordinateSystem,System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>Creates a geography point using the specified coordinate system, latitude, longitude and dimensions.</summary>
            <returns>The geography point that was created.</returns>
            <param name="coordinateSystem">The coordinate system to use.</param>
            <param name="latitude">The latitude.</param>
            <param name="longitude">The longitude.</param>
            <param name="z">The z dimension.</param>
            <param name="m">The m dimension.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyPoint.Equals(Microsoft.Spatial.GeographyPoint)">
            <summary>Determines whether this instance and another specified geography instance have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="other">The geography to compare to this instance.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyPoint.Equals(System.Object)">
            <summary>Determines whether this instance and the specified object have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="obj">The object to compare to this instance.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyPoint.GetHashCode">
            <summary>Gets the hash code.</summary>
            <returns>The hash code.</returns>
        </member>
        <member name="T:Microsoft.Spatial.GeographyPointImplementation">
            <summary>
            This class is an implementation of Geography point.
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeographyPointImplementation.latitude">
            <summary>
            Latitude
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeographyPointImplementation.longitude">
            <summary>
            Longitude
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeographyPointImplementation.z">
            <summary>
            Z
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeographyPointImplementation.m">
            <summary>
            M
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeographyPointImplementation.#ctor(Microsoft.Spatial.CoordinateSystem,Microsoft.Spatial.SpatialImplementation,System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Point constructor
            </summary>
            <param name="coordinateSystem">CoordinateSystem</param>
            <param name="creator">The implementation that created this instance.</param>
            <param name="latitude">latitude</param>
            <param name="longitude">longitude</param>
            <param name="zvalue">Z</param>
            <param name="mvalue">M</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyPointImplementation.#ctor(Microsoft.Spatial.CoordinateSystem,Microsoft.Spatial.SpatialImplementation)">
            <summary>
            Create a empty point
            </summary>
            <param name="coordinateSystem">CoordinateSystem</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="P:Microsoft.Spatial.GeographyPointImplementation.Latitude">
            <summary>
            Latitude
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.GeographyPointImplementation.Longitude">
            <summary>
            Longitude
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.GeographyPointImplementation.IsEmpty">
            <summary>
            Is Point Empty
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.GeographyPointImplementation.Z">
            <summary>
            Nullable Z
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.GeographyPointImplementation.M">
            <summary>
            Nullable M
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeographyPointImplementation.SendTo(Microsoft.Spatial.GeographyPipeline)">
            <summary>
            Sends the current spatial object to the given sink
            </summary>
            <param name="pipeline">The spatial pipeline</param>
        </member>
        <member name="T:Microsoft.Spatial.GeographyPolygon">
            <summary>Represents the geography polygon.</summary>
        </member>
        <member name="M:Microsoft.Spatial.GeographyPolygon.#ctor(Microsoft.Spatial.CoordinateSystem,Microsoft.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.Spatial.GeographyPolygon" /> class.</summary>
            <param name="coordinateSystem">The coordinate system of this instance.</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="P:Microsoft.Spatial.GeographyPolygon.Rings">
            <summary>Gets a collection of rings.</summary>
            <returns>A collection of rings.</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyPolygon.Equals(Microsoft.Spatial.GeographyPolygon)">
            <summary>Determines whether this instance and another specified geography instance have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="other">The geography to compare to this instance.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyPolygon.Equals(System.Object)">
            <summary>Determines whether this instance and the specified object have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="obj">The object to compare to this instance.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyPolygon.GetHashCode">
            <summary>Gets the hash code.</summary>
            <returns>The hash code.</returns>
        </member>
        <member name="T:Microsoft.Spatial.GeographyPolygonImplementation">
            <summary>
            Geography polygon
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeographyPolygonImplementation.rings">
            <summary>
            Rings
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeographyPolygonImplementation.#ctor(Microsoft.Spatial.CoordinateSystem,Microsoft.Spatial.SpatialImplementation,Microsoft.Spatial.GeographyLineString[])">
            <summary>
            Constructor
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <param name="creator">The implementation that created this instance.</param>
            <param name="rings">The rings of this polygon</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyPolygonImplementation.#ctor(Microsoft.Spatial.SpatialImplementation,Microsoft.Spatial.GeographyLineString[])">
            <summary>
            Constructor
            </summary>
            <param name="creator">The implementation that created this instance.</param>
            <param name="rings">The rings of this polygon</param>
        </member>
        <member name="P:Microsoft.Spatial.GeographyPolygonImplementation.IsEmpty">
            <summary>
            Is Polygon Empty
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.GeographyPolygonImplementation.Rings">
            <summary>
            Set of rings
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeographyPolygonImplementation.SendTo(Microsoft.Spatial.GeographyPipeline)">
            <summary>
            Sends the current spatial object to the given sink
            </summary>
            <param name="pipeline">The spatial pipeline</param>
        </member>
        <member name="T:Microsoft.Spatial.GeographyPosition">
            <summary>
            Represents one position in the Geographyal coordinate system
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeographyPosition.latitude">
            <summary>latitude portion of position</summary>
        </member>
        <member name="F:Microsoft.Spatial.GeographyPosition.longitude">
            <summary>longitude portion of position</summary>
        </member>
        <member name="F:Microsoft.Spatial.GeographyPosition.m">
            <summary>arbitrary measure associated with a position</summary>
        </member>
        <member name="F:Microsoft.Spatial.GeographyPosition.z">
            <summary>altitude portion of position</summary>
        </member>
        <member name="M:Microsoft.Spatial.GeographyPosition.#ctor(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>Creates a new instance of the <see cref="T:Microsoft.Spatial.GeographyPosition" /> class from components.</summary>
            <param name="latitude">The latitude portion of a position.</param>
            <param name="longitude">The longitude portion of a position.</param>
            <param name="z">The altitude portion of a position.</param>
            <param name="m">The arbitrary measure associated with a position.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyPosition.#ctor(System.Double,System.Double)">
            <summary>Creates a new instance of the <see cref="T:Microsoft.Spatial.GeographyPosition" /> class from components.</summary>
            <param name="latitude">The latitude portion of a position.</param>
            <param name="longitude">The longitude portion of a position.</param>
        </member>
        <member name="P:Microsoft.Spatial.GeographyPosition.Latitude">
            <summary>Gets the latitude portion of a position.</summary>
            <returns>The latitude portion of a position.</returns>
        </member>
        <member name="P:Microsoft.Spatial.GeographyPosition.Longitude">
            <summary>Gets the longitude portion of a position.</summary>
            <returns>The longitude portion of a position.</returns>
        </member>
        <member name="P:Microsoft.Spatial.GeographyPosition.M">
            <summary>Gets the arbitrary measure associated with a position.</summary>
            <returns>The arbitrary measure associated with a position.</returns>
        </member>
        <member name="P:Microsoft.Spatial.GeographyPosition.Z">
            <summary>Gets the altitude portion of a position.</summary>
            <returns>The altitude portion of a position.</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyPosition.op_Equality(Microsoft.Spatial.GeographyPosition,Microsoft.Spatial.GeographyPosition)">
            <summary>Performs equality comparison.</summary>
            <returns>true if each pair of coordinates is equal; otherwise, false.</returns>
            <param name="left">The first position.</param>
            <param name="right">The second position.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyPosition.op_Inequality(Microsoft.Spatial.GeographyPosition,Microsoft.Spatial.GeographyPosition)">
            <summary>Performs inequality comparison.</summary>
            <returns>true if left is not equal to right; otherwise, false.</returns>
            <param name="left">The first position.</param>
            <param name="right">The other position.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyPosition.Equals(System.Object)">
            <summary>Performs equality comparison on an object.</summary>
            <returns>true if each pair of coordinates is equal; otherwise, false.</returns>
            <param name="obj">The object for comparison.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyPosition.Equals(Microsoft.Spatial.GeographyPosition)">
            <summary>Performs equality comparison on a spatial geographic position.</summary>
            <returns>true if each pair of coordinates is equal; otherwise, false.</returns>
            <param name="other">The other position.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeographyPosition.GetHashCode">
            <summary>Computes a hash code.</summary>
            <returns>A hash code.</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeographyPosition.ToString">
            <summary>Formats this instance to a readable string.</summary>
            <returns>The string representation of this instance.</returns>
        </member>
        <member name="T:Microsoft.Spatial.GeographySurface">
            <summary>Represents the geography surface.</summary>
        </member>
        <member name="M:Microsoft.Spatial.GeographySurface.#ctor(Microsoft.Spatial.CoordinateSystem,Microsoft.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.Spatial.GeographySurface" /> class.</summary>
            <param name="coordinateSystem">The coordinate system of this instance.</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="T:Microsoft.Spatial.GeoJsonConstants">
            <summary>
            Constants for the GeoJSON format
            See http://geojson.org/geojson-spec.html for full details on GeoJson format.
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeoJsonConstants.TypeMemberName">
            <summary>
            Name of the type member that identifies the spatial type.
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeoJsonConstants.TypeMemberValuePoint">
            <summary>
            Value of the type member for Point values.
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeoJsonConstants.TypeMemberValueLineString">
            <summary>
            Value of the type member for LineString values.
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeoJsonConstants.TypeMemberValuePolygon">
            <summary>
            Value of the type member for Polygon values.
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeoJsonConstants.TypeMemberValueMultiPoint">
            <summary>
            Value of the type member for MultiPoint values.
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeoJsonConstants.TypeMemberValueMultiLineString">
            <summary>
            Value of the type member for MultiLineString values.
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeoJsonConstants.TypeMemberValueMultiPolygon">
            <summary>
            Value of the type member for MultiPolygon values.
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeoJsonConstants.TypeMemberValueGeometryCollection">
            <summary>
            Value of the type member for GeometryCollection values.
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeoJsonConstants.CoordinatesMemberName">
            <summary>
            Name of the coordinates member that contains the spatial data.
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeoJsonConstants.GeometriesMemberName">
            <summary>
            Name of the geometries member that contains the spatial data.
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeoJsonConstants.CrsMemberName">
            <summary>
            Name of the crs member that contains the coordinate reference system details.
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeoJsonConstants.CrsTypeMemberValue">
            <summary>
            Value of the type member inside of the crs object.
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeoJsonConstants.CrsNameMemberName">
            <summary>
            Name of the name member inside of the properties member in the crs object.
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeoJsonConstants.CrsPropertiesMemberName">
            <summary>
            Name of the properties member inside of the crs object.
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeoJsonConstants.CrsValuePrefix">
            <summary>
            Prefix to use when specifying the coordinate reference system inside the crs object.
            </summary>
        </member>
        <member name="T:Microsoft.Spatial.GeoJsonMember">
            <summary>
            Defines the members that may be found in a GeoJSON object.
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeoJsonMember.Type">
            <summary>
            "type" member in a GeoJSON object.
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeoJsonMember.Coordinates">
            <summary>
            "coordinates" member in GeoJSON object.
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeoJsonMember.Geometries">
            <summary>
            "geometries" member in GeoJSON object.
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeoJsonMember.Crs">
            <summary>
            "crs" member in GeoJSON object.
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeoJsonMember.Properties">
            <summary>
            'properties' member in GeoJSON object
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeoJsonMember.Name">
            <summary>
            'name' member in GeoJSON object
            </summary>
        </member>
        <member name="T:Microsoft.Spatial.GeoJsonObjectFormatter">
            <summary>Represents a formatter for Json object.</summary>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonObjectFormatter.Create">
            <summary>Creates the implementation of the formatter.</summary>
            <returns>The created <see cref="T:Microsoft.Spatial.GeoJsonObjectFormatter" /> implementation.</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonObjectFormatter.Read``1(System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>Reads from the source.</summary>
            <returns>The <see cref="T:Microsoft.Spatial.GeoJsonObjectFormatter" /> object that was read.</returns>
            <param name="source">The source json object.</param>
            <typeparam name="T">The spatial type to read.</typeparam>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonObjectFormatter.Write(Microsoft.Spatial.ISpatial)">
            <summary>Converts spatial value to a Json object.</summary>
            <returns>The json object.</returns>
            <param name="value">The spatial value.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonObjectFormatter.CreateWriter(Microsoft.Spatial.IGeoJsonWriter)">
            <summary> Creates the writerStream. </summary>
            <returns>The writerStream that was created.</returns>
            <param name="writer">The actual stream to write Json.</param>
        </member>
        <member name="T:Microsoft.Spatial.GeoJsonObjectFormatterImplementation">
            <summary>
            Formatter for Json Object
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeoJsonObjectFormatterImplementation.creator">
            <summary>
            The implementation that created this instance.
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeoJsonObjectFormatterImplementation.builder">
            <summary>
            Spatial builder
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeoJsonObjectFormatterImplementation.parsePipeline">
            <summary>
            The parse pipeline
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonObjectFormatterImplementation.#ctor(Microsoft.Spatial.SpatialImplementation)">
            <summary>
            Constructor
            </summary>
            <param name="creator">The SpatialImplementation that created this instance</param>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonObjectFormatterImplementation.Read``1(System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            Read from the source
            </summary>
            <typeparam name="T">The spatial type to read</typeparam>
            <param name="source">The source json object</param>
            <returns>The read instance</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonObjectFormatterImplementation.Write(Microsoft.Spatial.ISpatial)">
            <summary>
            Convert spatial value to a Json Object
            </summary>
            <param name="value">The spatial value</param>
            <returns>The json object</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonObjectFormatterImplementation.CreateWriter(Microsoft.Spatial.IGeoJsonWriter)">
            <summary> Creates the writerStream. </summary>
            <returns>The writerStream that was created.</returns>
            <param name="writer">The actual stream to write Json.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonObjectFormatterImplementation.EnsureParsePipeline">
            <summary>
            Initialize the pipeline
            </summary>
        </member>
        <member name="T:Microsoft.Spatial.GeoJsonObjectReader">
            <summary>
            The spatial reader that can read from a pre parsed GeoJson payload
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonObjectReader.#ctor(Microsoft.Spatial.SpatialPipeline)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Spatial.GeoJsonObjectReader"/> class.
            </summary>
            <param name="destination">The pipeline.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonObjectReader.ReadGeographyImplementation(System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            Parses some serialized format that represents a geography value, passing the result down the pipeline.
            </summary>
            <param name="input">The jsonObject to read from.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonObjectReader.ReadGeometryImplementation(System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            Parses some serialized format that represents a geometry value, passing the result down the pipeline.
            </summary>
            <param name="input">The jsonObject to read from.</param>
        </member>
        <member name="T:Microsoft.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline">
            <summary>
            A common way to call Geography and Geometry pipeline apis from the structured Json
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline.pipeline">
            <summary>
            Pipeline to use for the output of the translation of the GeoJSON object into pipeline method calls.
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline.#ctor(Microsoft.Spatial.TypeWashedPipeline)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline"/> class.
            </summary>
            <param name="pipeline">Spatial pipeline that will receive the pipeline method calls.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline.SendToPipeline(System.Collections.Generic.IDictionary{System.String,System.Object},System.Boolean)">
            <summary>
            Translates a dictionary of parsed GeoJSON members and values into method calls on the spatial pipeline.
            </summary>
            <param name="members">Dictionary containing GeoJSON members and values.</param>
            <param name="requireSetCoordinates">Coordinate System must be set for this pipeline</param>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline.SendArrayOfArray(System.Collections.IEnumerable,System.Action{System.Collections.IEnumerable})">
            <summary>
            Iterates over an object array, verifies that each element in the array is another array, and calls a delegate on the contained array.
            </summary>
            <param name="array">Array to iterate over.</param>
            <param name="send">Delegate to invoke for each element once it has been validated to be an array.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline.ValueAsNullableDouble(System.Object)">
            <summary>
            Convert an object to a nullable double value.
            </summary>
            <param name="value">Object to convert.</param>
            <returns>If the specified element was null, returns null, otherwise returns the converted double value.</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline.ValueAsDouble(System.Object)">
            <summary>
            Convert an object to a non-null double value.
            </summary>
            <param name="value">Object to convert.</param>
            <returns>Converted double value.</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline.ValueAsJsonArray(System.Object)">
            <summary>
            Values as json array.
            </summary>
            <param name="value">The value.</param>
            <returns>The value cast as a json array.</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline.ValueAsJsonObject(System.Object)">
            <summary>
            Values as json object.
            </summary>
            <param name="value">The value.</param>
            <returns>The value cast as IDictionary&lt;string, object&gt;</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline.ValueAsString(System.String,System.Object)">
            <summary>
            Values as string.
            </summary>
            <param name="propertyName">Name of the property.</param>
            <param name="value">The value.</param>
            <returns>The value cast as a string.</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline.GetSpatialType(System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            Get the type member value from the specified GeoJSON member dictionary.
            </summary>
            <param name="geoJsonObject">Dictionary containing the GeoJSON members and their values.</param>
            <returns>SpatialType for the GeoJSON object.</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline.TryGetCoordinateSystemId(System.Collections.Generic.IDictionary{System.String,System.Object},System.Nullable{System.Int32}@)">
            <summary>
            Tries to get a coordinate system id from the geo json object's 'crs' property
            </summary>
            <param name="geoJsonObject">The geo json object.</param>
            <param name="epsgId">The coordinate system id.</param>
            <returns>True if the object had a coordinate system</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline.GetCoordinateSystemIdFromCrs(System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            Gets the coordinate system ID from a representation of the CRS object
            </summary>
            <param name="crsJsonObject">The parsed representation of the CRS object.</param>
            <returns>The coordinate system ID</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline.GetMemberValueAsJsonArray(System.Collections.Generic.IDictionary{System.String,System.Object},System.String)">
            <summary>
            Get the designated member value from the specified GeoJSON member dictionary.
            </summary>
            <param name="geoJsonObject">Dictionary containing the GeoJSON members and their values.</param>
            <param name="memberName">The member's tag name</param>
            <returns>Member value for the GeoJSON object.</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline.EnumerableAny(System.Collections.IEnumerable)">
            <summary>
            This method assumes a non forward only enumerable
            </summary>
            <param name="enumerable">The enumerable to check</param>
            <returns>true if there is at least one element</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline.ReadTypeName(System.String)">
            <summary>
            Reads GeoJson 'type' value and maps it a valid SpatialType.
            </summary>
            <param name="typeName">The GeoJson standard type name</param>
            <returns>SpatialType corresponding to the GeoJson type name.</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline.SendShape(Microsoft.Spatial.SpatialType,System.Collections.IEnumerable)">
            <summary>
            Sends a shape to the spatial pipeline.
            </summary>
            <param name="spatialType">SpatialType of the shape.</param>
            <param name="contentMembers">Content member for the shape</param>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline.SendCoordinates(Microsoft.Spatial.SpatialType,System.Collections.IEnumerable)">
            <summary>
            Translates the coordinates member value into method calls on the spatial pipeline.
            </summary>
            <param name="spatialType">SpatialType of the GeoJSON object.</param>
            <param name="contentMembers">Coordinates value of the GeoJSON object, or inner geometries for collection</param>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline.SendPoint(System.Collections.IEnumerable)">
            <summary>
            Translates the coordinates member value of a Point object into method calls on the spatial pipeline.
            </summary>
            <param name="coordinates">Parsed coordinates array.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline.SendLineString(System.Collections.IEnumerable)">
            <summary>
            Translates the coordinates member value of a LineString object into method calls on the spatial pipeline.
            </summary>
            <param name="coordinates">Parsed coordinates array.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline.SendPolygon(System.Collections.IEnumerable)">
            <summary>
            Translates the coordinates member value of a Polygon object into method calls on the spatial pipeline.
            </summary>
            <param name="coordinates">Parsed coordinates array.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline.SendMultiShape(Microsoft.Spatial.SpatialType,System.Collections.IEnumerable)">
            <summary>
            Translates the coordinates member value of a MultiPoint, MultiLineString, or MultiPolygon object into method calls on the spatial pipeline.
            </summary>
            <param name="containedSpatialType">Type of the shape contained in the Multi shape.</param>
            <param name="coordinates">Parsed coordinates array.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline.SendPositionArray(System.Collections.IEnumerable)">
            <summary>
            Translates an array of positions into method calls on the spatial pipeline.
            </summary>
            <param name="positionArray">List containing the positions.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline.SendPosition(System.Collections.IEnumerable,System.Boolean)">
            <summary>
            Translates an individual position into a method call on the spatial pipeline.
            </summary>
            <param name="positionElements">List containing elements of the position.</param>
            <param name="first">True if the position is the first one being written to a figure, otherwise false.</param>
        </member>
        <member name="T:Microsoft.Spatial.GeoJsonObjectWriter">
            <summary>
            Convert Spatial objects into json writer
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeoJsonObjectWriter.containers">
            <summary>
            Stack of json objects
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeoJsonObjectWriter.currentPropertyName">
            <summary>
            Buffered key of the current name-value pair
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeoJsonObjectWriter.lastCompletedObject">
            <summary>
            Stores the last object fully serialized
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.GeoJsonObjectWriter.JsonObject">
            <summary>
            Get the top level json object
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.GeoJsonObjectWriter.IsArray">
            <summary>
            Test if the current container is an array
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonObjectWriter.StartObjectScope">
            <summary>
            Start a new json object scope
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonObjectWriter.StartArrayScope">
            <summary>
            Start a new json array scope
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonObjectWriter.AddPropertyName(System.String)">
            <summary>
            Add a property name to the current json object
            </summary>
            <param name="name">The name to add</param>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonObjectWriter.AddValue(System.String)">
            <summary>
            Add a value to the current json scope
            </summary>
            <param name="value">The value to add</param>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonObjectWriter.AddValue(System.Double)">
            <summary>
            Add a value to the current json scope
            </summary>
            <param name="value">The value to add</param>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonObjectWriter.EndArrayScope">
            <summary>
            End the current json array scope
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonObjectWriter.EndObjectScope">
            <summary>
            End the current json object scope
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonObjectWriter.AddToScope(System.Object)">
            <summary>
            Add an json object to the current scope
            </summary>
            <param name="jsonObject">The json object</param>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonObjectWriter.GetAndClearCurrentPropertyName">
            <summary>
            Return the current property name, and clear the buffer
            </summary>
            <returns>The current property name</returns>
            <remarks>
            When inserting to a dictionary, the name-value pair comes across multiple pipeline calls
            Therefore we need to buffer the name part and wait for the value part.
            You can get into an incorrect state (caught by asserts) if you add a property name without
            using it immediately next.
            </remarks>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonObjectWriter.AsList">
            <summary>
            Access the current container as a List
            </summary>
            <returns>The current container as list</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonObjectWriter.AsDictionary">
            <summary>
            Access the current container as a Dictionary
            </summary>
            <returns>The current container as dictionary</returns>
        </member>
        <member name="T:Microsoft.Spatial.GeoJsonWriterBase">
            <summary>
            Base Writer for GeoJson
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeoJsonWriterBase.stack">
            <summary>
            Stack to track the current type being written.
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeoJsonWriterBase.currentCoordinateSystem">
            <summary>
            CoordinateSystem for the types being written.
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeoJsonWriterBase.figureDrawn">
            <summary>
            Figure added in current shape
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonWriterBase.#ctor">
            <summary>
            Creates a new instance of the GeoJsonWriter.
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.GeoJsonWriterBase.ShapeHasObjectScope">
            <summary>
            True if the shape should write start and end object scope, otherwise false.
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.GeoJsonWriterBase.IsTopLevel">
            <summary>
            True if the shape is not a child of another shape.
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.GeoJsonWriterBase.FigureHasArrayScope">
            <summary>
            True if the shape should write start and end object scope, otherwise false.
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonWriterBase.OnLineTo(Microsoft.Spatial.GeographyPosition)">
            <summary>
            Draw a point in the specified coordinate
            </summary>
            <param name="position">Next position</param>
            <returns>
            The position to be passed down the pipeline
            </returns>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonWriterBase.OnLineTo(Microsoft.Spatial.GeometryPosition)">
            <summary>
            Draw a point in the specified coordinate
            </summary>
            <param name="position">Next position</param>
            <returns>
            The position to be passed down the pipeline
            </returns>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonWriterBase.OnBeginGeography(Microsoft.Spatial.SpatialType)">
            <summary>
            Begin drawing a spatial object
            </summary>
            <param name="type">The spatial type of the object</param>
            <returns>
            The type to be passed down the pipeline
            </returns>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonWriterBase.OnBeginGeometry(Microsoft.Spatial.SpatialType)">
            <summary>
            Begin drawing a spatial object
            </summary>
            <param name="type">The spatial type of the object</param>
            <returns>
            The type to be passed down the pipeline
            </returns>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonWriterBase.OnBeginFigure(Microsoft.Spatial.GeographyPosition)">
            <summary>
            Begin drawing a figure
            </summary>
            <param name="position">Next position</param>
            <returns>The position to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonWriterBase.OnBeginFigure(Microsoft.Spatial.GeometryPosition)">
            <summary>
            Begin drawing a figure
            </summary>
            <param name="position">Next position</param>
            <returns>The position to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonWriterBase.OnEndFigure">
            <summary>
            Ends the current figure
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonWriterBase.OnEndGeography">
            <summary>
            Ends the current spatial object
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonWriterBase.OnEndGeometry">
            <summary>
            Ends the current spatial object
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonWriterBase.OnSetCoordinateSystem(Microsoft.Spatial.CoordinateSystem)">
            <summary>
            Set the coordinate system
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <returns>
            the coordinate system to be passed down the pipeline
            </returns>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonWriterBase.OnReset">
            <summary>
            Setup the pipeline for reuse
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonWriterBase.AddPropertyName(System.String)">
            <summary>
            Add a property name to the current json object
            </summary>
            <param name="name">The name to add</param>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonWriterBase.AddValue(System.String)">
            <summary>
            Add a value to the current json scope
            </summary>
            <param name="value">The value to add</param>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonWriterBase.AddValue(System.Double)">
            <summary>
            Add a value to the current json scope
            </summary>
            <param name="value">The value to add</param>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonWriterBase.StartObjectScope">
            <summary>
            Start a new json object scope
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonWriterBase.StartArrayScope">
            <summary>
            Start a new json array scope
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonWriterBase.EndObjectScope">
            <summary>
            End the current json object scope
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonWriterBase.EndArrayScope">
            <summary>
            End the current json array scope
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonWriterBase.Reset">
            <summary>
            Setup the pipeline for reuse
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonWriterBase.GetSpatialTypeName(Microsoft.Spatial.SpatialType)">
            <summary>
            Gets the GeoJson type name to use when writing the specified type.
            </summary>
            <param name="type">SpatialType being written.</param>
            <returns>GeoJson type name corresponding to the specified <paramref name="type"/>.</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonWriterBase.GetDataName(Microsoft.Spatial.SpatialType)">
            <summary>
            Gets the name of the GeoJson member to use when writing the body of the spatial object.
            </summary>
            <param name="type">SpatialType being written.</param>
            <returns>Name of the GeoJson member to use when writing the body of the spatial object.</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonWriterBase.TypeHasArrayScope(Microsoft.Spatial.SpatialType)">
            <summary>
            Whether or not the specified type wraps its data in an outer array.
            </summary>
            <param name="type">SpatialType being written.</param>
            <returns>True if the type uses an outer array, otherwise false.</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonWriterBase.SetCoordinateSystem(Microsoft.Spatial.CoordinateSystem)">
            <summary>
            Sets the CoordinateSystem for Geography and Geometry shapes.
            </summary>
            <param name="coordinateSystem">CoordinateSystem value to set.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonWriterBase.BeginShape(Microsoft.Spatial.SpatialType,Microsoft.Spatial.CoordinateSystem)">
            <summary>
            Start writing a Geography or Geometry shape.
            </summary>
            <param name="type">SpatialType to use when writing the shape.</param>
            <param name="defaultCoordinateSystem">Default CoordinateSystem to use if SetCoordinateSystem is never called on this shape.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonWriterBase.WriteShapeHeader(Microsoft.Spatial.SpatialType)">
            <summary>
            Write the type header information for a shape.
            </summary>
            <param name="type">SpatialType being written.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonWriterBase.BeginFigure">
            <summary>
            Start writing a figure in a Geography or Geometry shape.
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonWriterBase.WriteControlPoint(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Write a position in a Geography or Geometry figure.
            </summary>
            <param name="first">First (X/Longitude) Coordinate</param>
            <param name="second">Second (Y/Latitude) Coordinate</param>
            <param name="z">Z Coordinate</param>
            <param name="m">M Coordinate</param>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonWriterBase.EndFigure">
            <summary>
            Ends a Geography or Geometry figure.
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonWriterBase.EndShape">
            <summary>
            Ends a Geography or Geometry shape.
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeoJsonWriterBase.WriteCrs">
            <summary>
            Writes the coordinate reference system footer for the GeoJson object.
            </summary>
        </member>
        <member name="T:Microsoft.Spatial.Geometry">
            <summary>Represents the base class of geography shapes.</summary>
        </member>
        <member name="F:Microsoft.Spatial.Geometry.creator">
            <summary>
            The implementation that created this instance.
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.Geometry.coordinateSystem">
            <summary>
            The CoordinateSystem of this geometry
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.Geometry.#ctor(Microsoft.Spatial.CoordinateSystem,Microsoft.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.Spatial.Geometry" /> class.</summary>
            <param name="coordinateSystem">The coordinate system of this instance.</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="P:Microsoft.Spatial.Geometry.CoordinateSystem">
            <summary>Gets the SRID of this instance of geometry.</summary>
            <returns>The SRID of this instance of geometry.</returns>
        </member>
        <member name="P:Microsoft.Spatial.Geometry.IsEmpty">
            <summary>Gets a value that indicates whether geometry is empty.</summary>
            <returns>true if the geometry is empty; otherwise, false.</returns>
        </member>
        <member name="P:Microsoft.Spatial.Geometry.Creator">
            <summary>
            Gets the implementation that created this instance.
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.Geometry.SendTo(Microsoft.Spatial.GeometryPipeline)">
            <summary>Sends the current spatial object to the given pipeline.</summary>
            <param name="chain">The spatial pipeline.</param>
        </member>
        <member name="M:Microsoft.Spatial.Geometry.BaseEquals(Microsoft.Spatial.Geometry)">
            <summary>
            Check for basic equality due to emptiness, nullness, referential equality and difference in coordinate system
            </summary>
            <param name="other">The other geography</param>
            <returns>Boolean value indicating equality, or null to indicate inconclusion</returns>
        </member>
        <member name="T:Microsoft.Spatial.GeometryBuilderImplementation">
            <summary>
            Builder for Geometry types
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeometryBuilderImplementation.builder">
            <summary>
            The tree builder
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeometryBuilderImplementation.#ctor(Microsoft.Spatial.SpatialImplementation)">
            <summary>
            Constructor
            </summary>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="E:Microsoft.Spatial.GeometryBuilderImplementation.ProduceGeometry">
            <summary>
            Fires when the provider constructs a geometry object.
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.GeometryBuilderImplementation.ConstructedGeometry">
            <summary>
            Constructed Geography
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeometryBuilderImplementation.LineTo(Microsoft.Spatial.GeometryPosition)">
            <summary>
            Draw a point in the specified coordinate
            </summary>
            <param name="position">Next position</param>
        </member>
        <member name="M:Microsoft.Spatial.GeometryBuilderImplementation.BeginFigure(Microsoft.Spatial.GeometryPosition)">
            <summary>
            Begin drawing a figure
            </summary>
            <param name="position">Next position</param>
        </member>
        <member name="M:Microsoft.Spatial.GeometryBuilderImplementation.BeginGeometry(Microsoft.Spatial.SpatialType)">
            <summary>
            Begin drawing a spatial object
            </summary>
            <param name="type">The spatial type of the object</param>
        </member>
        <member name="M:Microsoft.Spatial.GeometryBuilderImplementation.EndFigure">
            <summary>
            Ends the current figure
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeometryBuilderImplementation.EndGeometry">
            <summary>
            Ends the current spatial object
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeometryBuilderImplementation.Reset">
            <summary>
            Setup the pipeline for reuse
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeometryBuilderImplementation.SetCoordinateSystem(Microsoft.Spatial.CoordinateSystem)">
            <summary>
            Set the coordinate system
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
        </member>
        <member name="T:Microsoft.Spatial.GeometryBuilderImplementation.GeometryTreeBuilder">
            <summary>
            Geography Tree Builder
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeometryBuilderImplementation.GeometryTreeBuilder.creator">
            <summary>
            The implementation that created this instance.
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeometryBuilderImplementation.GeometryTreeBuilder.buildCoordinateSystem">
            <summary>
            CoordinateSystem for the building geography
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeometryBuilderImplementation.GeometryTreeBuilder.#ctor(Microsoft.Spatial.SpatialImplementation)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Spatial.GeometryBuilderImplementation.GeometryTreeBuilder"/> class.
            </summary>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeometryBuilderImplementation.GeometryTreeBuilder.SetCoordinateSystem(System.Nullable{System.Int32})">
            <summary>
            Set the coordinate system based on the given ID
            </summary>
            <param name="epsgId">The coordinate system ID to set. Null indicates the default should be used</param>
        </member>
        <member name="M:Microsoft.Spatial.GeometryBuilderImplementation.GeometryTreeBuilder.CreatePoint(System.Boolean,System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Create a new instance of Point
            </summary>
            <param name="isEmpty">Whether the point is empty</param>
            <param name="x">X</param>
            <param name="y">Y</param>
            <param name="z">Z</param>
            <param name="m">M</param>
            <returns>A new instance of point</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeometryBuilderImplementation.GeometryTreeBuilder.CreateShapeInstance(Microsoft.Spatial.SpatialType,System.Collections.Generic.IEnumerable{Microsoft.Spatial.Geometry})">
            <summary>
            Create a new instance of T
            </summary>
            <param name="type">The spatial type to create</param>
            <param name="spatialData">The arguments</param>
            <returns>A new instance of T</returns>
        </member>
        <member name="T:Microsoft.Spatial.GeometryCollection">
            <summary>Represents the geometry collection.</summary>
        </member>
        <member name="M:Microsoft.Spatial.GeometryCollection.#ctor(Microsoft.Spatial.CoordinateSystem,Microsoft.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.Spatial.GeometryCollection" /> class.</summary>
            <param name="coordinateSystem">The coordinate system of this instance.</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="P:Microsoft.Spatial.GeometryCollection.Geometries">
            <summary>Gets the geometry instances in this collection.</summary>
            <returns>A collection of geometries.</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeometryCollection.Equals(Microsoft.Spatial.GeometryCollection)">
            <summary>Determines whether this instance and another specified geometry instance have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="other">The geometry to compare to this instance.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeometryCollection.Equals(System.Object)">
            <summary>Determines whether this instance and the specified object have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="obj">The object to compare to this instance.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeometryCollection.GetHashCode">
            <summary>Gets the hash code.</summary>
            <returns>The hash code.</returns>
        </member>
        <member name="T:Microsoft.Spatial.GeometryCollectionImplementation">
            <summary>
            Geometry Collection
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeometryCollectionImplementation.geometryArray">
            <summary>
            Collection of Geometry instances
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeometryCollectionImplementation.#ctor(Microsoft.Spatial.CoordinateSystem,Microsoft.Spatial.SpatialImplementation,Microsoft.Spatial.Geometry[])">
            <summary>
            Constructor
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <param name="creator">The implementation that created this instance.</param>
            <param name="geometry">Collection of Geometry instances</param>
        </member>
        <member name="M:Microsoft.Spatial.GeometryCollectionImplementation.#ctor(Microsoft.Spatial.SpatialImplementation,Microsoft.Spatial.Geometry[])">
            <summary>
            Constructor
            </summary>
            <param name="creator">The implementation that created this instance.</param>
            <param name="geometry">Collection of Geometry instances</param>
        </member>
        <member name="P:Microsoft.Spatial.GeometryCollectionImplementation.IsEmpty">
            <summary>
            Is Geometry Collection Empty
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.GeometryCollectionImplementation.Geometries">
            <summary>
            Geographies
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeometryCollectionImplementation.SendTo(Microsoft.Spatial.GeometryPipeline)">
            <summary>
            Sends the current spatial object to the given pipeline
            </summary>
            <param name="pipeline">The spatial pipeline</param>
        </member>
        <member name="T:Microsoft.Spatial.GeometryCurve">
            <summary>Represents the geometry curve.</summary>
        </member>
        <member name="M:Microsoft.Spatial.GeometryCurve.#ctor(Microsoft.Spatial.CoordinateSystem,Microsoft.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.Spatial.GeometryCurve" /> class.</summary>
            <param name="coordinateSystem">The coordinate system of this instance.</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="T:Microsoft.Spatial.GeometryFactory">
            <summary>
            Geometry Factory
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeometryFactory.Point(Microsoft.Spatial.CoordinateSystem,System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Create a Geometry Point
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <param name="x">The X value</param>
            <param name="y">The Y value</param>
            <param name="z">The Z value</param>
            <param name="m">The M value</param>
            <returns>A Geometry Point Factory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeometryFactory.Point(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Create a Geometry Point
            </summary>
            <param name="x">The X value</param>
            <param name="y">The Y value</param>
            <param name="z">The Z value</param>
            <param name="m">The M value</param>
            <returns>A Geometry Point Factory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeometryFactory.Point(Microsoft.Spatial.CoordinateSystem,System.Double,System.Double)">
            <summary>
            Create a Geometry Point
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <param name="x">The X value</param>
            <param name="y">The Y value</param>
            <returns>A Geometry Point Factory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeometryFactory.Point(System.Double,System.Double)">
            <summary>
            Create a Geometry Point
            </summary>
            <param name="x">The X value</param>
            <param name="y">The Y value</param>
            <returns>A Geometry Point Factory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeometryFactory.Point(Microsoft.Spatial.CoordinateSystem)">
            <summary>
            Create a factory with an empty Geometry Point
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <returns>A Geometry Point Factory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeometryFactory.Point">
            <summary>
            Create a factory with an empty Geometry Point
            </summary>
            <returns>A Geometry Point Factory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeometryFactory.MultiPoint(Microsoft.Spatial.CoordinateSystem)">
            <summary>
            Create a Geometry MultiPoint
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <returns>A Geometry MultiPoint Factory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeometryFactory.MultiPoint">
            <summary>
            Create a Geometry MultiPoint
            </summary>
            <returns>A Geometry MultiPoint Factory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeometryFactory.LineString(Microsoft.Spatial.CoordinateSystem,System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Create a Geometry LineString with a starting position
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <param name="x">The X value</param>
            <param name="y">The Y value</param>
            <param name="z">The Z value</param>
            <param name="m">The M value</param>
            <returns>A Geometry LineString Factory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeometryFactory.LineString(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Create a Geometry LineString with a starting position
            </summary>
            <param name="x">The X value</param>
            <param name="y">The Y value</param>
            <param name="z">The Z value</param>
            <param name="m">The M value</param>
            <returns>A Geometry LineString Factory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeometryFactory.LineString(Microsoft.Spatial.CoordinateSystem,System.Double,System.Double)">
            <summary>
            Create a Geometry LineString with a starting position
            </summary>
            <param name="coordinateSystem">The coordinate system</param>
            <param name="x">The X value</param>
            <param name="y">The Y value</param>
            <returns>A Geometry LineString Factory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeometryFactory.LineString(System.Double,System.Double)">
            <summary>
            Create a Geometry LineString with a starting position
            </summary>
            <param name="x">The X value</param>
            <param name="y">The Y value</param>
            <returns>A Geometry LineString Factory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeometryFactory.LineString(Microsoft.Spatial.CoordinateSystem)">
            <summary>
            Create an empty Geometry LineString
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <returns>A Geometry LineString Factory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeometryFactory.LineString">
            <summary>
            Create an empty Geometry LineString
            </summary>
            <returns>A Geometry LineString Factory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeometryFactory.MultiLineString(Microsoft.Spatial.CoordinateSystem)">
            <summary>
            Create a Geometry MultiLineString
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <returns>A Geometry MultiLineString Factory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeometryFactory.MultiLineString">
            <summary>
            Create a Geometry MultiLineString
            </summary>
            <returns>A Geometry MultiLineString Factory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeometryFactory.Polygon(Microsoft.Spatial.CoordinateSystem)">
            <summary>
            Create a Geometry Polygon
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <returns>A Geometry Polygon Factory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeometryFactory.Polygon">
            <summary>
            Create a Geometry Polygon
            </summary>
            <returns>A Geometry Polygon Factory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeometryFactory.MultiPolygon(Microsoft.Spatial.CoordinateSystem)">
            <summary>
            Create a Geometry MultiPolygon
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <returns>A Geometry MultiPolygon Factory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeometryFactory.MultiPolygon">
            <summary>
            Create a Geometry MultiPolygon
            </summary>
            <returns>A Geometry MultiPolygon Factory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeometryFactory.Collection(Microsoft.Spatial.CoordinateSystem)">
            <summary>
            Create a Geometry Collection
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <returns>A Geometry Collection Factory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeometryFactory.Collection">
            <summary>
            Create a Geometry Collection
            </summary>
            <returns>A Geometry Collection Factory</returns>
        </member>
        <member name="T:Microsoft.Spatial.GeometryFactory`1">
            <summary>
            Geometry Spatial Factory
            </summary>
            <typeparam name="T">The target type</typeparam>
        </member>
        <member name="F:Microsoft.Spatial.GeometryFactory`1.provider">
            <summary>
            The provider of the built type
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeometryFactory`1.buildChain">
            <summary>
            The chain to build through
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeometryFactory`1.#ctor(Microsoft.Spatial.CoordinateSystem)">
            <summary>
            Initializes a new instance of the GeometryFactory class
            </summary>
            <param name="coordinateSystem">The coordinate system</param>
        </member>
        <member name="M:Microsoft.Spatial.GeometryFactory`1.op_Implicit(Microsoft.Spatial.GeometryFactory{`0})~`0">
            <summary>
            Cast a factory to the target type
            </summary>
            <param name="factory">The factory</param>
            <returns>The built instance of the target type</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeometryFactory`1.Point(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Start a new Point
            </summary>
            <param name="x">The X value</param>
            <param name="y">The Y value</param>
            <param name="z">The Z value</param>
            <param name="m">The M value</param>
            <returns>The current instance of GeometryFactory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeometryFactory`1.Point(System.Double,System.Double)">
            <summary>
            Start a new Point
            </summary>
            <param name="x">The X value</param>
            <param name="y">The Y value</param>
            <returns>The current instance of GeometryFactory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeometryFactory`1.Point">
            <summary>
            Start a new empty Point
            </summary>
            <returns>The current instance of GeometryFactory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeometryFactory`1.LineString(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Start a new LineString
            </summary>
            <param name="x">The X value</param>
            <param name="y">The Y value</param>
            <param name="z">The Z value</param>
            <param name="m">The M value</param>
            <returns>The current instance of GeometryFactory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeometryFactory`1.LineString(System.Double,System.Double)">
            <summary>
            Start a new LineString
            </summary>
            <param name="x">The X value</param>
            <param name="y">The Y value</param>
            <returns>The current instance of GeometryFactory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeometryFactory`1.LineString">
            <summary>
            Start a new empty LineString
            </summary>
            <returns>The current instance of GeometryFactory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeometryFactory`1.Polygon">
            <summary>
            Start a new Polygon
            </summary>
            <returns>The current instance of GeometryFactory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeometryFactory`1.MultiPoint">
            <summary>
            Start a new MultiPoint
            </summary>
            <returns>The current instance of GeometryFactory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeometryFactory`1.MultiLineString">
            <summary>
            Start a new MultiLineString
            </summary>
            <returns>The current instance of GeometryFactory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeometryFactory`1.MultiPolygon">
            <summary>
            Start a new MultiPolygon
            </summary>
            <returns>The current instance of GeometryFactory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeometryFactory`1.Collection">
            <summary>
            Start a new Collection
            </summary>
            <returns>The current instance of GeometryFactory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeometryFactory`1.Ring(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Start a new Polygon Ring
            </summary>
            <param name="x">The X value</param>
            <param name="y">The Y value</param>
            <param name="z">The Z value</param>
            <param name="m">The M value</param>
            <returns>The current instance of GeometryFactory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeometryFactory`1.Ring(System.Double,System.Double)">
            <summary>
            Start a new Polygon Ring
            </summary>
            <param name="x">The X value</param>
            <param name="y">The Y value</param>
            <returns>The current instance of GeometryFactory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeometryFactory`1.LineTo(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Add a new point in the current line figure
            </summary>
            <param name="x">The X value</param>
            <param name="y">The Y value</param>
            <param name="z">The Z value</param>
            <param name="m">The M value</param>
            <returns>The current instance of GeometryFactory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeometryFactory`1.LineTo(System.Double,System.Double)">
            <summary>
            Add a new point in the current line figure
            </summary>
            <param name="x">The X value</param>
            <param name="y">The Y value</param>
            <returns>The current instance of GeometryFactory</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeometryFactory`1.Build">
            <summary>
            Finish the current Geometry
            </summary>
            <returns>The constructed instance</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeometryFactory`1.BeginGeo(Microsoft.Spatial.SpatialType)">
            <summary>
            Begin a new geometry
            </summary>
            <param name="type">The spatial type</param>
        </member>
        <member name="M:Microsoft.Spatial.GeometryFactory`1.BeginFigure(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Begin drawing a figure
            </summary>
            <param name="x">X or Latitude Coordinate</param>
            <param name="y">Y or Longitude Coordinate</param>
            <param name="z">Z Coordinate</param>
            <param name="m">M Coordinate</param>
        </member>
        <member name="M:Microsoft.Spatial.GeometryFactory`1.AddLine(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Draw a point in the specified coordinate
            </summary>
            <param name="x">X or Latitude Coordinate</param>
            <param name="y">Y or Longitude Coordinate</param>
            <param name="z">Z Coordinate</param>
            <param name="m">M Coordinate</param>
        </member>
        <member name="M:Microsoft.Spatial.GeometryFactory`1.EndFigure">
            <summary>
            Ends the figure set on the current node
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeometryFactory`1.EndGeo">
            <summary>
            Ends the current spatial object
            </summary>
        </member>
        <member name="T:Microsoft.Spatial.GeometryHelperMethods">
            <summary>
            Helper methods for Geometry types
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeometryHelperMethods.SendFigure(Microsoft.Spatial.GeometryLineString,Microsoft.Spatial.GeometryPipeline)">
            <summary>
            Sends the current spatial object to the given pipeline with a figure that represents this LineString
            </summary>
            <param name="GeometryLineString">GeometryLineString instance for which the figure needs to be drawn.</param>
            <param name="pipeline">The pipeline to populate to</param>
        </member>
        <member name="T:Microsoft.Spatial.GeometryLineString">
            <summary>Represents the geometry line string.</summary>
        </member>
        <member name="M:Microsoft.Spatial.GeometryLineString.#ctor(Microsoft.Spatial.CoordinateSystem,Microsoft.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.Spatial.GeometryLineString" /> class.</summary>
            <param name="coordinateSystem">The coordinate system of this instance.</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="P:Microsoft.Spatial.GeometryLineString.Points">
            <summary>Gets the point list.</summary>
            <returns>The point list.</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeometryLineString.Equals(Microsoft.Spatial.GeometryLineString)">
            <summary>Determines whether this instance and another specified geometry instance have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="other">The geometry to compare to this instance.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeometryLineString.Equals(System.Object)">
            <summary>Determines whether this instance and the specified object have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="obj">The object to compare to this instance.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeometryLineString.GetHashCode">
            <summary>Gets the hash code.</summary>
            <returns>The hash code.</returns>
        </member>
        <member name="T:Microsoft.Spatial.GeometryLineStringImplementation">
            <summary>
            Geometry Line String
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeometryLineStringImplementation.points">
            <summary>
            Points array
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeometryLineStringImplementation.#ctor(Microsoft.Spatial.CoordinateSystem,Microsoft.Spatial.SpatialImplementation,Microsoft.Spatial.GeometryPoint[])">
            <summary>
            Constructor
            </summary>
            <param name="coordinateSystem">CoordinateSystem</param>
            <param name="creator">The implementation that created this instance.</param>
            <param name="points">The point list</param>
        </member>
        <member name="M:Microsoft.Spatial.GeometryLineStringImplementation.#ctor(Microsoft.Spatial.SpatialImplementation,Microsoft.Spatial.GeometryPoint[])">
            <summary>
            Constructor
            </summary>
            <param name="creator">The implementation that created this instance.</param>
            <param name="points">The point list</param>
        </member>
        <member name="P:Microsoft.Spatial.GeometryLineStringImplementation.IsEmpty">
            <summary>
            Is LineString Empty
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.GeometryLineStringImplementation.Points">
            <summary>
            Point list
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeometryLineStringImplementation.SendTo(Microsoft.Spatial.GeometryPipeline)">
            <summary>
            Sends the current spatial object to the given pipeline
            </summary>
            <param name="pipeline">The spatial pipeline</param>
        </member>
        <member name="T:Microsoft.Spatial.GeometryMultiCurve">
            <summary>Represents the geometry multi-curve.</summary>
        </member>
        <member name="M:Microsoft.Spatial.GeometryMultiCurve.#ctor(Microsoft.Spatial.CoordinateSystem,Microsoft.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.Spatial.GeometryMultiCurve" /> class.</summary>
            <param name="coordinateSystem">The coordinate system of this instance.</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="T:Microsoft.Spatial.GeometryMultiLineString">
            <summary>Represents the geometry multi-line string.</summary>
        </member>
        <member name="M:Microsoft.Spatial.GeometryMultiLineString.#ctor(Microsoft.Spatial.CoordinateSystem,Microsoft.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.Spatial.GeometryMultiLineString" /> class.</summary>
            <param name="coordinateSystem">The coordinate system of this instance.</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="P:Microsoft.Spatial.GeometryMultiLineString.LineStrings">
            <summary>Gets a collection of line strings.</summary>
            <returns>A collection of line strings.</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeometryMultiLineString.Equals(Microsoft.Spatial.GeometryMultiLineString)">
            <summary>Determines whether this instance and another specified geometry instance have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="other">The geometry to compare to this instance.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeometryMultiLineString.Equals(System.Object)">
            <summary>Determines whether this instance and the specified object have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="obj">The object to compare to this instance.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeometryMultiLineString.GetHashCode">
            <summary>Gets the hash code.</summary>
            <returns>The hash code.</returns>
        </member>
        <member name="T:Microsoft.Spatial.GeometryMultiLineStringImplementation">
            <summary>
            Geometry Multi-LineString
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeometryMultiLineStringImplementation.lineStrings">
            <summary>
            Line Strings
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeometryMultiLineStringImplementation.#ctor(Microsoft.Spatial.CoordinateSystem,Microsoft.Spatial.SpatialImplementation,Microsoft.Spatial.GeometryLineString[])">
            <summary>
            Constructor
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <param name="creator">The implementation that created this instance.</param>
            <param name="lineStrings">Line Strings</param>
        </member>
        <member name="M:Microsoft.Spatial.GeometryMultiLineStringImplementation.#ctor(Microsoft.Spatial.SpatialImplementation,Microsoft.Spatial.GeometryLineString[])">
            <summary>
            Constructor
            </summary>
            <param name="creator">The implementation that created this instance.</param>
            <param name="lineStrings">Line Strings</param>
        </member>
        <member name="P:Microsoft.Spatial.GeometryMultiLineStringImplementation.IsEmpty">
            <summary>
            Is MultiLineString Empty
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.GeometryMultiLineStringImplementation.Geometries">
            <summary>
            Geometry
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.GeometryMultiLineStringImplementation.LineStrings">
            <summary>
            Line Strings
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeometryMultiLineStringImplementation.SendTo(Microsoft.Spatial.GeometryPipeline)">
            <summary>
            Sends the current spatial object to the given pipeline
            </summary>
            <param name="pipeline">The spatial pipeline</param>
        </member>
        <member name="T:Microsoft.Spatial.GeometryMultiPoint">
            <summary>Represents the geometry multi-point.</summary>
        </member>
        <member name="M:Microsoft.Spatial.GeometryMultiPoint.#ctor(Microsoft.Spatial.CoordinateSystem,Microsoft.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.Spatial.GeometryMultiPoint" /> class.</summary>
            <param name="coordinateSystem">The coordinate system of this instance.</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="P:Microsoft.Spatial.GeometryMultiPoint.Points">
            <summary>Gets a collection of points.</summary>
            <returns>A collection of points.</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeometryMultiPoint.Equals(Microsoft.Spatial.GeometryMultiPoint)">
            <summary>Determines whether this instance and another specified geometry instance have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="other">The geometry to compare to this instance.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeometryMultiPoint.Equals(System.Object)">
            <summary>Determines whether this instance and the specified object have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="obj">The object to compare to this instance.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeometryMultiPoint.GetHashCode">
            <summary>Gets the hash code.</summary>
            <returns>The hash code.</returns>
        </member>
        <member name="T:Microsoft.Spatial.GeometryMultiPointImplementation">
            <summary>
            Geometry Multi-Point
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeometryMultiPointImplementation.points">
            <summary>
            Points
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeometryMultiPointImplementation.#ctor(Microsoft.Spatial.CoordinateSystem,Microsoft.Spatial.SpatialImplementation,Microsoft.Spatial.GeometryPoint[])">
            <summary>
            Constructor
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <param name="creator">The implementation that created this instance.</param>
            <param name="points">Points</param>
        </member>
        <member name="M:Microsoft.Spatial.GeometryMultiPointImplementation.#ctor(Microsoft.Spatial.SpatialImplementation,Microsoft.Spatial.GeometryPoint[])">
            <summary>
            Constructor
            </summary>
            <param name="creator">The implementation that created this instance.</param>
            <param name="points">Points</param>
        </member>
        <member name="P:Microsoft.Spatial.GeometryMultiPointImplementation.IsEmpty">
            <summary>
            Is MultiPoint Empty
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.GeometryMultiPointImplementation.Geometries">
            <summary>
            Geometry
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.GeometryMultiPointImplementation.Points">
            <summary>
            Points
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeometryMultiPointImplementation.SendTo(Microsoft.Spatial.GeometryPipeline)">
            <summary>
            Sends the current spatial object to the given pipeline
            </summary>
            <param name="pipeline">The spatial pipeline</param>
        </member>
        <member name="T:Microsoft.Spatial.GeometryMultiPolygon">
            <summary>Represents the geometry multi-polygon.</summary>
        </member>
        <member name="M:Microsoft.Spatial.GeometryMultiPolygon.#ctor(Microsoft.Spatial.CoordinateSystem,Microsoft.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.Spatial.GeometryMultiPolygon" /> class.</summary>
            <param name="coordinateSystem">The coordinate system of this instance.</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="P:Microsoft.Spatial.GeometryMultiPolygon.Polygons">
            <summary>Gets a collection of polygons.</summary>
            <returns>A collection of polygons.</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeometryMultiPolygon.Equals(Microsoft.Spatial.GeometryMultiPolygon)">
            <summary>Determines whether this instance and another specified geometry instance have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="other">The geometry to compare to this instance.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeometryMultiPolygon.Equals(System.Object)">
            <summary>Determines whether this instance and the specified object have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="obj">The object to compare to this instance.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeometryMultiPolygon.GetHashCode">
            <summary>Gets the hash code.</summary>
            <returns>The hash code.</returns>
        </member>
        <member name="T:Microsoft.Spatial.GeometryMultiPolygonImplementation">
            <summary>
            Geometry Multi-Polygon
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeometryMultiPolygonImplementation.polygons">
            <summary>
            Polygons
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeometryMultiPolygonImplementation.#ctor(Microsoft.Spatial.CoordinateSystem,Microsoft.Spatial.SpatialImplementation,Microsoft.Spatial.GeometryPolygon[])">
            <summary>
            Constructor
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <param name="creator">The implementation that created this instance.</param>
            <param name="polygons">Polygons</param>
        </member>
        <member name="M:Microsoft.Spatial.GeometryMultiPolygonImplementation.#ctor(Microsoft.Spatial.SpatialImplementation,Microsoft.Spatial.GeometryPolygon[])">
            <summary>
            Constructor
            </summary>
            <param name="creator">The implementation that created this instance.</param>
            <param name="polygons">Polygons</param>
        </member>
        <member name="P:Microsoft.Spatial.GeometryMultiPolygonImplementation.IsEmpty">
            <summary>
            Is MultiPolygon Empty
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.GeometryMultiPolygonImplementation.Geometries">
            <summary>
            Geometry
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.GeometryMultiPolygonImplementation.Polygons">
            <summary>
            Polygons
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeometryMultiPolygonImplementation.SendTo(Microsoft.Spatial.GeometryPipeline)">
            <summary>
            Sends the current spatial object to the given pipeline
            </summary>
            <param name="pipeline">The spatial pipeline</param>
        </member>
        <member name="T:Microsoft.Spatial.GeometryMultiSurface">
            <summary>Represents the geometry multi-surface.</summary>
        </member>
        <member name="M:Microsoft.Spatial.GeometryMultiSurface.#ctor(Microsoft.Spatial.CoordinateSystem,Microsoft.Spatial.SpatialImplementation)">
            <summary>
            Constructor
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="T:Microsoft.Spatial.GeometryOperationsExtensions">
            <summary>
            Extension methods for the Geography operations
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeometryOperationsExtensions.Distance(Microsoft.Spatial.Geometry,Microsoft.Spatial.Geometry)">
            <summary>Determines the distance of the geometry.</summary>
            <returns>The operation result.</returns>
            <param name="operand1">The first operand.</param>
            <param name="operand2">The second operand.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeometryOperationsExtensions.Length(Microsoft.Spatial.Geometry)">
            <summary>Determines the Length of the geometry LineString.</summary>
            <returns>The operation result.</returns>
            <param name="operand">The LineString operand.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeometryOperationsExtensions.Intersects(Microsoft.Spatial.Geometry,Microsoft.Spatial.Geometry)">
            <summary>Determines if geometry point and polygon will intersect.</summary>
            <returns>The operation result.</returns>
            <param name="operand1">The first operand, point.</param>
            <param name="operand2">The second operand, polygon.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeometryOperationsExtensions.OperationsFor(Microsoft.Spatial.Geometry[])">
            <summary>
            Finds the ops instance registered for the operands.
            </summary>
            <param name="operands">The operands.</param>
            <returns>The ops value, or null if any operand is null</returns>
        </member>
        <member name="T:Microsoft.Spatial.GeometryPipeline">
            <summary>Represents the pipeline of geometry.</summary>
        </member>
        <member name="M:Microsoft.Spatial.GeometryPipeline.BeginGeometry(Microsoft.Spatial.SpatialType)">
            <summary>Begins drawing a spatial object.</summary>
            <param name="type">The spatial type of the object.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeometryPipeline.BeginFigure(Microsoft.Spatial.GeometryPosition)">
            <summary>Begins drawing a figure.</summary>
            <param name="position">The position of the figure.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeometryPipeline.LineTo(Microsoft.Spatial.GeometryPosition)">
            <summary>Draws a point in the specified coordinate.</summary>
            <param name="position">The position of the line.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeometryPipeline.EndFigure">
            <summary>Ends the current figure.</summary>
        </member>
        <member name="M:Microsoft.Spatial.GeometryPipeline.EndGeometry">
            <summary>Ends the current spatial object.</summary>
        </member>
        <member name="M:Microsoft.Spatial.GeometryPipeline.SetCoordinateSystem(Microsoft.Spatial.CoordinateSystem)">
            <summary>Sets the coordinate system.</summary>
            <param name="coordinateSystem">The coordinate system to set.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeometryPipeline.Reset">
            <summary>Resets the pipeline.</summary>
        </member>
        <member name="T:Microsoft.Spatial.GeometryPoint">
            <summary>Represents the Geometry Point.</summary>
        </member>
        <member name="M:Microsoft.Spatial.GeometryPoint.#ctor(Microsoft.Spatial.CoordinateSystem,Microsoft.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.Spatial.GeometryPoint" /> class. Empty Point constructor.</summary>
            <param name="coordinateSystem">The CoordinateSystem.</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="P:Microsoft.Spatial.GeometryPoint.X">
            <summary>Gets the Latitude.</summary>
            <returns>The Latitude.</returns>
        </member>
        <member name="P:Microsoft.Spatial.GeometryPoint.Y">
            <summary>Gets the Longitude.</summary>
            <returns>The Longitude.</returns>
        </member>
        <member name="P:Microsoft.Spatial.GeometryPoint.Z">
            <summary>Gets the Nullable Z.</summary>
            <returns>The Nullable Z.</returns>
            <remarks>Z is the altitude portion of position.</remarks>
        </member>
        <member name="P:Microsoft.Spatial.GeometryPoint.M">
            <summary>Gets the Nullable M.</summary>
            <returns>The Nullable M.</returns>
            <remarks>M is the arbitrary measure associated with a position.</remarks>
        </member>
        <member name="M:Microsoft.Spatial.GeometryPoint.Create(System.Double,System.Double)">
            <summary> Creates the specified latitude. </summary>
            <returns>The GeographyPoint that was created.</returns>
            <param name="x">The x dimension.</param>
            <param name="y">The y dimension.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeometryPoint.Create(System.Double,System.Double,System.Nullable{System.Double})">
            <summary> Creates the specified latitude. </summary>
            <returns>The GeographyPoint that was created.</returns>
            <param name="x">The x dimension.</param>
            <param name="y">The y dimension.</param>
            <param name="z">The z dimension.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeometryPoint.Create(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary> Creates the specified latitude. </summary>
            <returns>The GeographyPoint that was created.</returns>
            <param name="x">The x dimension.</param>
            <param name="y">The y dimension.</param>
            <param name="z">The z dimension.</param>
            <param name="m">The m dimension.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeometryPoint.Create(Microsoft.Spatial.CoordinateSystem,System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary> Creates the specified latitude. </summary>
            <returns>The GeographyPoint that was created.</returns>
            <param name="coordinateSystem">The coordinate system to use.</param>
            <param name="x">The x dimension.</param>
            <param name="y">The y dimension.</param>
            <param name="z">The z dimension.</param>
            <param name="m">The m dimension.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeometryPoint.Equals(Microsoft.Spatial.GeometryPoint)">
            <summary> Determines whether this instance and another specified geography instance have the same value.  </summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="other">The geography to compare to this instance.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeometryPoint.Equals(System.Object)">
            <summary> Determines whether this instance and another specified geography instance have the same value.  </summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="obj">The geography to compare to this instance.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeometryPoint.GetHashCode">
            <summary> Gets the Hashcode.</summary>
            <returns>The hashcode.</returns>
        </member>
        <member name="T:Microsoft.Spatial.GeometryPointImplementation">
            <summary>
            Geometry Point
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeometryPointImplementation.x">
            <summary>
            Latitude
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeometryPointImplementation.y">
            <summary>
            Longitude
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeometryPointImplementation.z">
            <summary>
            Z
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeometryPointImplementation.m">
            <summary>
            M
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeometryPointImplementation.#ctor(Microsoft.Spatial.CoordinateSystem,Microsoft.Spatial.SpatialImplementation)">
            <summary>
            Empty Point constructor
            </summary>
            <param name="coordinateSystem">CoordinateSystem</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeometryPointImplementation.#ctor(Microsoft.Spatial.CoordinateSystem,Microsoft.Spatial.SpatialImplementation,System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Point constructor
            </summary>
            <param name="coordinateSystem">CoordinateSystem</param>
            <param name="creator">The implementation that created this instance.</param>
            <param name="x">latitude</param>
            <param name="y">longitude</param>
            <param name="z">Z</param>
            <param name="m">M</param>
        </member>
        <member name="P:Microsoft.Spatial.GeometryPointImplementation.X">
            <summary>
            Latitude
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.GeometryPointImplementation.Y">
            <summary>
            Longitude
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.GeometryPointImplementation.IsEmpty">
            <summary>
            Is Point Empty
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.GeometryPointImplementation.Z">
            <summary>
            Nullable Z
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.GeometryPointImplementation.M">
            <summary>
            Nullable M
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeometryPointImplementation.SendTo(Microsoft.Spatial.GeometryPipeline)">
            <summary>
            Sends the current spatial object to the given pipeline
            </summary>
            <param name="pipeline">The spatial pipeline</param>
        </member>
        <member name="T:Microsoft.Spatial.GeometryPolygon">
            <summary>Represents the Geometry polygon.</summary>
        </member>
        <member name="M:Microsoft.Spatial.GeometryPolygon.#ctor(Microsoft.Spatial.CoordinateSystem,Microsoft.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.Spatial.GeometryPolygon" /> class.</summary>
            <param name="coordinateSystem">The CoordinateSystem.</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="P:Microsoft.Spatial.GeometryPolygon.Rings">
            <summary>Gets the set of rings.</summary>
        </member>
        <member name="M:Microsoft.Spatial.GeometryPolygon.Equals(Microsoft.Spatial.GeometryPolygon)">
            <summary> Determines whether this instance and another specified geography instance have the same value.  </summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="other">The geography to compare to this instance.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeometryPolygon.Equals(System.Object)">
            <summary> Determines whether this instance and another specified geography instance have the same value.  </summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="obj">The geography to compare to this instance.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeometryPolygon.GetHashCode">
            <summary>Indicates the Get Hashcode.</summary>
            <returns>The hashcode.</returns>
        </member>
        <member name="T:Microsoft.Spatial.GeometryPolygonImplementation">
            <summary>
            Geometry polygon
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeometryPolygonImplementation.rings">
            <summary>
            Rings
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeometryPolygonImplementation.#ctor(Microsoft.Spatial.CoordinateSystem,Microsoft.Spatial.SpatialImplementation,Microsoft.Spatial.GeometryLineString[])">
            <summary>
            Constructor
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <param name="creator">The implementation that created this instance.</param>
            <param name="rings">The rings of this polygon</param>
        </member>
        <member name="M:Microsoft.Spatial.GeometryPolygonImplementation.#ctor(Microsoft.Spatial.SpatialImplementation,Microsoft.Spatial.GeometryLineString[])">
            <summary>
            Constructor
            </summary>
            <param name="creator">The implementation that created this instance.</param>
            <param name="rings">The rings of this polygon</param>
        </member>
        <member name="P:Microsoft.Spatial.GeometryPolygonImplementation.IsEmpty">
            <summary>
            Is Polygon Empty
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.GeometryPolygonImplementation.Rings">
            <summary>
            Set of rings
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GeometryPolygonImplementation.SendTo(Microsoft.Spatial.GeometryPipeline)">
            <summary>
            Sends the current spatial object to the given pipeline
            </summary>
            <param name="pipeline">The spatial pipeline</param>
        </member>
        <member name="T:Microsoft.Spatial.GeometryPosition">
            <summary>
            Represents one position in the Geometry coordinate system
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GeometryPosition.m">
            <summary>arbitrary measure associated with a position</summary>
        </member>
        <member name="F:Microsoft.Spatial.GeometryPosition.x">
            <summary>x portion of position</summary>
        </member>
        <member name="F:Microsoft.Spatial.GeometryPosition.y">
            <summary>y portion of position</summary>
        </member>
        <member name="F:Microsoft.Spatial.GeometryPosition.z">
            <summary>altitude portion of position</summary>
        </member>
        <member name="M:Microsoft.Spatial.GeometryPosition.#ctor(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>Creates a new instance of the <see cref="T:Microsoft.Spatial.GeometryPosition" /> from components.</summary>
            <param name="x">The X portion of position.</param>
            <param name="y">The Y portion of position.</param>
            <param name="z">The altitude portion of position.</param>
            <param name="m">The arbitrary measure associated with a position.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeometryPosition.#ctor(System.Double,System.Double)">
            <summary>Creates a new instance of the <see cref="T:Microsoft.Spatial.GeometryPosition" /> from components.</summary>
            <param name="x">The X portion of position.</param>
            <param name="y">The Y portion of position.</param>
        </member>
        <member name="P:Microsoft.Spatial.GeometryPosition.M">
            <summary>Gets the arbitrary measure associated with a position.</summary>
            <returns>The arbitrary measure associated with a position.</returns>
        </member>
        <member name="P:Microsoft.Spatial.GeometryPosition.X">
            <summary>Gets the X portion of position.</summary>
            <returns>The X portion of position.</returns>
        </member>
        <member name="P:Microsoft.Spatial.GeometryPosition.Y">
            <summary>Gets the Y portion of position.</summary>
            <returns>The Y portion of position.</returns>
        </member>
        <member name="P:Microsoft.Spatial.GeometryPosition.Z">
            <summary>Gets the altitude portion of position.</summary>
            <returns>The altitude portion of position.</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeometryPosition.op_Equality(Microsoft.Spatial.GeometryPosition,Microsoft.Spatial.GeometryPosition)">
            <summary>Performs the equality comparison.</summary>
            <returns>true if each pair of coordinates is equal; otherwise, false.</returns>
            <param name="left">The first position.</param>
            <param name="right">The second position.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeometryPosition.op_Inequality(Microsoft.Spatial.GeometryPosition,Microsoft.Spatial.GeometryPosition)">
            <summary>Performs the inequality comparison.</summary>
            <returns>true if left is not equal to right; otherwise, false.</returns>
            <param name="left">The first position.</param>
            <param name="right">The other position.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeometryPosition.Equals(System.Object)">
            <summary>Performs the equality comparison on an object.</summary>
            <returns>true if each pair of coordinates is equal; otherwise, false.</returns>
            <param name="obj">The object for comparison.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeometryPosition.Equals(Microsoft.Spatial.GeometryPosition)">
            <summary>Performs the equality comparison on a spatial geometry position.</summary>
            <returns>true if each pair of coordinates is equal; otherwise, false.</returns>
            <param name="other">The other position.</param>
        </member>
        <member name="M:Microsoft.Spatial.GeometryPosition.GetHashCode">
            <summary>Computes a hash code.</summary>
            <returns>A hash code.</returns>
        </member>
        <member name="M:Microsoft.Spatial.GeometryPosition.ToString">
            <summary>Formats this instance to a readable string.</summary>
            <returns>The string representation of this instance.</returns>
        </member>
        <member name="T:Microsoft.Spatial.GeometrySurface">
            <summary>Represents a geometry surface.</summary>
        </member>
        <member name="M:Microsoft.Spatial.GeometrySurface.#ctor(Microsoft.Spatial.CoordinateSystem,Microsoft.Spatial.SpatialImplementation)">
            <summary>
            Constructor
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="T:Microsoft.Spatial.GmlConstants">
            <summary>
            Gml Constants
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlConstants.GmlNamespace">
            <summary>
            Gml Namespace
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlConstants.FullGlobeNamespace">
            <summary>
            FullGlobe namespace
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlConstants.GmlPrefix">
            <summary>
            Gml Prefix
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlConstants.SrsName">
            <summary>
            System reference attribute name
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlConstants.IdName">
            <summary>
            gml:id attribute name
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlConstants.SrsPrefix">
            <summary>
            System Reference Prefix
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlConstants.Position">
            <summary>
            Gml representation of a point
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlConstants.Name">
            <summary>
            The Gml:name element name
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlConstants.Description">
            <summary>
            the Gml:Description element name
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlConstants.MetadataProperty">
            <summary>
            the metadata property element name
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlConstants.DescriptionReference">
            <summary>
            Description Reference element name
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlConstants.IdentifierElement">
            <summary>
            identifier element name
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlConstants.PointProperty">
            <summary>
            Gml representation of a point
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlConstants.PositionList">
            <summary>
            Gml representation of a point array
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlConstants.SrsDimension">
            <summary>
            Gml representation of srsDimension
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlConstants.Point">
            <summary>
            Gml Point
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlConstants.LineString">
            <summary>
            Gml representation of a linestring
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlConstants.Polygon">
            <summary>
            Gml Polygon
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlConstants.MultiPoint">
            <summary>
            Gml MultiPoint
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlConstants.MultiLineString">
            <summary>
            Gml MultiLineString
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlConstants.MultiPolygon">
            <summary>
            Gml MultiPolygon
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlConstants.Collection">
            <summary>
            Gml Collection
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlConstants.FullGlobe">
            <summary>
            Gml FullGlobe
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlConstants.ExteriorRing">
            <summary>
            Gml Polygon exterior ring
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlConstants.InteriorRing">
            <summary>
            Gml Polygon interior ring
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlConstants.LinearRing">
            <summary>
            Gml Ring
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlConstants.PointMember">
            <summary>
            Member Tag for MultiPoint
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlConstants.PointMembers">
            <summary>
            Members Tag for MultiPoint
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlConstants.LineStringMember">
            <summary>
            Member Tag for MultiLineString
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlConstants.LineStringMembers">
            <summary>
            Members Tag for MultiLineString
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlConstants.PolygonMember">
            <summary>
            Member Tag for MultiPolygon
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlConstants.PolygonMembers">
            <summary>
            Members Tag for MultiPolygon
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlConstants.CollectionMember">
            <summary>
            Member Tag for Collection
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlConstants.CollectionMembers">
            <summary>
            Members Tag for Collection
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlConstants.AxisLabels">
            <summary>
            Attribute name for Axis Labels
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlConstants.UomLabels">
            <summary>
            Attribute name for unit of measure labels
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlConstants.Count">
            <summary>
            Attribute name for count
            </summary>
        </member>
        <member name="T:Microsoft.Spatial.GmlFormatter">
            <summary>
              The object to move spatial types to and from the GML format
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GmlFormatter.#ctor(Microsoft.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.Spatial.GmlFormatter" /> class.</summary>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="M:Microsoft.Spatial.GmlFormatter.Create">
            <summary>Creates the implementation of the formatter.</summary>
            <returns>The created GmlFormatter implementation.</returns>
        </member>
        <member name="T:Microsoft.Spatial.GmlFormatterImplementation">
            <summary>
            The object to move spatial types to and from the GML format
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GmlFormatterImplementation.#ctor(Microsoft.Spatial.SpatialImplementation)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Spatial.GmlFormatterImplementation"/> class.
            </summary>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="M:Microsoft.Spatial.GmlFormatterImplementation.CreateWriter(System.Xml.XmlWriter)">
            <summary>
            Create the writer
            </summary>
            <param name="target">The object that should be the target of the ISpatialPipeline writer.</param>
            <returns>A writer that implements ISpatialPipeline.</returns>
        </member>
        <member name="M:Microsoft.Spatial.GmlFormatterImplementation.ReadGeography(System.Xml.XmlReader,Microsoft.Spatial.SpatialPipeline)">
            <summary>
            Reads the geography.
            </summary>
            <param name="readerStream">The reader stream.</param>
            <param name="pipeline">The pipeline.</param>
        </member>
        <member name="M:Microsoft.Spatial.GmlFormatterImplementation.ReadGeometry(System.Xml.XmlReader,Microsoft.Spatial.SpatialPipeline)">
            <summary>
            Reads the geometry.
            </summary>
            <param name="readerStream">The reader stream.</param>
            <param name="pipeline">The pipeline.</param>
        </member>
        <member name="T:Microsoft.Spatial.GmlReader">
            <summary>
            Gml Reader
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GmlReader.#ctor(Microsoft.Spatial.SpatialPipeline)">
            <summary>
            Creates a reader that that will send messages to the destination during read.
            </summary>
            <param name="destination">The instance to message to during read.</param>
        </member>
        <member name="M:Microsoft.Spatial.GmlReader.ReadGeographyImplementation(System.Xml.XmlReader)">
            <summary>
            Parses some serialized format that represents a geography value, passing the result down the pipeline.
            </summary>
            <param name = "input">The XmlReader instance to read from.</param>
        </member>
        <member name="M:Microsoft.Spatial.GmlReader.ReadGeometryImplementation(System.Xml.XmlReader)">
            <summary>
            Parses some serialized format that represents a geometry value, passing the result down the pipeline.
            </summary>
            <param name = "input">The XmlReader instance to read from.</param>
        </member>
        <member name="T:Microsoft.Spatial.GmlReader.Parser">
            <summary>
            This class parses the xml and calls the pipeline based on what is parsed
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlReader.Parser.coordinateDelimiter">
            <summary>
            Delimiters used in position arrays. As per Xml spec white space characters is: #x20 | #x9 | #xD | #xA
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlReader.Parser.skippableElements">
            <summary>
            List of known gml elements that can be ignored by the parser
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlReader.Parser.gmlNamespace">
            <summary>
            Atomized gml namespace
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlReader.Parser.fullGlobeNamespace">
            <summary>
            Atomized Full Globe namespace
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlReader.Parser.pipeline">
            <summary>
            Output pipeline
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlReader.Parser.reader">
            <summary>
            Input reader
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlReader.Parser.points">
            <summary>
            Number of points in the current figure
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GmlReader.Parser.#ctor(System.Xml.XmlReader,Microsoft.Spatial.TypeWashedPipeline)">
            <summary>
            Constructor
            </summary>
            <param name="reader">Input Reader</param>
            <param name="pipeline">Output pipeline</param>
        </member>
        <member name="M:Microsoft.Spatial.GmlReader.Parser.Read">
            <summary>
            Read
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GmlReader.Parser.ParseGmlGeometry(System.Boolean)">
            <summary>
            Parses the top level element in the document
            </summary>
            <param name="readCoordinateSystem">Whether coordinate system is expected</param>
        </member>
        <member name="M:Microsoft.Spatial.GmlReader.Parser.ReadAttributes(System.Boolean)">
            <summary>
            Set the CoordinateSystem
            </summary>
            <param name="expectSrsName">Should we allow CRS attributes</param>
        </member>
        <member name="M:Microsoft.Spatial.GmlReader.Parser.ParseGmlPointShape">
            <summary>
            creates a shape and parses the element.
            This is used to parse a top level Point element, as opposed to
            a point which is embedded in a linestring or a polygon.
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GmlReader.Parser.ParseGmlLineStringShape">
            <summary>
             creates a shape and parses the element for top level LineString shapes
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GmlReader.Parser.ParseGmlPolygonShape">
            <summary>
            Creates a shape and parses the Polygon element.
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GmlReader.Parser.ParseGmlMultiPointShape">
            <summary>
            Creates a shape and parses the MultiPoint element.
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GmlReader.Parser.ParseGmlMultiCurveShape">
            <summary>
            Creates a shape and parses the MultiLineString(Gml MultiCurve) element.
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GmlReader.Parser.ParseGmlMultiSurfaceShape">
            <summary>
            Creates a shape and parses the MultiPolygon(Gml MultiSurface) element.
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GmlReader.Parser.ParseGmlMultiGeometryShape">
            <summary>
            Creates a shape and parses the Collection(Gml MultiGeometry) element.
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GmlReader.Parser.ParseGmlFullGlobeElement">
            <summary>
            Creates a shape and parses the FullGlobe element
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GmlReader.Parser.ParseGmlPointElement(System.Boolean)">
            <summary>
            Parses a simple point.
            </summary>
            <param name="allowEmpty">Allow Empty Point</param>
        </member>
        <member name="M:Microsoft.Spatial.GmlReader.Parser.ParseGmlLineString">
            <summary>
            Parses the GmlLineStringElement.
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GmlReader.Parser.ParseGmlRingElement(System.String)">
            <summary>
            Parses the GmlExteriorLinearRingElement
            </summary>
            <param name="ringTag">The type or ring</param>
        </member>
        <member name="M:Microsoft.Spatial.GmlReader.Parser.ParseGmlLinearRingElement">
            <summary>
            ParseGmlLinearRingElement parses the GmlLinearRingElement
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GmlReader.Parser.ParseMultiItemElement(System.String,System.String,System.String,System.Action)">
            <summary>
            Common function for all item collections, since they are all parsed exactly the same way
            </summary>
            <param name="header">The wrapping header tag</param>
            <param name="member">The member tag</param>
            <param name="members">The members tag</param>
            <param name="parseItem">Parser for individual items</param>
        </member>
        <member name="M:Microsoft.Spatial.GmlReader.Parser.ParseGmlPosElement(System.Boolean)">
            <summary>
            parses a pos element, which eventually is used in most other top level elements.
            This represents a single point location with either two or zero coordinates.
            </summary>
            <param name="allowEmpty">Allow empty pos</param>
        </member>
        <member name="M:Microsoft.Spatial.GmlReader.Parser.ParsePosList(System.Boolean)">
            <summary>
            Parses a sequence of 1 or more pos and pointProperty elements
            </summary>
            <param name="allowEmpty">Allow Empty Point</param>
        </member>
        <member name="M:Microsoft.Spatial.GmlReader.Parser.ParseGmlPointPropertyElement(System.Boolean)">
            <summary>
            Parses a simple pointProperty.
            </summary>
            <param name="allowEmpty">Allow empty point</param>
        </member>
        <member name="M:Microsoft.Spatial.GmlReader.Parser.ParseGmlPosListElement(System.Boolean)">
            <summary>
            parses a GmlPosListElement.
            </summary>
            <param name="allowEmpty">Allow empty posList</param>
        </member>
        <member name="M:Microsoft.Spatial.GmlReader.Parser.ReadSrsDimension">
            <summary>
            Read SrsDimension integer value from SrsDimension attribute if presented.
            </summary>
            <returns>The SrsDimension value.</returns>
        </member>
        <member name="M:Microsoft.Spatial.GmlReader.Parser.ReadContentAsDoubleArray">
            <summary>
            Reads the current content in the xml element as a double array
            </summary>
            <remarks>
            XmlReader.ReadContentAs(typeof(double[])) basically does this but a lot slower, since it will handle a bunch of
            different splitters and formats. Here we simply parse it as a string and split in on one separator
            </remarks>
            <returns>The double array</returns>
        </member>
        <member name="M:Microsoft.Spatial.GmlReader.Parser.ReadStartOrEmptyElement(System.String)">
            <summary>
            Main element reading function.
            Returns true if it read a non-empty start element of the given name.
            possibilities:
                1- current element is not a start element named "element" - throw
                2- current element is named "element" but is an empty element - return false
                3- current element is named "element" and is not empty - return true
            If the function returns true, it means that a non-empty element of the given name
            was read, so the caller takes responsibility to read the corresponding end element.
            </summary>
            <param name="element">The element name</param>
            <returns>Returns true if it read a non-empty start element of the given name.</returns>
        </member>
        <member name="M:Microsoft.Spatial.GmlReader.Parser.IsStartElement(System.String)">
            <summary>
            Is Start Element
            </summary>
            <param name="element">Expected Element Tag</param>
            <returns>True if reader is at the expected element</returns>
        </member>
        <member name="M:Microsoft.Spatial.GmlReader.Parser.IsEndElement(System.String)">
            <summary>
            Is End Element
            </summary>
            <param name="element">Expected Element Tag</param>
            <returns>True if reader is at the end of the expected element</returns>
        </member>
        <member name="M:Microsoft.Spatial.GmlReader.Parser.ReadEndElement">
            <summary>
            Read End Element
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GmlReader.Parser.ReadSkippableElements">
            <summary>
            Call MoveToContent, then skip a known set of irrelevant elements (gml:name, gml:description)
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GmlReader.Parser.IsPosListStart">
            <summary>
            Is reader at the start of a pos or pointProperty
            </summary>
            <returns>True if reader is at the expected element</returns>
        </member>
        <member name="M:Microsoft.Spatial.GmlReader.Parser.PrepareFigure">
            <summary>
            Prepare for figure drawing
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GmlReader.Parser.AddPoint(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Draw a point in the current figure
            </summary>
            <param name="x">X coordinate</param>
            <param name="y">Y coordinate</param>
            <param name="z">Z coordinate</param>
            <param name="m">M coordinate</param>
        </member>
        <member name="M:Microsoft.Spatial.GmlReader.Parser.EndFigure">
            <summary>
            End Current Figure
            </summary>
        </member>
        <member name="T:Microsoft.Spatial.GmlWriter">
            <summary>
            Gml Writer
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlWriter.writer">
            <summary>
            The underlying writer
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlWriter.parentStack">
            <summary>
            Stack of spatial types currently been built
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlWriter.coordinateSystemWritten">
            <summary>
            If an SRID has been written already.
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlWriter.currentCoordinateSystem">
            <summary>
            The Coordinate System to write
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlWriter.figureWritten">
            <summary>
            Figure has been written to the current spatial type
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.GmlWriter.shouldWriteContainerWrapper">
            <summary>
            Whether there are shapes written in the current container
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GmlWriter.#ctor(System.Xml.XmlWriter)">
            <summary>
            Constructor
            </summary>
            <param name="writer">The Xml Writer to output to</param>
        </member>
        <member name="M:Microsoft.Spatial.GmlWriter.OnBeginGeography(Microsoft.Spatial.SpatialType)">
            <summary>
            Begin drawing a spatial object
            </summary>
            <param name="type">The spatial type of the object</param>
            <returns>The type to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Spatial.GmlWriter.OnLineTo(Microsoft.Spatial.GeographyPosition)">
            <summary>
            Draw a point in the specified coordinate
            </summary>
            <param name="position">Next position</param>
            <returns>The position to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Spatial.GmlWriter.OnEndGeography">
            <summary>
            Ends the current spatial object
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GmlWriter.OnBeginGeometry(Microsoft.Spatial.SpatialType)">
            <summary>
            Begin drawing a spatial object
            </summary>
            <param name="type">The spatial type of the object</param>
            <returns>The type to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Spatial.GmlWriter.OnLineTo(Microsoft.Spatial.GeometryPosition)">
            <summary>
            Draw a point in the specified coordinate
            </summary>
            <param name="position">Next position</param>
            <returns>The position to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Spatial.GmlWriter.OnEndGeometry">
            <summary>
            Ends the current spatial object
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GmlWriter.OnSetCoordinateSystem(Microsoft.Spatial.CoordinateSystem)">
            <summary>
            Set the coordinate system
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <returns>The coordinateSystem to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Spatial.GmlWriter.OnBeginFigure(Microsoft.Spatial.GeographyPosition)">
            <summary>
            Begin drawing a figure
            </summary>
            <param name="position">Next position</param>
            <returns>The position to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Spatial.GmlWriter.OnBeginFigure(Microsoft.Spatial.GeometryPosition)">
            <summary>
            Begin drawing a figure
            </summary>
            <param name="position">Next position</param>
            <returns>The position to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Spatial.GmlWriter.OnEndFigure">
            <summary>
            Ends the current figure
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GmlWriter.OnReset">
            <summary>
            Setup the pipeline for reuse
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GmlWriter.BeginFigure(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Begin a figure
            </summary>
            <param name="x">The first coordinate</param>
            <param name="y">The second coordinate</param>
            <param name="z">The optional third coordinate</param>
            <param name="m">The optional fourth coordinate</param>
        </member>
        <member name="M:Microsoft.Spatial.GmlWriter.BeginGeo(Microsoft.Spatial.SpatialType)">
            <summary>
            Begin drawing a spatial object
            </summary>
            <param name="type">The spatial type of the object</param>
        </member>
        <member name="M:Microsoft.Spatial.GmlWriter.WriteStartElement(System.String)">
            <summary>
            Write the element with namespaces
            </summary>
            <param name="elementName">The element name</param>
        </member>
        <member name="M:Microsoft.Spatial.GmlWriter.WriteCoordinateSystem">
            <summary>
            Write coordinate system
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.GmlWriter.WritePoint(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Write a Point
            </summary>
            <param name="x">The first coordinate</param>
            <param name="y">The second coordinate</param>
            <param name="z">The optional third coordinate</param>
            <param name="m">The optional fourth coordinate</param>
        </member>
        <member name="M:Microsoft.Spatial.GmlWriter.EndGeo">
            <summary>
            End Geography/Geometry
            </summary>
        </member>
        <member name="T:Microsoft.Spatial.IGeographyProvider">
            <summary>Provides access to the geography objects that this object constructs.</summary>
        </member>
        <member name="E:Microsoft.Spatial.IGeographyProvider.ProduceGeography">
            <summary>Fires when the provider constructs a geography object.</summary>
        </member>
        <member name="P:Microsoft.Spatial.IGeographyProvider.ConstructedGeography">
            <summary>Gets the geography object that was constructed most recently.</summary>
            <returns>The geography object that was constructed.</returns>
        </member>
        <member name="T:Microsoft.Spatial.IGeoJsonWriter">
            <summary>
            Represent an actual Json writing stream in Spatial.
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.IGeoJsonWriter.StartObjectScope">
            <summary>
            Start the object scope.
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.IGeoJsonWriter.EndObjectScope">
            <summary>
            End the current object scope.
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.IGeoJsonWriter.StartArrayScope">
            <summary>
            Start the array scope.
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.IGeoJsonWriter.EndArrayScope">
            <summary>
            End the current array scope.
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.IGeoJsonWriter.AddPropertyName(System.String)">
            <summary>
            Add a property name to the current json object.
            </summary>
            <param name="name">The name to add.</param>
        </member>
        <member name="M:Microsoft.Spatial.IGeoJsonWriter.AddValue(System.Double)">
            <summary>
            Add a value to the current json scope.
            </summary>
            <param name="value">The value to add.</param>
        </member>
        <member name="M:Microsoft.Spatial.IGeoJsonWriter.AddValue(System.String)">
            <summary>
            Add a value to the current json scope.
            </summary>
            <param name="value">The value to add.</param>
        </member>
        <member name="T:Microsoft.Spatial.IGeometryProvider">
            <summary>Provides access to the geometry objects that this object constructs.</summary>
        </member>
        <member name="E:Microsoft.Spatial.IGeometryProvider.ProduceGeometry">
            <summary>Fires when the provider constructs a geometry object.</summary>
        </member>
        <member name="P:Microsoft.Spatial.IGeometryProvider.ConstructedGeometry">
            <summary>Gets the geometry object that was constructed most recently.</summary>
            <returns>The geometry object that was constructed.</returns>
        </member>
        <member name="T:Microsoft.Spatial.IShapeProvider">
            <summary>Provides access to the constructed geography or geometry.</summary>
        </member>
        <member name="T:Microsoft.Spatial.LexerToken">
            <summary>
            Text Lexer Token
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.LexerToken.Text">
            <summary>
            The Token Text
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.LexerToken.Type">
            <summary>
            Token Type
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.LexerToken.MatchToken(System.Int32,System.String,System.StringComparison)">
            <summary>
            Test whether this token matches the input criterion
            </summary>
            <param name="targetType">The target type</param>
            <param name="targetText">The target text, or null</param>
            <param name="comparison">The StringComparison</param>
            <returns>True if this token matches the input criterion</returns>
        </member>
        <member name="M:Microsoft.Spatial.LexerToken.ToString">
            <summary>
            String representation of this token
            </summary>
            <returns>String representation of this token</returns>
        </member>
        <member name="T:Microsoft.Spatial.TextRes">
             <summary>
                AutoGenerated resource class. Usage:
            
                    string s = TextRes.GetString(TextRes.MyIdentifier);
             </summary>
        </member>
        <member name="T:Microsoft.Spatial.Strings">
            <summary>
               Strongly-typed and parameterized string resources.
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.Strings.SpatialImplementation_NoRegisteredOperations">
            <summary>
            A string like "No operations are registered. Please provide operations using SpatialImplementation.CurrentImplementation.Operations property."
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.Strings.InvalidPointCoordinate(System.Object,System.Object)">
            <summary>
            A string like "The value '{0}' is not valid for the coordinate '{1}'."
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.Strings.Point_AccessCoordinateWhenEmpty">
            <summary>
            A string like "Access to the coordinate properties of an empty point is not supported."
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.Strings.SpatialBuilder_CannotCreateBeforeDrawn">
            <summary>
            A string like "The builder cannot create an instance until all pipeline calls are completed."
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.Strings.GmlReader_UnexpectedElement(System.Object)">
            <summary>
            A string like "Incorrect GML Format: The XmlReader instance encountered an unexpected element "{0}"."
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.Strings.GmlReader_ExpectReaderAtElement">
            <summary>
            A string like "Incorrect GML Format: the XmlReader instance is expected to be at the start of a GML element."
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.Strings.GmlReader_InvalidSpatialType(System.Object)">
            <summary>
            A string like "Incorrect GML Format: unknown spatial type tag "{0}"."
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.Strings.GmlReader_EmptyRingsNotAllowed">
            <summary>
            A string like "Incorrect GML Format: a LinearRing element must not be empty."
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.Strings.GmlReader_PosNeedTwoNumbers">
            <summary>
            A string like "Incorrect GML Format: a pos element must contain at least two coordinates."
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.Strings.GmlReader_PosListNeedsEvenCount">
            <summary>
            A string like "Incorrect GML Format: a posList element must contain an even number of coordinates."
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.Strings.GmlReader_InvalidSrsName(System.Object)">
            <summary>
            A string like "Incorrect GML Format: a srsName attribute must begin with the namespace "{0}"."
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.Strings.GmlReader_InvalidAttribute(System.Object,System.Object)">
            <summary>
            A string like "The attribute '{0}' on element '{1}' is not supported."
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.Strings.GmlReader_InvalidSrsDimension">
            <summary>
            A string like "Incorrect GML Format: a srsDimension attribute must be either "2" or "3"."
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.Strings.WellKnownText_UnexpectedToken(System.Object,System.Object,System.Object)">
            <summary>
            A string like "Expecting token type "{0}" with text "{1}" but found "{2}"."
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.Strings.WellKnownText_UnexpectedCharacter(System.Object)">
            <summary>
            A string like "Unexpected character '{0}' found in text."
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.Strings.WellKnownText_UnknownTaggedText(System.Object)">
            <summary>
            A string like "Unknown Tagged Text "{0}"."
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.Strings.WellKnownText_TooManyDimensions">
            <summary>
            A string like "The WellKnownTextReader is configured to allow only two dimensions, and a third dimension was encountered."
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.Strings.Validator_SridMismatch">
            <summary>
            A string like "Invalid spatial data: An instance of spatial type can have only one unique CoordinateSystem for all of its coordinates."
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.Strings.Validator_InvalidType(System.Object)">
            <summary>
            A string like "Invalid spatial data: Invalid spatial type "{0}"."
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.Strings.Validator_FullGlobeInCollection">
            <summary>
            A string like "Invalid spatial data: the spatial type "FullGlobe" cannot be part of a collection type."
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.Strings.Validator_LineStringNeedsTwoPoints">
            <summary>
            A string like "Invalid spatial data: the spatial type "LineString" must contain at least two points."
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.Strings.Validator_FullGlobeCannotHaveElements">
            <summary>
            A string like "Invalid spatial data: the spatial type "FullGlobe" cannot contain figures."
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.Strings.Validator_NestingOverflow(System.Object)">
            <summary>
            A string like "Invalid spatial data: only {0} levels of nesting are supported in collection types."
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.Strings.Validator_InvalidPointCoordinate(System.Object,System.Object,System.Object,System.Object)">
            <summary>
            A string like "Invalid spatial data: the coordinates ({0} {1} {2} {3}) are not valid."
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.Strings.Validator_UnexpectedCall(System.Object,System.Object)">
            <summary>
            A string like "Invalid spatial data: expected call to "{0}" but got call to "{1}"."
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.Strings.Validator_UnexpectedCall2(System.Object,System.Object,System.Object)">
            <summary>
            A string like "Invalid spatial data: expected call to "{0}" or "{1}" but got call to "{2}"."
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.Strings.Validator_InvalidPolygonPoints">
            <summary>
            A string like "Invalid spatial data: A polygon ring must contain at least four points, and the last point must be equal to the first point."
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.Strings.Validator_InvalidLatitudeCoordinate(System.Object)">
            <summary>
            A string like "Invalid latitude coordinate {0}. A latitude coordinate must be a value between -90.0 and +90.0 degrees."
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.Strings.Validator_InvalidLongitudeCoordinate(System.Object)">
            <summary>
            A string like "Invalid longitude coordinate {0}. A longitude coordinate must be a value between -15069.0 and +15069.0 degrees"
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.Strings.Validator_UnexpectedGeography">
            <summary>
            A string like "A geography operation was called while processing a geometric shape."
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.Strings.Validator_UnexpectedGeometry">
            <summary>
            A string like "A geometry operation was called while processing a geographic shape."
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.Strings.GeoJsonReader_MissingRequiredMember(System.Object)">
            <summary>
            A string like "Invalid GeoJSON. The '{0}' member is required, but was not found."
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.Strings.GeoJsonReader_InvalidPosition">
            <summary>
            A string like "Invalid GeoJSON. A position must contain at least two and no more than four elements."
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.Strings.GeoJsonReader_InvalidTypeName(System.Object)">
            <summary>
            A string like "Invalid GeoJSON. The value '{0}' is not a valid value for the 'type' member."
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.Strings.GeoJsonReader_InvalidNullElement">
            <summary>
            A string like "Invalid GeoJSON. A null value was found in an array element where nulls are not allowed."
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.Strings.GeoJsonReader_ExpectedNumeric">
            <summary>
            A string like "Invalid GeoJSON. A non-numeric value was found in an array element where a numeric value was expected."
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.Strings.GeoJsonReader_ExpectedArray">
            <summary>
            A string like "Invalid GeoJSON. A primitive value was found in an array element where an array was expected."
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.Strings.GeoJsonReader_InvalidCrsType(System.Object)">
            <summary>
            A string like "Invalid GeoJSON. The value '{0}' is not a recognized CRS type."
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.Strings.GeoJsonReader_InvalidCrsName(System.Object)">
            <summary>
            A string like "Invalid GeoJSON. The value '{0}' is not a recognized CRS name."
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.Strings.JsonReaderExtensions_CannotReadPropertyValueAsString(System.Object,System.Object)">
            <summary>
            A string like "Cannot read the value '{0}' for the property '{1}' as a quoted JSON string value."
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.Strings.JsonReaderExtensions_CannotReadValueAsJsonObject(System.Object)">
            <summary>
            A string like "Cannot read the value '{0}' as a JSON object."
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.Strings.PlatformHelper_DateTimeOffsetMustContainTimeZone(System.Object)">
            <summary>
            A string like "The time zone information is missing on the DateTimeOffset value '{0}'. A DateTimeOffset value must contain the time zone information."
            </summary>
        </member>
        <member name="T:Microsoft.Spatial.Error">
            <summary>
               Strongly-typed and parameterized exception factory.
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.Error.ArgumentNull(System.String)">
            <summary>
            The exception that is thrown when a null reference (Nothing in Visual Basic) is passed to a method that does not accept it as a valid argument.
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.Error.ArgumentOutOfRange(System.String)">
            <summary>
            The exception that is thrown when the value of an argument is outside the allowable range of values as defined by the invoked method.
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.Error.NotImplemented">
            <summary>
            The exception that is thrown when the author has not yet implemented the logic at this point in the program. This can act as an exception based TODO tag.
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.Error.NotSupported">
            <summary>
            The exception that is thrown when an invoked method is not supported, or when there is an attempt to read, seek, or write to a stream that does not support the invoked functionality.
            </summary>
        </member>
        <member name="T:Microsoft.Spatial.ParseErrorException">
            <summary>The exception that is thrown on an unsuccessful parsing of the serialized format.</summary>
        </member>
        <member name="M:Microsoft.Spatial.ParseErrorException.#ctor">
            <summary>Creates a new instance of the <see cref="T:Microsoft.Spatial.ParseErrorException" /> class.</summary>
        </member>
        <member name="M:Microsoft.Spatial.ParseErrorException.#ctor(System.String,System.Exception)">
            <summary>Creates a new instance of the <see cref="T:Microsoft.Spatial.ParseErrorException" /> class from a message and previous exception.</summary>
            <param name="message">The message about the exception.</param>
            <param name="innerException">The exception that preceded this one.</param>
        </member>
        <member name="M:Microsoft.Spatial.ParseErrorException.#ctor(System.String)">
            <summary>Creates a new instance of the <see cref="T:Microsoft.Spatial.ParseErrorException" /> class from a message.</summary>
            <param name="message">The message about the exception.</param>
        </member>
        <member name="T:Microsoft.Spatial.ISpatial">
            <summary>Represents the spatial interface.</summary>
        </member>
        <member name="P:Microsoft.Spatial.ISpatial.CoordinateSystem">
            <summary>Gets the coordinate system.</summary>
            <returns>The coordinate system.</returns>
        </member>
        <member name="P:Microsoft.Spatial.ISpatial.IsEmpty">
            <summary>Gets a value that indicates whether the spatial type is empty.</summary>
            <returns>true if the spatial type is empty; otherwise, false.</returns>
        </member>
        <member name="T:Microsoft.Spatial.SpatialBuilder">
            <summary>Creates a geometry or geography instances from spatial data pipelines.</summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialBuilder.geographyOutput">
            <summary>
              The builder to be delegated to when this class is accessed from the IGeographyPipeline or IGeographyProvider interfaces.
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialBuilder.geometryOutput">
            <summary>
              The builder to be delegated to when this class is accessed from the IGeometryPipeline or IGeometryProvider interfaces.
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.SpatialBuilder.#ctor(Microsoft.Spatial.GeographyPipeline,Microsoft.Spatial.GeometryPipeline,Microsoft.Spatial.IGeographyProvider,Microsoft.Spatial.IGeometryProvider)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.Spatial.SpatialBuilder" /> class.</summary>
            <param name="geographyInput">The geography input.</param>
            <param name="geometryInput">The geometry input.</param>
            <param name="geographyOutput">The geography output.</param>
            <param name="geometryOutput">The geometry output.</param>
        </member>
        <member name="E:Microsoft.Spatial.SpatialBuilder.ProduceGeography">
            <summary>Fires when the provider constructs geography object.</summary>
        </member>
        <member name="E:Microsoft.Spatial.SpatialBuilder.ProduceGeometry">
            <summary>Fires when the provider constructs geometry object.</summary>
        </member>
        <member name="P:Microsoft.Spatial.SpatialBuilder.ConstructedGeography">
            <summary>Gets the geography object that was constructed most recently.</summary>
            <returns>The geography object that was constructed.</returns>
        </member>
        <member name="P:Microsoft.Spatial.SpatialBuilder.ConstructedGeometry">
            <summary>Gets the geometry object that was constructed most recently.</summary>
            <returns>The geometry object that was constructed.</returns>
        </member>
        <member name="M:Microsoft.Spatial.SpatialBuilder.Create">
            <summary>Creates an implementation of the builder.</summary>
            <returns>The created SpatialBuilder implementation.</returns>
        </member>
        <member name="T:Microsoft.Spatial.SpatialFactory">
            <summary>
            Base Spatial Factory
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialFactory.containers">
            <summary>
            Stack of Containers
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialFactory.figureDrawn">
            <summary>
            Whether a figure has been started
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialFactory.inRing">
            <summary>
            Inside a Polygon Ring
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialFactory.ringClosed">
            <summary>
            Current polygon ring has been closed
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialFactory.ringStartX">
            <summary>
            X coordinate of the current ring's starting position
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialFactory.ringStartY">
            <summary>
            Y coordinate of the current ring's starting position
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialFactory.ringStartZ">
            <summary>
            Z coordinate of the current ring's starting position
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialFactory.ringStartM">
            <summary>
            M coordinate of the current ring's starting position
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.SpatialFactory.#ctor">
            <summary>
            Initializes a new instance of the BaseSpatialFactory class
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.SpatialFactory.CurrentType">
            <summary>
            Gets the current container Definition
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.SpatialFactory.BeginGeo(Microsoft.Spatial.SpatialType)">
            <summary>
            Begin Geo
            </summary>
            <param name="type">The spatial type</param>
        </member>
        <member name="M:Microsoft.Spatial.SpatialFactory.BeginFigure(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Begin drawing a figure
            </summary>
            <param name="x">X or Latitude Coordinate</param>
            <param name="y">Y or Longitude Coordinate</param>
            <param name="z">Z Coordinate</param>
            <param name="m">M Coordinate</param>
        </member>
        <member name="M:Microsoft.Spatial.SpatialFactory.AddLine(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Draw a point in the specified coordinate
            </summary>
            <param name="x">X or Latitude Coordinate</param>
            <param name="y">Y or Longitude Coordinate</param>
            <param name="z">Z Coordinate</param>
            <param name="m">M Coordinate</param>
        </member>
        <member name="M:Microsoft.Spatial.SpatialFactory.EndFigure">
            <summary>
            Ends the figure set on the current node
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.SpatialFactory.EndGeo">
            <summary>
            Ends the current spatial object
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.SpatialFactory.Finish">
            <summary>
            Finish the current instance
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.SpatialFactory.AddPos(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Add a new position to the current line figure
            </summary>
            <param name="x">The X value</param>
            <param name="y">The Y value</param>
            <param name="z">The Z value</param>
            <param name="m">The M value</param>
        </member>
        <member name="M:Microsoft.Spatial.SpatialFactory.StartRing(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Start a new polygon ring
            </summary>
            <param name="x">The X value</param>
            <param name="y">The Y value</param>
            <param name="z">The Z value</param>
            <param name="m">The M value</param>
        </member>
        <member name="M:Microsoft.Spatial.SpatialFactory.CanContain(Microsoft.Spatial.SpatialType)">
            <summary>
            Can the current container contain the spatial type
            </summary>
            <param name="type">The spatial type to test</param>
            <returns>A boolean value indicating whether the current container can contain the spatial type</returns>
        </member>
        <member name="T:Microsoft.Spatial.SpatialFormatter`2">
            <summary>Represents the base class for all Spatial Formats.</summary>
            <typeparam name="TReaderStream">The type of reader to be read from.</typeparam>
            <typeparam name="TWriterStream">The type of reader to be read from.</typeparam>
        </member>
        <member name="F:Microsoft.Spatial.SpatialFormatter`2.creator">
            <summary>
            The implementation that created this instance.
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.SpatialFormatter`2.#ctor(Microsoft.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the &lt;see cref="Microsoft.Spatial.SpatialFormatter`2" /&gt; class. </summary>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="M:Microsoft.Spatial.SpatialFormatter`2.Read``1(`0)">
            <summary> Parses the input, and produces the object.</summary>
            <returns>The input.</returns>
            <param name="input">The input to be parsed.</param>
            <typeparam name="TResult">The type of object to produce.</typeparam>
        </member>
        <member name="M:Microsoft.Spatial.SpatialFormatter`2.Read``1(`0,Microsoft.Spatial.SpatialPipeline)">
            <summary> Parses the input, and produces the object.</summary>
            <param name="input">The input to be parsed.</param>
            <param name="pipeline">The pipeline to call during reading.</param>
            <typeparam name="TResult">The type of object to produce.</typeparam>
        </member>
        <member name="M:Microsoft.Spatial.SpatialFormatter`2.Write(Microsoft.Spatial.ISpatial,`1)">
            <summary> Creates a valid format from the spatial object.</summary>
            <param name="spatial">The object that the format is being created for.</param>
            <param name="writerStream">The stream to write the formatted object to.</param>
        </member>
        <member name="M:Microsoft.Spatial.SpatialFormatter`2.CreateWriter(`1)">
            <summary> Creates the writerStream. </summary>
            <returns>The writerStream that was created.</returns>
            <param name="writerStream">The stream that should be written to.</param>
        </member>
        <member name="M:Microsoft.Spatial.SpatialFormatter`2.ReadGeography(`0,Microsoft.Spatial.SpatialPipeline)">
            <summary> Reads the Geography from the readerStream and call the appropriate pipeline methods.</summary>
            <param name="readerStream">The stream to read from.</param>
            <param name="pipeline">The pipeline to call based on what is read.</param>
        </member>
        <member name="M:Microsoft.Spatial.SpatialFormatter`2.ReadGeometry(`0,Microsoft.Spatial.SpatialPipeline)">
            <summary> Reads the Geometry from the readerStream and call the appropriate pipeline methods.</summary>
            <param name="readerStream">The stream to read from.</param>
            <param name="pipeline">The pipeline to call based on what is read.</param>
        </member>
        <member name="M:Microsoft.Spatial.SpatialFormatter`2.MakeValidatingBuilder">
            <summary> Creates the builder that will be called by the parser to build the new type. </summary>
            <returns>The builder that was created.</returns>
        </member>
        <member name="T:Microsoft.Spatial.SpatialImplementation">
            <summary>
            Class responsible for knowing how to create the Geography and Geometry builders for
            a particular implementation of Spatial types
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialImplementation.spatialImplementation">
            <summary>Default Spatial Implementation.</summary>
        </member>
        <member name="P:Microsoft.Spatial.SpatialImplementation.CurrentImplementation">
            <summary> Returns an instance of SpatialImplementation that is currently being used. </summary>
        </member>
        <member name="P:Microsoft.Spatial.SpatialImplementation.Operations">
            <summary>Gets or sets the Spatial operations implementation.</summary>
        </member>
        <member name="M:Microsoft.Spatial.SpatialImplementation.CreateBuilder">
            <summary> Creates a SpatialBuilder for this implementation.</summary>
            <returns>The SpatialBuilder created.</returns>
        </member>
        <member name="M:Microsoft.Spatial.SpatialImplementation.CreateGeoJsonObjectFormatter">
            <summary> Creates a Formatter for Json Object.</summary>
            <returns>The JsonObjectFormatter created.</returns>
        </member>
        <member name="M:Microsoft.Spatial.SpatialImplementation.CreateGmlFormatter">
            <summary> Creates a GmlFormatter for this implementation.</summary>
            <returns>The GmlFormatter created.</returns>
        </member>
        <member name="M:Microsoft.Spatial.SpatialImplementation.CreateWellKnownTextSqlFormatter">
            <summary> Creates a WellKnownTextSqlFormatter for this implementation.</summary>
            <returns>The WellKnownTextSqlFormatter created.</returns>
        </member>
        <member name="M:Microsoft.Spatial.SpatialImplementation.CreateWellKnownTextSqlFormatter(System.Boolean)">
            <summary> Creates a WellKnownTextSqlFormatter for this implementation.</summary>
            <returns>The WellKnownTextSqlFormatter created.</returns>
            <param name="allowOnlyTwoDimensions">Controls the writing and reading of the Z and M dimension.</param>
        </member>
        <member name="M:Microsoft.Spatial.SpatialImplementation.CreateValidator">
            <summary> Creates a spatial Validator.</summary>
            <returns>The SpatialValidator created.</returns>
        </member>
        <member name="M:Microsoft.Spatial.SpatialImplementation.VerifyAndGetNonNullOperations">
            <summary>
            This method throws if the operations instance is null. It returns a non-null operations implementation.
            </summary>
            <returns>a SpatialOperations implementation.</returns>
        </member>
        <member name="T:Microsoft.Spatial.SpatialOperations">
            <summary>
            Class responsible for knowing how to perform operations for a particular implementation of Spatial types
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.SpatialOperations.Distance(Microsoft.Spatial.Geometry,Microsoft.Spatial.Geometry)">
            <summary>Indicates the Geometry Distance.</summary>
            <returns>The operation result.</returns>
            <param name="operand1">The Operand 1.</param>
            <param name="operand2">The Operand 2.</param>
        </member>
        <member name="M:Microsoft.Spatial.SpatialOperations.Distance(Microsoft.Spatial.Geography,Microsoft.Spatial.Geography)">
            <summary>Indicates a Geography Distance.</summary>
            <returns>The operation result.</returns>
            <param name="operand1">The Operand 1.</param>
            <param name="operand2">The Operand 2.</param>
        </member>
        <member name="M:Microsoft.Spatial.SpatialOperations.Length(Microsoft.Spatial.Geometry)">
            <summary>Indicates the Geometry LineString's length.</summary>
            <returns>The operation result.</returns>
            <param name="operand">The Operand.</param>
        </member>
        <member name="M:Microsoft.Spatial.SpatialOperations.Length(Microsoft.Spatial.Geography)">
            <summary>Indicates a Geography LineString's length.</summary>
            <returns>The operation result.</returns>
            <param name="operand">The Operand.</param>
        </member>
        <member name="M:Microsoft.Spatial.SpatialOperations.Intersects(Microsoft.Spatial.Geometry,Microsoft.Spatial.Geometry)">
            <summary>Indicates the Geometry Intersects() method.</summary>
            <returns>The operation result.</returns>
            <param name="operand1">The Operand 1, point.</param>
            <param name="operand2">The Operand 2, polygon.</param>
        </member>
        <member name="M:Microsoft.Spatial.SpatialOperations.Intersects(Microsoft.Spatial.Geography,Microsoft.Spatial.Geography)">
            <summary>Indicates a Geography Intersects() method.</summary>
            <returns>The operation result.</returns>
            <param name="operand1">The Operand 1, point.</param>
            <param name="operand2">The Operand 2, polygon.</param>
        </member>
        <member name="T:Microsoft.Spatial.SpatialPipeline">
            <summary>
            One link of a geospatial pipeline
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialPipeline.geographyPipeline">
            <summary>
            the geography side of the pipeline
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialPipeline.geometryPipeline">
            <summary>
            the geometry side of the pipeline
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialPipeline.startingLink">
            <summary>
            A reference to the beginning link of the chain
            useful for getting the startingLink when creating the chain fluently
            e.g.  new ForwardingSegment(new Node()).ChainTo(new Node()).ChainTo(new Node).StartingLink
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.SpatialPipeline.#ctor">
            <summary> Initializes a new instance of the <see cref="T:Microsoft.Spatial.SpatialPipeline" /> class. </summary>
        </member>
        <member name="M:Microsoft.Spatial.SpatialPipeline.#ctor(Microsoft.Spatial.GeographyPipeline,Microsoft.Spatial.GeometryPipeline)">
            <summary> Initializes a new instance of the <see cref="T:Microsoft.Spatial.SpatialPipeline" /> class. </summary>
            <param name="geographyPipeline">The geography chain.</param>
            <param name="geometryPipeline">The geometry chain.</param>
        </member>
        <member name="P:Microsoft.Spatial.SpatialPipeline.GeographyPipeline">
            <summary> Gets the geography side of the pipeline. </summary>
        </member>
        <member name="P:Microsoft.Spatial.SpatialPipeline.GeometryPipeline">
            <summary> Gets the geometry side of the pipeline. </summary>
        </member>
        <member name="P:Microsoft.Spatial.SpatialPipeline.StartingLink">
            <summary> Gets or sets the starting link. </summary>
            <returns> The starting link. </returns>
        </member>
        <member name="M:Microsoft.Spatial.SpatialPipeline.op_Implicit(Microsoft.Spatial.SpatialPipeline)~Microsoft.Spatial.GeographyPipeline">
            <summary>
            Performs an implicit conversion from <see cref="T:Microsoft.Spatial.SpatialPipeline"/> to <see cref="T:Microsoft.Spatial.GeographyPipeline"/>.
            </summary>
            <param name="spatialPipeline">The spatial chain.</param>
            <returns>
            The result of the conversion.
            </returns>
        </member>
        <member name="M:Microsoft.Spatial.SpatialPipeline.op_Implicit(Microsoft.Spatial.SpatialPipeline)~Microsoft.Spatial.GeometryPipeline">
            <summary>
            Performs an implicit conversion from <see cref="T:Microsoft.Spatial.SpatialPipeline"/> to <see cref="T:Microsoft.Spatial.GeometryPipeline"/>.
            </summary>
            <param name="spatialPipeline">The spatial chain.</param>
            <returns>
            The result of the conversion.
            </returns>
        </member>
        <member name="M:Microsoft.Spatial.SpatialPipeline.ChainTo(Microsoft.Spatial.SpatialPipeline)">
            <summary> Adds the next pipeline.</summary>
            <returns>The last pipesegment in the chain, usually the one just created.</returns>
            <param name="destination">The next pipeline.</param>
        </member>
        <member name="T:Microsoft.Spatial.SpatialReader`1">
            <summary>
            Reader to be used by spatial formats
            </summary>
            <typeparam name="TSource">The type of source that the reader operates on.</typeparam>
        </member>
        <member name="M:Microsoft.Spatial.SpatialReader`1.#ctor(Microsoft.Spatial.SpatialPipeline)">
            <summary>
            Creates a reader
            </summary>
            <param name="destination">the instance of the pipeline that the reader will message while it is reading.</param>
        </member>
        <member name="P:Microsoft.Spatial.SpatialReader`1.Destination">
            <summary>
            The pipeline that is messaged while the reader is reading.
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.SpatialReader`1.ReadGeography(`0)">
            <summary>
              Parses some serialized format that represents one or more Geography spatial values, passing the first one down the pipeline.
            </summary>
            <exception cref = "T:Microsoft.Spatial.ParseErrorException">Throws if the input is not valid. In that case, guarantees that it will not pass anything down the pipeline, or will clear the pipeline by passing down a Reset.</exception>
            <param name = "input">The input string</param>
        </member>
        <member name="M:Microsoft.Spatial.SpatialReader`1.ReadGeometry(`0)">
            <summary>
              Parses some serialized format that represents one or more Geometry spatial values, passing the first one down the pipeline.
            </summary>
            <exception cref = "T:Microsoft.Spatial.ParseErrorException">Throws if the input is not valid. In that case, guarantees that it will not pass anything down the pipeline, or will clear the pipeline by passing down a Reset.</exception>
            <param name = "input">The input string</param>
        </member>
        <member name="M:Microsoft.Spatial.SpatialReader`1.Reset">
            <summary>
            Sets the reader and underlying Destination back to a clean
            starting state after an exception
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.SpatialReader`1.ReadGeometryImplementation(`0)">
            <summary>
              Parses some serialized format that represents one or more Geometry spatial values, passing the first one down the pipeline.
            </summary>
            <exception cref = "T:Microsoft.Spatial.ParseErrorException">Throws if the input is not valid. In that case, guarantees that it will not pass anything down the pipeline, or will clear the pipeline by passing down a Reset.</exception>
            <param name = "input">The input string</param>
        </member>
        <member name="M:Microsoft.Spatial.SpatialReader`1.ReadGeographyImplementation(`0)">
            <summary>
              Parses some serialized format that represents one or more Geography spatial values, passing the first one down the pipeline.
            </summary>
            <exception cref = "T:Microsoft.Spatial.ParseErrorException">Throws if the input is not valid. In that case, guarantees that it will not pass anything down the pipeline, or will clear the pipeline by passing down a Reset.</exception>
            <param name = "input">The input string</param>
        </member>
        <member name="T:Microsoft.Spatial.SpatialTreeBuilder`1">
            <summary>
            Tree based builder for spatial types
            </summary>
            <typeparam name="T">Geography or Geometry</typeparam>
        </member>
        <member name="F:Microsoft.Spatial.SpatialTreeBuilder`1.currentFigure">
            <summary>
            The figure this builder is currently building
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialTreeBuilder`1.currentNode">
            <summary>
            Current builder tree root
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialTreeBuilder`1.lastConstructedNode">
            <summary>
            lastConstructed
            </summary>
        </member>
        <member name="E:Microsoft.Spatial.SpatialTreeBuilder`1.ProduceInstance">
            <summary>
            Fires when the builder creates a top-level spatial object.
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.SpatialTreeBuilder`1.ConstructedInstance">
            <summary>
            Get the constructed spatial instance
            </summary>
            <returns>The constructed spatial instance</returns>
        </member>
        <member name="P:Microsoft.Spatial.SpatialTreeBuilder`1.IsGeography">
            <summary>
            Gets a value indicating whether this instance is geography.
            </summary>
            <value>
            <c>true</c> if this instance is geography; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="M:Microsoft.Spatial.SpatialTreeBuilder`1.LineTo(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Draw a point in the specified coordinate
            </summary>
            <param name="x">X or Latitude Coordinate</param>
            <param name="y">Y or Longitude Coordinate</param>
            <param name="z">Z Coordinate</param>
            <param name="m">M Coordinate</param>
        </member>
        <member name="M:Microsoft.Spatial.SpatialTreeBuilder`1.BeginFigure(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Begin drawing a figure
            </summary>
            <param name="coordinate1">X or Latitude Coordinate</param>
            <param name="coordinate2">Y or Longitude Coordinate</param>
            <param name="coordinate3">Z Coordinate</param>
            <param name="coordinate4">M Coordinate</param>
        </member>
        <member name="M:Microsoft.Spatial.SpatialTreeBuilder`1.BeginGeo(Microsoft.Spatial.SpatialType)">
            <summary>
            Begin a new spatial type
            </summary>
            <param name="type">The spatial type</param>
        </member>
        <member name="M:Microsoft.Spatial.SpatialTreeBuilder`1.EndFigure">
            <summary>
            Ends the figure set on the current node
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.SpatialTreeBuilder`1.EndGeo">
            <summary>
            Ends the current spatial object
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.SpatialTreeBuilder`1.Reset">
            <summary>
            Setup the pipeline for reuse
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.SpatialTreeBuilder`1.CreatePoint(System.Boolean,System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Create a new instance of Point
            </summary>
            <param name="isEmpty">Whether the point is empty</param>
            <param name="x">X</param>
            <param name="y">Y</param>
            <param name="z">Z</param>
            <param name="m">M</param>
            <returns>A new instance of point</returns>
        </member>
        <member name="M:Microsoft.Spatial.SpatialTreeBuilder`1.CreateShapeInstance(Microsoft.Spatial.SpatialType,System.Collections.Generic.IEnumerable{`0})">
            <summary>
            Create a new instance of T
            </summary>
            <param name="type">The spatial type to create</param>
            <param name="spatialData">The arguments</param>
            <returns>A new instance of T</returns>
        </member>
        <member name="M:Microsoft.Spatial.SpatialTreeBuilder`1.NotifyIfWeJustFinishedBuildingSomething">
            <summary>
            Notifies if we just finished building something.
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.SpatialTreeBuilder`1.TraverseUpTheTree">
            <summary>
            Traverses up the tree.
            </summary>
        </member>
        <member name="T:Microsoft.Spatial.SpatialTreeBuilder`1.SpatialBuilderNode">
            <summary>
            A spatial instance node in the builder tree
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.SpatialTreeBuilder`1.SpatialBuilderNode.#ctor">
            <summary>
            Constructor
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.SpatialTreeBuilder`1.SpatialBuilderNode.Children">
            <summary>
            Children nodes
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.SpatialTreeBuilder`1.SpatialBuilderNode.Instance">
            <summary>
            Instance
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.SpatialTreeBuilder`1.SpatialBuilderNode.Parent">
            <summary>
            Parent node
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.SpatialTreeBuilder`1.SpatialBuilderNode.Type">
            <summary>
            Spatial Type
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.SpatialTreeBuilder`1.SpatialBuilderNode.CreateChildren(Microsoft.Spatial.SpatialType)">
            <summary>
            Create a child node
            </summary>
            <param name="type">The node type</param>
            <returns>The child node</returns>
        </member>
        <member name="T:Microsoft.Spatial.SpatialType">
            <summary> Defines a list of allowed OpenGisTypes types.  </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialType.Unknown">
            <summary>
            Unknown
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialType.Point">
            <summary>
            Point
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialType.LineString">
            <summary>
            Line String
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialType.Polygon">
            <summary>
            Polygon
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialType.MultiPoint">
            <summary>
            Multi-Point
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialType.MultiLineString">
            <summary>
            Multi-Line-String
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialType.MultiPolygon">
            <summary>
            Multi-Polygon
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialType.Collection">
            <summary>
            Collection
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialType.FullGlobe">
            <summary>
            Full Globe
            </summary>
        </member>
        <member name="T:Microsoft.Spatial.SpatialTypeExtensions">
            <summary>Provides a place to add extension methods that work with ISpatial.</summary>
        </member>
        <member name="M:Microsoft.Spatial.SpatialTypeExtensions.SendTo(Microsoft.Spatial.ISpatial,Microsoft.Spatial.SpatialPipeline)">
            <summary> Allows the delegation of the call to the proper type (geography or Geometry).</summary>
            <param name="shape">The instance that will have SendTo called.</param>
            <param name="destination">The pipeline that the instance will be sent to.</param>
        </member>
        <member name="T:Microsoft.Spatial.SpatialValidator">
            <summary>
            Base class for Spatial Type Validator implementations
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidator.Create">
            <summary>Creates the currently registered SpatialValidator implementation.</summary>
            <returns>The created SpatialValidator.</returns>
        </member>
        <member name="T:Microsoft.Spatial.SpatialValidatorImplementation">
            <summary>
            Semantically validate a GeoData
            </summary>
            <remarks>
            Grammar, states, and actions:
            <![CDATA[
                <Document> := SetSRID <Geometry> { Finish }
                <Geometry> := (Begin_Point <Point> | ... | Begin_FullGlobe (: verify depth = 1 :) <FullGlobe>)
                <Point> := [ BeginFigure 1 EndFigure ] 2 End
                <LineString> := [ BeginFigure 1 { LineTo } EndFigure (: verify 2+ points :) ] 2 End
                <Polygon> := { BeginFigure 1 { LineTo } EndFigure (: verify 4+ points and closed :) } End
                <MultiPoint> := { { SetSRID } Begin_Point <Point> } End
                <MultiLineString> := { { SetSRID } Begin_LineString <LineString> } End
                <MultiPolygon> := { { SetSRID } Begin_Polygon <Polygon> } End
                <GeometryCollection> := { { SetSRID } <Geometry> } End
                <FullGlobe> := End
                <CircularString> := [ BeginFigure 1 { AddCircularArc } EndFigure ] 2 End
                <CompoundCurve> := [ BeginFigure 1 { LineTo | AddCircularArc } EndFigure ] | <StructuredCompoundCurve> 2 End
                <StructuredCompoundCurve> := <StructuredCompoundCurveStart> { <StructuredCompoundCurvePart> } EndFigure
                <StructuredCompoundCurveStart> := AddSegmentLine 0 BeginFigure { LineTo } | AddSegmentArc 0 BeginFigure { AddCircularArc }
                <StructuredCompoundCurvePart> := AddSegmentLine { LineTo } | AddSegmentArc { AddCircularArc }
                <CurvePolygon> := { <CurvePolygonImplicitRing> | <CurvePolygonSimpleRing> | <CurvePolygonCompoundCurveRing> EndFigure (: verify closed and three distinct :)} End
                <CurvePolygonImplicitRing> := BeginFigure 1 { LineTo | AddCircularArc }
                <CurvePolygonSimpleRing> := StartSimpleRing 0 <CurvePolygonImplicitRing>
                <CurvePolygonCompoundCurveRing> := <CurvePolygonCompoundCurveRingStart> { <CurvePolygonCompoundCurveRingPart> }
                <CurvePolygonCompoundCurveRingStart> := AddSegmentLine 0 BeginFigure { LineTo } | AddSegmentArc 0 BeginFigure { AddCircularArc }
                <CurvePolygonCompoundCurveRingPart> := AddSegmentLine { LineTo } | AddSegmentArc { AddCircularArc }
            ]]>
            </remarks>
        </member>
        <member name="F:Microsoft.Spatial.SpatialValidatorImplementation.MaxLongitude">
            <summary>
            Max value for Longitude
            </summary>
            <remarks>
            ~263 radians converted to degrees
            </remarks>
        </member>
        <member name="F:Microsoft.Spatial.SpatialValidatorImplementation.MaxLatitude">
            <summary>
            Max value for latitude
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialValidatorImplementation.geographyValidatorInstance">
            <summary>
            The DrawBoth derived instance of the geography Validator that is nested in this class
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialValidatorImplementation.geometryValidatorInstance">
            <summary>
            The DrawBoth derived instance of the geometry Validator that is nested in this class
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.SpatialValidatorImplementation.GeographyPipeline">
            <summary>
            Gets the draw geography.
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.SpatialValidatorImplementation.GeometryPipeline">
            <summary>
            Gets the draw geometry.
            </summary>
        </member>
        <member name="T:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator">
            <summary>
            this is the actual validator, and derived from DrawBoth
            while the real SpatialValidator derives from DrawSpatial.
            We simple create an instance of this nested class and pass back
            the DrawGeometry, and DrawGeography when the outer classes DataSpatial
            properties are accessed.
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.CoordinateSystem">
            <summary>
            Set coordinate system
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.BeginSpatial">
            <summary>
            BeginGeo
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PointStart">
            <summary>
            Starting a point
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PointBuilding">
            <summary>
            Building a point
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PointEnd">
            <summary>
            Ending a point
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.LineStringStart">
            <summary>
            Starting a LineString
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.LineStringBuilding">
            <summary>
            Building a LineString
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.LineStringEnd">
            <summary>
            Ending a LineString
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PolygonStart">
            <summary>
            Starting a Polygon
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PolygonBuilding">
            <summary>
            Building a Polygon
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.MultiPoint">
            <summary>
            Starting a MultiPoint
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.MultiLineString">
            <summary>
            Starting a LineString
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.MultiPolygon">
            <summary>
            Starting a MultiPolygon
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.Collection">
            <summary>
            Starting a Collection
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.FullGlobe">
            <summary>
            Starting a FullGlobe
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.MaxGeometryCollectionDepth">
            <summary>
            Geometry Functional Specification *******
            Max Geometry Collection Depth
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.stack">
            <summary>
            States
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.validationCoordinateSystem">
            <summary>
            CoordinateSystem
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.ringCount">
            <summary>
            Number of rings in a polygon
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.initialFirstCoordinate">
            <summary>
            First point's X coordinate
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.initialSecondCoordinate">
            <summary>
            First point's Y coordinate
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.mostRecentFirstCoordinate">
            <summary>
            Last point's X coordinate
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.mostRecentSecondCoordinate">
            <summary>
            Last point's Y coordinate
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.processingGeography">
            <summary>
            we are validating a geography stream
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.pointCount">
            <summary>
            Number of points in the GeoData
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.depth">
            <summary>
            Stack depth
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.#ctor">
            <summary>
            Constructs a new SpatialValidatorImplementation segment
            </summary>
        </member>
        <member name="T:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall">
            <summary>
            Calls to the pipeline interface Represented as state transition
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall.SetCoordinateSystem">
            <summary>
            Set CoordinateSystem
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall.Begin">
            <summary>
            BeginGeo()
            </summary>
            <remarks>fake transition, just for exception</remarks>
        </member>
        <member name="F:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall.BeginPoint">
            <summary>
            BeginGeo(point)
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall.BeginLineString">
            <summary>
            BeginGeo(LineString)
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall.BeginPolygon">
            <summary>
            BeginGeo(Polygon)
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall.BeginMultiPoint">
            <summary>
            BeginGeo(MultiPoint)
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall.BeginMultiLineString">
            <summary>
            BeginGeo(MultiLineString)
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall.BeginMultiPolygon">
            <summary>
            BeginGeo(MultiPolygon)
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall.BeginCollection">
            <summary>
            BeginGeo(Collection)
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall.BeginFullGlobe">
            <summary>
            BeginGeo(FullGlobe)
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall.BeginFigure">
            <summary>
            BeginFigure
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall.LineTo">
            <summary>
            LineTo
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall.EndFigure">
            <summary>
            EndFigure
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall.End">
            <summary>
            EndGeo
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.OnSetCoordinateSystem(Microsoft.Spatial.CoordinateSystem)">
            <summary>
            Implemented by a subclass to handle the setting of a coordinate system
            </summary>
            <param name="coordinateSystem">the new coordinate system</param>
            <returns>the coordinate system to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.OnBeginGeography(Microsoft.Spatial.SpatialType)">
            <summary>
            Implemented by a subclass to handle the start of drawing a Geography figure
            </summary>
            <param name="shape">the  shape to draw</param>
            <returns>the SpatialType to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.OnEndGeography">
            <summary>
            Implemented by a subclass to handle the end of drawing a Geography figure
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.OnBeginGeometry(Microsoft.Spatial.SpatialType)">
            <summary>
            Implemented by a subclass to handle the start of drawing a Geometry figure
            </summary>
            <param name="shape">the  shape to draw</param>
            <returns>the SpatialType to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.OnEndGeometry">
            <summary>
            Implemented by a subclass to handle the end of drawing a Geometry figure
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.OnBeginFigure(Microsoft.Spatial.GeographyPosition)">
            <summary>
            Implemented by a subclass to handle the start of a figure
            </summary>
            <param name="position">Next position</param>
            <returns>The position to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.OnBeginFigure(Microsoft.Spatial.GeometryPosition)">
            <summary>
            Implemented by a subclass to handle the start of a figure
            </summary>
            <param name="position">Next position</param>
            <returns>The position to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.OnEndFigure">
            <summary>
            Implemented by a subclass to handle the end of a figure
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.OnReset">
            <summary>
            Implemented by a subclass to return to its initial state
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.OnLineTo(Microsoft.Spatial.GeographyPosition)">
            <summary>
            Implemented by a subclass to handle the addition of a waypoint to a Geography figure
            </summary>
            <param name="position">Next position</param>
            <returns>the GeographyPosition to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.OnLineTo(Microsoft.Spatial.GeometryPosition)">
            <summary>
            Implemented by a subclass to handle the addition of a waypoint to a Geometry figure
            </summary>
            <param name="position">Next position</param>
            <returns>the GeometryPosition to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.IsFinite(System.Double)">
            <summary>
            Test whether a double is finite
            </summary>
            <param name="value">The double value</param>
            <returns>True if the input double is not NaN or INF</returns>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.IsPointValid(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Test whether a point is in valid format
            </summary>
            <param name="first">The first coordinate</param>
            <param name="second">The second coordinate</param>
            <param name="z">The z coordinate</param>
            <param name="m">The m coordinate</param>
            <returns>Whether the input coordinate is valid</returns>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.ValidateOnePosition(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Validate one position
            </summary>
            <param name="first">the first two dimensional co-ordinate</param>
            <param name="second">the second two dimensional co-ordinate</param>
            <param name="z">the altitude</param>
            <param name="m">the measure</param>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.ValidateGeographyPosition(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Validate one Geography position
            </summary>
            <param name="latitude">the latitude</param>
            <param name="longitude">the longitude</param>
            <param name="z">the altitude</param>
            <param name="m">the measure</param>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.ValidateGeometryPosition(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Validate one Geography position
            </summary>
            <param name="x">the x coordinate</param>
            <param name="y">the y coordinate</param>
            <param name="z">the altitude</param>
            <param name="m">the measure</param>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.IsLatitudeValid(System.Double)">
            <summary>
            Test whether a latitude value is within acceptable range
            </summary>
            <param name="latitude">The latitude value</param>
            <returns>True if the latitude value is within range</returns>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.IsLongitudeValid(System.Double)">
            <summary>
            Test whether a longitude value is within acceptable range
            </summary>
            <param name="longitude">The longitude value</param>
            <returns>True if the longitude value is within range</returns>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.ValidateGeographyPolygon(System.Int32,System.Double,System.Double,System.Double,System.Double)">
            <summary>
            Validate a Geography polygon
            </summary>
            <param name="numOfPoints">The number of points in the ring</param>
            <param name="initialFirstCoordinate">its first latitude</param>
            <param name="initialSecondCoordinate">it first longitude</param>
            <param name="mostRecentFirstCoordinate">its last latitude</param>
            <param name="mostRecentSecondCoordinate">its last longitude</param>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.ValidateGeometryPolygon(System.Int32,System.Double,System.Double,System.Double,System.Double)">
            <summary>
            Validate a Geometry polygon
            </summary>
            <param name="numOfPoints">The number of points in the ring</param>
            <param name="initialFirstCoordinate">its first x</param>
            <param name="initialSecondCoordinate">it first y</param>
            <param name="mostRecentFirstCoordinate">its last x</param>
            <param name="mostRecentSecondCoordinate">its last y</param>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.AreLongitudesEqual(System.Double,System.Double)">
            <summary>
            Test whether two longitude values are equal
            </summary>
            <param name="left">Left longitude</param>
            <param name="right">Right longitude</param>
            <returns>True if the two longitudes are equals</returns>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.BeginFigure(System.Action{System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double}},System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Begins the figure.
            </summary>
            <param name="validate">The validate action.</param>
            <param name="x">The x.</param>
            <param name="y">The y.</param>
            <param name="z">The z.</param>
            <param name="m">The m.</param>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.BeginShape(Microsoft.Spatial.SpatialType)">
            <summary>
            Begin drawing a spatial object
            </summary>
            <param name="type">The spatial type of the object</param>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.AddControlPoint(System.Double,System.Double)">
            <summary>
             Add a control point to the current figure.
            </summary>
            <param name="first">the first coordinate</param>
            <param name="second">the second coordinate</param>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.TrackPosition(System.Double,System.Double)">
            <summary>
            Tracks the position.
            </summary>
            <param name="first">The first.</param>
            <param name="second">The second.</param>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.Execute(Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall)">
            <summary>
            Transit into a new state
            </summary>
            <param name="transition">The state to transit into</param>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.InitializeObject">
            <summary>
             initialize the object to a fresh clean smelling state
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.Call(Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.ValidatorState)">
            <summary>
            Push a new state onto the stack
            </summary>
            <param name="state">The new state</param>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.Return">
            <summary>
            Pop a state from the stack
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.Jump(Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.ValidatorState)">
            <summary>
            Replace the current state on the stack with the new state
            </summary>
            <param name="state">The new state</param>
        </member>
        <member name="T:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.ValidatorState">
            <summary>
            SpatialValidatorImplementation State
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.ValidatorState.ValidateTransition(Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall,Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator)">
            <summary>
            Validate a call to the pipeline interface (a state transition)
            </summary>
            <param name="transition">The transition</param>
            <param name="validator">The validator instance</param>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.ValidatorState.ThrowExpected(Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall,Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall)">
            <summary>
            Throw an incorrect state exception
            </summary>
            <param name="transition">The expected state</param>
            <param name="actual">The actual state</param>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.ValidatorState.ThrowExpected(Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall,Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall,Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall)">
            <summary>
            Throw an incorrect state exception
            </summary>
            <param name="transition1">The expected state1</param>
            <param name="transition2">The expected state2</param>
            <param name="actual">The actual state</param>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.ValidatorState.ThrowExpected(Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall,Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall,Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall,Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall)">
            <summary>
            Throw an incorrect state exception
            </summary>
            <param name="transition1">The expected state1</param>
            <param name="transition2">The expected state2</param>
            <param name="transition3">The expected state3</param>
            <param name="actual">The actual state</param>
        </member>
        <member name="T:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.SetCoordinateSystemState">
            <summary>
            SetCoordinateSystem State
            Validator is currently waiting for a SetCoordinateSystemCall
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.SetCoordinateSystemState.ValidateTransition(Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall,Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator)">
            <summary>
            Validate a call to the pipeline interface (a state transition)
            </summary>
            <param name="transition">The transition</param>
            <param name="validator">The validator instance</param>
        </member>
        <member name="T:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.BeginGeoState">
            <summary>
            Beginning a GeoData
            Validator is currently waiting for a BeginGeo() call
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.BeginGeoState.ValidateTransition(Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall,Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator)">
            <summary>
            Validate a call to the pipeline interface (a state transition)
            </summary>
            <param name="transition">The transition</param>
            <param name="validator">The validator instance</param>
        </member>
        <member name="T:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PointStartState">
            <summary>
            Point Start State
            After BeginGeo(Point), waiting for BeginFigure() or EndGeo()
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PointStartState.ValidateTransition(Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall,Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator)">
            <summary>
            Validate a call to the pipeline interface (a state transition)
            </summary>
            <param name="transition">The transition</param>
            <param name="validator">The validator instance</param>
        </member>
        <member name="T:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PointBuildingState">
            <summary>
            Point Building State
            After BeginFigure(), waiting for EndFigure() immediately
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PointBuildingState.ValidateTransition(Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall,Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator)">
            <summary>
            Validate a call to the pipeline interface (a state transition)
            </summary>
            <param name="transition">The transition</param>
            <param name="validator">The validator instance</param>
        </member>
        <member name="T:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PointEndState">
            <summary>
            Point End State
            After EndFigure() for a point, waiting for EndGeo()
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PointEndState.ValidateTransition(Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall,Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator)">
            <summary>
            Validate a call to the pipeline interface (a state transition)
            </summary>
            <param name="transition">The transition</param>
            <param name="validator">The validator instance</param>
        </member>
        <member name="T:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.LineStringStartState">
            <summary>
            LineString Start state
            After BeginGeo(LineString), waiting for BeginFigure/EndGeo
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.LineStringStartState.ValidateTransition(Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall,Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator)">
            <summary>
            Validate a call to the pipeline interface (a state transition)
            </summary>
            <param name="transition">The transition</param>
            <param name="validator">The validator instance</param>
        </member>
        <member name="T:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.LineStringBuildingState">
            <summary>
            LineString Building State
            After BeginFigure() for a line
            Waiting for LineTo/EndFigure
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.LineStringBuildingState.ValidateTransition(Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall,Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator)">
            <summary>
            Validate a call to the pipeline interface (a state transition)
            </summary>
            <param name="transition">The transition</param>
            <param name="validator">The validator instance</param>
        </member>
        <member name="T:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.LineStringEndState">
            <summary>
            LineString End State
            After EndFigure() on Line
            Waiting for EndGeo
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.LineStringEndState.ValidateTransition(Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall,Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator)">
            <summary>
            Validate a call to the pipeline interface (a state transition)
            </summary>
            <param name="transition">The transition</param>
            <param name="validator">The validator instance</param>
        </member>
        <member name="T:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PolygonStartState">
            <summary>
            PolygonStart State
            After polygon started, waiting for Rings to build
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PolygonStartState.ValidateTransition(Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall,Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator)">
            <summary>
            Validate a call to the pipeline interface (a state transition)
            </summary>
            <param name="transition">The transition</param>
            <param name="validator">The validator instance</param>
        </member>
        <member name="T:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PolygonBuildingState">
            <summary>
            Polygon Building State
            Drawing rings
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PolygonBuildingState.ValidateTransition(Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall,Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator)">
            <summary>
            Validate a call to the pipeline interface (a state transition)
            </summary>
            <param name="transition">The transition</param>
            <param name="validator">The validator instance</param>
        </member>
        <member name="T:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.MultiPointState">
            <summary>
            MultiPoint State
            Inside a MultiPoint Container
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.MultiPointState.ValidateTransition(Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall,Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator)">
            <summary>
            Validate a call to the pipeline interface (a state transition)
            </summary>
            <param name="transition">The transition</param>
            <param name="validator">The validator instance</param>
        </member>
        <member name="T:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.MultiLineStringState">
            <summary>
            MultiLineString State
            Inside a MultiLineString container
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.MultiLineStringState.ValidateTransition(Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall,Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator)">
            <summary>
            Validate a call to the pipeline interface (a state transition)
            </summary>
            <param name="transition">The transition</param>
            <param name="validator">The validator instance</param>
        </member>
        <member name="T:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.MultiPolygonState">
            <summary>
            MultiPolygon State
            Inside a MultiPolygon container
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.MultiPolygonState.ValidateTransition(Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall,Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator)">
            <summary>
            Validate a call to the pipeline interface (a state transition)
            </summary>
            <param name="transition">The transition</param>
            <param name="validator">The validator instance</param>
        </member>
        <member name="T:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.CollectionState">
            <summary>
            Collection State
            Inside a Collection container
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.CollectionState.ValidateTransition(Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall,Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator)">
            <summary>
            Validate a call to the pipeline interface (a state transition)
            </summary>
            <param name="transition">The transition</param>
            <param name="validator">The validator instance</param>
        </member>
        <member name="T:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.FullGlobeState">
            <summary>
            FullGlobe state
            Inside a FullGlobe container
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.FullGlobeState.ValidateTransition(Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall,Microsoft.Spatial.SpatialValidatorImplementation.NestedValidator)">
            <summary>
            Validate a call to the pipeline interface (a state transition)
            </summary>
            <param name="transition">The transition</param>
            <param name="validator">The validator instance</param>
        </member>
        <member name="T:Microsoft.Spatial.TextLexerBase">
            <summary>
            Lexer base
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.TextLexerBase.reader">
            <summary>
            Input text
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.TextLexerBase.currentToken">
            <summary>
            Current lexer output
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.TextLexerBase.peekToken">
            <summary>
            Peek lexer output, if this is not null then we have advanced already
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.TextLexerBase.#ctor(System.IO.TextReader)">
            <summary>
            Constructor
            </summary>
            <param name="text">The input text</param>
        </member>
        <member name="P:Microsoft.Spatial.TextLexerBase.CurrentToken">
            <summary>
            Current token
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.TextLexerBase.Peek(Microsoft.Spatial.LexerToken@)">
            <summary>
            Peek one token ahead of the current position
            </summary>
            <param name="token">The peeked token</param>
            <returns>True if there is one more token after the current position, otherwise false</returns>
        </member>
        <member name="M:Microsoft.Spatial.TextLexerBase.Next">
            <summary>
            Move to the next token
            </summary>
            <returns>True if lexer has moved, otherwise false</returns>
        </member>
        <member name="M:Microsoft.Spatial.TextLexerBase.MatchTokenType(System.Char,System.Nullable{System.Int32},System.Int32@)">
            <summary>
            Examine the current character and determine its token type
            </summary>
            <param name="nextChar">The char that will be read next</param>
            <param name="currentType">The currently active token type</param>
            <param name="type">The matched token type</param>
            <returns>Whether the current character is a delimiter, thereby terminate the current token immediately</returns>
        </member>
        <member name="T:Microsoft.Spatial.TypeWashedPipeline">
            <summary>
            Internal pipeline Interface that washes the distinction between Geography and Geometry
            </summary>
        </member>
        <member name="P:Microsoft.Spatial.TypeWashedPipeline.IsGeography">
            <summary>
            Gets a value indicating whether this instance is geography.
            </summary>
            <value>
            <c>true</c> if this instance is geography; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="M:Microsoft.Spatial.TypeWashedPipeline.SetCoordinateSystem(System.Nullable{System.Int32})">
            <summary>
            Set the coordinate system based on the given EPSG ID
            </summary>
            <param name="epsgId">The coordinate system ID to set. Null indicates the default should be used</param>
        </member>
        <member name="M:Microsoft.Spatial.TypeWashedPipeline.Reset">
            <summary>
            Setup the pipeline for reuse
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.TypeWashedPipeline.BeginGeo(Microsoft.Spatial.SpatialType)">
            <summary>
            Begin drawing a spatial object
            </summary>
            <param name="type">The spatial type of the object</param>
        </member>
        <member name="M:Microsoft.Spatial.TypeWashedPipeline.BeginFigure(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Begin drawing a figure
            </summary>
            <param name="coordinate1">X or Latitude Coordinate</param>
            <param name="coordinate2">Y or Longitude Coordinate</param>
            <param name="coordinate3">Z Coordinate</param>
            <param name="coordinate4">M Coordinate</param>
        </member>
        <member name="M:Microsoft.Spatial.TypeWashedPipeline.LineTo(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Add a control point to the current figure
            </summary>
            <param name="coordinate1">First coordinate</param>
            <param name="coordinate2">Second coordinate</param>
            <param name="coordinate3">Third coordinate</param>
            <param name="coordinate4">Fourth coordinate</param>
        </member>
        <member name="M:Microsoft.Spatial.TypeWashedPipeline.EndFigure">
            <summary>
            Ends the current figure
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.TypeWashedPipeline.EndGeo">
            <summary>
            Ends the current spatial object
            </summary>
        </member>
        <member name="T:Microsoft.Spatial.TypeWashedToGeographyLatLongPipeline">
            <summary>
            Adapter from the type washed API to Geography, where it assumes that coord1 is Latitude.
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.TypeWashedToGeographyLatLongPipeline.output">
            <summary>
            The pipeline to redirect the calls to
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.TypeWashedToGeographyLatLongPipeline.#ctor(Microsoft.Spatial.SpatialPipeline)">
            <summary>
            Constructor
            </summary>
            <param name="output">The pipeline to redirect the calls to</param>
        </member>
        <member name="P:Microsoft.Spatial.TypeWashedToGeographyLatLongPipeline.IsGeography">
            <summary>
            Gets a value indicating whether this instance is geography.
            </summary>
            <value>
            <c>true</c> if this instance is geography; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="M:Microsoft.Spatial.TypeWashedToGeographyLatLongPipeline.SetCoordinateSystem(System.Nullable{System.Int32})">
            <summary>
            Set the coordinate system based on the given ID
            </summary>
            <param name="epsgId">The coordinate system ID to set. Null indicates the default should be used</param>
        </member>
        <member name="M:Microsoft.Spatial.TypeWashedToGeographyLatLongPipeline.Reset">
            <summary>
            Setup the pipeline for reuse
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.TypeWashedToGeographyLatLongPipeline.BeginGeo(Microsoft.Spatial.SpatialType)">
            <summary>
            Begin drawing a spatial object
            </summary>
            <param name="type">The spatial type of the object</param>
        </member>
        <member name="M:Microsoft.Spatial.TypeWashedToGeographyLatLongPipeline.BeginFigure(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Begin drawing a figure
            </summary>
            <param name="coordinate1">1st Coordinate</param>
            <param name="coordinate2">2nd Coordinate</param>
            <param name="coordinate3">3rd Coordinate</param>
            <param name="coordinate4">4th Coordinate</param>
        </member>
        <member name="M:Microsoft.Spatial.TypeWashedToGeographyLatLongPipeline.LineTo(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Draw a line to a point in the specified coordinate
            </summary>
            <param name="coordinate1">1st Coordinate</param>
            <param name="coordinate2">2nd Coordinate</param>
            <param name="coordinate3">3rd Coordinate</param>
            <param name="coordinate4">4th Coordinate</param>
        </member>
        <member name="M:Microsoft.Spatial.TypeWashedToGeographyLatLongPipeline.EndFigure">
            <summary>
            Ends the current figure
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.TypeWashedToGeographyLatLongPipeline.EndGeo">
            <summary>
            Ends the current spatial object
            </summary>
        </member>
        <member name="T:Microsoft.Spatial.TypeWashedToGeographyLongLatPipeline">
            <summary>
            Adapter from the type washed API to Geography, where it assumes that coord1 is Longitude.
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.TypeWashedToGeographyLongLatPipeline.output">
            <summary>
            The pipeline to redirect the calls to
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.TypeWashedToGeographyLongLatPipeline.#ctor(Microsoft.Spatial.SpatialPipeline)">
            <summary>
            Constructor
            </summary>
            <param name="output">The pipeline to redirect the calls to</param>
        </member>
        <member name="P:Microsoft.Spatial.TypeWashedToGeographyLongLatPipeline.IsGeography">
            <summary>
            Gets a value indicating whether this instance is geography.
            </summary>
            <value>
            <c>true</c> if this instance is geography; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="M:Microsoft.Spatial.TypeWashedToGeographyLongLatPipeline.SetCoordinateSystem(System.Nullable{System.Int32})">
            <summary>
            Set the coordinate system based on the given ID
            </summary>
            <param name="epsgId">The coordinate system ID to set. Null indicates the default should be used</param>
        </member>
        <member name="M:Microsoft.Spatial.TypeWashedToGeographyLongLatPipeline.Reset">
            <summary>
            Setup the pipeline for reuse
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.TypeWashedToGeographyLongLatPipeline.BeginGeo(Microsoft.Spatial.SpatialType)">
            <summary>
            Begin drawing a spatial object
            </summary>
            <param name="type">The spatial type of the object</param>
        </member>
        <member name="M:Microsoft.Spatial.TypeWashedToGeographyLongLatPipeline.BeginFigure(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Begin drawing a figure
            </summary>
            <param name="coordinate1">1st Coordinate</param>
            <param name="coordinate2">2nd Coordinate</param>
            <param name="coordinate3">3rd Coordinate</param>
            <param name="coordinate4">4th Coordinate</param>
        </member>
        <member name="M:Microsoft.Spatial.TypeWashedToGeographyLongLatPipeline.LineTo(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Draw a line to a point in the specified coordinate
            </summary>
            <param name="coordinate1">1st Coordinate</param>
            <param name="coordinate2">2nd Coordinate</param>
            <param name="coordinate3">3rd Coordinate</param>
            <param name="coordinate4">4th Coordinate</param>
        </member>
        <member name="M:Microsoft.Spatial.TypeWashedToGeographyLongLatPipeline.EndFigure">
            <summary>
            Ends the current figure
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.TypeWashedToGeographyLongLatPipeline.EndGeo">
            <summary>
            Ends the current spatial object
            </summary>
        </member>
        <member name="T:Microsoft.Spatial.TypeWashedToGeometryPipeline">
            <summary>
            Adapter from the type washed API to Geometry, where it assumes that coord1 is X.
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.TypeWashedToGeometryPipeline.output">
            <summary>
            The pipeline to redirect the calls to
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.TypeWashedToGeometryPipeline.#ctor(Microsoft.Spatial.SpatialPipeline)">
            <summary>
            Constructor
            </summary>
            <param name="output">The pipeline to redirect the calls to</param>
        </member>
        <member name="P:Microsoft.Spatial.TypeWashedToGeometryPipeline.IsGeography">
            <summary>
            Gets a value indicating whether this instance is geography.
            </summary>
            <value>
            <c>true</c> if this instance is geography; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="M:Microsoft.Spatial.TypeWashedToGeometryPipeline.SetCoordinateSystem(System.Nullable{System.Int32})">
            <summary>
            Set the coordinate system based on the given ID
            </summary>
            <param name="epsgId">The coordinate system ID to set. Null indicates the default should be used</param>
        </member>
        <member name="M:Microsoft.Spatial.TypeWashedToGeometryPipeline.Reset">
            <summary>
            Setup the pipeline for reuse
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.TypeWashedToGeometryPipeline.BeginGeo(Microsoft.Spatial.SpatialType)">
            <summary>
            Begin drawing a spatial object
            </summary>
            <param name="type">The spatial type of the object</param>
        </member>
        <member name="M:Microsoft.Spatial.TypeWashedToGeometryPipeline.BeginFigure(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Begin drawing a figure
            </summary>
            <param name="coordinate1">1st Coordinate</param>
            <param name="coordinate2">2nd Coordinate</param>
            <param name="coordinate3">3rd Coordinate</param>
            <param name="coordinate4">4th Coordinate</param>
        </member>
        <member name="M:Microsoft.Spatial.TypeWashedToGeometryPipeline.LineTo(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Draw a line to a point in the specified coordinate
            </summary>
            <param name="coordinate1">1st Coordinate</param>
            <param name="coordinate2">2nd Coordinate</param>
            <param name="coordinate3">3rd Coordinate</param>
            <param name="coordinate4">4th Coordinate</param>
        </member>
        <member name="M:Microsoft.Spatial.TypeWashedToGeometryPipeline.EndFigure">
            <summary>
            Ends the current figure
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.TypeWashedToGeometryPipeline.EndGeo">
            <summary>
            Ends the current spatial object
            </summary>
        </member>
        <member name="T:Microsoft.Spatial.Util">
            <summary>
            Util class
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.Util.OutOfMemoryType">
            <summary>OutOfMemoryException exception type</summary>
        </member>
        <member name="F:Microsoft.Spatial.Util.NullReferenceType">
            <summary>NullReferenceException exception type</summary>
        </member>
        <member name="F:Microsoft.Spatial.Util.SecurityType">
            <summary>SecurityException exception type</summary>
        </member>
        <member name="M:Microsoft.Spatial.Util.CheckArgumentNull(System.Object,System.String)">
            <summary>
            Check if input is null, throw an ArgumentNullException if it is.
            </summary>
            <param name="arg">The input argument</param>
            <param name="errorMessage">The error to throw</param>
        </member>
        <member name="M:Microsoft.Spatial.Util.IsCatchableExceptionType(System.Exception)">
            <summary>
            Determines if the exception is one of the prohibited types that should not be caught.
            </summary>
            <param name="e">The exception to be checked against the prohibited list.</param>
            <returns>True if the exception is ok to be caught, false otherwise.</returns>
        </member>
        <member name="T:Microsoft.Spatial.Util.ValidatedNotNullAttribute">
            <summary>
            A workaround to a problem with FxCop which does not recognize the CheckArgumentNotNull method
            as the one which validates the argument is not null.
            </summary>
            <remarks>This has been suggested as a workaround in msdn forums by the VS team. Note that even though this is production code
            the attribute has no effect on anything else.</remarks>
        </member>
        <member name="T:Microsoft.Spatial.WellKnownTextConstants">
            <summary>
            Well Known Text Constants
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.WellKnownTextConstants.WktSrid">
            <summary>
            SRID
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.WellKnownTextConstants.WktPoint">
            <summary>
            POINT
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.WellKnownTextConstants.WktLineString">
            <summary>
            LINESTRING
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.WellKnownTextConstants.WktPolygon">
            <summary>
            POLYGON
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.WellKnownTextConstants.WktCollection">
            <summary>
            GEOMETRYCOLLECTION
            DEVNOTE: Because there is no inherent Geography support in the WKT specification,
            this constant is used for both GeographyCollection and GeometryCollection
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.WellKnownTextConstants.WktMultiPoint">
            <summary>
            MULTIPOINT
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.WellKnownTextConstants.WktMultiLineString">
            <summary>
            MULTILINESTRING
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.WellKnownTextConstants.WktMultiPolygon">
            <summary>
            MULTIPOLYGON
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.WellKnownTextConstants.WktFullGlobe">
            <summary>
            FULLGLOBE
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.WellKnownTextConstants.WktEmpty">
            <summary>
            NULL
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.WellKnownTextConstants.WktNull">
            <summary>
            NULL
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.WellKnownTextConstants.WktEquals">
            <summary>
            Equals Operator '='
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.WellKnownTextConstants.WktSemiColon">
            <summary>
            Semicolon ';'
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.WellKnownTextConstants.WktDelimiterWithWhiteSpace">
            <summary>
            Delimiter ',' + WktWhiteSpace
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.WellKnownTextConstants.WktOpenParen">
            <summary>
            Open Parenthesis '('
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.WellKnownTextConstants.WktCloseParen">
            <summary>
            Close Parenthesis ');
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.WellKnownTextConstants.WktWhitespace">
            <summary>
            Whitespace ' '
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.WellKnownTextConstants.WktPeriod">
            <summary>
            Period/Dot '.'
            </summary>
        </member>
        <member name="T:Microsoft.Spatial.WellKnownTextLexer">
            <summary>
            WellKnownText Lexer
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextLexer.#ctor(System.IO.TextReader)">
            <summary>
            Constructor
            </summary>
            <param name="text">Input text</param>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextLexer.MatchTokenType(System.Char,System.Nullable{System.Int32},System.Int32@)">
            <summary>
            Examine the current character and determine its token type
            </summary>
            <param name="nextChar">The next char that will be read.</param>
            <param name="activeTokenType">The currently active token type</param>
            <param name="tokenType">The matched token type</param>
            <returns>Whether the current character is a delimiter, thereby terminate the current token immediately</returns>
        </member>
        <member name="T:Microsoft.Spatial.WellKnownTextSqlFormatter">
            <summary>
            The object to move spatial types to and from the WellKnownTextSql format
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlFormatter.#ctor(Microsoft.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.Spatial.WellKnownTextSqlFormatter" /> class.</summary>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlFormatter.Create">
            <summary>Creates the implementation of the formatter.</summary>
            <returns>Returns the created WellKnownTextSqlFormatter implementation.</returns>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlFormatter.Create(System.Boolean)">
            <summary>Creates the implementation of the formatter and checks whether the specified formatter has Z.</summary>
            <returns>The created WellKnownTextSqlFormatter.</returns>
            <param name="allowOnlyTwoDimensions">Restricts the formatter to allow only two dimensions.</param>
        </member>
        <member name="T:Microsoft.Spatial.WellKnownTextSqlFormatterImplementation">
            <summary>
            The object to move spatial types to and from the WellKnownTextSql format
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.WellKnownTextSqlFormatterImplementation.allowOnlyTwoDimensions">
            <summary>
            restricts the writer and reader to allow only two dimensions.
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlFormatterImplementation.#ctor(Microsoft.Spatial.SpatialImplementation)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Spatial.WellKnownTextSqlFormatterImplementation"/> class.
            </summary>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlFormatterImplementation.#ctor(Microsoft.Spatial.SpatialImplementation,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Spatial.WellKnownTextSqlFormatterImplementation"/> class.
            </summary>
            <param name="creator">The implementation that created this instance.</param>
            <param name="allowOnlyTwoDimensions">restricts the reader to allow only two dimensions.</param>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlFormatterImplementation.CreateWriter(System.IO.TextWriter)">
            <summary>
            Create the writer
            </summary>
            <param name="target">The object that should be the target of the ISpatialPipeline writer.</param>
            <returns>A writer that implements ISpatialPipeline.</returns>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlFormatterImplementation.ReadGeography(System.IO.TextReader,Microsoft.Spatial.SpatialPipeline)">
            <summary>
            Reads the geography.
            </summary>
            <param name="readerStream">The reader stream.</param>
            <param name="pipeline">The pipeline.</param>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlFormatterImplementation.ReadGeometry(System.IO.TextReader,Microsoft.Spatial.SpatialPipeline)">
            <summary>
            Reads the geometry.
            </summary>
            <param name="readerStream">The reader stream.</param>
            <param name="pipeline">The pipeline.</param>
        </member>
        <member name="T:Microsoft.Spatial.WellKnownTextSqlReader">
            <summary>
            Reader for Extended Well Known Text, Case sensitive
            example:
            SRID=1234;POINT(10.0 20.0 NULL 30.0)
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.WellKnownTextSqlReader.allowOnlyTwoDimensions">
            <summary>
            restricts the reader to allow only two dimensions.
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlReader.#ctor(Microsoft.Spatial.SpatialPipeline)">
            <summary>
            Creates a reader that that will send messages to the destination during read.
            </summary>
            <param name="destination">The instance to message to during read.</param>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlReader.#ctor(Microsoft.Spatial.SpatialPipeline,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Spatial.WellKnownTextSqlReader"/> class.
            </summary>
            <param name="destination">The destination.</param>
            <param name="allowOnlyTwoDimensions">if set to <c>true</c> allows only two dimensions.</param>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlReader.ReadGeographyImplementation(System.IO.TextReader)">
            <summary>
            Parses some serialized format that represents a geography value, passing the result down the pipeline.
            </summary>
            <param name = "input">TextReader instance to read from.</param>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlReader.ReadGeometryImplementation(System.IO.TextReader)">
            <summary>
            Parses some serialized format that represents a geometry value, passing the result down the pipeline.
            </summary>
            <param name = "input">TextReader instance to read from.</param>
        </member>
        <member name="T:Microsoft.Spatial.WellKnownTextSqlReader.Parser">
            <summary>
            This class parses the text and calls the pipeline based on what is parsed
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.WellKnownTextSqlReader.Parser.allowOnlyTwoDimensions">
            <summary>
            restricts the parser to allow only two dimensions.
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.WellKnownTextSqlReader.Parser.lexer">
            <summary>
            Text lexer
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.WellKnownTextSqlReader.Parser.pipeline">
            <summary>
            Output pipeline
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlReader.Parser.#ctor(System.IO.TextReader,Microsoft.Spatial.TypeWashedPipeline,System.Boolean)">
            <summary>
            Creates a parser with the given reader and pipeline
            </summary>
            <param name="reader">The reader that is the source of what is parsed.</param>
            <param name="pipeline">The pipeline to be called as the parser recognizes tokens.</param>
            <param name="allowOnlyTwoDimensions">if set to <c>true</c> allows only two dimensions.</param>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlReader.Parser.Read">
            <summary>
            Read WellKnownText into an instance of Geography
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlReader.Parser.IsTokenMatch(Microsoft.Spatial.WellKnownTextTokenType,System.String)">
            <summary>
            Test whether the current token matches the expected token
            </summary>
            <param name="type">The expected token type</param>
            <param name="text">The expected token text</param>
            <returns>True if the two tokens match</returns>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlReader.Parser.NextToken">
            <summary>
            Move the lexer to the next non-whitespace token
            </summary>
            <returns>True if the lexer gets a new token</returns>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlReader.Parser.ParseCollectionText">
            <summary>
            Parse Collection Text
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlReader.Parser.ParseLineStringText">
            <summary>
            Parse a LineString text
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlReader.Parser.ParseMultiGeoText(Microsoft.Spatial.SpatialType,System.Action)">
            <summary>
            Parse a Multi* text
            </summary>
            <param name="innerType">The inner spatial type</param>
            <param name="innerReader">The inner reader</param>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlReader.Parser.ParsePoint(System.Boolean)">
            <summary>
            Parse Point Representation
            </summary>
            <param name="firstFigure">Whether this is the first point in the figure</param>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlReader.Parser.ParsePointText">
            <summary>
            Parse a point text
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlReader.Parser.ParsePolygonText">
            <summary>
            Parse a Polygon text
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlReader.Parser.ParseSRID">
            <summary>
            Parse an instance of SRID
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlReader.Parser.ParseTaggedText">
            <summary>
            Parse Tagged Text
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlReader.Parser.ReadDouble">
            <summary>
            Read a double literal
            </summary>
            <returns>The read double</returns>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlReader.Parser.ReadEmptySet">
            <summary>
            Check to see if the content is EMPTY
            </summary>
            <returns>True if the content is declared as EMPTY</returns>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlReader.Parser.ReadInteger">
            <summary>
            Read an integer literal
            </summary>
            <returns>The read integer</returns>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlReader.Parser.TryReadOptionalNullableDouble(System.Nullable{System.Double}@)">
            <summary>
            Read an optional double literal
            </summary>
            <param name="value">The value that was read.</param>
            <returns>true if a value was read, otherwise returns false</returns>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlReader.Parser.ReadOptionalToken(Microsoft.Spatial.WellKnownTextTokenType,System.String)">
            <summary>
            Read an optional token. If the read token matches the expected optional token, then consume it.
            </summary>
            <param name="expectedTokenType">The expected token type</param>
            <param name="expectedTokenText">The expected token text, or null</param>
            <returns>True if the optional token matches the next token in stream</returns>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlReader.Parser.ReadToken(Microsoft.Spatial.WellKnownTextTokenType,System.String)">
            <summary>
            Read and consume a token from the lexer, throw if the read token does not match the expected token
            </summary>
            <param name="type">The expected token type</param>
            <param name="text">The expected token text</param>
        </member>
        <member name="T:Microsoft.Spatial.WellKnownTextSqlWriter">
            <summary>
            WellKnownText Writer
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.WellKnownTextSqlWriter.allowOnlyTwoDimensions">
            <summary>
            restricts the writer to allow only two dimensions.
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.WellKnownTextSqlWriter.writer">
            <summary>
            The underlying writer
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.WellKnownTextSqlWriter.parentStack">
            <summary>
            Stack of spatial types currently been built
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.WellKnownTextSqlWriter.coordinateSystemWritten">
            <summary>
            Detects if a CoordinateSystem (SRID) has been written already.
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.WellKnownTextSqlWriter.figureWritten">
            <summary>
            Figure has been written to the current spatial type
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.WellKnownTextSqlWriter.shapeWritten">
            <summary>
            A shape has been written in the current nesting level
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlWriter.#ctor(System.IO.TextWriter)">
            <summary>
            Wells the known text SQL format. -- 2D writer
            </summary>
            <param name="writer">The writer.</param>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlWriter.#ctor(System.IO.TextWriter,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Spatial.WellKnownTextSqlWriter"/> class.
            </summary>
            <param name="writer">The writer.</param>
            <param name="allowOnlyTwoDimensions">if set to <c>true</c> allows only two dimensions.</param>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlWriter.OnLineTo(Microsoft.Spatial.GeographyPosition)">
            <summary>
            Draw a point in the specified coordinate
            </summary>
            <param name="position">Next position</param>
            <returns>
            The position to be passed down the pipeline
            </returns>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlWriter.OnLineTo(Microsoft.Spatial.GeometryPosition)">
            <summary>
            Draw a point in the specified coordinate
            </summary>
            <param name="position">Next position</param>
            <returns>
            The position to be passed down the pipeline
            </returns>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlWriter.OnBeginGeography(Microsoft.Spatial.SpatialType)">
            <summary>
            Begin drawing a spatial object
            </summary>
            <param name="type">The spatial type of the object</param>
            <returns>
            The type to be passed down the pipeline
            </returns>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlWriter.OnBeginGeometry(Microsoft.Spatial.SpatialType)">
            <summary>
            Begin drawing a spatial object
            </summary>
            <param name="type">The spatial type of the object</param>
            <returns>
            The type to be passed down the pipeline
            </returns>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlWriter.OnBeginFigure(Microsoft.Spatial.GeographyPosition)">
            <summary>
            Begin drawing a figure
            </summary>
            <param name="position">Next position</param>
            <returns>The position to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlWriter.OnBeginFigure(Microsoft.Spatial.GeometryPosition)">
            <summary>
            Begin drawing a figure
            </summary>
            <param name="position">Next position</param>
            <returns>The position to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlWriter.OnEndFigure">
            <summary>
            Ends the current figure
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlWriter.OnEndGeography">
            <summary>
            Ends the current spatial object
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlWriter.OnEndGeometry">
            <summary>
            Ends the current spatial object
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlWriter.OnSetCoordinateSystem(Microsoft.Spatial.CoordinateSystem)">
            <summary>
            Set the coordinate system
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <returns>
            the coordinate system to be passed down the pipeline
            </returns>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlWriter.OnReset">
            <summary>
            Setup the pipeline for reuse
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlWriter.WriteCoordinateSystem(Microsoft.Spatial.CoordinateSystem)">
            <summary>
            Write the coordinate system
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlWriter.Reset">
            <summary>
            Setup the pipeline for reuse
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlWriter.BeginGeo(Microsoft.Spatial.SpatialType)">
            <summary>
            Start to write a new Geography/Geometry
            </summary>
            <param name="type">The SpatialType to write</param>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlWriter.AddLineTo(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Adds the control point.
            </summary>
            <param name="x">The x.</param>
            <param name="y">The y.</param>
            <param name="z">The z.</param>
            <param name="m">The m.</param>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlWriter.EndFigure">
            <summary>
            Ends the figure.
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlWriter.WriteTaggedText(Microsoft.Spatial.SpatialType)">
            <summary>
            write tagged text for type
            </summary>
            <param name="type">the spatial type</param>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlWriter.WriteFigureScope(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Start to write a figure
            </summary>
            <param name="coordinate1">The coordinate1.</param>
            <param name="coordinate2">The coordinate2.</param>
            <param name="coordinate3">The coordinate3.</param>
            <param name="coordinate4">The coordinate4.</param>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlWriter.EndGeo">
            <summary>
            End the current Geography/Geometry
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.WellKnownTextSqlWriter.WritePoint(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Write out a point
            </summary>
            <param name="x">The x coordinate</param>
            <param name="y">The y coordinate</param>
            <param name="z">The z coordinate</param>
            <param name="m">The m coordinate</param>
        </member>
        <member name="T:Microsoft.Spatial.WellKnownTextTokenType">
            <summary>
            WellKnownText Lexer Token Type
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.WellKnownTextTokenType.Text">
            <summary>
            A-Z only support upper case text. i.e., POINT() instead of Point() or point()
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.WellKnownTextTokenType.Equals">
            <summary>
            character '='
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.WellKnownTextTokenType.Number">
            <summary>
            characters '0' to '9'
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.WellKnownTextTokenType.Semicolon">
            <summary>
            character ';'
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.WellKnownTextTokenType.LeftParen">
            <summary>
            character '('
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.WellKnownTextTokenType.RightParen">
            <summary>
            character ')'
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.WellKnownTextTokenType.Period">
            <summary>
            character '.'
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.WellKnownTextTokenType.Comma">
            <summary>
            character ','
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.WellKnownTextTokenType.WhiteSpace">
            <summary>
            character ' ', '\t'
            </summary>
        </member>
        <member name="T:Microsoft.Spatial.WrappedGeoJsonWriter">
            <summary>
            Writer to convert spatial types to Json that wraps a JsonWriter.
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.WrappedGeoJsonWriter.writer">
            <summary>
            The actual stream to write Json.
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.WrappedGeoJsonWriter.#ctor(Microsoft.Spatial.IGeoJsonWriter)">
            <summary>
            Constructor
            </summary>
            <param name="writer">The actual stream to write Json.</param>
        </member>
        <member name="M:Microsoft.Spatial.WrappedGeoJsonWriter.StartObjectScope">
            <summary>
            Start a new json object scope
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.WrappedGeoJsonWriter.StartArrayScope">
            <summary>
            Start a new json array scope
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.WrappedGeoJsonWriter.AddPropertyName(System.String)">
            <summary>
            Add a property name to the current json object
            </summary>
            <param name="name">The name to add</param>
        </member>
        <member name="M:Microsoft.Spatial.WrappedGeoJsonWriter.AddValue(System.String)">
            <summary>
            Add a value to the current json scope
            </summary>
            <param name="value">The value to add</param>
        </member>
        <member name="M:Microsoft.Spatial.WrappedGeoJsonWriter.AddValue(System.Double)">
            <summary>
            Add a value to the current json scope
            </summary>
            <param name="value">The value to add</param>
        </member>
        <member name="M:Microsoft.Spatial.WrappedGeoJsonWriter.EndArrayScope">
            <summary>
            End the current json array scope
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.WrappedGeoJsonWriter.EndObjectScope">
            <summary>
            End the current json object scope
            </summary>
        </member>
        <member name="T:Microsoft.Spatial.XmlConstants">
            <summary>
            Class that contains all the constants for various schemas.
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.XmlConstants.XmlnsNamespace">
            <summary>
            Namespace for xmlns
            </summary>
        </member>
        <member name="T:Microsoft.Spatial.PlatformHelper">
            <summary>
            Helper methods that provide a common API surface on all platforms.
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.PlatformHelper.EmptyTypes">
            <summary>
            Use this instead of Type.EmptyTypes.
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.PlatformHelper.DateValidator">
            <summary>
            This pattern eliminates all invalid dates, the supported format should be "YYYY-MM-DD"
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.PlatformHelper.TimeOfDayValidator">
            <summary>
            This pattern eliminates all invalid timeOfDay, the supported format should be "hh:mm:ss.fffffff"
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.PlatformHelper.PotentialDateTimeOffsetValidator">
            <summary>
            This pattern eliminates whether a text is potentially DateTimeOffset but not others like GUID, digit .etc
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.PlatformHelper.UriSchemeHttp">
            <summary>
            Replacement for Uri.UriSchemeHttp, which does not exist on.
            </summary>
        </member>
        <member name="F:Microsoft.Spatial.PlatformHelper.UriSchemeHttps">
            <summary>
            Replacement for Uri.UriSchemeHttps, which does not exist on.
            </summary>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.GetAssembly(System.Type)">
            <summary>
            Replacement for Type.Assembly.
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <returns>See documentation for property being accessed in the body of the method.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.IsValueType(System.Type)">
            <summary>
            Replacement for Type.IsValueType.
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <returns>See documentation for property being accessed in the body of the method.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.IsAbstract(System.Type)">
            <summary>
            Replacement for Type.IsAbstract.
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <returns>See documentation for property being accessed in the body of the method.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.IsGenericType(System.Type)">
            <summary>
            Replacement for Type.IsGenericType.
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <returns>See documentation for property being accessed in the body of the method.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.IsGenericTypeDefinition(System.Type)">
            <summary>
            Replacement for Type.IsGenericTypeDefinition.
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <returns>See documentation for property being accessed in the body of the method.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.IsVisible(System.Type)">
            <summary>
            Replacement for Type.IsVisible.
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <returns>See documentation for property being accessed in the body of the method.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.IsInterface(System.Type)">
            <summary>
            Replacement for Type.IsInterface.
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <returns>See documentation for property being accessed in the body of the method.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.IsClass(System.Type)">
            <summary>
            Replacement for Type.IsClass.
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <returns>See documentation for property being accessed in the body of the method.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.IsEnum(System.Type)">
            <summary>
            Replacement for Type.IsEnum.
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <returns>See documentation for property being accessed in the body of the method.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.GetBaseType(System.Type)">
            <summary>
            Replacement for Type.BaseType.
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <returns>See documentation for property being accessed in the body of the method.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.ContainsGenericParameters(System.Type)">
            <summary>
            Replacement for Type.ContainsGenericParameters.
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <returns>See documentation for property being accessed in the body of the method.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.ConvertStringToDateTimeOffset(System.String)">
            <summary>
            Converts a string to a DateTimeOffset.
            </summary>
            <param name="text">String to be converted.</param>
            <returns>See documentation for method being accessed in the body of the method.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.ValidateTimeZoneInformationInDateTimeOffsetString(System.String)">
            <summary>
            Validates that the DateTimeOffset string contains the time zone information.
            </summary>
            <param name="text">String to be validated.</param>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.AddSecondsPaddingIfMissing(System.String)">
            <summary>
            Adds the seconds padding as zeros to the date time string if seconds part is missing.
            </summary>
            <param name="text">String that needs seconds padding</param>
            <returns>DateTime string after adding seconds padding</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.GetTypeOrThrow(System.String)">
            <summary>
            Gets the specified type.
            </summary>
            <param name="typeName">Name of the type to get.</param>
            <exception cref="T:System.TypeLoadException">Throws if the type could not be found.</exception>
            <returns>Type instance that represents the specified type name.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.GetUnicodeCategory(System.Char)">
            <summary>
            Gets the Unicode Category of the specified character.
            </summary>
            <param name="c">Character to get category of.</param>
            <returns>Category of the character.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.IsProperty(System.Reflection.MemberInfo)">
            <summary>
            Replacement for usage of MemberInfo.MemberType property.
            </summary>
            <param name="member">MemberInfo on which to access this method.</param>
            <returns>True if the specified member is a property, otherwise false.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.IsPrimitive(System.Type)">
            <summary>
            Replacement for usage of Type.IsPrimitive property.
            </summary>
            <param name="type">Type on which to access this method.</param>
            <returns>True if the specified type is primitive, otherwise false.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.IsSealed(System.Type)">
            <summary>
            Replacement for usage of Type.IsSealed property.
            </summary>
            <param name="type">Type on which to access this method.</param>
            <returns>True if the specified type is sealed, otherwise false.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.IsMethod(System.Reflection.MemberInfo)">
            <summary>
            Replacement for usage of MemberInfo.MemberType property.
            </summary>
            <param name="member">MemberInfo on which to access this method.</param>
            <returns>True if the specified member is a method, otherwise false.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.AreMembersEqual(System.Reflection.MemberInfo,System.Reflection.MemberInfo)">
            <summary>
            Compares two methodInfos and returns true if they represent the same method.
            Need this for Windows Phone as the method Infos of the same method are not always instance equivalent.
            </summary>
            <param name="member1">MemberInfo to compare.</param>
            <param name="member2">MemberInfo to compare.</param>
            <returns>True if the specified member is a method, otherwise false.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.GetPublicProperties(System.Type,System.Boolean)">
            <summary>
            Gets public properties for the specified type.
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <param name="instanceOnly">True if method should return only instance properties, false if it should return both instance and static properties.</param>
            <returns>Enumerable of public properties for the type.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.GetPublicProperties(System.Type,System.Boolean,System.Boolean)">
            <summary>
            Gets public properties for the specified type.
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <param name="instanceOnly">True if method should return only instance properties, false if it should return both instance and static properties.</param>
            <param name="declaredOnly">True if method should return only properties that are declared on the type, false if it should return properties declared on the type as well as those inherited from any base types.</param>
            <returns>Enumerable of public properties for the type.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.GetNonPublicProperties(System.Type,System.Boolean,System.Boolean)">
            <summary>
            Gets non public properties for the specified type.
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <param name="instanceOnly">True if method should return only instance properties, false if it should return both instance and static properties.</param>
            <param name="declaredOnly">True if method should return only properties that are declared on the type, false if it should return properties declared on the type as well as those inherited from any base types.</param>
            <returns>Enumerable of non public properties for the type.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.GetInstanceConstructors(System.Type,System.Boolean)">
            <summary>
            Gets instance constructors for the specified type.
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <param name="isPublic">True if method should return only public constructors, false if it should return only non-public constructors.</param>
            <returns>Enumerable of instance constructors for the specified type.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.GetInstanceConstructor(System.Type,System.Boolean,System.Type[])">
            <summary>
            Gets a instance constructor for the type that takes the specified argument types.
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <param name="isPublic">True if method should search only public constructors, false if it should search only non-public constructors.</param>
            <param name="argTypes">Array of argument types for the constructor.</param>
            <returns>ConstructorInfo for the constructor with the specified characteristics if found, otherwise null.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.TryGetMethod(System.Type,System.String,System.Type[],System.Reflection.MethodInfo@)">
            <summary>
            Tries to the get method from the type, returns null if not found.
            </summary>
            <param name="type">The type.</param>
            <param name="name">The name.</param>
            <param name="parameterTypes">The parameter types.</param>
            <param name="foundMethod">The method output.</param>
            <returns>Returns True if found.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.GetMethods(System.Type)">
            <summary>
            Gets all methods on the specified type.
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <returns>Enumerable of all methods for the specified type.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.GetMethod(System.Type,System.String,System.Boolean,System.Boolean)">
            <summary>
            Gets a method on the specified type.
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <param name="name">Name of the method on the type.</param>
            <param name="isPublic">True if method should search only public methods, false if it should search only non-public methods.</param>
            <param name="isStatic">True if method should search only static methods, false if it should search only instance methods.</param>
            <returns>MethodInfo for the method with the specified characteristics if found, otherwise null.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.GetMethod(System.Type,System.String,System.Type[],System.Boolean,System.Boolean)">
            <summary>
            Gets a method on the specified type.
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <param name="name">Name of the method on the type.</param>
            <param name="types">Argument types for the method.</param>
            <param name="isPublic">True if method should search only public methods, false if it should search only non-public methods.</param>
            <param name="isStatic">True if method should search only static methods, false if it should search only instance methods.</param>
            <returns>MethodInfo for the method with the specified characteristics if found, otherwise null.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.GetPublicStaticMethods(System.Type)">
            <summary>
            Gets all public static methods for a type.
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <returns>Enumerable of all public static methods for the specified type.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.GetNonPublicNestedTypes(System.Type)">
            <summary>
            Replacement for Type.GetNestedTypes(BindingFlags.NonPublic)
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <returns>All types nested in the current type</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.CheckTypeArgs(System.Reflection.ConstructorInfo,System.Type[])">
            <summary>
            Checks if the specified constructor takes arguments of the specified types.
            </summary>
            <param name="constructorInfo">ConstructorInfo on which to call this helper method.</param>
            <param name="types">Array of type arguments to check against the constructor parameters.</param>
            <returns>True if the constructor takes arguments of the specified types, otherwise false.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.IsAssignableFrom(System.Type,System.Type)">
            <summary>
            Replacement for Type.IsAssignableFrom(Type)
            </summary>
            <param name="thisType">Type on which to call this helper method.</param>
            <param name="otherType">Type to test for assignability.</param>
            <returns>See documentation for method being accessed in the body of the method.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.IsSubclassOf(System.Type,System.Type)">
            <summary>
            Replacement for Type.IsSubclassOf(Type).
            </summary>
            <param name="thisType">Type on which to call this helper method.</param>
            <param name="otherType">Type to test if typeType is a subclass.</param>
            <returns>True if thisType is a subclass of otherType, otherwise false.</returns>
            <remarks>
            TODO: Add this back to TypeInfo. This method will still be needed since it works on Type, but the
                  implementation should just be able to call the TypeInfo version directly instead of the full implementation here.
            </remarks>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.GetMethod(System.Type,System.String)">
            <summary>
            Replacement for GetMethod(string).
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <param name="name">Method to find on the specified type.</param>
            <returns>MethodInfo if one was found for the specified type, otherwise false.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.GetMethod(System.Type,System.String,System.Type[])">
            <summary>
            Replacement for Type.GetMethod(string, Type[]).
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <param name="name">Name of method to find on the specified type.</param>
            <param name="types">Array of arguments to the method.</param>
            <returns>MethodInfo if one was found for the specified type, otherwise false.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.GetMethodWithGenericArgs(System.Type,System.String,System.Boolean,System.Boolean,System.Int32)">
            <summary>
            Gets a MethodInfo from the specified type. Replaces uses of Type.GetMember.
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <param name="name">Name of the method to find.</param>
            <param name="isPublic">True if the method is public, false otherwise.</param>
            <param name="isStatic">True if the method is static, false otherwise.</param>
            <param name="genericArgCount">Number of generics arguments the method has.</param>
            <returns>MethodInfo for the method that was found.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.GetProperty(System.Type,System.String,System.Type)">
            <summary>
            Replacement for Type.GetProperty(string, Type).
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <param name="name">Name of public property to find on the specified type.</param>
            <param name="returnType">Return type for the property.</param>
            <returns>PropertyInfo if a property was found on the type with the specified name and return type, otherwise null.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.GetProperty(System.Type,System.String)">
            <summary>
            Replacement for Type.GetProperty(string).
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <param name="name">Name of public property to find on the specified type.</param>
            <returns>PropertyInfo if a property was found on the type with the specified name and return type, otherwise null.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.GetGetMethod(System.Reflection.PropertyInfo)">
            <summary>
            Replacement for PropertyInfo.GetGetMethod().
            </summary>
            <param name="propertyInfo">PropertyInfo on which to call this helper method.</param>
            <returns>MethodInfo for the public get accessor of the specified PropertyInfo, or null if there is no get accessor or it is non-public.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.GetSetMethod(System.Reflection.PropertyInfo)">
            <summary>
            Replacement for PropertyInfo.GetSetMethod().
            </summary>
            <param name="propertyInfo">PropertyInfo on which to call this helper method.</param>
            <returns>MethodInfo for the public set accessor of the specified PropertyInfo, or null if there is no set accessor or it is non-public.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.GetBaseDefinition(System.Reflection.MethodInfo)">
            <summary>
            Replacement for MethodInfo.GetBaseDefinition().
            </summary>
            <param name="methodInfo">MethodInfo on which to call this helper method.</param>
            <returns>See documentation for method being accessed in the body of the method.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.GetProperties(System.Type)">
            <summary>
            Replacement for Type.GetProperties().
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <returns>Enumerable of all instance and static public properties on the type.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.GetFields(System.Type)">
            <summary>
            Replacement for Type.GetFields(string).
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <returns>Enumerable of all public instance fields for the specified type.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.GetCustomAttributes(System.Type,System.Type,System.Boolean)">
            <summary>
            Replacement for Type.GetCustomAttributes(Type, bool).
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <param name="attributeType">Attribute type to find on the specified type.</param>
            <param name="inherit">True if the base types should be searched, false otherwise.</param>
            <returns>See documentation for method being accessed in the body of the method.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.GetCustomAttributes(System.Type,System.Boolean)">
            <summary>
            Replacement for Type.GetCustomAttributes(bool).
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <param name="inherit">True if the base types should be searched, false otherwise.</param>
            <returns>See documentation for method being accessed in the body of the method.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.GetGenericArguments(System.Type)">
            <summary>
            Replacement for Type.GetGenericArguments().
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <returns>Array of Type objects that represent the type arguments of a generic type or the type parameters of a generic type definition.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.GetInterfaces(System.Type)">
            <summary>
            Replacement for Type.GetInterfaces().
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <returns>See documentation for property being accessed in the body of the method.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.IsInstanceOfType(System.Type,System.Object)">
            <summary>
            Replacement for Type.IsInstanceOfType(object).
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <param name="obj">Object to test to see if it's an instance of the specified type.</param>
            <returns>See documentation for method being accessed in the body of the method.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.GetType(System.Reflection.Assembly,System.String,System.Boolean)">
            <summary>
            Replacement for Assembly.GetType(string, bool).
            </summary>
            <param name="assembly">Assembly on which to call this helper method.</param>
            <param name="typeName">Name of the type to get from the assembly.</param>
            <param name="throwOnError">True if an exception should be thrown if the type cannot be found, otherwise false.</param>
            <returns>Type instance if the type could be found in the assembly, otherwise null.</returns>
            <remarks>
            TODO: Add a new method called Assembly.GetDefinedType(string) that returns a TypeInfo and will throw like Assembly.GetType(string, true) used to.
                  This helper method will still be needed but should be updated to use the new implementation once it exists.
            </remarks>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.GetTypes(System.Reflection.Assembly)">
            <summary>
            Replacement for Assembly.GetTypes().
            </summary>
            <param name="assembly">Assembly on which to call this helper method.</param>
            <returns>Enumerable of the types in the assembly.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.GetField(System.Type,System.String)">
            <summary>
            Replacement for GetField(string).
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <param name="name">Method to find on the specified type.</param>
            <returns>FieldInfo if one was found for the specified type, otherwise false.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.IsInstance(System.Reflection.PropertyInfo)">
            <summary>
            Checks if the specified PropertyInfo is an instance property.
            </summary>
            <param name="propertyInfo">PropertyInfo on which to call this helper method.</param>
            <returns>True if either the GetMethod or SetMethod for the property is an instance method.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.IsPublic(System.Reflection.PropertyInfo)">
            <summary>
            Checks if the specified PropertyInfo is a public property.
            </summary>
            <param name="propertyInfo">PropertyInfo on which to call this helper method.</param>
            <returns>True if either the GetMethod or SetMethod for the property is public.</returns>
        </member>
        <member name="M:Microsoft.Spatial.PlatformHelper.CreateCompiled(System.String,System.Text.RegularExpressions.RegexOptions)">
            <summary>
            Creates a Compiled Regex expression
            </summary>
            <param name="pattern">Pattern to match.</param>
            <param name="options">Options to use.</param>
            <returns>Regex expression to match supplied patter</returns>
            <remarks>Is marked as compiled option only in platforms otherwise RegexOption.None is used</remarks>
        </member>
        <member name="T:AssemblyRef">
            <summary>
            Sets public key string for friend assemblies.
            </summary>
        </member>
        <member name="F:AssemblyRef.ProductPublicKey">
            <summary>ProductPublicKey is an official MS supported public key for external releases.</summary>
        </member>
        <member name="F:AssemblyRef.TestPublicKey">
            <summary>TestPublicKey is an unsupported strong key for testing purpose only.</summary>
        </member>
        <member name="F:AssemblyRef.ProductPublicKeyToken">
            <summary>Don't know what this is</summary>
        </member>
    </members>
</doc>
