#
# Module manifest for module 'Microsoft.Graph.Users'
#
# Generated by: Microsoft Corporation
#
# Generated on: 7/9/2025
#

@{

# Script module or binary module file associated with this manifest.
RootModule = './Microsoft.Graph.Users.psm1'

# Version number of this module.
ModuleVersion = '2.29.0'

# Supported PSEditions
CompatiblePSEditions = 'Core', 'Desktop'

# ID used to uniquely identify this module
GUID = '71150504-37a3-48c6-82c7-7a00a12168db'

# Author of this module
Author = 'Microsoft Corporation'

# Company or vendor of this module
CompanyName = 'Microsoft Corporation'

# Copyright statement for this module
Copyright = 'Microsoft Corporation. All rights reserved.'

# Description of the functionality provided by this module
Description = 'Microsoft Graph PowerShell Cmdlets'

# Minimum version of the PowerShell engine required by this module
PowerShellVersion = '5.1'

# Name of the PowerShell host required by this module
# PowerShellHostName = ''

# Minimum version of the PowerShell host required by this module
# PowerShellHostVersion = ''

# Minimum version of Microsoft .NET Framework required by this module. This prerequisite is valid for the PowerShell Desktop edition only.
DotNetFrameworkVersion = '4.7.2'

# Minimum version of the common language runtime (CLR) required by this module. This prerequisite is valid for the PowerShell Desktop edition only.
# ClrVersion = ''

# Processor architecture (None, X86, Amd64) required by this module
# ProcessorArchitecture = ''

# Modules that must be imported into the global environment prior to importing this module
RequiredModules = @(@{ModuleName = 'Microsoft.Graph.Authentication'; RequiredVersion = '2.29.0'; })

# Assemblies that must be loaded prior to importing this module
RequiredAssemblies = './bin/Microsoft.Graph.Users.private.dll'

# Script files (.ps1) that are run in the caller's environment prior to importing this module.
# ScriptsToProcess = @()

# Type files (.ps1xml) to be loaded when importing this module
# TypesToProcess = @()

# Format files (.ps1xml) to be loaded when importing this module
FormatsToProcess = './Microsoft.Graph.Users.format.ps1xml'

# Modules to import as nested modules of the module specified in RootModule/ModuleToProcess
# NestedModules = @()

# Functions to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no functions to export.
FunctionsToExport = 'Get-MgUser', 'Get-MgUserByUserPrincipalName', 'Get-MgUserCount', 
               'Get-MgUserCreatedObject', 
               'Get-MgUserCreatedObjectAsServicePrincipal', 
               'Get-MgUserCreatedObjectCount', 
               'Get-MgUserCreatedObjectCountAsServicePrincipal', 
               'Get-MgUserDirectReport', 'Get-MgUserDirectReportAsOrgContact', 
               'Get-MgUserDirectReportAsUser', 'Get-MgUserDirectReportCount', 
               'Get-MgUserDirectReportCountAsOrgContact', 
               'Get-MgUserDirectReportCountAsUser', 'Get-MgUserExtension', 
               'Get-MgUserExtensionCount', 'Get-MgUserInsight', 
               'Get-MgUserInsightShared', 'Get-MgUserInsightSharedCount', 
               'Get-MgUserInsightSharedLastSharedMethod', 
               'Get-MgUserInsightSharedResource', 'Get-MgUserInsightTrending', 
               'Get-MgUserInsightTrendingCount', 
               'Get-MgUserInsightTrendingResource', 'Get-MgUserInsightUsed', 
               'Get-MgUserInsightUsedCount', 'Get-MgUserInsightUsedResource', 
               'Get-MgUserLicenseDetail', 'Get-MgUserLicenseDetailCount', 
               'Get-MgUserLicenseDetailTeamLicensingDetail', 
               'Get-MgUserMailboxSetting', 'Get-MgUserManager', 
               'Get-MgUserManagerByRef', 'Get-MgUserMemberOf', 
               'Get-MgUserMemberOfAsAdministrativeUnit', 
               'Get-MgUserMemberOfAsDirectoryRole', 'Get-MgUserMemberOfAsGroup', 
               'Get-MgUserMemberOfCount', 
               'Get-MgUserMemberOfCountAsAdministrativeUnit', 
               'Get-MgUserMemberOfCountAsDirectoryRole', 
               'Get-MgUserMemberOfCountAsGroup', 'Get-MgUserOauth2PermissionGrant', 
               'Get-MgUserOauth2PermissionGrantCount', 
               'Get-MgUserOutlookMasterCategory', 
               'Get-MgUserOutlookMasterCategoryCount', 'Get-MgUserOwnedDevice', 
               'Get-MgUserOwnedDeviceAsAppRoleAssignment', 
               'Get-MgUserOwnedDeviceAsDevice', 'Get-MgUserOwnedDeviceAsEndpoint', 
               'Get-MgUserOwnedDeviceCount', 
               'Get-MgUserOwnedDeviceCountAsAppRoleAssignment', 
               'Get-MgUserOwnedDeviceCountAsDevice', 
               'Get-MgUserOwnedDeviceCountAsEndpoint', 'Get-MgUserOwnedObject', 
               'Get-MgUserOwnedObjectAsApplication', 
               'Get-MgUserOwnedObjectAsGroup', 
               'Get-MgUserOwnedObjectAsServicePrincipal', 
               'Get-MgUserOwnedObjectCount', 
               'Get-MgUserOwnedObjectCountAsApplication', 
               'Get-MgUserOwnedObjectCountAsGroup', 
               'Get-MgUserOwnedObjectCountAsServicePrincipal', 'Get-MgUserPhoto', 
               'Get-MgUserPhotoContent', 'Get-MgUserRegisteredDevice', 
               'Get-MgUserRegisteredDeviceAsAppRoleAssignment', 
               'Get-MgUserRegisteredDeviceAsDevice', 
               'Get-MgUserRegisteredDeviceAsEndpoint', 
               'Get-MgUserRegisteredDeviceCount', 
               'Get-MgUserRegisteredDeviceCountAsAppRoleAssignment', 
               'Get-MgUserRegisteredDeviceCountAsDevice', 
               'Get-MgUserRegisteredDeviceCountAsEndpoint', 'Get-MgUserSetting', 
               'Get-MgUserSettingItemInsight', 'Get-MgUserSettingShiftPreference', 
               'Get-MgUserSettingStorage', 'Get-MgUserSettingStorageQuota', 
               'Get-MgUserSettingStorageQuotaService', 
               'Get-MgUserSettingStorageQuotaServiceCount', 
               'Get-MgUserSettingWindows', 'Get-MgUserSettingWindowsCount', 
               'Get-MgUserSettingWindowsInstance', 
               'Get-MgUserSettingWindowsInstanceCount', 'Get-MgUserSponsor', 
               'Get-MgUserSponsorCount', 'Get-MgUserTodoList', 
               'Get-MgUserTodoListCount', 'Get-MgUserTodoListDelta', 
               'Get-MgUserTodoListExtension', 'Get-MgUserTodoListExtensionCount', 
               'Get-MgUserTodoTask', 'Get-MgUserTodoTaskAttachment', 
               'Get-MgUserTodoTaskAttachmentContent', 
               'Get-MgUserTodoTaskAttachmentCount', 
               'Get-MgUserTodoTaskAttachmentSession', 
               'Get-MgUserTodoTaskAttachmentSessionContent', 
               'Get-MgUserTodoTaskAttachmentSessionCount', 
               'Get-MgUserTodoTaskChecklistItem', 
               'Get-MgUserTodoTaskChecklistItemCount', 'Get-MgUserTodoTaskCount', 
               'Get-MgUserTodoTaskDelta', 'Get-MgUserTodoTaskExtension', 
               'Get-MgUserTodoTaskExtensionCount', 
               'Get-MgUserTodoTaskLinkedResource', 
               'Get-MgUserTodoTaskLinkedResourceCount', 
               'Get-MgUserTransitiveMemberOf', 
               'Get-MgUserTransitiveMemberOfAsAdministrativeUnit', 
               'Get-MgUserTransitiveMemberOfAsDirectoryRole', 
               'Get-MgUserTransitiveMemberOfAsGroup', 
               'Get-MgUserTransitiveMemberOfCount', 
               'Get-MgUserTransitiveMemberOfCountAsAdministrativeUnit', 
               'Get-MgUserTransitiveMemberOfCountAsDirectoryRole', 
               'Get-MgUserTransitiveMemberOfCountAsGroup', 
               'Invoke-MgSupportedUserOutlookLanguage', 'Invoke-MgTimeUserOutlook', 
               'New-MgUser', 'New-MgUserExtension', 'New-MgUserInsightShared', 
               'New-MgUserInsightTrending', 'New-MgUserInsightUsed', 
               'New-MgUserOutlookMasterCategory', 
               'New-MgUserSettingStorageQuotaService', 'New-MgUserSettingWindows', 
               'New-MgUserSettingWindowsInstance', 'New-MgUserTodoList', 
               'New-MgUserTodoListExtension', 'New-MgUserTodoListTask', 
               'New-MgUserTodoListTaskAttachment', 
               'New-MgUserTodoListTaskAttachmentUploadSession', 
               'New-MgUserTodoListTaskChecklistItem', 
               'New-MgUserTodoListTaskExtension', 
               'New-MgUserTodoListTaskLinkedResource', 'Remove-MgUser', 
               'Remove-MgUserByUserPrincipalName', 'Remove-MgUserExtension', 
               'Remove-MgUserInsight', 'Remove-MgUserInsightShared', 
               'Remove-MgUserInsightTrending', 'Remove-MgUserInsightUsed', 
               'Remove-MgUserLicenseDetail', 'Remove-MgUserManagerByRef', 
               'Remove-MgUserOutlookMasterCategory', 'Remove-MgUserPhoto', 
               'Remove-MgUserPhotoContent', 'Remove-MgUserSetting', 
               'Remove-MgUserSettingItemInsight', 
               'Remove-MgUserSettingShiftPreference', 
               'Remove-MgUserSettingStorage', 'Remove-MgUserSettingStorageQuota', 
               'Remove-MgUserSettingStorageQuotaService', 
               'Remove-MgUserSettingWindows', 
               'Remove-MgUserSettingWindowsInstance', 'Remove-MgUserTodoList', 
               'Remove-MgUserTodoListExtension', 'Remove-MgUserTodoListTask', 
               'Remove-MgUserTodoListTaskAttachment', 
               'Remove-MgUserTodoListTaskAttachmentContent', 
               'Remove-MgUserTodoListTaskAttachmentSession', 
               'Remove-MgUserTodoListTaskAttachmentSessionContent', 
               'Remove-MgUserTodoListTaskChecklistItem', 
               'Remove-MgUserTodoListTaskExtension', 
               'Remove-MgUserTodoListTaskLinkedResource', 'Set-MgUserManagerByRef', 
               'Set-MgUserPhotoContent', 'Set-MgUserTodoListTaskAttachmentContent', 
               'Set-MgUserTodoListTaskAttachmentSessionContent', 'Update-MgUser', 
               'Update-MgUserByUserPrincipalName', 'Update-MgUserExtension', 
               'Update-MgUserInsight', 'Update-MgUserInsightShared', 
               'Update-MgUserInsightTrending', 'Update-MgUserInsightUsed', 
               'Update-MgUserLicenseDetail', 'Update-MgUserMailboxSetting', 
               'Update-MgUserOutlookMasterCategory', 'Update-MgUserSetting', 
               'Update-MgUserSettingItemInsight', 
               'Update-MgUserSettingShiftPreference', 
               'Update-MgUserSettingStorage', 'Update-MgUserSettingStorageQuota', 
               'Update-MgUserSettingStorageQuotaService', 
               'Update-MgUserSettingWindows', 
               'Update-MgUserSettingWindowsInstance', 'Update-MgUserTodoList', 
               'Update-MgUserTodoListExtension', 'Update-MgUserTodoListTask', 
               'Update-MgUserTodoListTaskAttachmentSession', 
               'Update-MgUserTodoListTaskChecklistItem', 
               'Update-MgUserTodoListTaskExtension', 
               'Update-MgUserTodoListTaskLinkedResource'

# Cmdlets to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no cmdlets to export.
CmdletsToExport = @()

# Variables to export from this module
# VariablesToExport = @()

# Aliases to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no aliases to export.
AliasesToExport = 'Set-MgUser'

# DSC resources to export from this module
# DscResourcesToExport = @()

# List of all modules packaged with this module
# ModuleList = @()

# List of all files packaged with this module
# FileList = @()

# Private data to pass to the module specified in RootModule/ModuleToProcess. This may also contain a PSData hashtable with additional module metadata used by PowerShell.
PrivateData = @{

    PSData = @{

        # Tags applied to this module. These help with module discovery in online galleries.
        Tags = 'Microsoft','Office365','Graph','PowerShell','PSModule','PSIncludes_Cmdlet'

        # A URL to the license for this module.
        LicenseUri = 'https://aka.ms/devservicesagreement'

        # A URL to the main website for this project.
        ProjectUri = 'https://github.com/microsoftgraph/msgraph-sdk-powershell'

        # A URL to an icon representing this module.
        IconUri = 'https://raw.githubusercontent.com/microsoftgraph/msgraph-sdk-powershell/dev/docs/images/graph_color256.png'

        # ReleaseNotes of this module
        ReleaseNotes = 'See https://aka.ms/GraphPowerShell-Release.'

        # Prerelease string of this module
        # Prerelease = ''

        # Flag to indicate whether the module requires explicit user acceptance for install/update/save
        # RequireLicenseAcceptance = $false

        # External dependent modules of this module
        # ExternalModuleDependencies = @()

    } # End of PSData hashtable

 } # End of PrivateData hashtable

# HelpInfo URI of this module
# HelpInfoURI = ''

# Default prefix for commands exported from this module. Override the default prefix using Import-Module -Prefix.
# DefaultCommandPrefix = ''

}


# SIG # Begin signature block
# MIIoOQYJKoZIhvcNAQcCoIIoKjCCKCYCAQExDzANBglghkgBZQMEAgEFADB5Bgor
# BgEEAYI3AgEEoGswaTA0BgorBgEEAYI3AgEeMCYCAwEAAAQQH8w7YFlLCE63JNLG
# KX7zUQIBAAIBAAIBAAIBAAIBADAxMA0GCWCGSAFlAwQCAQUABCCkBYJuebHjZGgh
# KddXoTEZ1FotJ3l0xV8066YWWrMbMaCCDYUwggYDMIID66ADAgECAhMzAAAEA73V
# lV0POxitAAAAAAQDMA0GCSqGSIb3DQEBCwUAMH4xCzAJBgNVBAYTAlVTMRMwEQYD
# VQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNy
# b3NvZnQgQ29ycG9yYXRpb24xKDAmBgNVBAMTH01pY3Jvc29mdCBDb2RlIFNpZ25p
# bmcgUENBIDIwMTEwHhcNMjQwOTEyMjAxMTEzWhcNMjUwOTExMjAxMTEzWjB0MQsw
# CQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9u
# ZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMR4wHAYDVQQDExVNaWNy
# b3NvZnQgQ29ycG9yYXRpb24wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIB
# AQCfdGddwIOnbRYUyg03O3iz19XXZPmuhEmW/5uyEN+8mgxl+HJGeLGBR8YButGV
# LVK38RxcVcPYyFGQXcKcxgih4w4y4zJi3GvawLYHlsNExQwz+v0jgY/aejBS2EJY
# oUhLVE+UzRihV8ooxoftsmKLb2xb7BoFS6UAo3Zz4afnOdqI7FGoi7g4vx/0MIdi
# kwTn5N56TdIv3mwfkZCFmrsKpN0zR8HD8WYsvH3xKkG7u/xdqmhPPqMmnI2jOFw/
# /n2aL8W7i1Pasja8PnRXH/QaVH0M1nanL+LI9TsMb/enWfXOW65Gne5cqMN9Uofv
# ENtdwwEmJ3bZrcI9u4LZAkujAgMBAAGjggGCMIIBfjAfBgNVHSUEGDAWBgorBgEE
# AYI3TAgBBggrBgEFBQcDAzAdBgNVHQ4EFgQU6m4qAkpz4641iK2irF8eWsSBcBkw
# VAYDVR0RBE0wS6RJMEcxLTArBgNVBAsTJE1pY3Jvc29mdCBJcmVsYW5kIE9wZXJh
# dGlvbnMgTGltaXRlZDEWMBQGA1UEBRMNMjMwMDEyKzUwMjkyNjAfBgNVHSMEGDAW
# gBRIbmTlUAXTgqoXNzcitW2oynUClTBUBgNVHR8ETTBLMEmgR6BFhkNodHRwOi8v
# d3d3Lm1pY3Jvc29mdC5jb20vcGtpb3BzL2NybC9NaWNDb2RTaWdQQ0EyMDExXzIw
# MTEtMDctMDguY3JsMGEGCCsGAQUFBwEBBFUwUzBRBggrBgEFBQcwAoZFaHR0cDov
# L3d3dy5taWNyb3NvZnQuY29tL3BraW9wcy9jZXJ0cy9NaWNDb2RTaWdQQ0EyMDEx
# XzIwMTEtMDctMDguY3J0MAwGA1UdEwEB/wQCMAAwDQYJKoZIhvcNAQELBQADggIB
# AFFo/6E4LX51IqFuoKvUsi80QytGI5ASQ9zsPpBa0z78hutiJd6w154JkcIx/f7r
# EBK4NhD4DIFNfRiVdI7EacEs7OAS6QHF7Nt+eFRNOTtgHb9PExRy4EI/jnMwzQJV
# NokTxu2WgHr/fBsWs6G9AcIgvHjWNN3qRSrhsgEdqHc0bRDUf8UILAdEZOMBvKLC
# rmf+kJPEvPldgK7hFO/L9kmcVe67BnKejDKO73Sa56AJOhM7CkeATrJFxO9GLXos
# oKvrwBvynxAg18W+pagTAkJefzneuWSmniTurPCUE2JnvW7DalvONDOtG01sIVAB
# +ahO2wcUPa2Zm9AiDVBWTMz9XUoKMcvngi2oqbsDLhbK+pYrRUgRpNt0y1sxZsXO
# raGRF8lM2cWvtEkV5UL+TQM1ppv5unDHkW8JS+QnfPbB8dZVRyRmMQ4aY/tx5x5+
# sX6semJ//FbiclSMxSI+zINu1jYerdUwuCi+P6p7SmQmClhDM+6Q+btE2FtpsU0W
# +r6RdYFf/P+nK6j2otl9Nvr3tWLu+WXmz8MGM+18ynJ+lYbSmFWcAj7SYziAfT0s
# IwlQRFkyC71tsIZUhBHtxPliGUu362lIO0Lpe0DOrg8lspnEWOkHnCT5JEnWCbzu
# iVt8RX1IV07uIveNZuOBWLVCzWJjEGa+HhaEtavjy6i7MIIHejCCBWKgAwIBAgIK
# YQ6Q0gAAAAAAAzANBgkqhkiG9w0BAQsFADCBiDELMAkGA1UEBhMCVVMxEzARBgNV
# BAgTCldhc2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jv
# c29mdCBDb3Jwb3JhdGlvbjEyMDAGA1UEAxMpTWljcm9zb2Z0IFJvb3QgQ2VydGlm
# aWNhdGUgQXV0aG9yaXR5IDIwMTEwHhcNMTEwNzA4MjA1OTA5WhcNMjYwNzA4MjEw
# OTA5WjB+MQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UE
# BxMHUmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSgwJgYD
# VQQDEx9NaWNyb3NvZnQgQ29kZSBTaWduaW5nIFBDQSAyMDExMIICIjANBgkqhkiG
# 9w0BAQEFAAOCAg8AMIICCgKCAgEAq/D6chAcLq3YbqqCEE00uvK2WCGfQhsqa+la
# UKq4BjgaBEm6f8MMHt03a8YS2AvwOMKZBrDIOdUBFDFC04kNeWSHfpRgJGyvnkmc
# 6Whe0t+bU7IKLMOv2akrrnoJr9eWWcpgGgXpZnboMlImEi/nqwhQz7NEt13YxC4D
# dato88tt8zpcoRb0RrrgOGSsbmQ1eKagYw8t00CT+OPeBw3VXHmlSSnnDb6gE3e+
# lD3v++MrWhAfTVYoonpy4BI6t0le2O3tQ5GD2Xuye4Yb2T6xjF3oiU+EGvKhL1nk
# kDstrjNYxbc+/jLTswM9sbKvkjh+0p2ALPVOVpEhNSXDOW5kf1O6nA+tGSOEy/S6
# A4aN91/w0FK/jJSHvMAhdCVfGCi2zCcoOCWYOUo2z3yxkq4cI6epZuxhH2rhKEmd
# X4jiJV3TIUs+UsS1Vz8kA/DRelsv1SPjcF0PUUZ3s/gA4bysAoJf28AVs70b1FVL
# 5zmhD+kjSbwYuER8ReTBw3J64HLnJN+/RpnF78IcV9uDjexNSTCnq47f7Fufr/zd
# sGbiwZeBe+3W7UvnSSmnEyimp31ngOaKYnhfsi+E11ecXL93KCjx7W3DKI8sj0A3
# T8HhhUSJxAlMxdSlQy90lfdu+HggWCwTXWCVmj5PM4TasIgX3p5O9JawvEagbJjS
# 4NaIjAsCAwEAAaOCAe0wggHpMBAGCSsGAQQBgjcVAQQDAgEAMB0GA1UdDgQWBBRI
# bmTlUAXTgqoXNzcitW2oynUClTAZBgkrBgEEAYI3FAIEDB4KAFMAdQBiAEMAQTAL
# BgNVHQ8EBAMCAYYwDwYDVR0TAQH/BAUwAwEB/zAfBgNVHSMEGDAWgBRyLToCMZBD
# uRQFTuHqp8cx0SOJNDBaBgNVHR8EUzBRME+gTaBLhklodHRwOi8vY3JsLm1pY3Jv
# c29mdC5jb20vcGtpL2NybC9wcm9kdWN0cy9NaWNSb29DZXJBdXQyMDExXzIwMTFf
# MDNfMjIuY3JsMF4GCCsGAQUFBwEBBFIwUDBOBggrBgEFBQcwAoZCaHR0cDovL3d3
# dy5taWNyb3NvZnQuY29tL3BraS9jZXJ0cy9NaWNSb29DZXJBdXQyMDExXzIwMTFf
# MDNfMjIuY3J0MIGfBgNVHSAEgZcwgZQwgZEGCSsGAQQBgjcuAzCBgzA/BggrBgEF
# BQcCARYzaHR0cDovL3d3dy5taWNyb3NvZnQuY29tL3BraW9wcy9kb2NzL3ByaW1h
# cnljcHMuaHRtMEAGCCsGAQUFBwICMDQeMiAdAEwAZQBnAGEAbABfAHAAbwBsAGkA
# YwB5AF8AcwB0AGEAdABlAG0AZQBuAHQALiAdMA0GCSqGSIb3DQEBCwUAA4ICAQBn
# 8oalmOBUeRou09h0ZyKbC5YR4WOSmUKWfdJ5DJDBZV8uLD74w3LRbYP+vj/oCso7
# v0epo/Np22O/IjWll11lhJB9i0ZQVdgMknzSGksc8zxCi1LQsP1r4z4HLimb5j0b
# pdS1HXeUOeLpZMlEPXh6I/MTfaaQdION9MsmAkYqwooQu6SpBQyb7Wj6aC6VoCo/
# KmtYSWMfCWluWpiW5IP0wI/zRive/DvQvTXvbiWu5a8n7dDd8w6vmSiXmE0OPQvy
# CInWH8MyGOLwxS3OW560STkKxgrCxq2u5bLZ2xWIUUVYODJxJxp/sfQn+N4sOiBp
# mLJZiWhub6e3dMNABQamASooPoI/E01mC8CzTfXhj38cbxV9Rad25UAqZaPDXVJi
# hsMdYzaXht/a8/jyFqGaJ+HNpZfQ7l1jQeNbB5yHPgZ3BtEGsXUfFL5hYbXw3MYb
# BL7fQccOKO7eZS/sl/ahXJbYANahRr1Z85elCUtIEJmAH9AAKcWxm6U/RXceNcbS
# oqKfenoi+kiVH6v7RyOA9Z74v2u3S5fi63V4GuzqN5l5GEv/1rMjaHXmr/r8i+sL
# gOppO6/8MO0ETI7f33VtY5E90Z1WTk+/gFcioXgRMiF670EKsT/7qMykXcGhiJtX
# cVZOSEXAQsmbdlsKgEhr/Xmfwb1tbWrJUnMTDXpQzTGCGgowghoGAgEBMIGVMH4x
# CzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRt
# b25kMR4wHAYDVQQKExVNaWNyb3NvZnQgQ29ycG9yYXRpb24xKDAmBgNVBAMTH01p
# Y3Jvc29mdCBDb2RlIFNpZ25pbmcgUENBIDIwMTECEzMAAAQDvdWVXQ87GK0AAAAA
# BAMwDQYJYIZIAWUDBAIBBQCgga4wGQYJKoZIhvcNAQkDMQwGCisGAQQBgjcCAQQw
# HAYKKwYBBAGCNwIBCzEOMAwGCisGAQQBgjcCARUwLwYJKoZIhvcNAQkEMSIEIPIz
# hi92iKRX77Rk4yeMHrjCk1SJOJXoQJKfx94f5MiRMEIGCisGAQQBgjcCAQwxNDAy
# oBSAEgBNAGkAYwByAG8AcwBvAGYAdKEagBhodHRwOi8vd3d3Lm1pY3Jvc29mdC5j
# b20wDQYJKoZIhvcNAQEBBQAEggEAhAf7djVbyQPP43ObXNRdtM4tRAUGGcxWh1i5
# BwRbTj630rON4/9RGXz86WO6zSODI7/hDlFnCWzhKN4FZq/brS7/sKX8lLQSQ04+
# xLa2l9K2tHUQ7u5497bEbJFt93/xprMnLRtzhyzqIHbIz/2nC3Zt2F0jHGhVlIld
# nj9wAk328jKbNT8NbdgyBpnm1ES2RHjyojZLT0J4eJ7XGp/ZCcMvR+9zTiMDkyQJ
# i7I8gj+lQ8lAkvAW66Yhe+4hRzzZdGW7wphco9A7OQxz0j06KEuErVc4unBBkIGf
# xMDIVHmeDayHvIqEvq4Dna1UDWWKpEyRg+FCFdgq2I1inkoSe6GCF5QwgheQBgor
# BgEEAYI3AwMBMYIXgDCCF3wGCSqGSIb3DQEHAqCCF20wghdpAgEDMQ8wDQYJYIZI
# AWUDBAIBBQAwggFSBgsqhkiG9w0BCRABBKCCAUEEggE9MIIBOQIBAQYKKwYBBAGE
# WQoDATAxMA0GCWCGSAFlAwQCAQUABCBqBN4FokwgX+ot1zfYM9AOh+MsTocLq5KZ
# a9bRwCs+sAIGaEseFyZrGBMyMDI1MDcwOTExMDcyNy42NzdaMASAAgH0oIHRpIHO
# MIHLMQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMH
# UmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSUwIwYDVQQL
# ExxNaWNyb3NvZnQgQW1lcmljYSBPcGVyYXRpb25zMScwJQYDVQQLEx5uU2hpZWxk
# IFRTUyBFU046ODkwMC0wNUUwLUQ5NDcxJTAjBgNVBAMTHE1pY3Jvc29mdCBUaW1l
# LVN0YW1wIFNlcnZpY2WgghHqMIIHIDCCBQigAwIBAgITMwAAAg4syyh9lSB1YwAB
# AAACDjANBgkqhkiG9w0BAQsFADB8MQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2Fz
# aGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENv
# cnBvcmF0aW9uMSYwJAYDVQQDEx1NaWNyb3NvZnQgVGltZS1TdGFtcCBQQ0EgMjAx
# MDAeFw0yNTAxMzAxOTQzMDNaFw0yNjA0MjIxOTQzMDNaMIHLMQswCQYDVQQGEwJV
# UzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwGA1UE
# ChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSUwIwYDVQQLExxNaWNyb3NvZnQgQW1l
# cmljYSBPcGVyYXRpb25zMScwJQYDVQQLEx5uU2hpZWxkIFRTUyBFU046ODkwMC0w
# NUUwLUQ5NDcxJTAjBgNVBAMTHE1pY3Jvc29mdCBUaW1lLVN0YW1wIFNlcnZpY2Uw
# ggIiMA0GCSqGSIb3DQEBAQUAA4ICDwAwggIKAoICAQCs5t7iRtXt0hbeo9ME78ZY
# jIo3saQuWMBFQ7X4s9vooYRABTOf2poTHatx+EwnBUGB1V2t/E6MwsQNmY5XpM/7
# 5aCrZdxAnrV9o4Tu5sBepbbfehsrOWRBIGoJE6PtWod1CrFehm1diz3jY3H8iFrh
# 7nqefniZ1SnbcWPMyNIxuGFzpQiDA+E5YS33meMqaXwhdb01Cluymh/3EKvknj4d
# IpQZEWOPM3jxbRVAYN5J2tOrYkJcdDx0l02V/NYd1qkvUBgPxrKviq5kz7E6AbOi
# fCDSMBgcn/X7RQw630Qkzqhp0kDU2qei/ao9IHmuuReXEjnjpgTsr4Ab33ICAKMY
# xOQe+n5wqEVcE9OTyhmWZJS5AnWUTniok4mgwONBWQ1DLOGFkZwXT334IPCqd4/3
# /Ld/ItizistyUZYsml/C4ZhdALbvfYwzv31Oxf8NTmV5IGxWdHnk2Hhh4bnzTKos
# EaDrJvQMiQ+loojM7f5bgdyBBnYQBm5+/iJsxw8k227zF2jbNI+Ows8HLeZGt8t6
# uJ2eVjND1B0YtgsBP0csBlnnI+4+dvLYRt0cAqw6PiYSz5FSZcbpi0xdAH/jd3dz
# yGArbyLuo69HugfGEEb/sM07rcoP1o3cZ8eWMb4+MIB8euOb5DVPDnEcFi4NDukY
# M91g1Dt/qIek+rtE88VS8QIDAQABo4IBSTCCAUUwHQYDVR0OBBYEFIVxRGlSEZE+
# 1ESK6UGI7YNcEIjbMB8GA1UdIwQYMBaAFJ+nFV0AXmJdg/Tl0mWnG1M1GelyMF8G
# A1UdHwRYMFYwVKBSoFCGTmh0dHA6Ly93d3cubWljcm9zb2Z0LmNvbS9wa2lvcHMv
# Y3JsL01pY3Jvc29mdCUyMFRpbWUtU3RhbXAlMjBQQ0ElMjAyMDEwKDEpLmNybDBs
# BggrBgEFBQcBAQRgMF4wXAYIKwYBBQUHMAKGUGh0dHA6Ly93d3cubWljcm9zb2Z0
# LmNvbS9wa2lvcHMvY2VydHMvTWljcm9zb2Z0JTIwVGltZS1TdGFtcCUyMFBDQSUy
# MDIwMTAoMSkuY3J0MAwGA1UdEwEB/wQCMAAwFgYDVR0lAQH/BAwwCgYIKwYBBQUH
# AwgwDgYDVR0PAQH/BAQDAgeAMA0GCSqGSIb3DQEBCwUAA4ICAQB14L2TL+L8OXLx
# nGSal2h30mZ7FsBFooiYkUVOY05F9pnwPTVufEDGWEpNNy2OfaUHWIOoQ/9/rjwO
# 0hS2SpB0BzMAk2gyz92NGWOpWbpBdMvrrRDpiWZi/uLS4ZGdRn3P2DccYmlkNP+v
# aRAXvnv+mp27KgI79mJ9hGyCQbvtMIjkbYoLqK7sF7Wahn9rLjX1y5QJL4lvEy3Q
# mA9KRBj56cEv/lAvzDq7eSiqRq/pCyqyc8uzmQ8SeKWyWu6DjUA9vi84QsmLjqPG
# CnH4cPyg+t95RpW+73snhew1iCV+wXu2RxMnWg7EsD5eLkJHLszUIPd+XClD+FTv
# V03GfrDDfk+45flH/eKRZc3MUZtnhLJjPwv3KoKDScW4iV6SbCRycYPkqoWBrHf7
# SvDA7GrH2UOtz1Wa1k27sdZgpG6/c9CqKI8CX5vgaa+A7oYHb4ZBj7S8u8sgxwWK
# 7HgWDRByOH3CiJu4LJ8h3TiRkRArmHRp0lbNf1iAKuL886IKE912v0yq55t8jMxj
# BU7uoLsrYVIoKkzh+sAkgkpGOoZL14+dlxVM91Bavza4kODTUlwzb+SpXsSqVx8n
# uB6qhUy7pqpgww1q4SNhAxFnFxsxiTlaoL75GNxPR605lJ2WXehtEi7/+YfJqvH+
# vnqcpqCjyQ9hNaVzuOEHX4MyuqcjwjCCB3EwggVZoAMCAQICEzMAAAAVxedrngKb
# SZkAAAAAABUwDQYJKoZIhvcNAQELBQAwgYgxCzAJBgNVBAYTAlVTMRMwEQYDVQQI
# EwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNyb3Nv
# ZnQgQ29ycG9yYXRpb24xMjAwBgNVBAMTKU1pY3Jvc29mdCBSb290IENlcnRpZmlj
# YXRlIEF1dGhvcml0eSAyMDEwMB4XDTIxMDkzMDE4MjIyNVoXDTMwMDkzMDE4MzIy
# NVowfDELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldhc2hpbmd0b24xEDAOBgNVBAcT
# B1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEmMCQGA1UE
# AxMdTWljcm9zb2Z0IFRpbWUtU3RhbXAgUENBIDIwMTAwggIiMA0GCSqGSIb3DQEB
# AQUAA4ICDwAwggIKAoICAQDk4aZM57RyIQt5osvXJHm9DtWC0/3unAcH0qlsTnXI
# yjVX9gF/bErg4r25PhdgM/9cT8dm95VTcVrifkpa/rg2Z4VGIwy1jRPPdzLAEBjo
# YH1qUoNEt6aORmsHFPPFdvWGUNzBRMhxXFExN6AKOG6N7dcP2CZTfDlhAnrEqv1y
# aa8dq6z2Nr41JmTamDu6GnszrYBbfowQHJ1S/rboYiXcag/PXfT+jlPP1uyFVk3v
# 3byNpOORj7I5LFGc6XBpDco2LXCOMcg1KL3jtIckw+DJj361VI/c+gVVmG1oO5pG
# ve2krnopN6zL64NF50ZuyjLVwIYwXE8s4mKyzbnijYjklqwBSru+cakXW2dg3viS
# kR4dPf0gz3N9QZpGdc3EXzTdEonW/aUgfX782Z5F37ZyL9t9X4C626p+Nuw2TPYr
# bqgSUei/BQOj0XOmTTd0lBw0gg/wEPK3Rxjtp+iZfD9M269ewvPV2HM9Q07BMzlM
# jgK8QmguEOqEUUbi0b1qGFphAXPKZ6Je1yh2AuIzGHLXpyDwwvoSCtdjbwzJNmSL
# W6CmgyFdXzB0kZSU2LlQ+QuJYfM2BjUYhEfb3BvR/bLUHMVr9lxSUV0S2yW6r1AF
# emzFER1y7435UsSFF5PAPBXbGjfHCBUYP3irRbb1Hode2o+eFnJpxq57t7c+auIu
# rQIDAQABo4IB3TCCAdkwEgYJKwYBBAGCNxUBBAUCAwEAATAjBgkrBgEEAYI3FQIE
# FgQUKqdS/mTEmr6CkTxGNSnPEP8vBO4wHQYDVR0OBBYEFJ+nFV0AXmJdg/Tl0mWn
# G1M1GelyMFwGA1UdIARVMFMwUQYMKwYBBAGCN0yDfQEBMEEwPwYIKwYBBQUHAgEW
# M2h0dHA6Ly93d3cubWljcm9zb2Z0LmNvbS9wa2lvcHMvRG9jcy9SZXBvc2l0b3J5
# Lmh0bTATBgNVHSUEDDAKBggrBgEFBQcDCDAZBgkrBgEEAYI3FAIEDB4KAFMAdQBi
# AEMAQTALBgNVHQ8EBAMCAYYwDwYDVR0TAQH/BAUwAwEB/zAfBgNVHSMEGDAWgBTV
# 9lbLj+iiXGJo0T2UkFvXzpoYxDBWBgNVHR8ETzBNMEugSaBHhkVodHRwOi8vY3Js
# Lm1pY3Jvc29mdC5jb20vcGtpL2NybC9wcm9kdWN0cy9NaWNSb29DZXJBdXRfMjAx
# MC0wNi0yMy5jcmwwWgYIKwYBBQUHAQEETjBMMEoGCCsGAQUFBzAChj5odHRwOi8v
# d3d3Lm1pY3Jvc29mdC5jb20vcGtpL2NlcnRzL01pY1Jvb0NlckF1dF8yMDEwLTA2
# LTIzLmNydDANBgkqhkiG9w0BAQsFAAOCAgEAnVV9/Cqt4SwfZwExJFvhnnJL/Klv
# 6lwUtj5OR2R4sQaTlz0xM7U518JxNj/aZGx80HU5bbsPMeTCj/ts0aGUGCLu6WZn
# OlNN3Zi6th542DYunKmCVgADsAW+iehp4LoJ7nvfam++Kctu2D9IdQHZGN5tggz1
# bSNU5HhTdSRXud2f8449xvNo32X2pFaq95W2KFUn0CS9QKC/GbYSEhFdPSfgQJY4
# rPf5KYnDvBewVIVCs/wMnosZiefwC2qBwoEZQhlSdYo2wh3DYXMuLGt7bj8sCXgU
# 6ZGyqVvfSaN0DLzskYDSPeZKPmY7T7uG+jIa2Zb0j/aRAfbOxnT99kxybxCrdTDF
# NLB62FD+CljdQDzHVG2dY3RILLFORy3BFARxv2T5JL5zbcqOCb2zAVdJVGTZc9d/
# HltEAY5aGZFrDZ+kKNxnGSgkujhLmm77IVRrakURR6nxt67I6IleT53S0Ex2tVdU
# CbFpAUR+fKFhbHP+CrvsQWY9af3LwUFJfn6Tvsv4O+S3Fb+0zj6lMVGEvL8CwYKi
# excdFYmNcP7ntdAoGokLjzbaukz5m/8K6TT4JDVnK+ANuOaMmdbhIurwJ0I9JZTm
# dHRbatGePu1+oDEzfbzL6Xu/OHBE0ZDxyKs6ijoIYn/ZcGNTTY3ugm2lBRDBcQZq
# ELQdVTNYs6FwZvKhggNNMIICNQIBATCB+aGB0aSBzjCByzELMAkGA1UEBhMCVVMx
# EzARBgNVBAgTCldhc2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoT
# FU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjElMCMGA1UECxMcTWljcm9zb2Z0IEFtZXJp
# Y2EgT3BlcmF0aW9uczEnMCUGA1UECxMeblNoaWVsZCBUU1MgRVNOOjg5MDAtMDVF
# MC1EOTQ3MSUwIwYDVQQDExxNaWNyb3NvZnQgVGltZS1TdGFtcCBTZXJ2aWNloiMK
# AQEwBwYFKw4DAhoDFQBK6HY/ZWLnOcMEQsjkDAoB/JZWCKCBgzCBgKR+MHwxCzAJ
# BgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25k
# MR4wHAYDVQQKExVNaWNyb3NvZnQgQ29ycG9yYXRpb24xJjAkBgNVBAMTHU1pY3Jv
# c29mdCBUaW1lLVN0YW1wIFBDQSAyMDEwMA0GCSqGSIb3DQEBCwUAAgUA7BiKEDAi
# GA8yMDI1MDcwOTA2MjYyNFoYDzIwMjUwNzEwMDYyNjI0WjB0MDoGCisGAQQBhFkK
# BAExLDAqMAoCBQDsGIoQAgEAMAcCAQACAgd2MAcCAQACAhKYMAoCBQDsGduQAgEA
# MDYGCisGAQQBhFkKBAIxKDAmMAwGCisGAQQBhFkKAwKgCjAIAgEAAgMHoSChCjAI
# AgEAAgMBhqAwDQYJKoZIhvcNAQELBQADggEBAJfv0EYW+0tTGY8DDKUmLlAe7HpH
# a9PqyEUb0KlF116LPwoH3K0GF0NFauoAeZnhkhANWP08dkeLPEnmp2pxqXitIeRu
# 7TdLMc58+8BGXyckqME035PFmLSXMmcQlQVpmHfYzh3ZrnVlzxKMvvYfHa3HEaTI
# yHJuB7oa5DIjpctApKul4Kwa+P0lRrRstbCSkuPoySp+Nv+PMyYvOS0GuoA9IT6O
# hZyCtd1tqkmoEuG/Fkh24pmv4TpGT/pNWq7XynPoefnCvC4RqH3rTMhaxpD0H5zX
# 98f2Mc9WK7qJYhAA9sJNp09CP7NtzHLtCnaKKYjJv7fIyMOuqWsWy1ttTkAxggQN
# MIIECQIBATCBkzB8MQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQ
# MA4GA1UEBxMHUmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9u
# MSYwJAYDVQQDEx1NaWNyb3NvZnQgVGltZS1TdGFtcCBQQ0EgMjAxMAITMwAAAg4s
# yyh9lSB1YwABAAACDjANBglghkgBZQMEAgEFAKCCAUowGgYJKoZIhvcNAQkDMQ0G
# CyqGSIb3DQEJEAEEMC8GCSqGSIb3DQEJBDEiBCD166aHxx6rId56rhK/3tleu8GC
# OKWdotSs+3Qt/WqVvDCB+gYLKoZIhvcNAQkQAi8xgeowgecwgeQwgb0EIAF0HXMl
# 8OmBkK267mxobKSihwOdP0eUNXQMypPzTxKGMIGYMIGApH4wfDELMAkGA1UEBhMC
# VVMxEzARBgNVBAgTCldhc2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNV
# BAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEmMCQGA1UEAxMdTWljcm9zb2Z0IFRp
# bWUtU3RhbXAgUENBIDIwMTACEzMAAAIOLMsofZUgdWMAAQAAAg4wIgQgym6+npRz
# vsZNJcCy+PAQhjspKnOptyujEzAKM58DDdAwDQYJKoZIhvcNAQELBQAEggIAnQ4Z
# QYdNWDnwJ/UNXe0eJ/EFLGYXaeZjHwDEx/DDOQqavbTP0poShTVn6i/l9B9sQ4C4
# VaX/vT85g+MrA/6TOdYYxhRVi3KevfZBS1tkzRl0doPg9hGJzI07bof1rJ0nhVHl
# 5eVZfuUUmRA5zOmKx2MSqymGNCNsywR7IQMOqXC7Cwi4BVprQ3YYZInHWN93zFIv
# h6iGKB98ZmfXbcY8/auaU+NyfgeDxsf3HujZYlWvxrjQk2bXKoCsDxxIw7X7SVca
# vmxlqpiG5+laIWsLqDV0IVlLG+s9ddwYdC5CyKNN9uY+QGEnx7vPXGGf9FPJJqk1
# J3ySMDn2DFgFKgEGktPsIiQzmtoC0Rif2fEqXe9x5m0rXzKelq0Ub3aWGxCe9Mh4
# YEISfhY8YAoZlZCVJt5qyY9tKKj4Ozi4YKT/banYvl9sQzZasmEqHZzcQZpPAZML
# /ZW1xm2vlW+t3yrJLmyIje9hxELntKWl3KRq9no9G4ugT2fkrPfU5+RomGcJ8cpc
# MRDn+oZXbD7sRCbJ5/VoOYHooRrfK9ORh7ckDCU1JAVVgdVCXIyjuGerO0b+E8UB
# Bx+nhSSgYtm8wO1gbsJJswITySPk0342aauW+K7tb/j//9MqNJCvJnAG/oASvwUq
# 0YYyKq9HRHOJdoOTxgYbJ79d6GGU/uuOjLuG33Y=
# SIG # End signature block
