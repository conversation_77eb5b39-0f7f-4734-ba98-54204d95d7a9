use dioxus::prelude::*;
use futures::{SinkExt, StreamExt};
use gloo_net::websocket::{futures::WebSocket, Message};
use rand::Rng;
use std::time::{Duration, SystemTime, UNIX_EPOCH};

fn main() {
    launch(App);
}

// Message structure to store message data
#[derive(<PERSON><PERSON>, Debug)]
struct ChatMessage {
    username: String,
    content: String,
    timestamp: u64,
    is_expiring: bool,
}

impl ChatMessage {
    fn new(username: String, content: String) -> Self {
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or(Duration::from_secs(0))
            .as_secs();

        Self {
            username,
            content,
            timestamp,
            is_expiring: false,
        }
    }

    fn from_string(message: String) -> Self {
        let parts: Vec<&str> = message.splitn(2, ':').collect();
        if parts.len() == 2 {
            Self::new(parts[0].to_string(), parts[1].to_string())
        } else {
            Self::new("Unknown".to_string(), message)
        }
    }

    fn format_time(&self) -> String {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or(Duration::from_secs(0))
            .as_secs();

        let diff = now.saturating_sub(self.timestamp);

        if diff < 60 {
            "Just now".to_string()
        } else if diff < 3600 {
            format!("{}m ago", diff / 60)
        } else if diff < 86400 {
            format!("{}h ago", diff / 3600)
        } else {
            format!("{}d ago", diff / 86400)
        }
    }
}

#[component]
fn App() -> Element {
    rsx!(
        document::Stylesheet { href: asset!("/assets/main.css") }
        Home {}
    )
}

// Online user indicator component
#[component]
fn OnlineIndicator() -> Element {
    rsx! {
        div { class: "online-indicator",
            span { class: "online-dot" }
            "Online"
        }
    }
}

// Message component
#[component]
fn MessageItem(message: ChatMessage, is_self: bool) -> Element {
    let expiry_class = if message.is_expiring { "expiring" } else { "" };
    let wrapper_class = if is_self { "self" } else { "others" };

    rsx! {
        div { class: "message-wrapper {wrapper_class}",
            div { class: "message-item {expiry_class}",
                div { class: "message-username", "{message.username}" }
                div { class: "message-content", "{message.content}" }
                div { class: "expiry-progress",
                    div { class: "expiry-bar" }
                }
            }
            div { class: "message-meta",
                span { class: "message-time", "{message.format_time()}" }
            }
        }
    }
}

// Login screen component
#[component]
fn LoginScreen(name: Signal<String>, on_join: EventHandler<()>) -> Element {
    rsx! {
        div { class: "chat-container",
            div { class: "login-container",
                h1 { class: "login-title", "Welcome to Real-Time Chat" }
                p { class: "login-subtitle", "Messages disappear after 10 seconds. Join with a username to start chatting!" }
                div { class: "login-form",
                    input {
                        class: "login-input",
                        r#type: "text",
                        value: name,
                        placeholder: "Enter your username...",
                        oninput: move |e| name.set(e.value()),
                        onkeydown: move |e| {
                            if e.key() == "Enter" && name().trim() != "" {
                                on_join.call(());
                            }
                        }
                    }
                    button {
                        class: "login-button",
                        onclick: move |_| on_join.call(()),
                        disabled: if name().trim() == "" { true },
                        "Join Chat"
                    }
                }
            }
        }
    }
}

#[component]
fn Home() -> Element {
    let mut messages = use_signal(|| vec![]);
    let mut message_content = use_signal(|| String::new());
    let mut receiver_ws = use_signal(|| None);
    let mut is_sending = use_signal(|| false);
    let mut online_count = use_signal(|| 1); // Default to 1 (self)

    let mut name = use_signal(|| String::new());
    let mut has_name = use_signal(|| false);

    // Handle message expiration
    use_future(move || async move {
        loop {
            tokio::time::sleep(Duration::from_secs(1)).await;

            let now = SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or(Duration::from_secs(0))
                .as_secs();

            let mut msgs = messages.write();

            // Mark messages older than 9 seconds as expiring
            for msg in msgs.iter_mut() {
                if now.saturating_sub(msg.timestamp) >= 9 && !msg.is_expiring {
                    msg.is_expiring = true;
                }
            }

            // Remove messages older than 10 seconds
            msgs.retain(|msg| now.saturating_sub(msg.timestamp) < 10);
        }
    });

    // WebSocket client for sending messages
    let chat_client = use_coroutine(move |mut rx: UnboundedReceiver<String>| async move {
        let (mut sender, receiver) = WebSocket::open("ws://localhost:3000/chat").unwrap().split();
        receiver_ws.set(Some(receiver));

        while let Some(msg) = rx.next().await {
            is_sending.set(true);
            let message = format!("{}:{}", name, msg);
            sender.send(Message::Text(message)).await.unwrap();

            // Simulate network delay for sending indicator
            tokio::time::sleep(Duration::from_millis(300)).await;
            is_sending.set(false);
        }
    });

    // WebSocket receiver for incoming messages
    let _ = use_future(move || async move {
        if let Some(mut receiver) = receiver_ws.take() {
            while let Some(msg) = receiver.next().await {
                if let Ok(msg) = msg {
                    match msg {
                        Message::Text(content) => {
                            let chat_message = ChatMessage::from_string(content);
                            messages.write().push(chat_message);

                            // Simulate random online count changes
                            let mut rng = rand::thread_rng();
                            if rng.gen::<f32>() < 0.3 {
                                let change = if rng.gen::<bool>() { 1 } else { -1 };
                                let new_count = (online_count() as i32 + change).max(1) as usize;
                                online_count.set(new_count);
                            }
                        },
                        _ => ()
                    }
                }
            }
        }
    });

    // Handle sending a message
    let send_message = move |_| {
        if message_content().trim().is_empty() {
            return;
        }

        chat_client.send(message_content());
        message_content.set(String::new());
    };

    // Handle joining the chat
    let join_chat = move |_| {
        if name().trim().is_empty() {
            return;
        }

        has_name.set(true);
    };

    rsx!(
        if !has_name() {
            LoginScreen {
                name: name,
                on_join: join_chat
            }
        } else {
            div { class: "chat-container",
                div { class: "chat",
                    div { class: "chat-header",
                        h1 { "Real-Time Chat" }
                        div {
                            OnlineIndicator {}
                            span { class: "online-count", " ({online_count} online)" }
                        }
                    }
                    div { class: "message-container",
                        {
                            messages()
                                .iter()
                                .rev()
                                .map(|message| {
                                    let is_self = message.username == name();
                                    rsx! {
                                        MessageItem {
                                            message: message.clone(),
                                            is_self: is_self
                                        }
                                    }
                                })
                        }
                    }
                    div { class: "input-container",
                        input {
                            r#type: "text",
                            value: message_content,
                            placeholder: "Type a message...",
                            oninput: move |e| message_content.set(e.value()),
                            onkeydown: move |e| {
                                if e.key() == "Enter" && !message_content().trim().is_empty() {
                                    send_message(());
                                }
                            }
                        }
                        button {
                            onclick: send_message,
                            disabled: if message_content().trim() == "" || is_sending() { true },
                            if is_sending() {
                                "Sending"
                                span { class: "sending-indicator",
                                    span { class: "sending-dot" }
                                    span { class: "sending-dot" }
                                    span { class: "sending-dot" }
                                }
                            } else {
                                "Send"
                            }
                        }
                    }
                }
            }
        }
    )
}