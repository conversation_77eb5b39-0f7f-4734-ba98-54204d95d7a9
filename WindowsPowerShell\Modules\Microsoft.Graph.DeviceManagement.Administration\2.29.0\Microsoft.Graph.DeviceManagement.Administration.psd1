#
# Module manifest for module 'Microsoft.Graph.DeviceManagement.Administration'
#
# Generated by: Microsoft Corporation
#
# Generated on: 7/9/2025
#

@{

# Script module or binary module file associated with this manifest.
RootModule = './Microsoft.Graph.DeviceManagement.Administration.psm1'

# Version number of this module.
ModuleVersion = '2.29.0'

# Supported PSEditions
CompatiblePSEditions = 'Core', 'Desktop'

# ID used to uniquely identify this module
GUID = '32b02ba6-c992-4ded-a37e-71bbf8274995'

# Author of this module
Author = 'Microsoft Corporation'

# Company or vendor of this module
CompanyName = 'Microsoft Corporation'

# Copyright statement for this module
Copyright = 'Microsoft Corporation. All rights reserved.'

# Description of the functionality provided by this module
Description = 'Microsoft Graph PowerShell Cmdlets'

# Minimum version of the PowerShell engine required by this module
PowerShellVersion = '5.1'

# Name of the PowerShell host required by this module
# PowerShellHostName = ''

# Minimum version of the PowerShell host required by this module
# PowerShellHostVersion = ''

# Minimum version of Microsoft .NET Framework required by this module. This prerequisite is valid for the PowerShell Desktop edition only.
DotNetFrameworkVersion = '4.7.2'

# Minimum version of the common language runtime (CLR) required by this module. This prerequisite is valid for the PowerShell Desktop edition only.
# ClrVersion = ''

# Processor architecture (None, X86, Amd64) required by this module
# ProcessorArchitecture = ''

# Modules that must be imported into the global environment prior to importing this module
RequiredModules = @(@{ModuleName = 'Microsoft.Graph.Authentication'; RequiredVersion = '2.29.0'; })

# Assemblies that must be loaded prior to importing this module
RequiredAssemblies = 
               './bin/Microsoft.Graph.DeviceManagement.Administration.private.dll'

# Script files (.ps1) that are run in the caller's environment prior to importing this module.
# ScriptsToProcess = @()

# Type files (.ps1xml) to be loaded when importing this module
# TypesToProcess = @()

# Format files (.ps1xml) to be loaded when importing this module
FormatsToProcess = './Microsoft.Graph.DeviceManagement.Administration.format.ps1xml'

# Modules to import as nested modules of the module specified in RootModule/ModuleToProcess
# NestedModules = @()

# Functions to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no functions to export.
FunctionsToExport = 'Disconnect-MgDeviceManagementRemoteAssistancePartner', 
               'Get-MgDeviceManagementApplePushNotificationCertificate', 
               'Get-MgDeviceManagementAuditEvent', 
               'Get-MgDeviceManagementAuditEventAuditActivityType', 
               'Get-MgDeviceManagementAuditEventAuditCategory', 
               'Get-MgDeviceManagementAuditEventCount', 
               'Get-MgDeviceManagementComplianceManagementPartner', 
               'Get-MgDeviceManagementComplianceManagementPartnerCount', 
               'Get-MgDeviceManagementExchangeConnector', 
               'Get-MgDeviceManagementExchangeConnectorCount', 
               'Get-MgDeviceManagementIoUpdateStatus', 
               'Get-MgDeviceManagementIoUpdateStatusCount', 
               'Get-MgDeviceManagementMobileThreatDefenseConnector', 
               'Get-MgDeviceManagementMobileThreatDefenseConnectorCount', 
               'Get-MgDeviceManagementPartner', 
               'Get-MgDeviceManagementPartnerCount', 
               'Get-MgDeviceManagementRemoteAssistancePartner', 
               'Get-MgDeviceManagementRemoteAssistancePartnerCount', 
               'Get-MgDeviceManagementResourceOperation', 
               'Get-MgDeviceManagementResourceOperationCount', 
               'Get-MgDeviceManagementRoleAssignment', 
               'Get-MgDeviceManagementRoleAssignmentCount', 
               'Get-MgDeviceManagementRoleAssignmentRoleDefinition', 
               'Get-MgDeviceManagementRoleDefinition', 
               'Get-MgDeviceManagementRoleDefinitionCount', 
               'Get-MgDeviceManagementRoleDefinitionRoleAssignment', 
               'Get-MgDeviceManagementRoleDefinitionRoleAssignmentCount', 
               'Get-MgDeviceManagementRoleDefinitionRoleAssignmentRoleDefinition', 
               'Get-MgDeviceManagementTelecomExpenseManagementPartner', 
               'Get-MgDeviceManagementTelecomExpenseManagementPartnerCount', 
               'Get-MgDeviceManagementTermAndCondition', 
               'Get-MgDeviceManagementTermAndConditionAcceptanceStatus', 
               'Get-MgDeviceManagementTermAndConditionAcceptanceStatusCount', 
               'Get-MgDeviceManagementTermAndConditionAcceptanceStatusTermAndCondition', 
               'Get-MgDeviceManagementTermAndConditionAssignment', 
               'Get-MgDeviceManagementTermAndConditionAssignmentCount', 
               'Get-MgDeviceManagementTermAndConditionCount', 
               'Get-MgDeviceManagementVirtualEndpoint', 
               'Get-MgDeviceManagementVirtualEndpointAuditEvent', 
               'Get-MgDeviceManagementVirtualEndpointAuditEventAuditActivityType', 
               'Get-MgDeviceManagementVirtualEndpointAuditEventCount', 
               'Get-MgDeviceManagementVirtualEndpointCloudPc', 
               'Get-MgDeviceManagementVirtualEndpointCloudPcCount', 
               'Get-MgDeviceManagementVirtualEndpointDeviceImage', 
               'Get-MgDeviceManagementVirtualEndpointDeviceImageCount', 
               'Get-MgDeviceManagementVirtualEndpointDeviceImageSourceImage', 
               'Get-MgDeviceManagementVirtualEndpointGalleryImage', 
               'Get-MgDeviceManagementVirtualEndpointGalleryImageCount', 
               'Get-MgDeviceManagementVirtualEndpointOnPremiseConnection', 
               'Get-MgDeviceManagementVirtualEndpointOnPremiseConnectionCount', 
               'Get-MgDeviceManagementVirtualEndpointProvisioningPolicy', 
               'Get-MgDeviceManagementVirtualEndpointProvisioningPolicyAssignment', 
               'Get-MgDeviceManagementVirtualEndpointProvisioningPolicyAssignmentAssignedUser', 
               'Get-MgDeviceManagementVirtualEndpointProvisioningPolicyAssignmentAssignedUserByUserPrincipalName', 
               'Get-MgDeviceManagementVirtualEndpointProvisioningPolicyAssignmentAssignedUserCount', 
               'Get-MgDeviceManagementVirtualEndpointProvisioningPolicyAssignmentAssignedUserMailboxSetting', 
               'Get-MgDeviceManagementVirtualEndpointProvisioningPolicyAssignmentAssignedUserServiceProvisioningError', 
               'Get-MgDeviceManagementVirtualEndpointProvisioningPolicyAssignmentAssignedUserServiceProvisioningErrorCount', 
               'Get-MgDeviceManagementVirtualEndpointProvisioningPolicyAssignmentCount', 
               'Get-MgDeviceManagementVirtualEndpointProvisioningPolicyCount', 
               'Get-MgDeviceManagementVirtualEndpointUserSetting', 
               'Get-MgDeviceManagementVirtualEndpointUserSettingAssignment', 
               'Get-MgDeviceManagementVirtualEndpointUserSettingAssignmentCount', 
               'Get-MgDeviceManagementVirtualEndpointUserSettingCount', 
               'Invoke-MgBeginDeviceManagementRemoteAssistancePartnerOnboarding', 
               'Invoke-MgDownloadDeviceManagementApplePushNotificationCertificateApplePushNotificationCertificateSigningRequest', 
               'Invoke-MgTerminateDeviceManagementPartner', 
               'Invoke-MgTroubleshootDeviceManagementVirtualEndpointCloudPc', 
               'New-MgDeviceManagementAuditEvent', 
               'New-MgDeviceManagementComplianceManagementPartner', 
               'New-MgDeviceManagementExchangeConnector', 
               'New-MgDeviceManagementIoUpdateStatus', 
               'New-MgDeviceManagementMobileThreatDefenseConnector', 
               'New-MgDeviceManagementPartner', 
               'New-MgDeviceManagementRemoteAssistancePartner', 
               'New-MgDeviceManagementResourceOperation', 
               'New-MgDeviceManagementRoleAssignment', 
               'New-MgDeviceManagementRoleDefinition', 
               'New-MgDeviceManagementRoleDefinitionRoleAssignment', 
               'New-MgDeviceManagementTelecomExpenseManagementPartner', 
               'New-MgDeviceManagementTermAndCondition', 
               'New-MgDeviceManagementTermAndConditionAcceptanceStatus', 
               'New-MgDeviceManagementTermAndConditionAssignment', 
               'New-MgDeviceManagementVirtualEndpointDeviceImage', 
               'New-MgDeviceManagementVirtualEndpointGalleryImage', 
               'New-MgDeviceManagementVirtualEndpointOnPremiseConnection', 
               'New-MgDeviceManagementVirtualEndpointProvisioningPolicy', 
               'New-MgDeviceManagementVirtualEndpointProvisioningPolicyAssignment', 
               'New-MgDeviceManagementVirtualEndpointUserSetting', 
               'New-MgDeviceManagementVirtualEndpointUserSettingAssignment', 
               'Remove-MgDeviceManagementApplePushNotificationCertificate', 
               'Remove-MgDeviceManagementAuditEvent', 
               'Remove-MgDeviceManagementComplianceManagementPartner', 
               'Remove-MgDeviceManagementExchangeConnector', 
               'Remove-MgDeviceManagementIoUpdateStatus', 
               'Remove-MgDeviceManagementMobileThreatDefenseConnector', 
               'Remove-MgDeviceManagementPartner', 
               'Remove-MgDeviceManagementRemoteAssistancePartner', 
               'Remove-MgDeviceManagementResourceOperation', 
               'Remove-MgDeviceManagementRoleAssignment', 
               'Remove-MgDeviceManagementRoleDefinition', 
               'Remove-MgDeviceManagementRoleDefinitionRoleAssignment', 
               'Remove-MgDeviceManagementTelecomExpenseManagementPartner', 
               'Remove-MgDeviceManagementTermAndCondition', 
               'Remove-MgDeviceManagementTermAndConditionAcceptanceStatus', 
               'Remove-MgDeviceManagementTermAndConditionAssignment', 
               'Remove-MgDeviceManagementVirtualEndpointDeviceImage', 
               'Remove-MgDeviceManagementVirtualEndpointGalleryImage', 
               'Remove-MgDeviceManagementVirtualEndpointOnPremiseConnection', 
               'Remove-MgDeviceManagementVirtualEndpointProvisioningPolicy', 
               'Remove-MgDeviceManagementVirtualEndpointProvisioningPolicyAssignment', 
               'Remove-MgDeviceManagementVirtualEndpointUserSetting', 
               'Remove-MgDeviceManagementVirtualEndpointUserSettingAssignment', 
               'Rename-MgDeviceManagementVirtualEndpointCloudPc', 
               'Restart-MgDeviceManagementVirtualEndpointCloudPc', 
               'Restore-MgDeviceManagementVirtualEndpointCloudPc', 
               'Set-MgDeviceManagementVirtualEndpointProvisioningPolicy', 
               'Set-MgDeviceManagementVirtualEndpointUserSetting', 
               'Start-MgDeviceManagementVirtualEndpointOnPremiseConnectionHealthCheck', 
               'Stop-MgDeviceManagementVirtualEndpointCloudPcGracePeriod', 
               'Sync-MgDeviceManagementExchangeConnector', 
               'Update-MgDeviceManagementApplePushNotificationCertificate', 
               'Update-MgDeviceManagementAuditEvent', 
               'Update-MgDeviceManagementComplianceManagementPartner', 
               'Update-MgDeviceManagementExchangeConnector', 
               'Update-MgDeviceManagementIoUpdateStatus', 
               'Update-MgDeviceManagementMobileThreatDefenseConnector', 
               'Update-MgDeviceManagementPartner', 
               'Update-MgDeviceManagementRemoteAssistancePartner', 
               'Update-MgDeviceManagementResourceOperation', 
               'Update-MgDeviceManagementRoleAssignment', 
               'Update-MgDeviceManagementRoleDefinition', 
               'Update-MgDeviceManagementRoleDefinitionRoleAssignment', 
               'Update-MgDeviceManagementTelecomExpenseManagementPartner', 
               'Update-MgDeviceManagementTermAndCondition', 
               'Update-MgDeviceManagementTermAndConditionAcceptanceStatus', 
               'Update-MgDeviceManagementTermAndConditionAssignment', 
               'Update-MgDeviceManagementVirtualEndpointDeviceImage', 
               'Update-MgDeviceManagementVirtualEndpointGalleryImage', 
               'Update-MgDeviceManagementVirtualEndpointOnPremiseConnection', 
               'Update-MgDeviceManagementVirtualEndpointProvisioningPolicy', 
               'Update-MgDeviceManagementVirtualEndpointProvisioningPolicyAssignment', 
               'Update-MgDeviceManagementVirtualEndpointProvisioningPolicyAssignmentAssignedUserMailboxSetting', 
               'Update-MgDeviceManagementVirtualEndpointUserSetting', 
               'Update-MgDeviceManagementVirtualEndpointUserSettingAssignment'

# Cmdlets to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no cmdlets to export.
CmdletsToExport = @()

# Variables to export from this module
# VariablesToExport = @()

# Aliases to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no aliases to export.
AliasesToExport = '*'

# DSC resources to export from this module
# DscResourcesToExport = @()

# List of all modules packaged with this module
# ModuleList = @()

# List of all files packaged with this module
# FileList = @()

# Private data to pass to the module specified in RootModule/ModuleToProcess. This may also contain a PSData hashtable with additional module metadata used by PowerShell.
PrivateData = @{

    PSData = @{

        # Tags applied to this module. These help with module discovery in online galleries.
        Tags = 'Microsoft','Office365','Graph','PowerShell','PSModule','PSIncludes_Cmdlet'

        # A URL to the license for this module.
        LicenseUri = 'https://aka.ms/devservicesagreement'

        # A URL to the main website for this project.
        ProjectUri = 'https://github.com/microsoftgraph/msgraph-sdk-powershell'

        # A URL to an icon representing this module.
        IconUri = 'https://raw.githubusercontent.com/microsoftgraph/msgraph-sdk-powershell/dev/docs/images/graph_color256.png'

        # ReleaseNotes of this module
        ReleaseNotes = 'See https://aka.ms/GraphPowerShell-Release.'

        # Prerelease string of this module
        # Prerelease = ''

        # Flag to indicate whether the module requires explicit user acceptance for install/update/save
        # RequireLicenseAcceptance = $false

        # External dependent modules of this module
        # ExternalModuleDependencies = @()

    } # End of PSData hashtable

 } # End of PrivateData hashtable

# HelpInfo URI of this module
# HelpInfoURI = ''

# Default prefix for commands exported from this module. Override the default prefix using Import-Module -Prefix.
# DefaultCommandPrefix = ''

}


# SIG # Begin signature block
# MIIoOQYJKoZIhvcNAQcCoIIoKjCCKCYCAQExDzANBglghkgBZQMEAgEFADB5Bgor
# BgEEAYI3AgEEoGswaTA0BgorBgEEAYI3AgEeMCYCAwEAAAQQH8w7YFlLCE63JNLG
# KX7zUQIBAAIBAAIBAAIBAAIBADAxMA0GCWCGSAFlAwQCAQUABCDPT6FFohmp6VPE
# bWPPfTymktnTY9JXpZB7v8y+7M7+9qCCDYUwggYDMIID66ADAgECAhMzAAAEA73V
# lV0POxitAAAAAAQDMA0GCSqGSIb3DQEBCwUAMH4xCzAJBgNVBAYTAlVTMRMwEQYD
# VQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNy
# b3NvZnQgQ29ycG9yYXRpb24xKDAmBgNVBAMTH01pY3Jvc29mdCBDb2RlIFNpZ25p
# bmcgUENBIDIwMTEwHhcNMjQwOTEyMjAxMTEzWhcNMjUwOTExMjAxMTEzWjB0MQsw
# CQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9u
# ZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMR4wHAYDVQQDExVNaWNy
# b3NvZnQgQ29ycG9yYXRpb24wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIB
# AQCfdGddwIOnbRYUyg03O3iz19XXZPmuhEmW/5uyEN+8mgxl+HJGeLGBR8YButGV
# LVK38RxcVcPYyFGQXcKcxgih4w4y4zJi3GvawLYHlsNExQwz+v0jgY/aejBS2EJY
# oUhLVE+UzRihV8ooxoftsmKLb2xb7BoFS6UAo3Zz4afnOdqI7FGoi7g4vx/0MIdi
# kwTn5N56TdIv3mwfkZCFmrsKpN0zR8HD8WYsvH3xKkG7u/xdqmhPPqMmnI2jOFw/
# /n2aL8W7i1Pasja8PnRXH/QaVH0M1nanL+LI9TsMb/enWfXOW65Gne5cqMN9Uofv
# ENtdwwEmJ3bZrcI9u4LZAkujAgMBAAGjggGCMIIBfjAfBgNVHSUEGDAWBgorBgEE
# AYI3TAgBBggrBgEFBQcDAzAdBgNVHQ4EFgQU6m4qAkpz4641iK2irF8eWsSBcBkw
# VAYDVR0RBE0wS6RJMEcxLTArBgNVBAsTJE1pY3Jvc29mdCBJcmVsYW5kIE9wZXJh
# dGlvbnMgTGltaXRlZDEWMBQGA1UEBRMNMjMwMDEyKzUwMjkyNjAfBgNVHSMEGDAW
# gBRIbmTlUAXTgqoXNzcitW2oynUClTBUBgNVHR8ETTBLMEmgR6BFhkNodHRwOi8v
# d3d3Lm1pY3Jvc29mdC5jb20vcGtpb3BzL2NybC9NaWNDb2RTaWdQQ0EyMDExXzIw
# MTEtMDctMDguY3JsMGEGCCsGAQUFBwEBBFUwUzBRBggrBgEFBQcwAoZFaHR0cDov
# L3d3dy5taWNyb3NvZnQuY29tL3BraW9wcy9jZXJ0cy9NaWNDb2RTaWdQQ0EyMDEx
# XzIwMTEtMDctMDguY3J0MAwGA1UdEwEB/wQCMAAwDQYJKoZIhvcNAQELBQADggIB
# AFFo/6E4LX51IqFuoKvUsi80QytGI5ASQ9zsPpBa0z78hutiJd6w154JkcIx/f7r
# EBK4NhD4DIFNfRiVdI7EacEs7OAS6QHF7Nt+eFRNOTtgHb9PExRy4EI/jnMwzQJV
# NokTxu2WgHr/fBsWs6G9AcIgvHjWNN3qRSrhsgEdqHc0bRDUf8UILAdEZOMBvKLC
# rmf+kJPEvPldgK7hFO/L9kmcVe67BnKejDKO73Sa56AJOhM7CkeATrJFxO9GLXos
# oKvrwBvynxAg18W+pagTAkJefzneuWSmniTurPCUE2JnvW7DalvONDOtG01sIVAB
# +ahO2wcUPa2Zm9AiDVBWTMz9XUoKMcvngi2oqbsDLhbK+pYrRUgRpNt0y1sxZsXO
# raGRF8lM2cWvtEkV5UL+TQM1ppv5unDHkW8JS+QnfPbB8dZVRyRmMQ4aY/tx5x5+
# sX6semJ//FbiclSMxSI+zINu1jYerdUwuCi+P6p7SmQmClhDM+6Q+btE2FtpsU0W
# +r6RdYFf/P+nK6j2otl9Nvr3tWLu+WXmz8MGM+18ynJ+lYbSmFWcAj7SYziAfT0s
# IwlQRFkyC71tsIZUhBHtxPliGUu362lIO0Lpe0DOrg8lspnEWOkHnCT5JEnWCbzu
# iVt8RX1IV07uIveNZuOBWLVCzWJjEGa+HhaEtavjy6i7MIIHejCCBWKgAwIBAgIK
# YQ6Q0gAAAAAAAzANBgkqhkiG9w0BAQsFADCBiDELMAkGA1UEBhMCVVMxEzARBgNV
# BAgTCldhc2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jv
# c29mdCBDb3Jwb3JhdGlvbjEyMDAGA1UEAxMpTWljcm9zb2Z0IFJvb3QgQ2VydGlm
# aWNhdGUgQXV0aG9yaXR5IDIwMTEwHhcNMTEwNzA4MjA1OTA5WhcNMjYwNzA4MjEw
# OTA5WjB+MQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UE
# BxMHUmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSgwJgYD
# VQQDEx9NaWNyb3NvZnQgQ29kZSBTaWduaW5nIFBDQSAyMDExMIICIjANBgkqhkiG
# 9w0BAQEFAAOCAg8AMIICCgKCAgEAq/D6chAcLq3YbqqCEE00uvK2WCGfQhsqa+la
# UKq4BjgaBEm6f8MMHt03a8YS2AvwOMKZBrDIOdUBFDFC04kNeWSHfpRgJGyvnkmc
# 6Whe0t+bU7IKLMOv2akrrnoJr9eWWcpgGgXpZnboMlImEi/nqwhQz7NEt13YxC4D
# dato88tt8zpcoRb0RrrgOGSsbmQ1eKagYw8t00CT+OPeBw3VXHmlSSnnDb6gE3e+
# lD3v++MrWhAfTVYoonpy4BI6t0le2O3tQ5GD2Xuye4Yb2T6xjF3oiU+EGvKhL1nk
# kDstrjNYxbc+/jLTswM9sbKvkjh+0p2ALPVOVpEhNSXDOW5kf1O6nA+tGSOEy/S6
# A4aN91/w0FK/jJSHvMAhdCVfGCi2zCcoOCWYOUo2z3yxkq4cI6epZuxhH2rhKEmd
# X4jiJV3TIUs+UsS1Vz8kA/DRelsv1SPjcF0PUUZ3s/gA4bysAoJf28AVs70b1FVL
# 5zmhD+kjSbwYuER8ReTBw3J64HLnJN+/RpnF78IcV9uDjexNSTCnq47f7Fufr/zd
# sGbiwZeBe+3W7UvnSSmnEyimp31ngOaKYnhfsi+E11ecXL93KCjx7W3DKI8sj0A3
# T8HhhUSJxAlMxdSlQy90lfdu+HggWCwTXWCVmj5PM4TasIgX3p5O9JawvEagbJjS
# 4NaIjAsCAwEAAaOCAe0wggHpMBAGCSsGAQQBgjcVAQQDAgEAMB0GA1UdDgQWBBRI
# bmTlUAXTgqoXNzcitW2oynUClTAZBgkrBgEEAYI3FAIEDB4KAFMAdQBiAEMAQTAL
# BgNVHQ8EBAMCAYYwDwYDVR0TAQH/BAUwAwEB/zAfBgNVHSMEGDAWgBRyLToCMZBD
# uRQFTuHqp8cx0SOJNDBaBgNVHR8EUzBRME+gTaBLhklodHRwOi8vY3JsLm1pY3Jv
# c29mdC5jb20vcGtpL2NybC9wcm9kdWN0cy9NaWNSb29DZXJBdXQyMDExXzIwMTFf
# MDNfMjIuY3JsMF4GCCsGAQUFBwEBBFIwUDBOBggrBgEFBQcwAoZCaHR0cDovL3d3
# dy5taWNyb3NvZnQuY29tL3BraS9jZXJ0cy9NaWNSb29DZXJBdXQyMDExXzIwMTFf
# MDNfMjIuY3J0MIGfBgNVHSAEgZcwgZQwgZEGCSsGAQQBgjcuAzCBgzA/BggrBgEF
# BQcCARYzaHR0cDovL3d3dy5taWNyb3NvZnQuY29tL3BraW9wcy9kb2NzL3ByaW1h
# cnljcHMuaHRtMEAGCCsGAQUFBwICMDQeMiAdAEwAZQBnAGEAbABfAHAAbwBsAGkA
# YwB5AF8AcwB0AGEAdABlAG0AZQBuAHQALiAdMA0GCSqGSIb3DQEBCwUAA4ICAQBn
# 8oalmOBUeRou09h0ZyKbC5YR4WOSmUKWfdJ5DJDBZV8uLD74w3LRbYP+vj/oCso7
# v0epo/Np22O/IjWll11lhJB9i0ZQVdgMknzSGksc8zxCi1LQsP1r4z4HLimb5j0b
# pdS1HXeUOeLpZMlEPXh6I/MTfaaQdION9MsmAkYqwooQu6SpBQyb7Wj6aC6VoCo/
# KmtYSWMfCWluWpiW5IP0wI/zRive/DvQvTXvbiWu5a8n7dDd8w6vmSiXmE0OPQvy
# CInWH8MyGOLwxS3OW560STkKxgrCxq2u5bLZ2xWIUUVYODJxJxp/sfQn+N4sOiBp
# mLJZiWhub6e3dMNABQamASooPoI/E01mC8CzTfXhj38cbxV9Rad25UAqZaPDXVJi
# hsMdYzaXht/a8/jyFqGaJ+HNpZfQ7l1jQeNbB5yHPgZ3BtEGsXUfFL5hYbXw3MYb
# BL7fQccOKO7eZS/sl/ahXJbYANahRr1Z85elCUtIEJmAH9AAKcWxm6U/RXceNcbS
# oqKfenoi+kiVH6v7RyOA9Z74v2u3S5fi63V4GuzqN5l5GEv/1rMjaHXmr/r8i+sL
# gOppO6/8MO0ETI7f33VtY5E90Z1WTk+/gFcioXgRMiF670EKsT/7qMykXcGhiJtX
# cVZOSEXAQsmbdlsKgEhr/Xmfwb1tbWrJUnMTDXpQzTGCGgowghoGAgEBMIGVMH4x
# CzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRt
# b25kMR4wHAYDVQQKExVNaWNyb3NvZnQgQ29ycG9yYXRpb24xKDAmBgNVBAMTH01p
# Y3Jvc29mdCBDb2RlIFNpZ25pbmcgUENBIDIwMTECEzMAAAQDvdWVXQ87GK0AAAAA
# BAMwDQYJYIZIAWUDBAIBBQCgga4wGQYJKoZIhvcNAQkDMQwGCisGAQQBgjcCAQQw
# HAYKKwYBBAGCNwIBCzEOMAwGCisGAQQBgjcCARUwLwYJKoZIhvcNAQkEMSIEII/o
# fE2t4k6eQ7+qxVHF2sF3hVwCzq54BFbnZm/awC9lMEIGCisGAQQBgjcCAQwxNDAy
# oBSAEgBNAGkAYwByAG8AcwBvAGYAdKEagBhodHRwOi8vd3d3Lm1pY3Jvc29mdC5j
# b20wDQYJKoZIhvcNAQEBBQAEggEATtLmFKhtHyqasIP6/vFym7Rzq+ClY/Gwd7vZ
# l0exJoj0f5z/QrWwU72eYpKOLmdgp+F8gMjmXaggwo2/omQ3J931w99vvOZQ9ZIj
# MRdnPz/duLCsn4ZCZ2bUADPtaD979XdKpTkX5qm3ZW0AQtyarDnh2Qas9q8e9gAe
# XnMQoh15CecRk2rNXh9TxI5eIy7N0Da+I4YoaHaPfxdf6yzve1td/jPJgDHfQ8qk
# 7nAsilqUjO1sTvmdYhwAurSbz9Ad7KzoXFwP2K+fkmStt54K1MhhJySxHk/R8sNd
# UWsjYCNLiAS6gmnEEy50zi56wepcdEnFKhTEpHmJ5eFu25i1naGCF5QwgheQBgor
# BgEEAYI3AwMBMYIXgDCCF3wGCSqGSIb3DQEHAqCCF20wghdpAgEDMQ8wDQYJYIZI
# AWUDBAIBBQAwggFSBgsqhkiG9w0BCRABBKCCAUEEggE9MIIBOQIBAQYKKwYBBAGE
# WQoDATAxMA0GCWCGSAFlAwQCAQUABCBE2+qDpGeYnJWNH8FaU8tKd4kgElcU/j6A
# m8jqXt1aFAIGaEsRScY1GBMyMDI1MDcwOTExMDcyNC43NTFaMASAAgH0oIHRpIHO
# MIHLMQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMH
# UmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSUwIwYDVQQL
# ExxNaWNyb3NvZnQgQW1lcmljYSBPcGVyYXRpb25zMScwJQYDVQQLEx5uU2hpZWxk
# IFRTUyBFU046N0YwMC0wNUUwLUQ5NDcxJTAjBgNVBAMTHE1pY3Jvc29mdCBUaW1l
# LVN0YW1wIFNlcnZpY2WgghHqMIIHIDCCBQigAwIBAgITMwAAAgbXvFE4mCPsLAAB
# AAACBjANBgkqhkiG9w0BAQsFADB8MQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2Fz
# aGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENv
# cnBvcmF0aW9uMSYwJAYDVQQDEx1NaWNyb3NvZnQgVGltZS1TdGFtcCBQQ0EgMjAx
# MDAeFw0yNTAxMzAxOTQyNTBaFw0yNjA0MjIxOTQyNTBaMIHLMQswCQYDVQQGEwJV
# UzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwGA1UE
# ChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSUwIwYDVQQLExxNaWNyb3NvZnQgQW1l
# cmljYSBPcGVyYXRpb25zMScwJQYDVQQLEx5uU2hpZWxkIFRTUyBFU046N0YwMC0w
# NUUwLUQ5NDcxJTAjBgNVBAMTHE1pY3Jvc29mdCBUaW1lLVN0YW1wIFNlcnZpY2Uw
# ggIiMA0GCSqGSIb3DQEBAQUAA4ICDwAwggIKAoICAQDpRIWbIM3Rlr397cjHaYx8
# 5l7I+ZVWGMCBCM911BpU6+IGWCqksqgqefZFEjKzNVDYC9YcgITAz276NGgvECm4
# ZfNv/FPwcaSDz7xbDbsOoxbwQoHUNRro+x5ubZhT6WJeU97F06+vDjAw/Yt1vWOg
# RTqmP/dNr9oqIbE5oCLYdH3wI/noYmsJVc7966n+B7UAGAWU2se3Lz+xdxnNsNX4
# CR6zIMVJTSezP/2STNcxJTu9k2sl7/vzOhxJhCQ38rdaEoqhGHrXrmVkEhSv+S00
# DMJc1OIXxqfbwPjMqEVp7K3kmczCkbum1BOIJ2wuDAbKuJelpteNZj/S58NSQw6k
# hfuJAluqHK3igkS/Oux49qTP+rU+PQeNuD+GtrCopFucRmanQvxISGNoxnBq3UeD
# Tqphm6aI7GMHtFD6DOjJlllH1gVWXPTyivf+4tN8TmO6yIgB4uP00bH9jn/dyyxS
# jxPQ2nGvZtgtqnvq3h3TRjRnkc+e1XB1uatDa1zUcS7r3iodTpyATe2hgkVX3m4D
# hRzI6A4SJ6fbJM9isLH8AGKcymisKzYupAeFSTJ10JEFa6MjHQYYohoCF77R0CCw
# MNjvE4XfLHu+qKPY8GQfsZdigQ9clUAiydFmVt61hytoxZP7LmXbzjD0VecyzZoL
# 4Equ1XszBsulAr5Ld2KwcwIDAQABo4IBSTCCAUUwHQYDVR0OBBYEFO0wsLKdDGpT
# 97cx3Iymyo/SBm4SMB8GA1UdIwQYMBaAFJ+nFV0AXmJdg/Tl0mWnG1M1GelyMF8G
# A1UdHwRYMFYwVKBSoFCGTmh0dHA6Ly93d3cubWljcm9zb2Z0LmNvbS9wa2lvcHMv
# Y3JsL01pY3Jvc29mdCUyMFRpbWUtU3RhbXAlMjBQQ0ElMjAyMDEwKDEpLmNybDBs
# BggrBgEFBQcBAQRgMF4wXAYIKwYBBQUHMAKGUGh0dHA6Ly93d3cubWljcm9zb2Z0
# LmNvbS9wa2lvcHMvY2VydHMvTWljcm9zb2Z0JTIwVGltZS1TdGFtcCUyMFBDQSUy
# MDIwMTAoMSkuY3J0MAwGA1UdEwEB/wQCMAAwFgYDVR0lAQH/BAwwCgYIKwYBBQUH
# AwgwDgYDVR0PAQH/BAQDAgeAMA0GCSqGSIb3DQEBCwUAA4ICAQB23GZOfe9ThTUv
# D29i4t6lDpxJhpVRMme+UbyZhBFCZhoGTtjDdphAArU2Q61WYg3YVcl2RdJm5PUb
# Z2bA77zk+qtLxC+3dNxVsTcdtxPDSSWgwBHxTj6pCmoDNXolAYsWpvHQFCHDqEfA
# iBxX1dmaXbiTP1d0XffvgR6dshUcqaH/mFfjDZAxLU1s6HcVgCvBQJlJ7xEG5jFK
# dtqapKWcbUHwTVqXQGbIlHVClNJ3yqW6Z3UJH/CFcYiLV/e68urTmGtiZxGSYb4S
# BSPArTrTYeHOlQIj/7loVWmfWX2y4AGV/D+MzyZMyvFw4VyL0Vgq96EzQKyteiVe
# BaVEjxQKo3AcPULRF4Uzz98P2tCM5XbFZ3Qoj9PLg3rgFXr0oJEhfh2tqUrhTJd1
# 3+i4/fek9zWicoshlwXgFu002ZWBVzASEFuqED48qyulZ/2jGJBcta+Fdk2loP2K
# 3oSj4PQQe1MzzVZO52AXO42MHlhm3SHo3/RhQ+I1A0Ny+9uAehkQH6LrxkrVNvZG
# 4f0PAKMbqUcXG7xznKJ0x0HYr5ayWGbHKZRcObU+/34ZpL9NrXOedVDXmSd2ylKS
# l/vvi1QwNJqXJl/+gJkQEetqmHAUFQkFtemi8MUXQG2w/RDHXXwWAjE+qIDZLQ/k
# 4z2Z216tWaR6RDKHGkweCoDtQtzkHTCCB3EwggVZoAMCAQICEzMAAAAVxedrngKb
# SZkAAAAAABUwDQYJKoZIhvcNAQELBQAwgYgxCzAJBgNVBAYTAlVTMRMwEQYDVQQI
# EwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNyb3Nv
# ZnQgQ29ycG9yYXRpb24xMjAwBgNVBAMTKU1pY3Jvc29mdCBSb290IENlcnRpZmlj
# YXRlIEF1dGhvcml0eSAyMDEwMB4XDTIxMDkzMDE4MjIyNVoXDTMwMDkzMDE4MzIy
# NVowfDELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldhc2hpbmd0b24xEDAOBgNVBAcT
# B1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEmMCQGA1UE
# AxMdTWljcm9zb2Z0IFRpbWUtU3RhbXAgUENBIDIwMTAwggIiMA0GCSqGSIb3DQEB
# AQUAA4ICDwAwggIKAoICAQDk4aZM57RyIQt5osvXJHm9DtWC0/3unAcH0qlsTnXI
# yjVX9gF/bErg4r25PhdgM/9cT8dm95VTcVrifkpa/rg2Z4VGIwy1jRPPdzLAEBjo
# YH1qUoNEt6aORmsHFPPFdvWGUNzBRMhxXFExN6AKOG6N7dcP2CZTfDlhAnrEqv1y
# aa8dq6z2Nr41JmTamDu6GnszrYBbfowQHJ1S/rboYiXcag/PXfT+jlPP1uyFVk3v
# 3byNpOORj7I5LFGc6XBpDco2LXCOMcg1KL3jtIckw+DJj361VI/c+gVVmG1oO5pG
# ve2krnopN6zL64NF50ZuyjLVwIYwXE8s4mKyzbnijYjklqwBSru+cakXW2dg3viS
# kR4dPf0gz3N9QZpGdc3EXzTdEonW/aUgfX782Z5F37ZyL9t9X4C626p+Nuw2TPYr
# bqgSUei/BQOj0XOmTTd0lBw0gg/wEPK3Rxjtp+iZfD9M269ewvPV2HM9Q07BMzlM
# jgK8QmguEOqEUUbi0b1qGFphAXPKZ6Je1yh2AuIzGHLXpyDwwvoSCtdjbwzJNmSL
# W6CmgyFdXzB0kZSU2LlQ+QuJYfM2BjUYhEfb3BvR/bLUHMVr9lxSUV0S2yW6r1AF
# emzFER1y7435UsSFF5PAPBXbGjfHCBUYP3irRbb1Hode2o+eFnJpxq57t7c+auIu
# rQIDAQABo4IB3TCCAdkwEgYJKwYBBAGCNxUBBAUCAwEAATAjBgkrBgEEAYI3FQIE
# FgQUKqdS/mTEmr6CkTxGNSnPEP8vBO4wHQYDVR0OBBYEFJ+nFV0AXmJdg/Tl0mWn
# G1M1GelyMFwGA1UdIARVMFMwUQYMKwYBBAGCN0yDfQEBMEEwPwYIKwYBBQUHAgEW
# M2h0dHA6Ly93d3cubWljcm9zb2Z0LmNvbS9wa2lvcHMvRG9jcy9SZXBvc2l0b3J5
# Lmh0bTATBgNVHSUEDDAKBggrBgEFBQcDCDAZBgkrBgEEAYI3FAIEDB4KAFMAdQBi
# AEMAQTALBgNVHQ8EBAMCAYYwDwYDVR0TAQH/BAUwAwEB/zAfBgNVHSMEGDAWgBTV
# 9lbLj+iiXGJo0T2UkFvXzpoYxDBWBgNVHR8ETzBNMEugSaBHhkVodHRwOi8vY3Js
# Lm1pY3Jvc29mdC5jb20vcGtpL2NybC9wcm9kdWN0cy9NaWNSb29DZXJBdXRfMjAx
# MC0wNi0yMy5jcmwwWgYIKwYBBQUHAQEETjBMMEoGCCsGAQUFBzAChj5odHRwOi8v
# d3d3Lm1pY3Jvc29mdC5jb20vcGtpL2NlcnRzL01pY1Jvb0NlckF1dF8yMDEwLTA2
# LTIzLmNydDANBgkqhkiG9w0BAQsFAAOCAgEAnVV9/Cqt4SwfZwExJFvhnnJL/Klv
# 6lwUtj5OR2R4sQaTlz0xM7U518JxNj/aZGx80HU5bbsPMeTCj/ts0aGUGCLu6WZn
# OlNN3Zi6th542DYunKmCVgADsAW+iehp4LoJ7nvfam++Kctu2D9IdQHZGN5tggz1
# bSNU5HhTdSRXud2f8449xvNo32X2pFaq95W2KFUn0CS9QKC/GbYSEhFdPSfgQJY4
# rPf5KYnDvBewVIVCs/wMnosZiefwC2qBwoEZQhlSdYo2wh3DYXMuLGt7bj8sCXgU
# 6ZGyqVvfSaN0DLzskYDSPeZKPmY7T7uG+jIa2Zb0j/aRAfbOxnT99kxybxCrdTDF
# NLB62FD+CljdQDzHVG2dY3RILLFORy3BFARxv2T5JL5zbcqOCb2zAVdJVGTZc9d/
# HltEAY5aGZFrDZ+kKNxnGSgkujhLmm77IVRrakURR6nxt67I6IleT53S0Ex2tVdU
# CbFpAUR+fKFhbHP+CrvsQWY9af3LwUFJfn6Tvsv4O+S3Fb+0zj6lMVGEvL8CwYKi
# excdFYmNcP7ntdAoGokLjzbaukz5m/8K6TT4JDVnK+ANuOaMmdbhIurwJ0I9JZTm
# dHRbatGePu1+oDEzfbzL6Xu/OHBE0ZDxyKs6ijoIYn/ZcGNTTY3ugm2lBRDBcQZq
# ELQdVTNYs6FwZvKhggNNMIICNQIBATCB+aGB0aSBzjCByzELMAkGA1UEBhMCVVMx
# EzARBgNVBAgTCldhc2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoT
# FU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjElMCMGA1UECxMcTWljcm9zb2Z0IEFtZXJp
# Y2EgT3BlcmF0aW9uczEnMCUGA1UECxMeblNoaWVsZCBUU1MgRVNOOjdGMDAtMDVF
# MC1EOTQ3MSUwIwYDVQQDExxNaWNyb3NvZnQgVGltZS1TdGFtcCBTZXJ2aWNloiMK
# AQEwBwYFKw4DAhoDFQAEa0f118XHM/VNdqKBs4QXxNnN96CBgzCBgKR+MHwxCzAJ
# BgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25k
# MR4wHAYDVQQKExVNaWNyb3NvZnQgQ29ycG9yYXRpb24xJjAkBgNVBAMTHU1pY3Jv
# c29mdCBUaW1lLVN0YW1wIFBDQSAyMDEwMA0GCSqGSIb3DQEBCwUAAgUA7Bh9QjAi
# GA8yMDI1MDcwOTA1MzE0NloYDzIwMjUwNzEwMDUzMTQ2WjB0MDoGCisGAQQBhFkK
# BAExLDAqMAoCBQDsGH1CAgEAMAcCAQACAin7MAcCAQACAhOrMAoCBQDsGc7CAgEA
# MDYGCisGAQQBhFkKBAIxKDAmMAwGCisGAQQBhFkKAwKgCjAIAgEAAgMHoSChCjAI
# AgEAAgMBhqAwDQYJKoZIhvcNAQELBQADggEBAChiucSv2xFTuI2DXCEbr4WDI/Qn
# yPFDKoNNQijL5EIKZs73rhM8jlGA0MPnhnT5rynFhHJeC0mOfocmgKf+g/i1cbD3
# ol/LsTxvvk0/cVpW4VXCdvOhrsxrFLzy4ilRDZ2jTHUeXv6hVTnwWmfgUSHJ+EHz
# HlJBodEN7LKNrLHM+Vrzs8zYnDVGzCxjG5V/ptY0PnjKes6qckrcGkNauylskM6k
# s+IA3y3QejEjxZwy4QYw+i6U4bH6lqiIzA+7AO9A95Q/yNbZwN5OyMC/kKE8ilGj
# G+A5aIHl0XWJcfTe///+3+eISsX279oJ61g8kJ8DeRZyTLU18TjnOXox2scxggQN
# MIIECQIBATCBkzB8MQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQ
# MA4GA1UEBxMHUmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9u
# MSYwJAYDVQQDEx1NaWNyb3NvZnQgVGltZS1TdGFtcCBQQ0EgMjAxMAITMwAAAgbX
# vFE4mCPsLAABAAACBjANBglghkgBZQMEAgEFAKCCAUowGgYJKoZIhvcNAQkDMQ0G
# CyqGSIb3DQEJEAEEMC8GCSqGSIb3DQEJBDEiBCA0Gy7WNkdFv0sVD9KikZ/Y3yqr
# lzmtX4xVdzzi/6ahAjCB+gYLKoZIhvcNAQkQAi8xgeowgecwgeQwgb0EIODo9ZSI
# kZ6dVtKT+E/uZx2WAy7KiXM5R1JIOhNJf0vSMIGYMIGApH4wfDELMAkGA1UEBhMC
# VVMxEzARBgNVBAgTCldhc2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNV
# BAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEmMCQGA1UEAxMdTWljcm9zb2Z0IFRp
# bWUtU3RhbXAgUENBIDIwMTACEzMAAAIG17xROJgj7CwAAQAAAgYwIgQgpZrAF23x
# vfE13FfXgxFae+PBDfel7lk/GABByKeqxBEwDQYJKoZIhvcNAQELBQAEggIAXTyc
# 6KKa+zZY4NwKJ9YKgd5R4QvhNDDL1DtRzCzVUyJnk0efL6Rt1IjQvMJLbGM4vOVW
# LFFXoBT6/zGwGTzHzMaMSpY4O3qz71XMvUxmlCfDA/8RAuS7ZrwfCaNtrAVwSjvh
# N0pyianKMKe2kd5aA0anM49RwelAseAd8W3IzHjwFzT5hu1pbDgjwjGXzq1WFh0Q
# sz9YQ6mmZ0lcxPUtqM+FaAkxLUfPa2jP/qpjaHgmAc+NTsGk3gP/p/YeEJ4QXwpP
# EJs7tjLw9Y+IzTCLmDmi/QllM2i5e7AruKMaxINOGCOiyr/3H/6ANxJM8TaSSYXE
# py/0xRrS/exIoSdsCy62dZ7Re92EoLMVoudNLBI6NH724IJxpubztWou19P7PYhl
# Hy4oGfD1XpUa5wfZf2wS1fG+m6QMx6u3dFvOkJD9Caa1RXbhmTl1FHRXBCR4E99W
# ETi1GeFGlhD+2612q9DnaygxxGxiaMg09Zg896bZXN1QATvnBhd2mupXAjl4vGwr
# oG85jgWW24MrJs5oyQoXghuiX7Or5Y327S5eoKe4gbYUISuCnnK0ei7gjvdQWgXj
# iN/pe8ZULra+NNV/xxDu7qC4p5e9I46hLTgBVUJccb5lCtLYFdLaDvhM46JDbpaQ
# oEm4bsX49l82w+ZYzZxAB5Snx2Q+nE7MKk7qql8=
# SIG # End signature block
