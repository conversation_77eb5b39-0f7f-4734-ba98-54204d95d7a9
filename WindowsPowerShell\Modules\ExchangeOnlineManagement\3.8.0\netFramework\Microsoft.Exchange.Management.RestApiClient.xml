<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Exchange.Management.RestApiClient</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2">
            <summary>
            AdminCmdlet class.
            The base class for all the client side Cmdlets based on Admin API implementation
            </summary>
            <typeparam name="TDomainObject">TDomainObject param</typeparam>
            <typeparam name="TCmdlet">TCmdlet param</typeparam>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.ResultSize">
            <summary>
            The maximum result set size for searches.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.useBatching">
            <summary> 
            If user wants to get batching benefits on the command he should use "-BatchRequests"
            If not then he should not use "-BatchRequests" in the cmdlet
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.useMultithreading">
            <summary> 
            If user wants to get multithreading benefits on the command he should use "-UseMultithreading 1"
            If not then he should provide "-UseMultithreading 0" in the cmdlet
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.ClientRequestId">
            <summary>
            unique GUID to track an individual commandlet run
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.AuthProvider">
            <summary>
            Auth Provider
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.context">
            <summary>
            OData Client Context
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.cmdletActivity">
            <summary>
            CmdletActivity instance for this cmdlet execution
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.serviceActivity">
            <summary>
            ServiceActivity instance for this cmdlet execution,
            signifies for the time taken from the ProcessRecord to WriteObject.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.CancellationTokenSource">
            <summary>
            Cancellation Token Source
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.assemblyVersion">
            <summary>
            Contains the version info of the loaded assembly
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.cancellationTokenSource">
            <summary>
            Cancellation Token Source
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.powerEventLogs">
            <summary>
            To track sleep or wakeup Events
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.objectCount">
            <summary>
            To track total number of objects written to pipeline
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.progressRecord">
            <summary>
            Holds the information to print in progress bar
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.CountOfObjectsReturned">
            <summary>
            To show the progress of count of objects processed during cmdlet run 
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.previousTimeStamp">
            <summary>
            List object write timestamp
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.resultSize">
            <summary>
            By default, we return 1000 records. 
            If user wants to get all the records, he should specify ResultSize -Unlimited
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.authProvider">
            <summary>
            Instance of the Auth provider for the running cmdlet.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.authResult">
            <summary>
            The cached authentication result from which cmdlets can retrieve auth token.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.batchHelper">
            <summary>
            Batch Helper object used to store queries for 
            running cmdlets in batching mode
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.asyncConsoleLogger">
            <summary>
            Async console logger
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.#ctor">
            <summary>
            constructor for AdminCmdlet
            ClientRequestId - Every Cmdlet run is uniquely tracked with this GUID regardless of standalone run or pipe scenario
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.WriteError(System.Management.Automation.ErrorRecord)">
            <summary>
            WriteError Method.
            This API overide base (PsCmdlet) class method and make it virtual, so Unit Test can mock this method.
            </summary>
            <param name="errorRecord">Error Record </param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.#cctor">
            <summary>
            Static constructor for AdminCmdlet
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.ExecuteWithExceptionHandling(System.Action,System.Exception@)">
            <summary>
            Helper method to executes the action wrapped in try-catch block.
            </summary>
            <param name="action">
            The action to be executed.
            </param>
            <param name="exception">
            The exception out param.
            Contains the exception after execution. (if any).
            </param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.PrintUnsupportedParameterMessage(System.String)">
            <summary>
            Throws unsupported parameter message
            </summary>
            <param name="parameterName">parameterName param</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.BeginProcessing">
            <summary>
            Begin Processing
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.InternalBeginProcessing">
            <summary>
            The internal begin processing override for cmdlets.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.ProcessRecord">
            <summary>
            The process record override.
            Handles core processing across admin cmdlets.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.CreateNewServiceActivity">
            <summary>
            Create a new service activity instance
            </summary>
            <returns>A new instance of ServiceActivity</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.ProcessRequest">
            <summary>
            The method which would handle the sending of a request
            The individual cmdlets which want to have a custom implementation
            can override this method
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.ProcessLastBatching">
            <summary>
            The method which would trigeer the remaining batch requests
            It is being used during end processing in case of batching
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.SendBatchRequest(Microsoft.OData.Client.DataServiceQuery{`0})">
            <summary>
            The internal method of cmdlet which would create the query
            </summary>
            <param name="adminApiQuery">adminApiQuery param</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.SendRequest(Microsoft.OData.Client.DataServiceQuery{`0})">
            <summary>
            Send request method which is overriden by cmdlet classes
            </summary>
            <param name="adminApiQuery">adminApiQuery param</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.GetODataQueryForCmdlet">
            <summary>
            The internal method of cmdlet which would create the query
            </summary>
            <returns> Returns dataServiceQuery instance for the cmdlet</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.IsBatchingImplemented">
            <summary>
            The internal method of cmdlet which indicates whether or not batching has been implemented
            </summary>
            <returns> Returns bool whether or not batching is implemented</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.InternalProcessRecord">
            <summary>
            The internal process record override for cmdlets.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.EndProcessing">
            <summary>
            The end processing override.
            Handles the core cleanup across admin cmdlets.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.InternalEndProcessing">
            <summary>
            The internal end processing override for cmdlets.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.ConstructAndThrowExceptionWithMeaningfullMessage(System.Exception)">
            <summary>
            Construct client specific exception based on given exception.
            This method constructs meaningful error messages for known exceptions and throws RestClientException.
            </summary>
            <param name="ex">Exception to be processed.</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.GetPSPropertyValue(System.Reflection.PropertyInfo,System.Object)">
            <summary>
            Return Property value from result object.
            </summary>
            <param name="property"> PS Property infromation</param>
            <param name="obj"> Result Object</param>
            <returns> Property Value </returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.GetGuidTypeConversionProperties">
            <summary>
            This method returns list of properties which should be converted to Guid types.
            </summary>
            <returns>Null, if we do not have any fields to convert to guid.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.GetRequestHeaders">
            <summary>
            Add Header heade Key value in collection.
            </summary>
            <returns> Return Collection of header Key Value</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.GetBoolTypeConversionProperties">
            <summary>
            This method returns list of properties which should be converted to Bool types.
            </summary>
            <returns>Null, if we do not have any fields to convert to Bool.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.GetDateTimeTypeConversionProperties">
            <summary>
            This method returns list of properties which should be converted to Bool types.
            </summary>
            <returns>Null, if we do not have any fields to convert to Bool.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.ValidateRequestedProperties(System.Type,System.String[])">
            <summary>
            This method verifies that requested property names are valid as per OData domain model.
            Throws RestClientException if any one of given requested properties is not valid.
            </summary>
            <param name="type">OData model object type.</param>
            <param name="requestedProperties">Array of requested properties.</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.ExtractPropertiesFromObject(System.Object,System.Collections.Generic.ISet{System.String})">
            <summary>
            Construct PSObject based on the given properties and values from given object. If given object does not have a property requested, it will ignore. 
            </summary>
            <param name="obj">Object which provides values.</param>
            <param name="requestedProperties">Requested property names.</param>
            <returns>PSObject by populating the values.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.GetPSObject">
            <summary>
            Get the PSObject Instance to be written to the pipeline
            [DoNotOverrideThisMethod]
            </summary>
            <returns>An instance of OutputPSObject</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.GetResultSize">
            <summary>
            Gets the Result Size
            </summary>
            <returns>Result Size</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.WriteObjectInternal(System.Object)">
            <summary>
            Wrapper function which does writeObject and displays the progress of cmdlet run
            </summary>
            <param name="sendToPipeline">The object to be written to pipeline</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.WriteObjectCount(System.Boolean)">
            <summary>
            Function to show object count progress bar
            </summary>
            <param name="forceWrite"> forceWrite param</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.TransformObjectToList(System.Object)">
            <summary>
            Transform given List object into a custom list.
            This change is required in order to print output properly in csv file when Export-Csv is done
            </summary>
            <param name="listObject">List object</param>
            <returns>Returns custom list</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.LogCmdletActivity">
            <summary>
            Post processing logs of cmdlet during success or failure scenarios
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.TransformObjectToGuid(System.String,System.Object)">
            <summary>
            Transform given object into Guid type.
            </summary>
            <param name="propertyName">Property name.</param>
            <param name="propertyValue">Property value.</param>
            <returns>Returns transformed object></returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.TransformObjectToBool(System.String,System.Object)">
            <summary>
            Transform given object into Boolean type.
            </summary>
            <param name="propertyName">Property name.</param>
            <param name="propertyValue">Property value.</param>
            <returns>Returns transformed object</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.TransformObjectToDatetime(System.String,System.Object)">
            <summary>
            Transform given object into Datetime Object.
            Ex: Input   : "2019-12-03T02:14:19.0000000-08:00"
                returns : "12/3/2019 3:44:19 PM"
            </summary>
            <param name="propertyName">Property name.</param>
            <param name="propertyValue">Property value.</param>
            <returns>Datetime Object or null</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.LogPowerEvent">
            <summary>
            Logs a list of Power Events happened during commandlet execution
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.GetServiceRoot(System.String)">
            <summary>
            Generates the service root for the Admin API
            </summary>
            <param name="tenantId">Tenant Id</param>
            <returns>service root for the Admin API</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2.OnPowerChange(System.Object,Microsoft.Win32.PowerModeChangedEventArgs)">
            <summary>
            Creates a Power Event log during Sleep or Wake up instances
            </summary>
            <param name="obj">obj param</param>
            <param name="eventArgs">eventArgs param</param>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Briefing.BriefingCmdlet`2">
            <summary>
            Base class for all Briefing email cmdlets
            </summary>
            <typeparam name="TDomainObject">The api type</typeparam>
            <typeparam name="TCmdlet">The type of the cmdlet</typeparam>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Briefing.BriefingCmdlet`2.Identity">
            <summary>
            Specifies the Identity of the user to update MyAnalytics settings
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Briefing.BriefingCmdlet`2.UrlStem">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Briefing.BriefingFeatureSettings">
            <summary>
            Cortana Briefing user settings that are controlled by the tenant admin.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Briefing.BriefingFeatureSettings.UserId">
            <summary>
            Gets or sets the user id.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Briefing.BriefingFeatureSettings.IsEnabled">
            <summary>
            Gets or sets if Briefing is enabled.
            If admin turns the setting off, the user cannot opt back in. 
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Briefing.GetDefaultTenantBriefingConfig">
            <summary>
            This class implements "Get-DefaultTenantBriefingConfig" Cmdlet
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Briefing.GetDefaultTenantBriefingConfig.CmdletName">
            <summary>
            The name of the cmdlet
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Briefing.GetDefaultTenantBriefingConfig.InternalProcessRecord">
            <summary>
            Internal process of the cmdlet
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Briefing.GetUserBriefingConfig">
            <summary>
            This class implements "Get-UserBriefingConfig" Cmdlet
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Briefing.GetUserBriefingConfig.CmdletName">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Briefing.GetUserBriefingConfig.InternalProcessRecord">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Briefing.SetDefaultTenantBriefingConfig">
            <summary>
            This class implements "Set-DefaultTenantBriefingConfig" Cmdlet
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Briefing.SetDefaultTenantBriefingConfig.IsEnabledByDefault">
            <summary>
            Gets or sets a value indicating whether the Briefing is opt-in/opt-out by default.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Briefing.SetDefaultTenantBriefingConfig.CmdletName">
            <summary>
            Name of the cmdlet
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Briefing.SetDefaultTenantBriefingConfig.InternalProcessRecord">
            <summary>
            Internal process of the cmdlet
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Briefing.SetUserBriefingConfig">
            <summary>
            This class implements "Set-UserBriefingConfig" Cmdlet
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Briefing.SetUserBriefingConfig.Enabled">
            <summary>
            Gets or sets a value indicating whether the Briefing is enabled.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Briefing.SetUserBriefingConfig.CmdletName">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Briefing.SetUserBriefingConfig.InternalProcessRecord">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Briefing.TenantBriefingCmdlet`2">
            <summary>
            Base class for all Briefing Tenant email cmdlets
            </summary>
            <typeparam name="TDomainObject">The api type</typeparam>
            <typeparam name="TCmdlet">The type of the cmdlet</typeparam>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Briefing.TenantBriefingCmdlet`2.UrlStem">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Briefing.TenantBriefingCmdlet`2.GetDefaultTenantSettings(Microsoft.Exchange.Management.RestApiClient.Briefing.TenantBriefingFeatureSettings)">
            <summary>
            creates the display object of settings to show admin on powershell session
            </summary>
            <param name="settings">The tenant's briefing settings</param>
            <returns>Tenant's Briefing Settings</returns>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Briefing.TenantBriefingDisplaySettings">
            <summary>
            This class represent the display object of the tenant's briefing settings
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Briefing.TenantBriefingDisplaySettings.IsEnabledByDefault">
            <summary>
            Whether the tenant has opted-in/out of Receiving Briefing Email By default
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Briefing.TenantBriefingFeatureSettings">
            <summary>
            Cortana Briefing user settings that are controlled by the tenant admin.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Briefing.TenantBriefingFeatureSettings.IsSubscribedByDefault">
            <summary>
            If Briefing Email is subscribed by default
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize">
            <summary>
            A class wrapping a ulong that can be initialized with a
            'byte quantified' number. The quantifiers are:
            Kb (2^10), Mb (2^20), Gb (2^30) and Tb (2^40)
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.MinValue">
            <summary>
            Minimal value of ByteQuantifiedSize: 0 Byte
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.MaxValue">
            <summary>
            Maximal value of ByteQuantifiedSize: 18,446,744,073,709,551,615 Bytes
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.Zero">
            <summary>
            Zero Value of ByteQuantifiedSize: 0 Byte
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.KilobyteQuantifierProvider">
            <summary>
            The format provider to return KB;
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.MegabyteQuantifierProvider">
            <summary>
            The format provider to return MB;
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.LargestAppropriateUnitFormatPattern">
            <summary>
            This Regex is designed to match the following pattern:
            [ShortBytesRepresentation] [Unit] ([LongBytesRepresentation] bytes)
            The LongBytesRepresentation is optional for the match to be successful.
            The ShortBytesRepresentation may not contain more than 15 digits total;
            for more precision, use the LongBytesRepresentation
            
            The following are examples of acceptable values that will match:
            "1.23 KB (1,234 bytes)"     # Default ToString() format
            "1.54 MB"                   # Only Short Representation NOTE: Fractional bytes will be truncated
            "154 B (154 bytes)"         # Default ToString() format
            "1 kb (1024 BYTES)"         # Case is ignored
            "145Gb"                     # No space between value and unit
            "1.23E+3Kb"                 # Exponential notation
            "12345"                     # Simple number
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.bytes">
            <summary>
            The bytes value representation for this ByteQuantifiedSized
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.unit">
            <summary>
            The unit that ToString() will be using
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.canonicalForm">
            <summary>
            the cached result of ToString().
            because ByteQuantifiedSize is an immutable type,
            we need not calculate the output string more than once.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.#ctor(System.UInt64)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize" /> struct.
            Creates a ByteQuantifiedSize without quantifier and with byteValue
            </summary>
            <param name="byteValue">Number of bytes for this ByteQuantifiedSize</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.#ctor(System.UInt64,Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.Quantifier)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize" /> struct.
            Initializes a new ByteQuantifiedSize to the specified number and desired unit.
            </summary>
            <param name="byteValue">Number of bytes for this ByteQuantifiedSize</param>
            <param name="desiredUnitToDisplay">Select a desired unit to display.</param>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.Quantifier">
            <summary>
            The supported units
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.Quantifier.None">
            <summary>No unit (2^0 = 1)</summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.Quantifier.KB">
            <summary>Defines the unit Kilobyte (2^10 = 1024) in bytes.</summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.Quantifier.MB">
            <summary>Defines the unit Megabyte (2^20 = 1024 * 1024) in bytes.</summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.Quantifier.GB">
            <summary>Defines the unit Gigabyte (2^30 = 1024 * 1024 * 1024) in bytes.</summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.Quantifier.TB">
            <summary>Defines the unit Terabyte (2^40 = 1024 * 1024 * 1024 * 1024) in bytes.</summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.MaximumValues">
            <summary>
            Maximum values of quantifiers.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.MaximumValues.None">
            <summary>No unit, 2^64-1 = 0xFFFFFFFFFFFFFFFF.</summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.MaximumValues.KB">
            <summary>Kilobyte (2^64 - 1) >> 10.</summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.MaximumValues.MB">
            <summary>Megabyte (2^64 - 1) >> 20.</summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.MaximumValues.GB">
            <summary>Gigabyte (2^64 - 1) >> 30.</summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.MaximumValues.TB">
            <summary>Terabyte (2^64 - 1) >> 40.</summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.QuantifierZeroBits">
            <summary>
            Number of unset bits for each quantifier.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.QuantifierZeroBits.None">
            <summary>1 Byte = 2^0 Bytes</summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.QuantifierZeroBits.KB">
            <summary>1 KByte = 2^10 Bytes</summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.QuantifierZeroBits.MB">
            <summary>1 MByte = 2^20 Bytes</summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.QuantifierZeroBits.GB">
            <summary>1 GByte = 2^30 Bytes</summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.QuantifierZeroBits.TB">
            <summary>1 TByte = 2^40 Bytes</summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.FromBytes(System.UInt64)">
            <summary>
            Returns a ByteQuantifiedSize that represents a specified number of Bytes.       
            </summary>
            <param name="bytesValue">Size in unit Bytes.</param>
            <returns>ByteQuantifiedSize with desired unit of Byte</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.FromKB(System.UInt64)">
            <summary>
            Returns a ByteQuantifiedSize that represents a specified number of Kilobytes.       
            </summary>
            <param name="kbValue">Size in unit KB.</param>
            <returns>ByteQuantifiedSize with desired unit of Kilobyte</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.FromMB(System.UInt64)">
            <summary>
            Returns a ByteQuantifiedSize that represents a specified number of Megabytes.       
            </summary>
            <param name="mbValue">Size in unit MB.</param>
            <returns>ByteQuantifiedSize with desired unit of Megabyte</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.FromGB(System.UInt64)">
            <summary>
            Returns a ByteQuantifiedSize that represents a specified number of Gigabytes.       
            </summary>
            <param name="gbValue">Size in unit GB.</param>
            <returns>ByteQuantifiedSize with desired unit of Gigabyte</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.FromTB(System.UInt64)">
            <summary>
            Returns a ByteQuantifiedSize that represents a specified number of Terabytes.       
            </summary>
            <param name="tbValue">Size in unit TB.</param>
            <returns>ByteQuantifiedSize with desired unit of Terabyte</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.op_LessThanOrEqual(Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize,Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize)">
            <summary>
            Implements less than or equal to operator
            </summary>
            <param name="value1">Value 1 to compare</param>
            <param name="value2">Value 2 to compare</param>
            <returns>True, if first parameter is less than or equal to second. Otherwise, false.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.op_GreaterThan(Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize,Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize)">
            <summary>
            Implements greater than operator
            </summary>
            <param name="value1">Value 1 to compare</param>
            <param name="value2">Value 2 to compare</param>
            <returns>True, if first parameter is greater than second. Otherwise, false.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.op_GreaterThanOrEqual(Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize,Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize)">
            <summary>
            Implements greater than or equal to operator
            </summary>
            <param name="value1">Value 1 to compare</param>
            <param name="value2">Value 2 to compare</param>
            <returns>True, if first parameter is greater than or equal to second. Otherwise, false.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.op_Equality(Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize,Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize)">
            <summary>
            Override == operator
            </summary>
            <param name="value1">Value 1 to compare</param>
            <param name="value2">Value 2 to compare</param>
            <returns>true if equal, otherwise false</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.op_Inequality(Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize,Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize)">
            <summary>
            Override != operator
            </summary>
            <param name="value1">Value 1 to compare</param>
            <param name="value2">Value 2 to compare</param>
            <returns>true if not equal, otherwise false</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.op_Multiply(Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize,Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize)">
            <summary>
            Override the '*' operator for ByteQuantifiedSize,
            </summary>
            <param name="value1">Right operand</param>
            <param name="value2">Left operand</param>
            <returns>The result of applying (value1 * value2)</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.op_Multiply(Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize,System.UInt64)">
            <summary>
            Override the '*' operator for ByteQuantifiedSize,
            </summary>
            <param name="value1">Right operand</param>
            <param name="value2">Left operand</param>
            <returns>The result of applying (value1.bytes * value2)</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.op_Multiply(Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize,System.Int32)">
            <summary>
            Override the '*' operator for ByteQuantifiedSize,
            </summary>
            <param name="value1">Right operand</param>
            <param name="value2">Left operand</param>
            <returns>The result of applying (value1.bytes * value2)</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.op_Division(Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize,Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize)">
            <summary>
            Override the '/' operator for ByteQuantifiedSize,
            </summary>
            <param name="value1">Right operand</param>
            <param name="value2">Left operand</param>
            <returns>The result of applying (value1 / value2)</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.op_Division(Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize,System.UInt64)">
            <summary>
            Override the '/' operator for ByteQuantifiedSize,
            </summary>
            <param name="value1">Right operand</param>
            <param name="value2">Left operand</param>
            <returns>The result of applying (value1.bytes / value2)</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.op_Division(Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize,System.Int32)">
            <summary>
            Override the '/' operator for ByteQuantifiedSize,
            </summary>
            <param name="value1">Right operand</param>
            <param name="value2">Left operand</param>
            <returns>The result of applying (value1.bytes / value2)</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.op_Addition(Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize,System.UInt64)">
            <summary>
            Override the '+' operator for ByteQuantifiedSize,
            </summary>
            <param name="value1">Right operand</param>
            <param name="value2">Left operand</param>
            <returns>The result of applying (value1.bytes + value2)</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.op_Addition(Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize,System.Int32)">
            <summary>
            Override the '+' operator for ByteQuantifiedSize,
            </summary>
            <param name="value1">Right operand</param>
            <param name="value2">Left operand</param>
            <returns>The result of applying (value1.bytes + value2)</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.op_Addition(Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize,Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize)">
            <summary>
            Override the '+' operator for ByteQuantifiedSize,
            </summary>
            <param name="value1">Right operand</param>
            <param name="value2">Left operand</param>
            <returns>The result of applying (value1 + value2)</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.op_Subtraction(Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize,Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize)">
            <summary>
            Override the '-' operator for ByteQuantifiedSize,
            </summary>
            <param name="value1">Right operand</param>
            <param name="value2">Left operand</param>
            <returns>The result of applying (value1 - value2)</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.op_Subtraction(Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize,System.UInt64)">
            <summary>
            Override the '-' operator for ByteQuantifiedSize,
            </summary>
            <param name="value1">Right operand</param>
            <param name="value2">Left operand</param>
            <returns>The result of applying (value1.bytes - value2)</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.op_Subtraction(Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize,System.Int32)">
            <summary>
            Override the '-' operator for ByteQuantifiedSize,
            </summary>
            <param name="value1">Right operand</param>
            <param name="value2">Left operand</param>
            <returns>The result of applying (value1.bytes - value2)</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.op_Explicit(Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize)~System.UInt64">
            <summary>
            Explicit cast from ByteQuantifiedSize to UInt64
            </summary>
            <param name="size">Instance of ByteQuantifiedSize to cast.</param>
            <returns>Size in bytes.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.op_Explicit(Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize)~System.Double">
            <summary>
            Explicit cast from ByteQuantifiedSize to Double
            </summary>
            <param name="size">Instance of ByteQuantifiedSize to cast.</param>
            <returns>Size in bytes.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.Parse(System.String,Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.Quantifier)">
            <summary>
            Overload method. Parse a quantified string with specified default unit.
            </summary>
            <param name="expression">The quantified string representation of a number</param>
            <param name="defaultUnit" cref="T:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.Quantifier">Default unit if no quantifier</param>
            <returns>Parse result</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.TryParse(System.String,Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.Quantifier,Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize@)">
            <summary>
            Overload method. Try to parse a quantified string with specified default unit.
            </summary>
            <param name="expression">The quantified string representation of a number</param>
            <param name="defaultUnit" cref="T:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.Quantifier">Default unit if no quantifier</param>
            <param name="byteQuantifiedSize">Parse result</param>
            <returns>True if succeeded</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.Parse(System.String)">
            <summary>
            Parse a quantified string representation of a number.
            </summary>
            <param name="expression">
            The quantified string representation of a number
            </param>
            <returns>An instance with the actual number assigned</returns>
            <exception cref="T:System.FormatException">
            Thrown if format is invalid
            </exception>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.TryParse(System.String,Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize@)">
            <summary>
            Try to parse a quantified string representation of a number.
            </summary>
            <param name="expression">
            The quantified string representation of a number
            </param>
            <param name="byteQuantifiedSize">The actual number</param>
            <returns>True if succeeded</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.FromSpecifiedUnit(System.UInt64,Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.Quantifier)">
            <summary>
            Return ByteQuantifiedSize with quantity and specified unit
            </summary>
            <param name="number">A given quantity</param>
            <param name="specifiedUnit">For a specified unit</param>
            <returns>A new instance</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.op_LessThan(Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize,Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize)">
            <summary>
            Implements less than operator
            </summary>
            <param name="value1">Value 1 to compare.</param>
            <param name="value2">Value 2 to compare.</param>
            <returns>True, if first parameter is less than second. Otherwise, false.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.ToBytes">
            <summary>
            Returns a ulong that represents the current size in Bytes. 
            </summary>
            <returns>Returns size in Bytes.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.ToKB">
            <summary>
            Returns a ulong that represents the current size in Kilobytes. 
            </summary>
            <returns>Returns size in KB.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.ToMB">
            <summary>
            Returns a ulong that represents the current size in Megabytes. 
            </summary>
            <returns>Returns size in MB.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.ToGB">
            <summary>
            Returns a ulong that represents the current size in Gigabytes. 
            </summary>
            <returns>Returns size in GB.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.ToTB">
            <summary>
            Returns a ulong that represents the current size in Terabytes. 
            </summary>
            <returns>Returns size in TB.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.RoundUpToUnit(Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.Quantifier)">
            <summary>
            Convert the value to a specified unit and round up if required.
            </summary>
            <param name="quantifier">Specified unit.</param>
            <returns>Converted value.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.ToString">
            <summary>
            ToString Method
            </summary>
            <returns>String representation of ByteQuantifiedSize</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.ToString(System.String)">
            <summary>
            Overload method, output the string with different units
            </summary>
            <param name="format">
            "B" - output as bytes
            "K" - output as KB
            "M" - output as MB
            "G" - output as GB
            "T" - output as TB
            "A" - output as largest appropriate unit with approximate value along with full bytes
            "a" - output as largest appropriate unit with approximate value 
            </param>
            <returns>String representation of ByteQuantifiedSize with different units</returns>
            <exception cref="T:System.FormatException">unsupported output format</exception>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.ToString(System.String,System.IFormatProvider)">
            <summary>
            Method for IFormattable
            </summary>
            <param name="format">
            "B" - output as bytes
            "K" - output as KB
            "M" - output as MB
            "G" - output as GB
            "T" - output as TB
            "A" - output as largest appropriate unit with approximate value 
            </param>
            <param name="formatProvider">An IFormatProvider instance</param>
            <returns>String representation of ByteQuantifiedSize with different units</returns>
            <exception cref="T:System.FormatException">unsupported output format</exception>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.Equals(System.Object)">
            <summary>
            Override Equals operator.
            </summary>
            <param name="obj">Object to compare with</param>
            <returns>true if equal, otherwise false</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.Equals(Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize)">
            <summary>
            Override Equals operator.
            </summary>
            <param name="other">Other object to compare with</param>
            <returns>true if equal, otherwise false</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.GetHashCode">
            <summary>
            Returns the hash code for this instance.
            </summary>
            <returns>A 32-bit signed integer hash code.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.CompareTo(Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize)">
            <summary>
            Compare with same type instance
            </summary>
            <param name="other">The other instance</param>
            <returns>A signed integer that indicates the relative values of this instance compared against the other instance.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.System#IComparable#CompareTo(System.Object)">
            <summary>
            Compare with object
            </summary>
            <param name="other">The other instance</param>
            <returns>A signed integer that indicates the relative values of this instance compared against the other instance.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.InternalTryParse(System.String,System.UInt64@,Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.Quantifier@,System.Exception@)">
            <summary>
            Try to parse a quantified string representation to the
            corresponding ulong.
            </summary>
            <param name="expression">The quatified string representation</param>
            <param name="bytes">The actual represented number</param>
            <param name="unit">The unit quantifier</param>
            <param name="error">The error code</param>
            <returns>True if succeeded, false if failed</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.ToLargestAppropriateUnitFormatString(System.Boolean)">
            <summary>
            Returns a string that represents the current size in largest appropriate unit with 
            approximate value.
            </summary>
            <param name="includeFullBytes">Should the full bytes be included in the string as well?</param>
            <returns>current size in largest appropriate unit</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.ToString(Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.Quantifier)">
            <summary>
            Overload method, output the string with different units
            </summary>
            <param name="quantifier">Quantifier defining the unit.</param>
            <returns>String representation of ByteQuantifiedSize with different units.</returns>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.DataStrings">
            <summary>
            Contains the DataStrings for ByteQuantifiedSize.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.DataStrings.ExceptionFormatNotSupported">
            <summary>
            Formats the ExceptionFormatNotSupported exception text.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.DataStrings.ExceptionUnknownUnit">
            <summary>
            Formats the ExceptionUnknownUnit exception text.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.DataStrings.ExceptionObjectInvalid">
            <summary>
            Formats the ExceptionObjectInvalid exception text.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.DataStrings.ExceptionValueOverflow(System.String,System.String,System.String)">
            <summary>
            Formats the ExceptionValueOverflow exception text.
            </summary>
            <param name="minValue">The minimum value for ByteQuantifiedSize.</param>
            <param name="maxValue">The maximum value for ByteQuantifiedSize.</param>
            <param name="value">The value for which an exception is thrown.</param>
            <returns>The formatted exception text.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.DataStrings.ExceptionFormatNotCorrect(System.String)">
            <summary>
            Formats the ExceptionFormatNotCorrect exception text.
            </summary>
            <param name="value">The value for which an exception is thrown.</param>
            <returns>The formatted exception text.</returns>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.QuantifierProvider">
            <summary>
            If this class is changed to a mutable class while the reference types to the objects of this class
            are defined as readonly, DoNotDeclareReadOnlyMutableReferenceTypes rule in fxcop will be violated. 
            In that case, remove members of this type in sources\dev\data\src\core\FxCopExclusions.cs and other 
            fxcopexclusions.cs files in code tree, run fxcop for each affected binary, and analyze the 
            violations. see http://blogs.msdn.com/fxcop/archive/2006/04/04/568562.aspx
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.QuantifierProvider.quantifier">
            <summary>
            The private instance returned by GetFormat.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.QuantifierProvider.#ctor(Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.Quantifier)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.QuantifierProvider" /> class.
            </summary>
            <param name="quantifier">The size quantifier.</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ByteQuantifiedSize.QuantifierProvider.GetFormat(System.Type)">
            <summary>
            The method to retrieve the format type;
            </summary>
            <param name="formatType">Parameter must be of type Quantifier</param>
            <returns>A Quantifier instance</returns>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.CustomList">
            <summary>
            Provides overriden functionality of ToString() of existing List class
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.CustomList.#ctor(System.Collections.Generic.List{System.Object})">
            <summary>
             Constructor Definition
            </summary>
            <param name="list">Input list object</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.CustomList.ToString">
            <summary>
             This overridden method prints the list as comma separated values
            </summary>
            <returns> Return a stringified list </returns>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.CmdletIOPipeline">
            <summary>
            This class takes care of logging in PowerShell Console
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.CmdletIOPipeline.cmdlet">
            <summary>
            Cmdlet class whose Logger we want to use
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.CmdletIOPipeline.#ctor(System.Management.Automation.Cmdlet)">
            <summary>
            Constructor for CmdletIOPipeline
            </summary>
            <param name="cmdlet">Cmdlet param</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.CmdletIOPipeline.Info(System.String)">
            <summary>
            Logs verbose information to the console
            </summary>
            <param name="message">Message to log</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.CmdletIOPipeline.Debug(System.String)">
            <summary>
            Logs debug information to the console
            </summary>
            <param name="message">Message to log</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.CmdletIOPipeline.Warning(System.String)">
            <summary>
            Logs warning information to the console
            </summary>
            <param name="message">Message to log</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.CmdletIOPipeline.Error(System.String)">
            <summary>
            Logs error information to the console
            </summary>
            <param name="message">Message to log</param>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.CmdletUtilities">
            <summary>
            Utilities for cmdlet classes.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.CmdletUtilities.IsValidGuid(System.String)">
            <summary>
            Returns true if given string is in valid GUID format.
            </summary>
            <param name="guidStr">Inout string.</param>
            <returns>True if given string is in valid guid format. False otherwise.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.CmdletUtilities.Base64Encode(System.String)">
            <summary>
            function that converts a string into a base64 encoded string
            </summary>
            <param name="plainText">string to be base64 encoded</param>
            <returns>returns base64 encoded string</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.CmdletUtilities.ScrubSenstiveContentFromErrorMessage(System.String)">
            <summary>
            Remove sensitive information from error message like stacktrace etc. 
            </summary>
            <param name="errorMessage">Error message sent from server.</param>
            <returns>Scrubbed output.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.CmdletUtilities.ScrubServerStackTraceFromErrorMessage(System.String)">
            <summary>
            Utility method to remove server stack trace from error message.
            </summary>
            <param name="errorMessage">Error message.</param>
            <returns>Error message after removing the stack trace.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.CmdletUtilities.ParseDateTimeFromString(System.String)">
            <summary>
            Method to Parse DateTime from API using the AdminAPI Datetime Serialization Format which is RoundTrip.
            This can return 2 types of Datetimes,
            1) DateTimeKind.Utc Ex: "2019-12-03T02:14:19.0000000Z"
            2) DateTimeKind.Local Ex: "2019-12-03T02:14:19.0000000-08:00"
            Input:   "2019-12-03T02:14:19.0000000-08:00" 
            returns: {03-12-2019 15:44:19}
                       Date: {03-12-2019 00:00:00}
                       Day: 3
                       DayOfWeek: Tuesday
                       DayOfYear: 337
                       Hour: 15
                       Kind: Local
                       Millisecond: 0
                       Minute: 44
                       Month: 12
                       Second: 19
                       Ticks: 637109846590000000
                       TimeOfDay: {15:44:19}
                       Year: 2019
            </summary>
            <param name="value">Datetime Field json serialization data value</param>
            <returns>Datetime object after Successful Parsing</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.CmdletUtilities.TransformCultureInfoFromString(System.String)">
            <summary>
            This method transforms the culture info string to Native CultureInfo object
            </summary>
            <param name="cultureinfo">Param to identify culture like en-Us</param>
            <returns>CultureInfo Object</returns>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.CommonParameterSets">
            <summary>
            The strings representing the most common parameter sets used by data access tasks. 
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.CommonParameterSets.Identity">
            <summary>
            The Identity parameter set.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.CommonParameterSets.Anr">
            <summary>
            Anr parameter set.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.CommonParameterSets.Default">
            <summary>
            The Default parameter set.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.CommonParameterSets.MobileDeviceIdentity">
            <summary>
            The MobileDeviceIdentity parameter set.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeAssignments.CustomNudgeAssignment">
            <summary>
            The custom nudge assignment data type
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeAssignments.CustomNudgeAssignment.AssigneeId">
            <summary>
            The user's identity
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeAssignments.CustomNudgeAssignment.NudgeName">
            <summary>
            The name of the nudge
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeAssignments.CustomNudgeAssignment.StartTime">
            <summary>
            The start time of the assignment
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeAssignments.CustomNudgeAssignment.EndTime">
            <summary>
            The end time of the assignment
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeAssignments.CustomNudgeAssignmentList">
            <summary>
            List of custom nudge assignment
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeAssignments.CustomNudgeAssignmentList.Value">
            <summary>
            The value
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeAssignments.GetCustomNudgeAssignment">
            <summary>
            Implements Get-CustomNudgeAssignment cmdlet
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeAssignments.GetCustomNudgeAssignment.urlStem">
            <summary>
            Private backing field for UrlStem
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeAssignments.GetCustomNudgeAssignment.AssigneeId">
            <summary>
            The user's identity
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeAssignments.GetCustomNudgeAssignment.NudgeName">
            <summary>
            The name of the nudge
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeAssignments.GetCustomNudgeAssignment.CmdletName">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeAssignments.GetCustomNudgeAssignment.UrlStem">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeAssignments.GetCustomNudgeAssignment.InternalProcessRecord">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeAssignments.RemoveCustomNudgeAssignment">
            <summary>
            Implements Remove-CustomNudgeAssignment cmdlet
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeAssignments.RemoveCustomNudgeAssignment.AssigneeId">
            <summary>
            The user's identity
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeAssignments.RemoveCustomNudgeAssignment.NudgeName">
            <summary>
            The name of the nudge
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeAssignments.RemoveCustomNudgeAssignment.CmdletName">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeAssignments.RemoveCustomNudgeAssignment.UrlStem">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeAssignments.RemoveCustomNudgeAssignment.InternalProcessRecord">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeAssignments.NewCustomNudgeAssignment">
            <summary>
            Implements New-CustomNudgeAssignment cmdlet
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeAssignments.NewCustomNudgeAssignment.AssigneeId">
            <summary>
            The user's identity
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeAssignments.NewCustomNudgeAssignment.NudgeName">
            <summary>
            The name of the nudge
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeAssignments.NewCustomNudgeAssignment.StartTime">
            <summary>
            The start time of the assignment
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeAssignments.NewCustomNudgeAssignment.EndTime">
            <summary>
            The end time of the assignment
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeAssignments.NewCustomNudgeAssignment.CmdletName">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeAssignments.NewCustomNudgeAssignment.UrlStem">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeAssignments.NewCustomNudgeAssignment.InternalProcessRecord">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeAssignments.SetCustomNudgeAssignment">
            <summary>
            Implements Set-CustomNudgeAssignment cmdlet
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeAssignments.SetCustomNudgeAssignment.AssigneeId">
            <summary>
            The user's identity
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeAssignments.SetCustomNudgeAssignment.NudgeName">
            <summary>
            The name of the nudge
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeAssignments.SetCustomNudgeAssignment.StartTime">
            <summary>
            The start time of the assignment
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeAssignments.SetCustomNudgeAssignment.EndTime">
            <summary>
            The end time of the assignment
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeAssignments.SetCustomNudgeAssignment.CmdletName">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeAssignments.SetCustomNudgeAssignment.UrlStem">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeAssignments.SetCustomNudgeAssignment.InternalProcessRecord">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeFeatureCmdlet`2">
            <summary>
            Base class for all custom nudge feature cmdlets
            </summary>
            <typeparam name="TDomainObject">The api type</typeparam>
            <typeparam name="TCmdlet">The cmdlet type</typeparam>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeFeatureCmdlet`2.CustomNudgesEndpoint">
            <summary>
            The /CustomNudges/ tenant endpoint
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeFeatureCmdlet`2.CustomNudgeSettingsEndpoint">
            <summary>
            The /CustomNudges/ tenant endpoint
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeFeatureCmdlet`2.CustomNudgeAssignmentsEndpoint">
            <summary>
            The /CustomNudgeAssignments tenant endpoint
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeFeatureCmdlet`2.CustomNudgesByNameEndpoint(System.String)">
            <summary>
            The /CustomNudges('name')/ tenant endpoint
            </summary>
            <param name="name">Name of the nudge to target</param>
            <returns>The full endpoint</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeFeatureCmdlet`2.CustomNudgeAssignmentsByBothKeysEndpoint(System.String,System.String)">
            <summary>
            The /CustomNudgeAssignments(AssigneeId='assigneeId',NudgeName='nudgeName')/ tenant endpoint
            </summary>
            <param name="assigneeId">User to target</param>
            <param name="nudgeName">Nudge to target</param>
            <returns>The full endpoint</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeFeatureCmdlet`2.CustomNudgeAssignmentsByAssigneeIdEndpoint(System.String)">
            <summary>
            The /CustomNudgeAssignments(AssigneeId='assigneeId',NudgeName='nudgeName')/ tenant endpoint
            </summary>
            <param name="assigneeId">User to target</param>
            <returns>The full endpoint</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeFeatureCmdlet`2.CustomNudgeAssignmentsByNudgeNameEndpoint(System.String)">
            <summary>
            The /CustomNudgeAssignments(AssigneeId='assigneeId',NudgeName='nudgeName')/ tenant endpoint
            </summary>
            <param name="nudgeName">User to target</param>
            <returns>The full endpoint</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeFeatureCmdlet`2.SetStartTimeEndTimeOfAssignment(Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeAssignments.CustomNudgeAssignment,System.String,System.String)">
            <summary>
            Sets StartTime, EndTime properties of the assignment if they are present and valid
            </summary>
            <param name="assignment">The assignment</param>
            <param name="startTime">The start time</param>
            <param name="endTime">The end time</param>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeSettings.CustomNudgeSettings">
            <summary>
            Custom nudge settings data type
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeSettings.CustomNudgeSettings.Enabled">
            <summary>
            Whether or not admin has enabled custom nudges for their tenant.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeSettings.CustomNudgeSettings.Title">
            <summary>
            Displayed title of section.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeSettings.GetCustomNudgeSettings">
            <summary>
            Gets the custom nudge settings for a tenant
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeSettings.GetCustomNudgeSettings.CmdletName">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeSettings.GetCustomNudgeSettings.UrlStem">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeSettings.GetCustomNudgeSettings.InternalBeginProcessing">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeSettings.GetCustomNudgeSettings.InternalEndProcessing">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeSettings.GetCustomNudgeSettings.InternalProcessRecord">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeSettings.SetCustomNudgeSettings">
            <summary>
            Patches the custom nudge settings
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeSettings.SetCustomNudgeSettings.Enabled">
            <summary>
            Whether custom nudge feature is enabled for tenant
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeSettings.SetCustomNudgeSettings.Title">
            <summary>
            Title of custom nudge section
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeSettings.SetCustomNudgeSettings.CmdletName">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeSettings.SetCustomNudgeSettings.UrlStem">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeSettings.SetCustomNudgeSettings.InternalBeginProcessing">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeSettings.SetCustomNudgeSettings.InternalEndProcessing">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudgeSettings.SetCustomNudgeSettings.InternalProcessRecord">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudges.CustomNudge">
            <summary>
            Custom nudge data type
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudges.CustomNudge.Name">
            <summary>
            The unique name of the nudge.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudges.CustomNudge.From">
            <summary>
            Who sent the nudge.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudges.CustomNudge.Title">
            <summary>
            The displayed title of the nudge.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudges.CustomNudge.Content">
            <summary>
            The displayed body of the nudge.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudges.CustomNudgeList">
            <summary>
            List of custom nudges
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudges.CustomNudgeList.Value">
            <summary>
            The value
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudges.GetCustomNudge">
            <summary>
            Gets a custom nudge by name or all custom nudges
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudges.GetCustomNudge.urlStem">
            <summary>
            Private backing field for UrlStem
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudges.GetCustomNudge.Name">
            <summary>
            The name property
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudges.GetCustomNudge.CmdletName">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudges.GetCustomNudge.UrlStem">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudges.GetCustomNudge.InternalProcessRecord">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudges.NewCustomNudge">
            <summary>
            Implements New-CustomNudge cmdlet
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudges.NewCustomNudge.Name">
            <summary>
            Name of the nudge.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudges.NewCustomNudge.From">
            <summary>
            Who sent the nudge.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudges.NewCustomNudge.Title">
            <summary>
            The displayed title of the nudge.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudges.NewCustomNudge.Content">
            <summary>
            The displayed body of the nudge.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudges.NewCustomNudge.CmdletName">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudges.NewCustomNudge.UrlStem">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudges.NewCustomNudge.InternalProcessRecord">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudges.RemoveCustomNudge">
            <summary>
            Removes a custom nudge by name
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudges.RemoveCustomNudge.Name">
            <summary>
            Name of the nudge.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudges.RemoveCustomNudge.CmdletName">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudges.RemoveCustomNudge.UrlStem">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudges.RemoveCustomNudge.InternalProcessRecord">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudges.SetCustomNudge">
            <summary>
            Patches a custom nudge by name
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudges.SetCustomNudge.Name">
            <summary>
            Name of the nudge.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudges.SetCustomNudge.From">
            <summary>
            Who sent the nudge.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudges.SetCustomNudge.Title">
            <summary>
            The displayed title of the nudge.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudges.SetCustomNudge.Content">
            <summary>
            The displayed body of the nudge.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudges.SetCustomNudge.CmdletName">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudges.SetCustomNudge.UrlStem">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.CustomNudgesFeature.CustomNudges.SetCustomNudge.InternalProcessRecord">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Delegation.AddDelegatedRoleResult">
            <summary>
            Represents the possible result from the add delegated role operation.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.Delegation.AddDelegatedRoleResult.Added">
            <summary>
            Delegated role record added successfully.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.Delegation.AddDelegatedRoleResult.MissingLicense">
            <summary>
            Delegate doesn't have a valid license.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.Delegation.AddDelegatedRoleResult.NotProcessed">
            <summary>
            The delegated role record already exists.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.Delegation.AddDelegatedRoleResult.InvalidDelegator">
            <summary>
            Group manager delegator does not have the desired role or its team size is not met.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Delegation.AddVivaOrgInsightsDelegatedRole">
            <summary>
            This class implements "Add-DelegatedRole" Cmdlet
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Delegation.AddVivaOrgInsightsDelegatedRole.Delegate">
            <summary>
            Gets or sets the delegate user id.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Delegation.AddVivaOrgInsightsDelegatedRole.Delegator">
            <summary>
            Gets or sets the delegator user id.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Delegation.AddVivaOrgInsightsDelegatedRole.CmdletName">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Delegation.AddVivaOrgInsightsDelegatedRole.GetDisplayMessage(Microsoft.Exchange.Management.RestApiClient.Delegation.AddDelegatedRoleResult)">
            <summary>
            Gets the corresponding message by the result.
            </summary>
            <param name="result">The add delegated role result.</param>
            <returns>The message to display.</returns>
            <exception cref="T:Microsoft.Exchange.Management.RestApiClient.RestClientException">Throws when the result is unexpected.</exception>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Delegation.AddVivaOrgInsightsDelegatedRole.ConvertToAddDelegatedRoleResult(System.String)">
            <summary>
            Converts string value to AddDelegatedRole result.
            </summary>
            <param name="rawStringValue">The string value.</param>
            <returns>The enum AddDelegatedRoleResult.</returns>
            <exception cref="T:Microsoft.Exchange.Management.RestApiClient.RestClientException">Throws when converting to enum fails.</exception>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Delegation.AddVivaOrgInsightsDelegatedRole.InternalProcessRecord">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Delegation.Cmdlets.GetVivaOrgInsightsDelegatedRole">
            <summary>
            This class implements "Get-VivaOrgInsightsDelegatedRole" Cmdlet.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Delegation.Cmdlets.GetVivaOrgInsightsDelegatedRole.Delegator">
            <summary>
            Gets or sets the delegator user id.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Delegation.Cmdlets.GetVivaOrgInsightsDelegatedRole.CmdletName">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Delegation.Cmdlets.GetVivaOrgInsightsDelegatedRole.UrlStem">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Delegation.Cmdlets.GetVivaOrgInsightsDelegatedRole.InternalProcessRecord">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Delegation.Cmdlets.RemoveVivaOrgInsightsDelegatedRole">
            <summary>
            This class implements "Remove-DelegatedRole" Cmdlet.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Delegation.Cmdlets.RemoveVivaOrgInsightsDelegatedRole.Delegate">
            <summary>
            Gets or sets the delegate user id.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Delegation.Cmdlets.RemoveVivaOrgInsightsDelegatedRole.Delegator">
            <summary>
            Gets or sets the delegator user id.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Delegation.Cmdlets.RemoveVivaOrgInsightsDelegatedRole.CmdletName">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Delegation.Cmdlets.RemoveVivaOrgInsightsDelegatedRole.UrlStem">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Delegation.Cmdlets.RemoveVivaOrgInsightsDelegatedRole.InternalProcessRecord">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Delegation.AddDelegatesResponse">
            <summary>
            Represents the response of add-delegates-role request.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Delegation.AddDelegatesResponse.AddResults">
            <summary>
            Gets or sets the result of add-delegates. The dictionary is delegate user id to status.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Delegation.AddDelegatesResponse.AddedDelegatedRoles">
            <summary>
            Gets or sets the delegated roles.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Delegation.DelegatedRole">
            <summary>
            Represents the delegated role.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Delegation.DelegatedRole.Id">
            <summary>
            Gets or sets the id of the role.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Delegation.DelegatedRole.Role">
            <summary>
            Gets or sets the role.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Delegation.DelegatedRole.AssignedTo">
            <summary>
            Gets or sets the assigned to user object id.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Delegation.DelegatedRole.DelegatedFrom">
            <summary>
            Gets or sets the delegated from user object id.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Delegation.DelegatedRole.ExpiresOn">
            <summary>
            Gets or sets expiry time for delegated role.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Delegation.DelegatedRoleDisplay">
            <summary>
            Represents the delegated role to display.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Delegation.DelegatedRoleDisplay.Delegate">
            <summary>
            Gets or sets the assigned to user object id.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Delegation.DelegatedRoleDisplay.Delegator">
            <summary>
            Gets or sets the delegated from user object id.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Delegation.DelegatedRolesList">
            <summary>
            Represents the list of delegated roles
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Delegation.DelegatedRolesList.DelegatedRoles">
            <summary>
            Gets or sets the list of delegated roles.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Delegation.DelegatedRolesListDisplay">
            <summary>
            Represents the list of delegated roles to display
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Delegation.DelegatedRolesListDisplay.DelegatedRoles">
            <summary>
            Gets or sets the list of delegated roles.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Delegation.LeaderDelegationCmdlet`2">
            <summary>
            The base class for client side leader delegation cmdlets implemented WeveAdminCmdlet
            </summary>
            <typeparam name="TDomainObject">The api type.</typeparam>
            <typeparam name="TCmdlet">The cmdlet type.</typeparam>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.Delegation.LeaderDelegationCmdlet`2.GroupManagerRole">
            <summary>
            The string const of group manager role.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.Delegation.LeaderDelegationCmdlet`2.KnownErrorCodeToMessage">
            <summary>
            The dictionary to map message from the http status code.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Delegation.LeaderDelegationCmdlet`2.UrlStem">
            <summary>
            The delegated roles url stem. 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Delegation.LeaderDelegationCmdlet`2.RemoveDelegatedRoleEndpoint">
            <summary>
            The delegated roles url stem for remove delegatedd role.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Delegation.LeaderDelegationCmdlet`2.GetDelegatedRoleEndpoint">
            <summary>
            The delegated roles url stem for get delegatedd role.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.Delegation.LeaderDelegationCmdlet`2.ForbiddenErrorMessage">
            <summary>
            The message displayed for users when there is Forbidden error occurred.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Delegation.LeaderDelegationCmdlet`2.GetDelegatedRolesListDisplay(Microsoft.Exchange.Management.RestApiClient.Delegation.DelegatedRolesList)">
            <summary>
            The helper function to convert delegated role list to its display model.
            </summary>
            <param name="delegatedRolesList">The delegated role list.</param>
            <returns>The delegated roles list for display.</returns>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Delegation.Models.RemoveDelegatedRoleRequest">
            <summary>
            Represents the delegated role to remove.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Delegation.Models.RemoveDelegatedRoleRequest.DelegatedRole">
            <summary>
            Gets or sets the delegated role
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.EmailValidator">
            <summary>
            Validates given email is in valid format or not. 
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.EmailValidator.EmailAddressRegexPattern">
            <summary>
            Regex for validating whether given string is in email format or not.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.EmailValidator.IsValidEmail(System.String)">
            <summary>
            Checks whether given string is in email format.
            </summary>
            <param name="str">Input string.</param>
            <returns>true if given string is in email format.</returns>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.ExchangeEnvironment">
            <summary>
            Environment Enums for various deployments
               Worldwide : Worldwide + GCC (Government Community Cloud)
               O365GermanyCloud : Office 365 Germany
               O365USGovGCCHigh : Office 365 U.S. Government GCC High
               O365USGovDoD : Office 365 U.S. Government Department of Defense
               O365China : Office 365 operated by 21 Vianet
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.EnvironmentUtils">
            <summary>
            Utility file for evaluating the Uris specific to different exchange environments.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.EnvironmentUtils.GetPowerShellConnectionUri(Microsoft.Exchange.Management.RestApiClient.ExchangeEnvironment)">
            <summary>
            Returns the Connection Uri for the given exchangeEnvironment
            </summary>
            <param name="exchangeEnvironment">Exchange Environment</param>
            <returns>Connection Uri for the current Environment</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.EnvironmentUtils.GetAzureAuthEndpointUri(Microsoft.Exchange.Management.RestApiClient.ExchangeEnvironment)">
            <summary>
            Returns the Azure Authentication Uri for the given exchangeEnvironment
            </summary>
            <param name="exchangeEnvironment">Exchange Environment</param>
            <returns>Azure Authentication Uri for the current Environment</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.EnvironmentUtils.GetConnectionUri(Microsoft.Exchange.Management.RestApiClient.ExchangeEnvironment)">
            <summary>
            Returns the  for the given exchangeEnvironment
            </summary>
            <param name="exchangeEnvironment">Exchange Environment</param>
            <returns>Azure Authentication Uri for the current Environment</returns>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.ExoCasMailbox">
            <summary>
                 CasMailbox presentation object class.
                 This class represents how we store CasMailbox in the client side
                 This is what will be written into the pipeline when Get-CasMailbox is run
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ExoCasMailbox.#ctor(Microsoft.Exchange.Management.AdminApiProvider.CasMailbox)">
            <summary>
            CasMailbox presentation object constructor
            </summary>
            <param name="casMailbox">CasMailbox param</param>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoCasMailbox.Identity">
            <summary>
            Identity field 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoCasMailbox.ActiveSyncEnabled">
            <summary>
            ActiveSyncEnabled field 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoCasMailbox.ActiveSyncMailboxPolicy">
            <summary>
            ActiveSyncMailboxPolicy field 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoCasMailbox.ActiveSyncAllowedDeviceIDs">
            <summary>
            ActiveSyncAllowedDeviceIDs field 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoCasMailbox.ActiveSyncBlockedDeviceIDs">
            <summary>
            ActiveSyncBlockedDeviceIDs field 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoCasMailbox.OWAforDevicesEnabled">
            <summary>
            OWAforDevicesEnabled field 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoCasMailbox.WhenChanged">
            <summary>
            WhenChanged field 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoCasMailbox.MAPIEnabled">
            <summary>
            MAPIEnabled field 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoCasMailbox.ImapEnabled">
            <summary>
            ImapEnabled field 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoCasMailbox.OWAEnabled">
            <summary>
            OWAEnabled field 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoCasMailbox.PopEnabled">
            <summary>
            PopEnabled field 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoCasMailbox.SmtpClientAuthenticationDisabled">
            <summary>
            SmtpClientAuthenticationDisabled field 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoCasMailbox.EwsEnabled">
            <summary>
            EwsEnabled field 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoCasMailbox.EwsAllowOutlook">
            <summary>
            EwsAllowOutlook field 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoCasMailbox.EwsAllowMacOutlook">
            <summary>
            EwsAllowMacOutlook field 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoCasMailbox.EwsAllowEntourage">
            <summary>
            EwsAllowEntourage field 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoCasMailbox.EwsApplicationAccessPolicy">
            <summary>
            EwsApplicationAccessPolicy field 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoCasMailbox.EwsAllowList">
            <summary>
            EwsAllowList field 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoCasMailbox.EwsBlockList">
            <summary>
            EwsBlockList field 
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.ExoMailbox">
            <summary>
            Definition of ExoMailbox.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.#ctor(Microsoft.Exchange.Management.AdminApiProvider.Mailbox)">
            <summary>
            ExoMailbox Copy Constructor
            </summary>
            <param name="mailbox">mailbox param</param>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.ExternalDirectoryObjectId">
            <summary>
            the ExternalDirectoryObjectId key.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.Database">
            <summary>
            the Database where mailbox is mounted.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.DatabaseGuid">
            <summary>
            the Database where mailbox is mounted.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.MailboxProvisioningConstraint">
            <summary>
            the Mailbox Provisioning Constraint.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.IsMonitoringMailbox">
            <summary>
            the IsMonitoringMailbox
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.MailboxRegion">
            <summary>
            The mailbox region that the mailbox belongs to.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.MailboxRegionLastUpdateTime">
            <summary>
            When the MailboxRegion was last changed.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.MessageRecallProcessingEnabled">
            <summary>
            Whether the recipient automatically processes Recall Messages
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.MessageCopyForSentAsEnabled">
            <summary>
            Gets the message copy for sent as items is enabled.
            </summary>
            <value>
            True if message sent as need to be copied to the shared mailbox.
            </value>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.MessageCopyForSendOnBehalfEnabled">
            <summary>
            Gets a value indicating whether messages copy for sent on behalf items is enabled.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.MailboxProvisioningPreferences">
            <summary>
            The MailboxProvisioningPreferences.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.UseDatabaseRetentionDefaults">
            <summary>
            The UseDatabaseRetentionDefaults.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.RetainDeletedItemsUntilBackup">
            <summary>
            The RetainDeletedItemsUntilBackup.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.DeliverToMailboxAndForward">
            <summary>
            the DeliverToMailboxAndForward
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.IsExcludedFromServingHierarchy">
            <summary>
            the IsExcludedFromServingHierarchy
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.IsHierarchyReady">
            <summary>
            the IsHierarchyReady
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.IsHierarchySyncEnabled">
            <summary>
            the IsHierarchySyncEnabled
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.HasSnackyAppData">
            <summary>
            the HasSnackyAppData
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.LitigationHoldEnabled">
            <summary>
            the LitigationHoldEnabled 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.SingleItemRecoveryEnabled">
            <summary>
            the SingleItemRecoveryEnabled 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.RetentionHoldEnabled">
            <summary>
            the RetentionHoldEnabled 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.EndDateForRetentionHold">
            <summary>
            the EndDateForRetentionHold 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.StartDateForRetentionHold">
            <summary>
            the StartDateForRetentionHold 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.RetentionComment">
            <summary>
            the RetentionComment 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.RetentionUrl">
            <summary>
            the RetentionUrl 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.LitigationHoldDate">
            <summary>
            the LitigationHoldDate 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.LitigationHoldOwner">
            <summary>
            the LitigationHoldOwner 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.ElcProcessingDisabled">
            <summary>
            the ElcProcessingDisabled 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.ComplianceTagHoldApplied">
            <summary>
            the ComplianceTagHoldApplied 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.WasInactiveMailbox">
            <summary>
            the WasInactiveMailbox 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.DelayHoldApplied">
            <summary>
            the DelayHoldApplied 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.InactiveMailboxRetireTime">
            <summary>
            the InactiveMailboxRetireTime 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.OrphanSoftDeleteTrackingTime">
            <summary>
            the OrphanSoftDeleteTrackingTime 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.LitigationHoldDuration">
            <summary>
            the LitigationHoldDuration 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.ManagedFolderMailboxPolicy">
            <summary>
            the ManagedFolderMailboxPolicy 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.RetentionPolicy">
            <summary>
            the RetentionPolicy 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.AddressBookPolicy">
            <summary>
            the AddressBookPolicy 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.CalendarRepairDisabled">
            <summary>
            the CalendarRepairDisabled 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.ExchangeGuid">
            <summary>
            the ExchangeGuid 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.MailboxContainerGuid">
            <summary>
            the MailboxContainerGuid 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.UnifiedMailbox">
            <summary>
            the UnifiedMailbox 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.MailboxLocations">
            <summary>
            the MailboxLocations 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.AggregatedMailboxGuids">
            <summary>
            the AggregatedMailboxGuids 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.ExchangeSecurityDescriptor">
            <summary>
            the ExchangeSecurityDescriptor 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.ExchangeUserAccountControl">
            <summary>
            the ExchangeUserAccountControl 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.ExoExchangeSecurityDescriptor">
            <summary>
            the ExoExchangeSecurityDescriptor 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.MessageTrackingReadStatusEnabled">
            <summary>
            the MessageTrackingReadStatusEnabled 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.ExternalOofOptions">
            <summary>
            the ExternalOofOptions
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.ForwardingAddress">
            <summary>
            the ForwardingAddress 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.ForwardingSmtpAddress">
            <summary>
            the ForwardingSmtpAddress
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.RetainDeletedItemsFor">
            <summary>
            the RetainDeletedItemsFor
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.IsMailboxEnabled">
            <summary>
            the IsMailboxEnabled
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.Languages">
            <summary>
            the Languages
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.OfflineAddressBook">
            <summary>
            the OfflineAddressBook
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.ProhibitSendQuota">
            <summary>
            the ProhibitSendQuota
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.ProhibitSendReceiveQuota">
            <summary>
            the ProhibitSendReceiveQuota 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.RecoverableItemsQuota">
            <summary>
            the RecoverableItemsQuota 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.RecoverableItemsWarningQuota">
            <summary>
            the RecoverableItemsWarningQuota 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.CalendarLoggingQuota">
            <summary>
            the CalendarLoggingQuota 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.DowngradeHighPriorityMessagesEnabled">
            <summary>
            the DowngradeHighPriorityMessagesEnabled 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.ProtocolSettings">
            <summary>
            the ProtocolSettings 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.RecipientLimits">
            <summary>
            the RecipientLimits 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.ImListMigrationCompleted">
            <summary>
            the ImListMigrationCompleted 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.SiloName">
            <summary>
            the SiloName 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.IsResource">
            <summary>
            the IsResource
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.IsLinked">
            <summary>
            the IsLinked
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.IsShared">
            <summary>
            the IsShared
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.IsRootPublicFolderMailbox">
            <summary>
            the IsRootPublicFolderMailbox
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.LinkedMasterAccount">
            <summary>
            the LinkedMasterAccount
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.ResetPasswordOnNextLogon">
            <summary>
            the ResetPasswordOnNextLogon
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.ResourceCapacity">
            <summary>
            the ResourceCapacity
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.ResourceCustom">
            <summary>
            the ResourceCustom
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.ResourceType">
            <summary>
            the ResourceType
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.RoomMailboxAccountEnabled">
            <summary>
            the RoomMailboxAccountEnabled
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.SamAccountName">
            <summary>
            the SamAccountName
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.SCLDeleteThreshold">
            <summary>
            the SCLDeleteThreshold
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.SCLDeleteEnabled">
            <summary>
            the SCLDeleteEnabled
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.SCLRejectThreshold">
            <summary>
            the SCLRejectThreshold
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.SCLRejectEnabled">
            <summary>
            the SCLRejectEnabled
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.SCLQuarantineThreshold">
            <summary>
            the SCLQuarantineThreshold
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.SCLQuarantineEnabled">
            <summary>
            the SCLQuarantineEnabled
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.SCLJunkThreshold">
            <summary>
            the SCLJunkThreshold
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.SCLJunkEnabled">
            <summary>
            the SCLJunkEnabled
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.AntispamBypassEnabled">
            <summary>
            the AntispamBypassEnabled
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.ServerLegacyDN">
            <summary>
            the ServerLegacyDN
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.UseDatabaseQuotaDefaults">
            <summary>
            the UseDatabaseQuotaDefaults
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.IssueWarningQuota">
            <summary>
            the IssueWarningQuota  
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.RulesQuota">
            <summary>
            the RulesQuota  
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.Office">
            <summary>
            the Office
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.UserPrincipalName">
            <summary>
            the UserPrincipalName
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.UMEnabled">
            <summary>
            the UMEnabled
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.MaxSafeSenders">
            <summary>
            the MaxSafeSenders
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.MaxBlockedSenders">
            <summary>
            the MaxBlockedSenders
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.NetID">
            <summary>
            the NetID
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.ReconciliationId">
            <summary>
            the ReconciliationId
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.WindowsLiveID">
            <summary>
            the WindowsLiveID
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.MicrosoftOnlineServicesID">
            <summary>
            the MicrosoftOnlineServicesID
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.ThrottlingPolicy">
            <summary>
            the ThrottlingPolicy
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.RoleAssignmentPolicy">
            <summary>
            the RoleAssignmentPolicy
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.DefaultPublicFolderMailbox">
            <summary>
            the DefaultPublicFolderMailbox
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.EffectivePublicFolderMailbox">
            <summary>
            the EffectivePublicFolderMailbox
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.SharingPolicy">
            <summary>
            the SharingPolicy
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.RemoteAccountPolicy">
            <summary>
            the RemoteAccountPolicy
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.MailboxPlan">
            <summary>
            the MailboxPlan
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.ArchiveDatabase">
            <summary>
            the ArchiveDatabase
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.ArchiveDatabaseGuid">
            <summary>
            the ArchiveDatabase
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.ArchiveGuid">
            <summary>
            the ArchiveGuid
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.ArchiveName">
            <summary>
            the ArchiveName
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.JournalArchiveAddress">
            <summary>
            the JournalArchiveAddress
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.ArchiveQuota">
            <summary>
            the ArchiveQuota
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.ArchiveWarningQuota">
            <summary>
            the ArchiveWarningQuota
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.ArchiveDomain">
            <summary>
            the ArchiveDomain
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.ArchiveStatus">
            <summary>
            the ArchiveStatus
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.ArchiveState">
            <summary>
            the ArchiveState
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.AutoExpandingArchiveEnabled">
            <summary>
            the AutoExpandingArchiveEnabled
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.DisabledMailboxLocations">
            <summary>
            the DisabledMailboxLocations
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.RemoteRecipientType">
            <summary>
            the RemoteRecipientType
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.DisabledArchiveDatabase">
            <summary>
            the DisabledArchiveDatabase
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.DisabledArchiveGuid">
            <summary>
            the DisabledArchiveGuid
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.QueryBaseDN">
            <summary>
            the QueryBaseDN
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.QueryBaseDNRestrictionEnabled">
            <summary>
            the QueryBaseDNRestrictionEnabled
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.MailboxMoveTargetMDB">
            <summary>
            the MailboxMoveTargetMDB
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.MailboxMoveSourceMDB">
            <summary>
            the MailboxMoveSourceMDB
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.MailboxMoveFlags">
            <summary>
            the MailboxMoveFlags
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.MailboxMoveRemoteHostName">
            <summary>
            the MailboxMoveRemoteHostName
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.MailboxMoveBatchName">
            <summary>
            the MailboxMoveBatchName
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.MailboxMoveStatus">
            <summary>
            the MailboxMoveStatus
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.MailboxRelease">
            <summary>
            the MailboxRelease
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.ArchiveRelease">
            <summary>
            the ArchiveRelease
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.IsPersonToPersonTextMessagingEnabled">
            <summary>
            the IsPersonToPersonTextMessagingEnabled
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.IsMachineToPersonTextMessagingEnabled">
            <summary>
            the IsMachineToPersonTextMessagingEnabled
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.UserSMimeCertificate">
            <summary>
            the UserSMimeCertificate
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.UserCertificate">
            <summary>
            the UserCertificate
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.CalendarVersionStoreDisabled">
            <summary>
            the CalendarVersionStoreDisabled
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.ImmutableId">
            <summary>
            the ImmutableId
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.PersistedCapabilities">
            <summary>
            the PersistedCapabilities
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.SKUAssigned">
            <summary>
            the SKUAssigned
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.AuditEnabled">
            <summary>
            the AuditEnabled
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.AuditLogAgeLimit">
            <summary>
            the AuditLogAgeLimit
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.AuditAdmin">
            <summary>
            the AuditAdmin
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.AuditDelegate">
            <summary>
            the AuditDelegate
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.AuditOwner">
            <summary>
            the AuditOwner
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.DefaultAuditSet">
            <summary>
            the DefaultAuditSet
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.WhenMailboxCreated">
            <summary>
            the WhenMailboxCreated
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.SourceAnchor">
            <summary>
            the SourceAnchor
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.UsageLocation">
            <summary>
            the UsageLocation
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.IsSoftDeletedByRemove">
            <summary>
            the IsSoftDeletedByRemove
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.IsSoftDeletedByDisable">
            <summary>
            the IsSoftDeletedByDisable
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.IsInactiveMailbox">
            <summary>
            the IsInactiveMailbox
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.IncludeInGarbageCollection">
            <summary>
            the IncludeInGarbageCollection
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.WhenSoftDeleted">
            <summary>
            the WhenSoftDeleted
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.InPlaceHolds">
            <summary>
            the InPlaceHolds
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.GeneratedOfflineAddressBooks">
            <summary>
            the GeneratedOfflineAddressBooks
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.AccountDisabled">
            <summary>
            the AccountDisabled
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.StsRefreshTokensValidFrom">
            <summary>
            the StsRefreshTokensValidFrom
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.NonCompliantDevices">
            <summary>
            the NonCompliantDevices
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.DataEncryptionPolicy">
            <summary>
            the DataEncryptionPolicy
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.HasPicture">
            <summary>
            the HasPicture
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.HasSpokenName">
            <summary>
            the HasSpokenName
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.IsDirSynced">
            <summary>
            IsDirSynced property
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.AcceptMessagesOnlyFrom">
            <summary>
            The AcceptMessagesOnlyFrom property
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.AcceptMessagesOnlyFromDLMembers">
            <summary>
            The AcceptMessagesOnlyFromDLMembers property
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.AcceptMessagesOnlyFromSendersOrMembers">
            <summary>
            The AcceptMessagesOnlyFromSendersOrMembers property
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.AddressListMembership">
            <summary>
            The AddressListMembership property
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.AdministrativeUnits">
            <summary>
            The AdministrativeUnits property
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.Alias">
            <summary>
            the Alias
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.ArbitrationMailbox">
            <summary>
            the ArbitrationMailbox
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.BypassModerationFromSendersOrMembers">
            <summary>
            the BypassModerationFromSendersOrMembers
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.OrganizationalUnit">
            <summary>
            the OrganizationalUnit
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.CustomAttribute1">
            <summary>
            the CustomAttribute1
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.CustomAttribute10">
            <summary>
            the CustomAttribute10
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.CustomAttribute11">
            <summary>
            the CustomAttribute11
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.CustomAttribute12">
            <summary>
            the CustomAttribute12
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.CustomAttribute13">
            <summary>
            the CustomAttribute13
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.CustomAttribute14">
            <summary>
            the CustomAttribute14
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.CustomAttribute15">
            <summary>
            the CustomAttribute15
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.CustomAttribute2">
            <summary>
            the CustomAttribute2
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.CustomAttribute3">
            <summary>
            the CustomAttribute3
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.CustomAttribute4">
            <summary>
            the CustomAttribute4
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.CustomAttribute5">
            <summary>
            the CustomAttribute5
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.CustomAttribute6">
            <summary>
            the CustomAttribute6
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.CustomAttribute7">
            <summary>
            the CustomAttribute7
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.CustomAttribute8">
            <summary>
            the CustomAttribute8
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.CustomAttribute9">
            <summary>
            the CustomAttribute9
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.ExtensionCustomAttribute1">
            <summary>
            the ExtensionCustomAttribute1
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.ExtensionCustomAttribute2">
            <summary>
            the ExtensionCustomAttribute2
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.ExtensionCustomAttribute3">
            <summary>
            the ExtensionCustomAttribute3
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.ExtensionCustomAttribute4">
            <summary>
            the ExtensionCustomAttribute4
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.ExtensionCustomAttribute5">
            <summary>
            the ExtensionCustomAttribute5
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.DisplayName">
            <summary>
            the DisplayName
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.EmailAddresses">
            <summary>
            the EmailAddresses
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.GrantSendOnBehalfTo">
            <summary>
            the GrantSendOnBehalfTo
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.HiddenFromAddressListsEnabled">
            <summary>
            the HiddenFromAddressListsEnabled
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.LastExchangeChangedTime">
            <summary>
            the LastExchangeChangedTime
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.LegacyExchangeDN">
            <summary>
            the LegacyExchangeDN
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.MaxSendSize">
            <summary>
            the MaxSendSize 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.MaxReceiveSize">
            <summary>
            the MaxReceiveSize
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.ModeratedBy">
            <summary>
            the ModeratedBy
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.ModerationEnabled">
            <summary>
            the ModerationEnabled
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.PoliciesIncluded">
            <summary>
            the PoliciesIncluded
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.PoliciesExcluded">
            <summary>
            the PoliciesExcluded
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.EmailAddressPolicyEnabled">
            <summary>
            the EmailAddressPolicyEnabled
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.PrimarySmtpAddress">
            <summary>
            the PrimarySmtpAddress
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.RecipientType">
            <summary>
            the RecipientType
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.RecipientTypeDetails">
            <summary>
            the RecipientTypeDetails
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.RejectMessagesFrom">
            <summary>
            the RejectMessagesFrom
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.RejectMessagesFromDLMembers">
            <summary>
            the RejectMessagesFromDLMembers
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.RejectMessagesFromSendersOrMembers">
            <summary>
            the RejectMessagesFromSendersOrMembers
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.RequireSenderAuthenticationEnabled">
            <summary>
            the RequireSenderAuthenticationEnabled
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.SimpleDisplayName">
            <summary>
            the SimpleDisplayName
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.SendModerationNotifications">
            <summary>
            the SendModerationNotifications
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.UMDtmfMap">
            <summary>
            the UMDtmfMap
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.WindowsEmailAddress">
            <summary>
            the WindowsEmailAddress
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.MailTip">
            <summary>
            the MailTip 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.MailTipTranslations">
            <summary>
            the MailTipTranslations 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.Identity">
            <summary>
            the Identity Filed. 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.Id">
            <summary>
            the Id Filed. 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.ExchangeVersion">
            <summary>
            the ExchangeVersion
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.Name">
            <summary>
            the Name
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.DistinguishedName">
            <summary>
            the DistinguishedName
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.ObjectCategory">
            <summary>
            Object category.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.ObjectClass">
            <summary>
            Object class.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.WhenChanged">
            <summary>
            When changed.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.WhenCreated">
            <summary>
            Object creation time.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.WhenChangedUTC">
            <summary>
            When changed in utc.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.WhenCreatedUTC">
            <summary>
            Object creation time in UTC.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.ExchangeObjectId">
            <summary>
            Exchange specific object id.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.OrganizationId">
            <summary>
            Organization id to which this object belongs to.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailbox.Guid">
            <summary>
            Guid of Mailbox.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.ExoMailboxFolderPermission">
            <summary>
            ExoMailboxFolderPermission class. Object of this class is created once we receive results from API.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ExoMailboxFolderPermission.#ctor(Microsoft.Exchange.Management.AdminApiProvider.MailboxFolderPermission)">
            <summary>
            ExoMailboxFolderPermission Object 
            </summary>
            <param name="mailboxFolderPermission"> ExoMailboxFolderPermission object from which will construct ExoMailboxFolderPermission object to write in pipeline</param>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailboxFolderPermission.Identity">
            <summary>
            this is the folder path ex. Inbox\Personal
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailboxFolderPermission.FolderName">
            <summary>
            string for the foldername. ex. Personal
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailboxFolderPermission.User">
            <summary>
            User whose permissions are fetched
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailboxFolderPermission.AccessRights">
            <summary>
            the accessrights a user has on this folder
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailboxFolderPermission.SharingPermissionFlags">
            <summary>
            the sharingpermissionflags
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.ExoMailboxFolderUserID">
            <summary>
            This a wrapper on top of MailboxFolderUserID to override ToString
            Implementation and hide ChangedProperties Field.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ExoMailboxFolderUserID.#ctor(Microsoft.Exchange.Management.AdminApiProvider.MailboxFolderUserID,System.String)">
            <summary>
            Copies all the properties of MailboxFolderUserID object to this
            Object
            </summary>
            <param name="userIdObject">
            MailboxFolderUser object Data returned from Service
            </param>
            <param name="userAsString">
            User string returned from Service
            </param>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailboxFolderUserID.DisplayName">
            <summary>
            DisplayName of User
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailboxFolderUserID.UserPrincipalName">
            <summary>
            UserprincipalName of User
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailboxFolderUserID.UserType">
            <summary>
            UserType of User
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ExoMailboxFolderUserID.ToString">
            <summary>
            Overriding ToString implementation so that Powershell
            Users will see the proper value instead of Object Name.
            </summary>
            <returns>
            UserPrincipalName or DisplayName whichever is not null
            </returns>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.ExoMailboxPermission">
            <summary>
            Definition of ExoMailboxPermission. This class is created as the MailboxPermission object returned from AdminApi is not identical in structure with RPS cmdlet output.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ExoMailboxPermission.#ctor(Microsoft.Exchange.Management.AdminApiProvider.MailboxPermission,Microsoft.Exchange.Management.AdminApiProvider.MailboxPermissionInfo)">
            <summary>
            ExoMailboxPermission Object.
            </summary>
            <param name="mailboxPermission"> MailboxPermission object from which will construct ExoMailboxPermission object to write in pipeline</param>
            <param name="permissionInfo"> MailboxPermissionInfo object that has one permission related information of mailbox user</param>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailboxPermission.Identity">
            <summary>
            Identity of the Mailbox
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailboxPermission.User">
            <summary>
            User of the Mailbox
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailboxPermission.AccessRights">
            <summary>
            the AccessRights is MailboxRights Enum
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailboxPermission.IsInherited">
            <summary>
            the IsInherited
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailboxPermission.Deny">
            <summary>
            the Deny. Denotes whether the permissions are denied to the user or not.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailboxPermission.InheritanceType">
            <summary>
            InheritanceType is ActiveDirectorySecurityInheritance enum
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.ExoMailboxPermissionOwnerInfo">
            <summary>
            Definition of ExoMailboxPermissionOwnerInfo.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ExoMailboxPermissionOwnerInfo.#ctor(Microsoft.Exchange.Management.AdminApiProvider.MailboxPermission)">
            <summary>
            ExoMailboxPermission Object 
            </summary>
            <param name="mailboxPermission"> MailboxPermission object from which will construct ExoMailboxPermissionOwnerInfo object to write in pipeline</param>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailboxPermissionOwnerInfo.Identity">
            <summary>
            Identity of the Mailbox
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoMailboxPermissionOwnerInfo.Owner">
            <summary>
            Owner of the Mailbox
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.ExoRecipientPermission">
            <summary>
            Definition of ExoRecipientPermission.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ExoRecipientPermission.#ctor(Microsoft.Exchange.Management.AdminApiProvider.RecipientPermission)">
            <summary>
            ExoRecipientPermission Object 
            </summary>
            <param name="recipientPermission"> RecipientPermission object from which will construct ExoRecipientPermission object to write in pipeline</param>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoRecipientPermission.Identity">
            <summary>
            Identity of the recipient
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoRecipientPermission.Trustee">
            <summary>
            Identity or friendly name of the trustee
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoRecipientPermission.AccessControlType">
            <summary>
            Access control type (Allow or Deny)
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoRecipientPermission.AccessRights">
            <summary>
            the AccessRights is RecipientAccessRight (SendAs)
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoRecipientPermission.IsInherited">
            <summary>
            the IsInherited
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.ExoRecipientPermission.InheritanceType">
            <summary>
            InheritanceType is ActiveDirectorySecurityInheritance enum
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.FilterConverter">
            <summary>
            Convert a powershell filter string to an OData filter node.
            Right now there are some limitation of this converter:
            1, Only support -eq / -ne / -lt / -le / -gt / -ge / -like operators
            2, Support parentheses
            3, for -like operator, only support one '*' in the right operand
            4, No validation about converted Odata filter is support by AdminApi or not
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.FilterConverter.ValidPowerShellFilterOperators">
            <summary>
            The supported operators and precedence.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.FilterConverter.OperandConversionStandardPowershellValueToODataMap">
            <summary>
             Map to handle special powershell values and corresponding ODATA values.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.FilterConverter.ConvertPowerShellFilter(System.String,System.Type)">
            <summary>
            Convert a powershell filter string to OData filter string
            </summary>
            <param name="powershellFilter">powershell filter</param>
            <param name="type">type param</param>
            <returns>OData filter string</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.FilterConverter.ReplaceSorroundingDoubleQuotesWithSingleQuotes(System.String)">
            <summary>
            Remove sorrounding double quotes with single quotes.
            </summary>
            <param name="operand">String value to be processed.</param>
            <returns>Returns the replaced string</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.FilterConverter.BuildNotFilter(System.Collections.Generic.Stack{System.String}@,System.Collections.Generic.Stack{System.String}@)">
            <summary>
            Handle -not cases
            </summary>
            <param name="operators">operators stack</param>
            <param name="operands">operands stack</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.FilterConverter.BuildSingleFilter(System.Collections.Generic.Stack{System.String}@,System.Collections.Generic.Stack{System.String}@,System.Collections.Generic.List{System.String})">
            <summary>
            Read one operator from operators and 2 operands from operands to build a new operand
            </summary>
            <param name="operators">stack of operators</param>
            <param name="operands">stack of operands</param>
            <param name="collectionProperties">List of collection properties</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.FilterConverter.BuildOtherFilter(System.String,System.Collections.Generic.Stack{System.String}@,System.Collections.Generic.List{System.String})">
            <summary>
            Build a new operand when the operator is not "-like"
            </summary>
            <param name="op">the operator</param>
            <param name="operands">stack of operands</param>
            <param name="collectionProperties">List of collection properties</param>
            <returns>string of filter consists of a operator</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.FilterConverter.EscapeStandardPowershellValues(System.String)">
            <summary>
            Handle any special powershell value to odata conversions. 
            </summary>
            <param name="operand">Operand in powershell filter.</param>
            <returns>Corresponding value in OData.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.FilterConverter.BuildLikeFilter(System.Collections.Generic.Stack{System.String}@,System.Collections.Generic.List{System.String},System.String)">
            <summary>
            Build a new operand when the operator is "-like", only support pattern like "a*b" / "*a" / "b*"
            It also handles -like and -notlike 
            Ex:  -Filter "A -notlike 'B*'"  =>   "not (startswith(A,'B'))"
                 -Filter "A -like 'B*'"     =>   "startswith(A, 'B')"
            </summary>
            <param name="operands">stack of operands</param>
            <param name="collectionProperties">collectionProperties param</param>
            <param name="operatorvalue">-like or -notlike operator</param>
            <returns>string of filter consists of a like operator</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.FilterConverter.HandleCollectionsForLike(System.Collections.Generic.List{System.String},System.String,System.String,System.String,System.String)">
            <summary>
            This method forms Filter construct for -like powershell filter
            and also handles the collectionProperties
            </summary>
            <param name="collectionProperties">stack of operands</param>
            <param name="property">Variable name</param>
            <param name="pattern">Filterable string for like or notlike</param>
            <param name="funcname">oData Function Name</param>
            <param name="operatorvalue">operator value is one of -like / -notlike</param>
            <returns>oData filter query for $filter</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.FilterConverter.HandleCollectionsForBinaryOperators(System.Collections.Generic.List{System.String},System.String,System.String,System.String)">
            <summary>
            This method handles collection properties for binary operators like
            eq, ne, gt, lt, ge, le. These are by default supported by oData so 
            there is no need for functions to handle them.
            like and notlike are handled separately using functions.
            </summary>
            <param name="collectionProperties">stack of operands</param>
            <param name="left">left side operand</param>
            <param name="right">right side operand</param>
            <param name="op">the operator</param>
            <returns>oData filter query</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.FilterConverter.HandleLikeWithOutStar(System.Collections.Generic.List{System.String},System.String,System.String,System.String)">
            <summary>
            This is a helper function to handle A -like / -notlike 'anystringwithoutstar'
            A -like 'anystringwithoutstar'    => A eq 'anystringwithoutstar'
            A -notlike 'anystringwithoutstar' => A ne 'anystringwithoutstar'
            </summary>
            <param name="collectionProperties">stack of operands</param>
            <param name="property">Variable name</param>
            <param name="pattern">Filterable string for like or notlike</param>
            <param name="operatorvalue">operator value is one of -like / -notlike</param>
            <returns>oData filter query for $filter</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.FilterConverter.ReadOperatorOrOperand(System.String,System.Int32@,System.String@)">
            <summary>
            Read operator or operand from the filter, if operand is starting with '\'', keep it.
            </summary>
            <param name="input">whole powershell filter string</param>
            <param name="start">start index to read an operator or operand (including) </param>
            <param name="output">the operator or operand</param>
            <returns>true means operator, false means operand</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.FilterConverter.ReadUntilSpecficChar(System.String,System.Char,System.Int32@)">
            <summary>
            Read a operator or operand until meeting a terminate char. Terminate char should only be ' ' or '\''
            If the terminate char is ' ', it would also stop reading when meeting a '( or ')'
            </summary>
            <param name="input">powershell filter string</param>
            <param name="terminateChar">terminate char</param>
            <param name="start">start index</param>
            <returns>an operator or operand</returns>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.GetExoCasMailbox">
            <summary>
            This class implements "Get-ExoCasMailbox" Cmdlet
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoCasMailbox.GuidConversionProperties">
            <summary>
            List of fields which need to be converted to Guid strong type.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoCasMailbox.DatetimeConversionProperties">
            <summary>
            List of Datetime Fields to be converted.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.GetExoCasMailbox.PropertySet">
            <summary>
            Specifies which property set to return
            Each enumerator contains a set of properties of CasMailbox
            By default, Minimum set is returned
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoCasMailbox.PropertySet.Minimum">
            <summary>
            Returns minimum set of properties
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoCasMailbox.PropertySet.All">
            <summary>
            Returns all properties
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoCasMailbox.PropertySet.ProtocolSettings">
            <summary>
            The ProtocolSettings switch returns the server names, TCP ports and encryption methods for the following settings:
            1. ExternalImapSettings
            2. InternalImapSettings
            3. ExternalPopSettings
            4. InternalPopSettings
            5. ExternalSmtpSettings
            6. InternalSmtpSettings
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoCasMailbox.PropertySet.Pop">
            <summary>
            Pop related properties.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoCasMailbox.PropertySet.Imap">
            <summary>
            Imap related properties.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoCasMailbox.PropertySet.ActiveSync">
            <summary>
            ActiveSync related properties.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoCasMailbox.PropertySet.Mapi">
            <summary>
            mapi related properties.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoCasMailbox.PropertySet.Ews">
            <summary>
            Ews related properties.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoCasMailbox.asyncJobController">
            <summary>
            Async job controller
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoCasMailbox.Filter">
            <summary>
            Filter to apply
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoCasMailbox.Anr">
            <summary>
            Read Anr value for cmdlet.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoCasMailbox.OrganizationalUnit">
            <summary>
            Organizational unit in AD. This cmdlet will fetch all mailbox data under this organization. This organization must be with in admin's root organization. 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoCasMailbox.Properties">
            <summary>
            Here, you can specify exactly what properties you need.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoCasMailbox.PropertySets">
            <summary>
            Here, you can specify which property set you want.
            A property set is a set of CasMailbox properties
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoCasMailbox.ProtocolSettings">
            <summary>
            Get protocol setting related fields.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoCasMailbox.ActiveSyncDebugLogging">
            <summary>
            Unsupported Paramter
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoCasMailbox.ReadIsOptimizedForAccessibility">
            <summary>
            Unsupported Paramter
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoCasMailbox.IgnoreDefaultScope">
            <summary>
            Unsupported Paramter
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoCasMailbox.GetImapProtocolLog">
            <summary>
            Unsupported Paramter
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoCasMailbox.GetPopProtocolLog">
            <summary>
            Unsupported Paramter
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoCasMailbox.SendLogsTo">
            <summary>
            Unsupported Paramter
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoCasMailbox.InternalBeginProcessing">
            <summary>
            Begin Processing
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoCasMailbox.InternalEndProcessing">
            <summary>
            The internal end processing override.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoCasMailbox.IsBatchingImplemented">
            <summary>
            Whether or not batching is implemented
            </summary>
            <returns>Returns whether batching is implemented</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoCasMailbox.GetODataQueryForCmdlet">
            <summary>
            returns the OData query for cmdlet
            </summary>
            <returns>Returns the OData query</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoCasMailbox.SendRequest(Microsoft.OData.Client.DataServiceQuery{Microsoft.Exchange.Management.AdminApiProvider.CasMailbox})">
            <summary>
            Sends the request
            </summary>
            <param name="adminApiQuery">adminApiQuery param</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoCasMailbox.GetGuidTypeConversionProperties">
            <summary>
            Configure to convert Guid and ExchangeGuid fields to Guid types.
            </summary>
            <returns>List of fields which should be converted to guid types.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoCasMailbox.GetDateTimeTypeConversionProperties">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoCasMailbox.WriteData(Microsoft.Exchange.Management.AdminApiProvider.CasMailbox)">
            <summary>
            This method is used to write the object
            </summary>
            <param name="casMailBoxEntry">casMailBoxEntry param</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoCasMailbox.ConstructODataQuery">
            <summary>
            This Constructs the DataServiceQuery taking care of all the Parameters which are passed in
            </summary>
            <returns>Returns DataServiceQuery</returns>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox">
            <summary>
            This class implements "Get-ExoMailbox" Cmdlet
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.GuidConversionProperties">
            <summary>
            List of fields which need to be converted to Guid strong type.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.BoolConversionProperties">
            <summary>
            List of fields which need to be converted to Bool type.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.DatetimeConversionProperties">
            <summary>
            List of Datetime Fields.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.PropertySet">
            <summary>
            Specifies which property set to return
            Each enumerator contains a set of properties of Mailbox
            By default, Minimum set is returned
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.PropertySet.All">
            <summary>
            Returns all properties
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.PropertySet.Minimum">
            <summary>
            Returns minimum set of properties
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.PropertySet.AddressList">
            <summary>
            Returns properties related to AddressList
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.PropertySet.Archive">
            <summary>
            Returns properties related to Archive
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.PropertySet.Audit">
            <summary>
            Returns properties related to Audit
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.PropertySet.Custom">
            <summary>
            Returns properties related to Custom
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.PropertySet.Delivery">
            <summary>
            Returns properties related to Delivery
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.PropertySet.Hold">
            <summary>
            Returns properties related to Hold
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.PropertySet.Moderation">
            <summary>
            Returns properties related to Moderation
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.PropertySet.Move">
            <summary>
            Returns properties related to Move
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.PropertySet.Policy">
            <summary>
            Returns properties related to Policy
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.PropertySet.PublicFolder">
            <summary>
            Returns properties related to PublicFolder
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.PropertySet.Quota">
            <summary>
            Returns properties related to Quota
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.PropertySet.Resource">
            <summary>
            Returns properties related to Resource
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.PropertySet.Retention">
            <summary>
            Returns properties related to Retention
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.PropertySet.SCL">
            <summary>
            Returns properties related to Spam Confidence Level
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.PropertySet.SoftDelete">
            <summary>
            Returns properties related to SoftDelete
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.PropertySet.StatisticsSeed">
            <summary>
            Returns properties needed for seeding into Statistics cmdlets
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.asyncJobController">
            <summary>
            Async job controller instance
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.Anr">
            <summary>
            Read Anr value for cmdlet.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.Filter">
            <summary>
            Filter to apply
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.OrganizationalUnit">
            <summary>
            Organizational unit in AD. This cmdlet will fetch all mailbox data under this organization.
            This organization must be with in admin's root organization. 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.Properties">
            <summary>
            Here, you can specify exactly what properties you need.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.PropertySets">
            <summary>
            Here, you can specify which property set you want.
            A property set is a set of Mailbox properties
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.SoftDeletedMailbox">
            <summary>
            Parameter to specify only soft deleted mailbox to be retrieved.
            You don't need to specify a value with this switch.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.InactiveMailboxOnly">
            <summary>
            Parameter to specify only inactive mailbox to be retrieved.
            You don't need to specify a value with this switch.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.Archive">
            <summary>
            Parameter to specify only archive mailboxes are to be retrieved.
            You don't need to specify a value with this switch.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.IncludeInactiveMailbox">
            <summary>
            Parameter to specify the result should including inactive mailbox to be retrieved.
            You don't need to specify a value with this switch.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.MailboxPlan">
            <summary>
            Parameter to specify mailbox plan. If specified the results will be restricted
            to mailboxes with the specified plan.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.RecipientTypeDetails">
            <summary>
            Parameter to limit the results to given RecipientTypeDetails
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.PublicFolder">
            <summary>
            Unsupported Parameter
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.GroupMailbox">
            <summary>
            Unsupported Parameter
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.Migration">
            <summary>
            Unsupported Parameter
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.Async">
            <summary>
            Unsupported Parameter
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.InternalBeginProcessing">
            <summary>
            Begin Processing
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.InternalProcessRecord">
            <summary>
            Process Record
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.GetGuidTypeConversionProperties">
            <summary>
            Configure to convert Guid and ExchangeGuid fields to Guid types.
            </summary>
            <returns>List of fields which should be converted to guid types.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.GetBoolTypeConversionProperties">
            <summary>
            This method returns list of properties which should be converted to Bool types.
            </summary>
            <returns>List of fields which should be converted to guid types.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.GetDateTimeTypeConversionProperties">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.InternalEndProcessing">
            <summary>
            The internal end processing override.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.GetPSPropertyValue(System.Reflection.PropertyInfo,System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.GetRequestHeaders">
            <summary>
            Header for Request calls
            This is an override method for Get-ExoMailbox Endpoint to handle currently
            -IncludeInactiveMailbox and  -InactiveMailboxOnly Parameters.
            In this scenario we add -Identity parameter as Header and value as base64 encoded String Param.
            So the Header becomes XParamIdentity : base64encoded(identityValue)
            </summary>
            <returns> Collection of Header Key Value</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.WriteData(Microsoft.Exchange.Management.AdminApiProvider.Mailbox)">
            <summary>
            This method is used to write the object
            </summary>
            <param name="mailBoxEntry">mailBoxEntry param</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.ConstructODataQuery">
            <summary>
            This Constructs the DataServiceQuery taking care of all the Parameters which are passed in
            </summary>
            <returns>Returns DataServiceQuery</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailbox.ShouldSkipIdentityKeyInConstructODataQuery">
            <summary>
            Logic to check if Identity Key should be Skipped.
            Currently for IncludeInactiveMailbox, InactiveMailboxOnly parameters we are skipping the
            Identity Key since it expects more than 1 result.
            </summary>
            <returns>True if InactiveMailboxOnly or IncludeInactivemailbox parameters are passed</returns>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxFolderPermission">
            <summary>
            class for Get-ExoMailboxFolderPermission cmdlet
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxFolderPermission.ResultSize">
            <summary>
            hiding base class ResultSize so that it is not treated as a cmdlet parameter
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxFolderPermission.User">
            <summary>
            the user identity
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxFolderPermission.GroupMailbox">
            <summary>
            the groupmailbox switch parameter
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxFolderPermission.asyncJobController">
            <summary>
            AsyncJobController instance
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxFolderPermission.ExtractAndValidateMailboxFolderPermissionIdentity">
            <summary>
            Extracts the MailboxId and FolderId set for the current cmdlet execution.
            This functions throws exception in case the format of the Identity is not correct.
            </summary>
            <returns>returns identity if it is acceptable otherwise returns null</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxFolderPermission.InternalBeginProcessing">
            <summary>
            Begin Processing
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxFolderPermission.InternalProcessRecord">
            <summary>
            process record
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxFolderPermission.InternalEndProcessing">
            <summary>
            The internal end processing override.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxFolderPermission.ConstructAndThrowExceptionWithMeaningfullMessage(System.Exception)">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxFolderPermission.WriteData(Microsoft.Exchange.Management.AdminApiProvider.MailboxFolderPermission)">
            <summary>
            This method is used to write the object
            </summary>
            <param name="mailboxFolderPermissionEntry">mailboxFolderPermissionEntry param</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxFolderPermission.ConstructAdditionalODataQuery(System.String,System.String,System.String)">
            <summary>
            function to construct odataquery
            </summary>
            <param name="mailboxIdentity">the identity of mailbox</param>
            <param name="folderIdentity">the identity of folder (folderpath)</param>
            <param name="userIdentity">the identity of user</param>
            <returns>returns the constructed query</returns>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxFolderStatistics">
            <summary>
            ExoMailboxFolderStatistics Class. This Class takes the
            Get-ExoMailboxFolderStatistics Cmdlet and calls REST API
            that directly Invokes Get-MailboxFolderStatistics.
            Mailbox Identity is the Mandatory Field for this cmdlet, so
            deriving from IdentityCmdlet.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxFolderStatistics.ResultSize">
            <summary>
            Hiding base class ResultSize so that it is not treated as a cmdlet parameter
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxFolderStatistics.Folderscope">
            <summary>
            FolderScope Parameter Ex: Calendar, Contacts
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxFolderStatistics.Archive">
            <summary>
            This Parameter to specify that the mailbox is an Archive Mailbox
            and return the mailbox folder statistics for this mailbox.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxFolderStatistics.IncludeSoftDeletedRecipients">
            <summary>
            This Parameter to include Analysis of each folder for Soft Deleted 
            Recipients
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxFolderStatistics.IncludeOldestAndNewestItems">
            <summary>
            This Paramter to include Dates of last modified folders and newest
            modified. If not specified values are stale values / default.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxFolderStatistics.asyncJobController">
            <summary>
            Async job controller instance
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxFolderStatistics.sizeConvertProperties">
            <summary>
            Contains an array of properties which need to be converted to local time zone from UTC
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxFolderStatistics.DatetimeConversionProperties">
            <summary>
            List of Datetime Fields to be converted.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxFolderStatistics.InternalBeginProcessing">
            <summary>
            BeginProcessing Need not be Changed.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxFolderStatistics.IsBatchingImplemented">
            <summary>
            Whether or not batching is implemented
            </summary>
            <returns>Returns whether batching is implemented</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxFolderStatistics.InternalProcessRecord">
            <summary>
            ProcessRecord Called After BeginProcessing.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxFolderStatistics.InternalEndProcessing">
            <summary>
            EndProcessing Need not be Changed.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxFolderStatistics.ConstructODataQuery(System.String)">
            <summary>
            Constructing the Admin API Query REST URL to perform a GET Request.
            </summary>
            <param name="preferredIdentity">preferredIdentity param</param>
            <returns>Query with all the segments.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxFolderStatistics.GetPSPropertyValue(System.Reflection.PropertyInfo,System.Object)">
            <summary>
            Method when Printing to Console and also used to override.
            Converting Long Size Values to Unlimited ByteQuantifiedSize
            </summary>
            <param name="property">MailboxFolderStatistics Property Setter</param>
            <param name="obj">MailboxFolderStatistics Output Object returned from API</param>
            <returns>In case of Size Related properties it returns Size Fields. Others will get default values.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxFolderStatistics.GetDateTimeTypeConversionProperties">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxFolderStatistics.WriteData(Microsoft.Exchange.Management.AdminApiProvider.MailboxFolderStatistics)">
            <summary>
            WriteObject Action
            Action Used for Writing Data to be passed in InvokeTask which internally
            calls InvokeQuery.
            </summary>
            <param name="mailboxFolderStatisticsEntry">mailboxFolderStatisticsEntry param</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxFolderStatistics.HandleCmdletParameters(Microsoft.OData.Client.DataServiceQuery{Microsoft.Exchange.Management.AdminApiProvider.MailboxFolderStatistics}@)">
            <summary>
            Additional Paramters to be handled If Client adds them.
            </summary>
            <param name="adminApiQuery">adminApiQuery param</param>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxPermission">
            <summary>
            Cmdlet Definition of Get-ExoMailboxPermission.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxPermission.User">
            <summary>
            Specifies the User for Mailbox Permission. This needs be used along with Identity. Hence this is part
            of Identity Parameter Set.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxPermission.Owner">
            <summary>
            Specifies the Owner for Recipient Permission.This needs be used along with Identity. Hence this is part
            of Identity Parameter Set.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxPermission.SoftDeletedMailbox">
            <summary>
            Parameter to specify only soft deleted mailbox permissions to be retrieved.
            You don't need to specify a value with this switch.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxPermission.asyncJobController">
            <summary>
            Async job controller instance
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxPermission.writtenObjectCountByIdentity">
            <summary>
            Number of objects this cmdlet wrote to pipeline.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxPermission.mailboxPermissionProperties">
            <summary>
            This field captures all the properties of ExoMailboxPermission class.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxPermission.mailboxPermissionOwnerInfoProperties">
            <summary>
            This field captures all the properties of ExoMailboxPermissionOwnerInfo class.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxPermission.MailboxPermissionProperties">
            <summary>
            MailboxPermissionProperties Property to get all the properties of ExoMailboxPermission class
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxPermission.MailboxPermissionOwnerInfoProperties">
            <summary>
            MailboxPermissionOwnerInfoProperties Property to get all the properties of 
            ExoMailboxPermissionOwnerInfo class
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxPermission.WriteData(Microsoft.Exchange.Management.AdminApiProvider.MailboxPermission)">
            <summary>
            This method unwinds the API response and prepares the output which matches that of RPS cmdlet.
            This is per object in the output of the Api call.
            </summary>
            <param name="permission">Permission object form Admin Api.</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxPermission.WriteOwnerData(Microsoft.Exchange.Management.AdminApiProvider.MailboxPermission)">
            <summary>
            This method writes the Mailbox Owner information to pipeline.
            </summary>
            <param name="permission">Permission object form Admin Api.</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxPermission.InternalBeginProcessing">
            <summary>
            Begin Processing
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxPermission.InternalProcessRecord">
            <summary>
            Process Record
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxPermission.InternalEndProcessing">
            <summary>
            The internal end processing override.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxPermission.GetPSPropertyValue(System.Reflection.PropertyInfo,System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxPermission.ConstructAdditionalODataQuery(System.String)">
            <summary>
            This Constructs the DataServiceQuery taking care of all the Parameters which are passed in
            </summary>
            <param name="identity">mailboxId parameter from the cmdlet</param>
            <returns>DataServiceQuery object</returns>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxStatistics">
            <summary>
            This class implements "Get-ExoMailboxStatistics" Cmdlet
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxStatistics.PropertySet">
            <summary>
            Specifies which property set to return
            Each enumerator contains a set of properties of MailboxStatistics
            By default, Minimum set is returned
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxStatistics.PropertySet.All">
            <summary>
            Returns all properties
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxStatistics.PropertySet.Minimum">
            <summary>
            Returns minimum set of properties
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxStatistics.DatetimeConversionProperties">
            <summary>
            List of Datetime Fields to be converted to Datetime Objects.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxStatistics.EnumConversionProperties">
            <summary>
            List of Enum Fields to be converted to Enum Type
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxStatistics.sizeConvertProperties">
            <summary>
            Contains an array of properties related to size which need to be converted to appropriate size string for display
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxStatistics.ResultSize">
            <summary>
            Hiding base class ResultSize so that it is not treated as a cmdlet parameter
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxStatistics.IncludeMoveHistory">
            <summary>
            This parameter is not supported, but adding it for RPS parity. Specifying this parameter will throw exception.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxStatistics.IncludeMoveReport">
            <summary>
            This parameter is not supported, but adding it for RPS parity. Specifying this parameter will throw exception.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxStatistics.ExchangeGuid">
            <summary>
            Gets or sets the exchange guid (a.k.a mailbox guid) value from the incoming pipeline object.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxStatistics.DatabaseGuid">
            <summary>
            Gets or sets the database (hosting the primary mailbox) guid value from the incoming pipeline object.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxStatistics.Archive">
            <summary>
            The Archive switch parameter specifies whether to return mailbox statistics for the archive mailbox associated with the specified mailbox.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxStatistics.IncludeSoftDeletedRecipients">
            <summary>
            The IncludeSoftDeletedRecipients switch specifies whether to include SoftDeletedRecipients
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxStatistics.Properties">
            <summary>
            Here, you can specify exactly what properties you need.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxStatistics.PropertySets">
            <summary>
            Here, you can specify which property set you want.
            A property set is a set of properties
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxStatistics.ExternalDirectoryObjectId">
            <summary>
            ExternalDirectoryObhectId is not supported during Proxy case in Get-MailboxStatistics cmdlet.
            So we have decided not to provide ExternalDirectoryObjectId as Parameter in this cmdlet.
            In place of this user can use UserPrincipalName,Identity or ExchangeGuid.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxStatistics.asyncJobController">
            <summary>
            Mailbox Statistics job controller
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxStatistics.batchHelperV1">
            <summary>
            batch helper for adminApiQuery of version 1
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxStatistics.batchHelperV2">
            <summary>
            batch helper for adminApiQuery of version 2
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxStatistics.preferredIdentity">
            <summary>
            preferredIdentity param
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxStatistics.BeginProcessing">
            <summary>
            Overriding this method instead of InternaBeginProcessing, because we want to throw exception when non-supported parameters are specified.
            Throwing exception from InternalBeginProcessing, will result in generating an error record instead of actual exception.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxStatistics.InternalBeginProcessing">
            <summary>
            Begin Processing
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxStatistics.IsBatchingImplemented">
            <summary>
            Whether or not batching is implemented
            </summary>
            <returns>Returns whether batching is implemented</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxStatistics.ProcessRequest">
            <summary>
            Process the individual requests
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxStatistics.ProcessLastBatching">
            <summary>
            used to process the remaining batched requests
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxStatistics.InternalEndProcessing">
            <summary>
            The internal end processing override.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxStatistics.GetPSPropertyValue(System.Reflection.PropertyInfo,System.Object)">
            <summary>
            Method when Printing to Console and also used to override.
            Converting Long Size Values to Unlimited ByteQuantifiedSize
            </summary>
            <param name="property">MailboxStatistics Property Setter</param>
            <param name="obj">MailboxStatistics Output Object returned from API</param>
            <returns>In case of Size Related properties it returns Size Fields. Others will get default values.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxStatistics.ValidateIdentityParms">
            <summary>
            Validate given identity parameters.Order of priority for parameters.
            ExchangeGuid
            UserPrincipalName
            PrimarySmtpAddress
            Identity
            </summary>
            <exception cref="T:Microsoft.Exchange.Management.RestApiClient.RestClientException">Throws exception if identity parameters are there but they are not valid.</exception>
            <returns>Return valid identity if identity params are there and valid. null if they are not there.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxStatistics.GetDateTimeTypeConversionProperties">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxStatistics.SetRoutingKeyAndPrefixValue(System.String@,System.String@)">
            <summary>
            Set the Routing key value and Prefix value.
            </summary>
            <param name="routeKeyValue">Routing Key Value</param>
            <param name="routeKeyPrefix">Routing Prefix Value</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxStatistics.WriteData(Microsoft.Exchange.Management.AdminApiProvider.MailboxStatistics)">
            <summary>
            This method is used to write the object
            </summary>
            <param name="mailBoxStatsEntry">mailBoxStatsEntry param</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxStatistics.SendBatchRequest(System.String,Microsoft.OData.Client.DataServiceQuery{Microsoft.Exchange.Management.AdminApiProvider.MailboxStatistics},Microsoft.OData.Client.DataServiceQuery{Microsoft.Exchange.Management.AdminApiProvider.MailboxStatistics})">
            <summary>
            Sends the batched requests
            </summary>
            <param name="preferredIdentity">preferredIdentity param</param>
            <param name="adminApiQueryV2">adminApiQueryV2 param</param>
            <param name="adminApiQueryV1">adminApiQueryV1 param</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxStatistics.SendRequest(System.String,Microsoft.OData.Client.DataServiceQuery{Microsoft.Exchange.Management.AdminApiProvider.MailboxStatistics},Microsoft.OData.Client.DataServiceQuery{Microsoft.Exchange.Management.AdminApiProvider.MailboxStatistics})">
            <summary>
            Sends the request
            </summary>
            <param name="preferredIdentity">preferredIdentity param</param>
            <param name="adminApiQueryV2">adminApiQueryV2 param</param>
            <param name="adminApiQueryV1">adminApiQueryV1 param</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxStatistics.ConstructODataQuery(System.Guid,System.Guid,System.Boolean)">
            <summary>
            Constructs the OData Admin API query using the passed in exchange guid and database guid.
            </summary>
            <param name="exchangeGuid">
            The exchange guid value.
            </param>
            <param name="databaseGuid">
            The database guid value.
            </param>
            <param name="useIdentityInXAnchorMailboxHeader">
            Flag to indicate whether to use the identity value in the 'X-AnchorMailbox' header.
            </param>
            <returns>
            The data service query.
            </returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxStatistics.RecordProcessorAndHandler(Microsoft.Exchange.Management.AdminApiProvider.ApiProviderContext{Microsoft.Exchange.Management.AdminApiProvider.MailboxStatistics},Microsoft.Exchange.Management.AdminApiProvider.ApiProviderContext{Microsoft.Exchange.Management.AdminApiProvider.MailboxStatistics},System.Action{Microsoft.Exchange.Management.AdminApiProvider.MailboxStatistics})">
            <summary>
            Executes the MailBoxStatistics request. Tries to invoke stats v2 cmdlet, if failed then it goes to v1 cmdlet
            </summary>
            <param name="queryContextv2">
            The query container for invoking v2 cmdlet.
            </param>
            <param name="queryContextv1">
            The query container for invoking v1 cmdlet.
            </param>
            <param name="writeObject">
            Method to consume the object received in response.
            </param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxStatistics.ConstructODataQuery(System.String)">
            <summary>
            This Constructs the DataServiceQuery taking care of all the Parameters which are passed in
            </summary>
            <param name="preferredIdentity">preferredIdentity param</param>
            <returns>Returns DataServiceQuery</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxStatistics.HandleCmdletParameters(Microsoft.OData.Client.DataServiceQuery{Microsoft.Exchange.Management.AdminApiProvider.MailboxStatistics}@)">
            <summary>
            Handles the transformation and inclusion of cmdlet parameters to the admin api query.
            </summary>
            <param name="adminApiQuery">
            The admin api query to be updated.
            </param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxStatistics.HandlePropertiesAndPropertySetsParameter(Microsoft.OData.Client.DataServiceQuery{Microsoft.Exchange.Management.AdminApiProvider.MailboxStatistics}@)">
            <summary>
            Handles the transformation of the properties and propertysets cmdlet parameter to url parameter.
            </summary>
            <param name="adminApiQuery">
            The admin api query to be updated.
            </param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMailboxStatistics.ExecuteQuery(Microsoft.Exchange.Management.AdminApiProvider.ApiProviderContext{Microsoft.Exchange.Management.AdminApiProvider.MailboxStatistics},System.Action{Microsoft.Exchange.Management.AdminApiProvider.MailboxStatistics})">
            <summary>
            Executes the the given admin api query.
            </summary>
            <param name="queryContext">
            The query container.
            </param>
            <param name="writeObject">
            Method to consume the object received in response.
            </param>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.GetExoMobileDeviceStatistics">
            <summary>
            class for Get-ExoMobileDeviceStatistics cmdlet
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoMobileDeviceStatistics.ResultSize">
            <summary>
            Hiding base class ResultSize so that it is not treated as a cmdlet parameter
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoMobileDeviceStatistics.Mailbox">
            <summary>
            Device mailbox
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoMobileDeviceStatistics.ActiveSync">
            <summary>
            Filter only ActiveSync devices
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoMobileDeviceStatistics.OWAforDevices">
            <summary>
            Filter only MOWA devices
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoMobileDeviceStatistics.RestApi">
            <summary>
            Filter only REST based devices
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoMobileDeviceStatistics.UniversalOutlook">
            <summary>
            Filter only Universal Outlook devices
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoMobileDeviceStatistics.ShowRecoveryPassword">
            <summary>
            Unsupported Paramter
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoMobileDeviceStatistics.NotificationEmailAddresses">
            <summary>
            Unsupported Paramter
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoMobileDeviceStatistics.GetMailboxLog">
            <summary>
            Unsupported Paramter
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoMobileDeviceStatistics.asyncJobController">
            <summary>
            Async job controller instance
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoMobileDeviceStatistics.DatetimeConversionProperties">
            <summary>
            List of Datetime Fields to be converted.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoMobileDeviceStatistics.EnumConversionProperties">
            <summary>
            List of Enum Fields to be converted to Enum Type
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMobileDeviceStatistics.InternalBeginProcessing">
            <summary>
            The internal Begin Processing override
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMobileDeviceStatistics.InternalProcessRecord">
            <summary>
             The internal Process Record override
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMobileDeviceStatistics.InternalEndProcessing">
            <summary>
            The internal end processing override
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMobileDeviceStatistics.GetDateTimeTypeConversionProperties">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMobileDeviceStatistics.GetPSPropertyValue(System.Reflection.PropertyInfo,System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMobileDeviceStatistics.GetRequestHeaders">
            <summary>
            Header for Request calls
            </summary>
            <returns> Collection of Header Key Value</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMobileDeviceStatistics.ConstructODataQuery(System.String)">
            <summary>
            Constructing the Admin API Query REST URL to perform a GET Request.
            </summary>
            <param name="preferredMailboxId">preferredMailboxId param</param>
            <returns>Query with all the segments.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMobileDeviceStatistics.WriteData(Microsoft.Exchange.Management.AdminApiProvider.MobileDeviceStatistics)">
            <summary>
            This method is used to write the object
            </summary>
            <param name="mobileDeviceStatisticsEntry">mobileDeviceStatisticsEntry param</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMobileDeviceStatistics.HandleCmdletParameters(Microsoft.OData.Client.DataServiceQuery{Microsoft.Exchange.Management.AdminApiProvider.MobileDeviceStatistics}@)">
            <summary>
            Handles the transformation and inclusion of cmdlet parameters to the admin api query.
            </summary>
            <param name="adminApiQuery">
            The admin api query to be updated.
            </param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoMobileDeviceStatistics.ValidateMobileDeviceStatisticsIdentity">
            <summary>
            Handles -Identity parameter which is a long string
            </summary>
            <returns>Returns the identity after validating identity params</returns>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.GetExoRecipient">
            <summary>
            This class implements "Get-ExoRecipient" Cmdlet
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoRecipient.GuidConversionProperties">
            <summary>
            List of fields which need to be converted to Guid strong type.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoRecipient.DatetimeConversionProperties">
            <summary>
            List of Datetime Fields to be converted.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.GetExoRecipient.PropertySet">
            <summary>
            Specifies which property set to return
            Each enumerator contains a set of properties of Recipient
            By default, Minimum set is returned
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoRecipient.PropertySet.All">
            <summary>
            Returns all properties
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoRecipient.PropertySet.Minimum">
            <summary>
            Returns minimum set of properties
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoRecipient.PropertySet.Custom">
            <summary>
            Returns properties related to Custom
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoRecipient.PropertySet.MailboxMove">
            <summary>
            Returns properties related to MailboxMove
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoRecipient.PropertySet.Policy">
            <summary>
            Returns properties related to Policy
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoRecipient.PropertySet.Archive">
            <summary>
            Returns properties related to Archive
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoRecipient.asyncJobController">
            <summary>
            Async job controller instance
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoRecipient.Anr">
            <summary>
            Read Anr value for cmdlet.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoRecipient.Filter">
            <summary>
            Filter to apply
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoRecipient.OrganizationalUnit">
            <summary>
            Organizational unit in AD. This cmdlet will fetch all recipients data under this organization.
            This organization must be with in admin's root organization. 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoRecipient.Properties">
            <summary>
            Here, you can specify exactly what properties you need.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoRecipient.PropertySets">
            <summary>
            Here, you can specify which property set you want.
            A property set is a set of recipient properties
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoRecipient.IncludeSoftDeletedRecipients">
            <summary>
            Parameter to specify of including soft deleted recipients.
            You don't need to specify a value with this switch.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoRecipient.RecipientType">
            <summary>
            Parameter to limit the results to given RecipientType
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoRecipient.RecipientTypeDetails">
            <summary>
            Parameter to limit the results to given RecipientTypeDetails
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoRecipient.RecipientPreviewFilter">
            <summary>
            Unsupported Parameter
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoRecipient.InternalBeginProcessing">
            <summary>
            Begin Processing
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoRecipient.InternalProcessRecord">
            <summary>
            Process Record
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoRecipient.GetGuidTypeConversionProperties">
            <summary>
            Configure to convert Guid and ExchangeGuid fields to Guid types.
            </summary>
            <returns>List of fields which should be converted to guid types.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoRecipient.GetDateTimeTypeConversionProperties">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoRecipient.InternalEndProcessing">
            <summary>
            The internal end processing override.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoRecipient.GetRequestHeaders">
            <summary>
            Header for Request calls
            This is an override method for Get-EXORecipient Endpoint to handle -Identity parameter,
            when none of the identifying paramerters (ExternalDirectoryObjectId or UserPrincipalName or PrimarySmtpAddress) are passed
            In this scenario we add -Identity parameter as Header and value as base64 encoded String Param.
            So the Header becomes XParamIdentity : base64encoded(identityValue)
            </summary>
            <returns> Collection of Header Key Value</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoRecipient.WriteData(Microsoft.Exchange.Management.AdminApiProvider.Recipient)">
            <summary>
            This method is used to write the object
            </summary>
            <param name="recipientEntry"> recipientEntry object</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoRecipient.ConstructODataQuery">
            <summary>
            This Constructs the DataServiceQuery taking care of all the Parameters which are passed in
            </summary>
            <returns>Returns DataServiceQuery</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoRecipient.ShouldSkipIdentityKeyInConstructODataQuery">
            <summary>
            Logic to check if Identity Key should be skipped to be included in queryParam
            If ExternalDirectoryObjectId or UserPrincipalName or PrimarySMTPAddress is present
            we would not skip Identity key in query param as they are unique and would not have more than one identifying records
            Else, we would like to skip Identity key in query params since it expects more than 1 result.
            </summary>
            <returns>False if one of ExternalDirectoryObjectId or UserPrincipalName or PrimarySmtpAddress parameters are passed</returns>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.GetExoRecipientPermission">
            <summary>
            Cmdlet Definition of Get-ExoRecipientPermission.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoRecipientPermission.accessRights">
            <summary>
            This property holds AccessRights Passed as Paramter during cmdlet Call.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.GetExoRecipientPermission.asyncJobController">
            <summary>
            Async job controller instance
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoRecipientPermission.Trustee">
            <summary>
            Specifies the Trustee for Recipient Permission
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.GetExoRecipientPermission.AccessRights">
            <summary>
            Specifies the AccessRights for Recipient Permission
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoRecipientPermission.ValidateTrusteeParameter(System.String,System.String)">
            <summary>
            This method validates if Trustee is present along with valid identity. 
            otherwise it will throw Error.
            </summary>
            <param name="trustee">Trustee Parameter</param>
            <param name="identity">Identity Parameter</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoRecipientPermission.InternalBeginProcessing">
            <summary>
            Begin Processing
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoRecipientPermission.InternalProcessRecord">
            <summary>
            Process Record
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoRecipientPermission.InternalEndProcessing">
            <summary>
            The internal end processing override.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoRecipientPermission.WriteData(Microsoft.Exchange.Management.AdminApiProvider.Recipient)">
            <summary>
            This method is used to write the object
            </summary>
            <param name="entry">Recipient entry param</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.GetExoRecipientPermission.ConstructAdditionalODataQuery">
            <summary>
            This Constructs the DataServiceQuery taking care of all the Parameters which are passed in
            </summary>
            <returns>Returns DataServiceQuery</returns>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.HeaderKeyNames">
            <summary>
            class for Header Key Names.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.HeaderKeyNames.XIdentity">
            <summary> Key of Identity </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.IdentityCmdlet`2">
            <summary>
            Base class for all Identity related cmdlet
            </summary>
            <typeparam name="TDomainObject">TDomainObject param</typeparam>
            <typeparam name="TCmdlet">TCmdlet param</typeparam>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.IdentityCmdlet`2.Identity">
            <summary>
            Specifies the Identity of the specific Object
            Identity is a special kind of filter which will return only one value if present
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.IdentityCmdlet`2.ExternalDirectoryObjectId">
            <summary>
            ExternalDirectoryObjectId is GUID identifier (msExchExternalDirectoryObjectId) of the Recipient.
            This is different from ExchangeGuid, ExchangeGuid is internal to exchange. This could be exposed to external entities.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.IdentityCmdlet`2.UserPrincipalName">
            <summary>
            UserPrincipalName, it should be in <NAME_EMAIL> or <EMAIL>
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.IdentityCmdlet`2.PrimarySmtpAddress">
            <summary>
            PrimarySmtpAddress, it should be in <NAME_EMAIL> or <EMAIL>
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.IdentityCmdlet`2.ValidateIdentityParms">
            <summary>
            Validate given identity parameters.Order of priority for parameters.
            ExternalDirectoryObjectId
            UserPrincipalName
            PrimarySmtpAddress
            Identity
            </summary>
            <exception cref="T:Microsoft.Exchange.Management.RestApiClient.RestClientException">Throws exception if identity parameters are there but they are not valid.</exception>
            <returns>Return true if identity params are there and valid. False if they are not there.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.IdentityCmdlet`2.ConstructODataQueryFromIdentityParams``1(System.Type)">
            <summary>
            Construct OData query from identity parameters. 
            </summary>
            <param name="type">Type of the param</param>
            <typeparam name="T">Type of the cmdlet </typeparam>
            <returns>OData query.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.IdentityCmdlet`2.GetValidIdentity">
            <summary>
            Parse and get Valid Identity
            </summary>
            <returns>Identity as string</returns>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.M365Insights.AutoDiscover.AutoDiscoverErrorObject">
            <summary>
            The auto discover error object.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.M365Insights.AutoDiscover.AutoDiscoverErrorObject.ErrorCode">
            <summary>
            Gets or sets the error code.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.M365Insights.AutoDiscover.AutoDiscoverErrorObject.ErrorMessage">
            <summary>
            Gets or sets the error message.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.M365Insights.AutoDiscover.AutoDiscoverObject">
            <summary>
            The auto discover object that contains a Protocol (e.g. Weve) and the Autodiscover Url (e.g. https://substrate.office.com/weve)
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.M365Insights.AutoDiscover.AutoDiscoverObject.Protocol">
            <summary>
            Gets or sets the protocol name.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.M365Insights.AutoDiscover.AutoDiscoverObject.Url">
            <summary>
            Gets or sets the autodiscover uri.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.M365Insights.WeveErrorObject">
            <summary>
            The weve error object.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.M365Insights.WeveErrorObject.Code">
            <summary>
            Gets or sets the code.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.M365Insights.WeveErrorObject.Message">
            <summary>
            Gets or sets the message.
            </summary> 
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.M365Insights.WeveErrorObject.Target">
            <summary>
            Gets or sets the target.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.M365Insights.WeveAdminCmdlet`2">
            <summary>
            Base class for all cmdlets that make requests to Weve
            </summary>
            <typeparam name="TDomainObject">The api type</typeparam>
            <typeparam name="TCmdlet">The cmdlet type</typeparam>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.M365Insights.WeveAdminCmdlet`2.HttpClient">
            <summary>
            Gets the http client.
            Each PowerShell cmd will be a subclass of WeveCmdlet with different generic parameters so will have their own
            HttpClient.
            IE SetUserBriefingConfig, GetMyAnalyticsFeatureConfig will have their own HttpClients.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.M365Insights.WeveAdminCmdlet`2.AutoDiscoverUri">
            <summary>
            The Substrate auto discovery uri for the Weve protocol.
            The {0} must be replaced by connection Uri.
            The {1} must be replaced by the admin's smtp address.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.M365Insights.WeveAdminCmdlet`2.DefaultApplicationPath">
            <summary>
            The default app path if autodiscover call fails
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.M365Insights.WeveAdminCmdlet`2.UriMapping">
            <summary>
            A static dictionary for the Connection Uri mapping where the key is the connectionUri, and the value is the
            localPath
            Users may switch ConnectionUris within the same Powershell session, so we will keep a mapping of
            the base url's that have already been retrieved.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.M365Insights.WeveAdminCmdlet`2.CmdletName">
            <summary>
            The name of the cmdlet
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.M365Insights.WeveAdminCmdlet`2.UrlStem">
            <summary>
            The last segment in the URL that navigates to the actual resource
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.M365Insights.WeveAdminCmdlet`2.GetCompleteUri">
            <summary>
            Full URI to make Weve requests to
            </summary>
            <returns>The full URI</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.M365Insights.WeveAdminCmdlet`2.MakeAndSendPatchRequest``1(``0,System.String,System.Uri)">
            <summary>
            Sends patch request using HTTP client
            </summary>
            <param name="requestBody">The body of the request</param>
            <param name="settingsName">The optional settings name</param>
            <param name="uri">The uri</param>
            <typeparam name="T">The type to parse the response as</typeparam>
            <returns>The response</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.M365Insights.WeveAdminCmdlet`2.MakeAndSendGetRequest``1(System.String,System.Uri,System.Collections.Generic.IDictionary{System.Net.HttpStatusCode,System.String})">
            <summary>
            Sends GET request using HTTP client
            </summary>
            <param name="settingsName">The optional settings name</param>
            <param name="uri">The uri</param>
            <param name="customErrorCodeToMessage">The custom message for expected error code.</param>
            <typeparam name="T">The type to parse the response as</typeparam>
            <returns>The response</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.M365Insights.WeveAdminCmdlet`2.MakeAndSendPostRequest``1(``0,System.Uri,System.Collections.Generic.IDictionary{System.Net.HttpStatusCode,System.String})">
            <summary>
            Sends POST request using HTTP client
            </summary>
            <typeparam name="T">The type to parse the response as</typeparam>
            <param name="requestBody">The request body</param>
            <param name="uri">The uri</param>
            <param name="customErrorCodeToMessage">The custom message for expected error code.</param>
            <returns>The posted object</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.M365Insights.WeveAdminCmdlet`2.MakeAndSendPostRequest``1(System.Net.Http.StringContent,System.Uri,System.Collections.Generic.IDictionary{System.Net.HttpStatusCode,System.String})">
            <summary>
            Sends POST request using HTTP client
            </summary>
            <typeparam name="T">The type to parse the response as</typeparam>
            <param name="stringContent">The request body</param>
            <param name="uri">The uri</param>
            <param name="customErrorCodeToMessage">The custom message for expected error code.</param>
            <returns>The posted object</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.M365Insights.WeveAdminCmdlet`2.MakeAndSendDeleteRequest(System.Uri)">
            <summary>
            Makes and sends a DELETE request to weve
            </summary>
            <param name="uri">The uri to send the request to</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.M365Insights.WeveAdminCmdlet`2.RetrieveApplicationPath">
            <summary>
            Retrieve the application path.
            An example local path is /weveb2.
            </summary>
            <returns>The local path.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.M365Insights.WeveAdminCmdlet`2.PrepareHttpClientForRequestWithBody``1(``0)">
            <summary>
            Sets header values for requests with body
            </summary>
            <param name="requestBodyObject">The request body</param>
            <typeparam name="T">The type to parse the response as</typeparam>
            <returns>The body payload</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.M365Insights.WeveAdminCmdlet`2.CallAutodiscover">
            <summary>
            Make a call to the Autodiscover API. If successful, the call will return the up to date Url base
            </summary>
            <returns>The http response message from the autodiscover request</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.M365Insights.WeveAdminCmdlet`2.SetHttpClientAuthHeader">
            <summary>
            Sets header values for requests without body
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.M365Insights.WeveAdminCmdlet`2.HandleSuccessResponse``1(System.Net.Http.HttpResponseMessage,System.String)">
            <summary>
            Handles successful response
            </summary>
            <typeparam name="T">The type to parse the response as</typeparam>
            <param name="response">The response to parse</param>
            <param name="settingsName">Optional settings name</param>
            <returns>The parsed response</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.M365Insights.WeveAdminCmdlet`2.HandleSuccessResponse(System.Net.Http.HttpResponseMessage)">
            <summary>
            Handles success response without reponse object
            </summary>
            <param name="response">The response to handle</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.M365Insights.WeveAdminCmdlet`2.HandleErrorResponse(System.Net.Http.HttpResponseMessage,System.String,System.Collections.Generic.IDictionary{System.Net.HttpStatusCode,System.String})">
            <summary>
            Generates logs and throws in case of error
            </summary>
            <param name="response">The response</param>
            <param name="settingsName">The settings name</param>
            <param name="customErrorCodeToMessage">The custom message for expected error code.</param>
            <exception cref="T:Microsoft.Exchange.Management.RestApiClient.RestClientException">Throws the RestClientException with the error message from response.</exception>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Analytics.GetMyAnalyticsFeatureConfig">
            <summary>
            This class implements "Get-MyAnalyticsFeatureConfig" Cmdlet
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Analytics.GetMyAnalyticsFeatureConfig.CmdletName">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Analytics.GetMyAnalyticsFeatureConfig.InternalProcessRecord">
            <inheritdoc/>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Analytics.GetDefaultTenantMyAnalyticsFeatureConfig">
            <summary>
            This class implements "Get-DefaultTenantMyAnalyticsFeatureConfig" Cmdlet
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Analytics.GetDefaultTenantMyAnalyticsFeatureConfig.CmdletName">
            <summary>
            Name of the cmdlet
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Analytics.GetDefaultTenantMyAnalyticsFeatureConfig.InternalProcessRecord">
            <summary>
            Internal process of the cmdlet
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Analytics.MyAnalyticsDisplaySettings">
            <summary>
            Represents the output of the cmdlet
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Analytics.MyAnalyticsDisplaySettings.UserId">
            <summary>
            Gets or sets the user id.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Analytics.MyAnalyticsDisplaySettings.PrivacyMode">
            <summary>
            Gets or sets the PrivacyMode.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Analytics.MyAnalyticsDisplaySettings.IsDashboardEnabled">
            <summary>
            Gets or sets the a value indicating the dashboard is enabled.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Analytics.MyAnalyticsDisplaySettings.IsAddInEnabled">
            <summary>
            Gets or sets the a value indicating the addIn is enabled.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Analytics.MyAnalyticsDisplaySettings.IsDigestEmailEnabled">
            <summary>
            Gets or sets the a value indicating the weeklyDigest is enabled.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Analytics.TenantMyAnalyticsDisplaySettings">
            <summary>
            Represents the output of the cmdlet
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Analytics.TenantMyAnalyticsDisplaySettings.TenantId">
            <summary>
            Gets or sets the tenant id.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Analytics.TenantMyAnalyticsDisplaySettings.IsDashboardEnabled">
            <summary>
            Gets or sets the a value indicating the dashboard is enabled.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Analytics.TenantMyAnalyticsDisplaySettings.IsAddInEnabled">
            <summary>
            Gets or sets the a value indicating the addIn is enabled.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Analytics.TenantMyAnalyticsDisplaySettings.IsDigestEmailEnabled">
            <summary>
            Gets or sets the a value indicating the weeklyDigest is enabled.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Analytics.TenantMyAnalyticsDisplaySettings.IsMeetingEffectivenessSurveyEnabled">
            <summary>
            Gets or sets the a value indicating the whether meeting effectiveness survey is available or not
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Analytics.TenantMyAnalyticsDisplaySettings.MeetingEffectivenessSurveySamplingRate">
            <summary>
            Gets or sets the a value indicating what is the meeting effectiveness survey sampling rate
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Analytics.TenantMyAnalyticsDisplaySettings.IsScheduleSendEnabled">
            <summary>
            Gets or sets the a value indicating the scheduled send is enabled.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.MyAnalyticsCmdlet`2">
            <summary>
            The base class for the client side MyAnalytics Cmdlets based on Weve implementation.
            </summary>
            <typeparam name="TDomainObject">The type of the domain object.</typeparam>
            <typeparam name="TCmdlet">The type of the cmdlet.</typeparam>
            <seealso cref="T:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2" />
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.MyAnalyticsCmdlet`2.Identity">
            <summary>
            Specifies the Identity of the user to update MyAnalytics settings
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.MyAnalyticsCmdlet`2.UrlStem">
            <summary>
            The myanalytics settings url stem. 
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.MyAnalyticsCmdlet`2.GetUpdatedDisplaySettings(System.String,Microsoft.Exchange.Management.RestApiClient.MyAnalytics.MyAnalyticsSettings)">
            <summary>
            Converts the API response to the cmdlet output
            Sample response :
            UserId                : xyz
            PrivacyMode           : opt-in
            IsDashboardEnabled    : False
            IsAddInEnabled        : False
            IsDigestEmailEnabled  : True
            </summary>
            <param name="identity">Identity of the user</param>
            <param name="responseSettings">updated cmdlet output</param>
            <returns>Updated MyAnalyticsDisplaySettings</returns>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.MyAnalyticsConstants">
            <summary>
            Consists of all hard-coded strings
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.MyAnalyticsConstants.AddIn">
            <summary>
            Compare value for addIn
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.MyAnalyticsConstants.All">
            <summary>
            Compare value for all surfaces
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.MyAnalyticsConstants.Dashboard">
            <summary>
            Compare value for dashboard
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.MyAnalyticsConstants.DigestEmail">
            <summary>
            Compare value for digest email
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.MyAnalyticsConstants.OptIn">
            <summary>
            Privacy-mode display string opt-in
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.MyAnalyticsConstants.OptOut">
            <summary>
            Privacy-mode display string opt-out
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.MyAnalyticsConstants.OptedOut">
            <summary>
            OptedOut string
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.MyAnalyticsConstants.OptedIn">
            <summary>
            OptedIn string
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.MyAnalyticsConstants.Unavailable">
            <summary>
            Unavailable string
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.MyAnalyticsConstants.MeetingEffectivenessSurvey">
            <summary>
            Compare value for meeting effectiveness survey
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.MyAnalyticsConstants.MeetingEffectivenessSurveySamplingRate">
            <summary>
            Compare value for meeting effectiveness survey sampling rate
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.MyAnalyticsConstants.MeetingEffectivenessSurveySamplingRateLowerBound">
            <summary>
            Lower bound for meeting effectiveness survey sampling rate
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.MyAnalyticsConstants.MeetingEffectivenessSurveySamplingRateUpperBound">
            <summary>
            Upper bound for meeting effectiveness survey sampling rate
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.MyAnalyticsConstants.ScheduledSend">
            <summary>
            Compare value for scheduled send
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.MyAnalyticsSettings">
            <summary>
            Represents the MyAnalytics settings
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.MyAnalyticsSettings.ServiceSetting">
            <summary>
            ServiceSettings of the user
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.MyAnalyticsSettings.FeatureSetting">
            <summary>
            FeatureSettings of the user
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.MyAnalyticsSettings.MyAnalyticsFeatureSettings">
            <summary>
             Represents the MyAnalytics Feauture settings
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.MyAnalyticsSettings.MyAnalyticsFeatureSettings.IsDashboardOptedOut">
            <summary>
            Gets or sets the user id.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.MyAnalyticsSettings.MyAnalyticsFeatureSettings.IsDigestEmailOptedOut">
            <summary>
            Gets or sets the user id.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.MyAnalyticsSettings.MyAnalyticsFeatureSettings.IsAddInOptedOut">
            <summary>
            Gets or sets the user id.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.MyAnalyticsSettings.MyAnalyticsServiceSettings">
            <summary>
            Represents the MyAnalytics Service settings
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.MyAnalyticsSettings.MyAnalyticsServiceSettings.IsOptedOut">
            <summary>
            Gets or sets the user id.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.SetMyAnalyticsFeatureConfig">
            <summary>
            This class implements "Set-MyAnalyticsFeatureConfig" Cmdlet
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.SetMyAnalyticsFeatureConfig.PrivacyMode">
            <summary>
            Gets or sets a value indicating whether the MyAnalytics is opted-in/out.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.SetMyAnalyticsFeatureConfig.Feature">
            <summary>
            Gets or sets a value indicating the MyAnalytics platform
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.SetMyAnalyticsFeatureConfig.IsEnabled">
            <summary>
            Gets or sets a value indicating whether the MyAnalytics platform is enabled.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.SetMyAnalyticsFeatureConfig.CmdletName">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.SetMyAnalyticsFeatureConfig.IsCommandValid">
            <summary>
            Function to validate the command
            Valid command types and the rest are invalid
            Set-MyAnalyticsFeatureConfig -Identity [string] -PrivacyMode [opt-in/opt-out]
            Set-MyAnalyticsFeatureConfig -Identity [string] -Feature [dashboard/add-in/digest-email/all] -isEnabled [$true/$false]
            Set-MyAnalyticsFeatureConfig -Identity [string] -PrivacyMode [opt-in/opt-out] -Feature [dashboard/add-in/digest-email/all] -isEnabled [$true/$false]
            </summary>
            <returns>Validity of the command</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.SetMyAnalyticsFeatureConfig.InternalProcessRecord">
            <summary>
            Processes record if command is valid, else throws exception
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.SetMyAnalyticsFeatureConfig.UpdateValuesBasedOnParameters(System.String,System.String,System.Nullable{System.Boolean})">
            <summary>
            Convert the user's input parameters to a JSON
            </summary>
            <param name="privacyMode">PrivacyMode param</param>
            <param name="feature">Feature param</param>
            <param name="isEnabled">isEnabled param</param>
            <returns>Returns MyAnalyticsSettings</returns>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.SetDefaultTenantMyAnalyticsFeatureConfig">
            <summary>
            This class implements "Set-DefaultTenantMyAnalyticsFeatureConfig" Cmdlet
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.SetDefaultTenantMyAnalyticsFeatureConfig.Feature">
            <summary>
            Gets or sets a value indicating the MyAnalytics platform
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.SetDefaultTenantMyAnalyticsFeatureConfig.IsEnabled">
            <summary>
            Gets or sets a value indicating whether the MyAnalytics platform is enabled.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.SetDefaultTenantMyAnalyticsFeatureConfig.SamplingRate">
            <summary>
            Gets or sets a value indicating what the sampling rate is.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.SetDefaultTenantMyAnalyticsFeatureConfig.CmdletName">
            <summary>
            Name of the cmdlet
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.SetDefaultTenantMyAnalyticsFeatureConfig.IsCommandValid">
            <summary>
            Function to validate the command
            Valid command types and the rest are invalid
            Set-DefaultTenantMyAnalyticsFeatureConfig -Feature [dashboard/add-in/digest-email/all] -isEnabled [$true/$false]
            Set-DefaultTenantMyAnalyticsFeatureConfig -Feature Meeting-Effectiveness-Survey-Sampling-Rate -SamplingRate [0.1-0.7]
            </summary>
            <returns>Validity of the command</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.SetDefaultTenantMyAnalyticsFeatureConfig.InternalProcessRecord">
            <summary>
            Processes record if command is valid, else throws exception
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.SetDefaultTenantMyAnalyticsFeatureConfig.UpdateValuesBasedOnParameters(System.String,System.Nullable{System.Boolean},System.Nullable{System.Double},System.Boolean@,System.Boolean@)">
            <summary>
            Convert the user's input parameters to a JSON
            </summary>
            <param name="feature">Feature param</param>
            <param name="isEnabled">isEnabled param</param>
            <param name="samplingRate">samplingRate param, used for MES sampling rate</param>
            <param name="areSeededSettingsChanged">areSeededSettingsChanged param</param>
            <param name="arePremiumSettingsChanged">arePremiumSettingsChanged param</param>
            <returns>Returns MyAnalyticsSettings</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.SetDefaultTenantMyAnalyticsFeatureConfig.MakeAndSendPatchRequests(Microsoft.Exchange.Management.RestApiClient.MyAnalytics.TenantMyAnalyticsSettings,System.Boolean,System.Boolean)">
            <summary>
            Generates patch requests for the requested seeded and premium tenant settings
            </summary>
            <param name="tenantSettings">tenantSettings param</param>
            <param name="areSeededSettingsChanged">areSeededSettingsChanged param</param>
            <param name="arePremiumSettingsChanged">arePremiumSettingsChanged param</param>
            <returns>Returns TenantMyAnalyticsSettings</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.SetDefaultTenantMyAnalyticsFeatureConfig.GenerateSeededPatchRequestBody(Microsoft.Exchange.Management.RestApiClient.MyAnalytics.TenantMyAnalyticsSettings)">
            <summary>
            Generates patch request body for seeded/non-premium settings only
            </summary>
            <param name="tenantSettings">tenantSettings param</param>
            <returns>Returns TenantMyAnalyticsSettings</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.SetDefaultTenantMyAnalyticsFeatureConfig.GeneratePremiumPatchRequestBody(Microsoft.Exchange.Management.RestApiClient.MyAnalytics.TenantMyAnalyticsSettings)">
            <summary>
            Generates patch request body for premium settings only
            </summary>
            <param name="tenantSettings">tenantSettings param</param>
            <returns>Returns TenantMyAnalyticsSettings</returns>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.TenantMyAnalyticsCmdlet`2">
            <summary>
            The base class for the client side MyAnalytics Cmdlets based on Weve implementation.
            </summary>
            <typeparam name="TDomainObject">The type of the domain object.</typeparam>
            <typeparam name="TCmdlet">The type of the cmdlet.</typeparam>
            <seealso cref="T:Microsoft.Exchange.Management.RestApiClient.AdminCmdlet`2" />
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.TenantMyAnalyticsCmdlet`2.UrlStem">
            <summary>
            The myanalytics settings url stem. 
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.TenantMyAnalyticsCmdlet`2.GetMeetingEffectivenessSurveyEnabled(System.String)">
            <summary>
            Returns the associated value if meeting effectiveness survey was opted-in or opted-out
            </summary>
            <param name="meetingEffectivenessValue">represents whether meeting effectiveness surveys are opted-in or not</param>
            <returns>Returns whether Meeting Effectiveness Survey is Opted-In/Opted-Out/Unavailable</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.TenantMyAnalyticsCmdlet`2.GetUpdatedDisplaySettings(System.String,Microsoft.Exchange.Management.RestApiClient.MyAnalytics.TenantMyAnalyticsSettings)">
            <summary>
            Converts the API response to the cmdlet output
            Sample response :
            TenantId                                : xyz
            IsDashboardEnabled                      : False
            IsAddInEnabled                          : False
            IsDigestEmailEnabled                    : True
            IsMeetingEffectivenessSurveyEnabled     : OptedIn
            MeetingEffectivenessSurveySamplingRate  : 0.2
            IsScheduleSendEnabled                   : False
            </summary>
            <param name="tenantId">Tenant's Id</param>
            <param name="responseSettings">updated cmdlet output</param>
            <returns>Updated MyAnalyticsDisplaySettings</returns>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.TenantMyAnalyticsSettings">
            <summary>
            Represents the Tenant's MyAnalytics settings
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.TenantMyAnalyticsSettings.IsDashboardOptedOut">
            <summary>
            Gets or sets the default tenant value of IsDashboardOptedOut.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.TenantMyAnalyticsSettings.IsDigestEmailOptedOut">
            <summary>
            Gets or sets the default tenant value of IsDigestEmailOptedOut.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.TenantMyAnalyticsSettings.IsAddInOptedOut">
            <summary>
            Gets or sets the default tenant value of IsAddInOptedOut.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.TenantMyAnalyticsSettings.MeetingEffectivenessSurvey">
            <summary>
            Gets or sets the default tenant value of MeetingEffectivenessSurvey.
            It is a premium setting.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.TenantMyAnalyticsSettings.MeetingEffectivenessSurveySamplingRate">
            <summary>
            Gets or sets the default tenant value of MeetingEffectivenessSurveySamplingRate.
            It is a premium setting.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.TenantMyAnalyticsSettings.IsScheduleSendOptedOut">
            <summary>
            Gets or sets the default tenant value of IsScheduleSendOptedOut.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.MyAnalytics.TenantMyAnalyticsSettings.ShallowCopy">
            <summary>
            Shallow copy of this TenantMyAnalyticsSettings
            </summary>
            <returns>The copied TenantMyAnalyticsSettings</returns>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.OutputPSObject">
            <summary>
            Output PSObject which controls what should be written to pipeline.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.OutputPSObject.#ctor">
            <summary>
            Default constructor
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.OutputPSObject.#ctor(System.Object)">
            <summary>
            Default Copy constructor
            </summary>
            <param name="obj">input object</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.OutputPSObject.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Default Serializable implementation
            </summary>
            <param name="serializationInfo">SerializationInfo object</param>
            <param name="streamingContext">StreamingContext object</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.OutputPSObject.ToString">
            <summary>
            Override implementation of the ToString method
            </summary>
            <returns>Tostring method</returns>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.RestClientException">
            Important: This attribute is NOT inherited from Exception, and MUST be specified 
            otherwise serialization will fail with a SerializationException stating that
            "Type X in Assembly Y is not marked as serializable."
            <summary>
            Base class of all exceptions thrown by this rest client.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.RestClientException.#ctor">
            <summary>
            RestClientException Constructor
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.RestClientException.#ctor(System.String)">
            <summary>
            RestClientException Constructor
            </summary>
            <param name="message">message param</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.RestClientException.#ctor(System.String,System.Exception)">
            <summary>
            RestClientException Constructor
            </summary>
            <param name="message">message param</param>
            <param name="innerException">innerException param</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.RestClientException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Without this constructor, deserialization will fail
            </summary>
            <param name="info">SerializationInfo param</param>
            <param name="context">StreamingContext param</param>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.ServiceModelUtils">
            <summary>
            Service model utilities.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.ServiceModelUtils.serviceModel">
            <summary>
            Service model instance.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.ServiceModelUtils.GetServiceModel">
            <summary>
            Reads the service model from ODataContext.
            </summary>
            <returns>Service model instance.</returns>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.SetExoAppSettings">
            <summary>
            This class implements "Set-ExoAppSettings" Cmdlet
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.SetExoAppSettings.ConnectionUri">
            <summary>
            Connection URI
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.SetExoAppSettings.ReturnSize">
            <summary>
            Specifies how many number of records will be returned by default
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.SetExoAppSettings.PrintWarningsReceivedFromServer">
            <summary>
            Should we show warnings sent by server to user. 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.SetExoAppSettings.UseBetaEndpoint">
            <summary>
            Specifies if we are using Beta endpoint for the Admin API
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.SetExoAppSettings.MaxRetryTimes">
            <summary>
            Maximum number of times for retry
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.SetExoAppSettings.PageSize">
            <summary>
            Page size
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.SetExoAppSettings.RetryIntervalExponentBase">
            <summary>
            For exponential retries, the base of exponents
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.SetExoAppSettings.RetryIntervalInMilliSeconds">
            <summary>
            Initial wait time between retries in miliseconds
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.SetExoAppSettings.LogLevel">
            <summary>
            Log Level to log
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.SetExoAppSettings.LogDirectoryPath">
            <summary>
            Gets or sets the log directory path parameter.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.SetExoAppSettings.MaxLogSize">
            <summary>
            Gets or sets the log file size
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.SetExoAppSettings.MaxDirectorySize">
            <summary>
            Gets or sets the log directory size
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.SetExoAppSettings.HTTPTimeout">
            <summary>
            Gets or sets the HTTPTimeout parameter.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.SetExoAppSettings.TrackPerformance">
            <summary>
            Switch to Enable Performance Tracking
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.SetExoAppSettings.ShowProgress">
            <summary>
            Flag to enable/disable showing of the number of objects written
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.SetExoAppSettings.EnableErrorReporting">
            <summary>
            Flag to enable/disable multithreading
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.SetExoAppSettings.UseMultithreading">
            <summary>
            Flag to enable/disable multithreading
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.SetExoAppSettings.Credential">
            <summary>
            Gets or sets the Credential parameter, Initialized to a default value to recognize a expilicit null assignment
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.SetExoAppSettings.ProcessRecord">
            <summary>
            Process Record
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.SetExoAppSettings.ValidateParams">
            <summary>
            Validate the param values
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.SetExoAppSettings.ConfigureAppSettings">
            <summary>
            Configure the initial settings of tha app
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.SetExoCasMailbox">
            <summary>
            Cmdlet for Set-ExoCasMailbox
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.SetExoCmdletBase`2">
            <summary>
            Base class for all set exo cmdlets. 
            </summary>
            <typeparam name="TDomainObject">Type of the domain object</typeparam>
            <typeparam name="TCmdLet">Type of the cmdlet</typeparam>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.SetExoCmdletBase`2.propNameToPropInfo">
            <summary>
            Dictionary with entity's property name to corresponding property info.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.SetExoCmdletBase`2.keyPropertyName">
            <summary>
            name of the key property for entity.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.SetExoCmdletBase`2.propertiesToIgnoreAsDynamicParams">
            <summary>
            Ignore these properties as they are defined as regular parameters in base classes.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.SetExoCmdletBase`2.updateRestrictionsTermName">
            <summary>
            Name of the update restrictions annotation in metadata.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.SetExoCmdletBase`2.nonUpdatableNavigationPropertiesName">
            <summary>
            Name of the non updatable navigational properties field.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.SetExoCmdletBase`2.nonUpdatablePropertiesName">
            <summary>
            Field name in UpdateRestrictions annotation which represents non-updatable fields.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.SetExoCmdletBase`2.nonUpdatablePropertiesFromMetadata">
            <summary>
            List of non updatable fields extracted from metadata.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.SetExoCmdletBase`2.dynamicParameterDictionary">
            <summary>
            Dynamic parameters. 
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.SetExoCmdletBase`2.#cctor">
            <summary>
            Static block which initializes entity properties, and metadata parsing.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.SetExoCmdletBase`2.ResultSize">
            <summary>
            Hiding ResultSize parameter as it is not valid for set based cmdlets.
            TODO when cmdlet class hierarchy is refactored move this to GetBulkCmdletBase.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.SetExoCmdletBase`2.GetDynamicParameters">
            <summary>
            Method which returns dynamic parameters based on entity properties.
            </summary>
            <returns>Dynamic parameters dictionary.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.SetExoCmdletBase`2.InternalProcessRecord">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.SetExoCmdletBase`2.SaveNonUpdatablePropertiesInCache(System.Collections.Generic.ISet{System.String})">
            <summary>
            Cache non updatable properties read from metadata.
            </summary>
            <param name="nonUpdatableProperties">Non updatable properties.</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.SetExoCmdletBase`2.GetNonUpdatablePropertiesFromCache">
            <summary>
            Fetch non updatable properties from cache.
            </summary>
            <returns>Non updatable properties from cache.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.SetExoCmdletBase`2.CreateApiProviderContext">
            <summary>
            Create api provider context.
            </summary>
            <returns>Returns created api provider context.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.SetExoCmdletBase`2.ShouldUseIdentityInXAnchorMailbox">
            <summary>
            Returns true if we should use identity in X-AnchorMailbox.
            </summary>
            <returns>true if identity should be used in x-anchormailbox. false otherwise(loggedin user identity is used in x-anchormailbox).</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.SetExoCmdletBase`2.GetListOfParamsShouldForceIdentityInXAnchorMailbox">
            <summary>
            Returns list of parameters, if specified by user, we should add identity as X-AnchorMailbox. 
            </summary>
            <returns>By default returns empty list.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.SetExoCmdletBase`2.CreateApiProvider(Microsoft.Exchange.Management.AdminApiProvider.ApiProviderContext{`0})">
            <summary>
            Create api provider with given context.
            </summary>
            <param name="apiProviderContext">Api provider context.</param>
            <returns>Api provider instance.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.SetExoCmdletBase`2.InvokeApiProviderToUpdateEntity(Microsoft.Exchange.Management.AdminApiProvider.IApiProvider{`0})">
             <summary>
             Update entity by calling api provider. This is hook method for sub classes to customize it.
            
             If sub classes need to handle some custom parameters like invoking action as result of a parameter, override this function and
             implement action.
             </summary>
             <param name="apiProvider">Api provider instance.</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.SetExoCmdletBase`2.UpdateModelEntityObjectWithDynamicParamValues(`0)">
            <summary>
            Update the given entity object with values mentioned in dynamic parameters.
            </summary>
            <param name="entityObject">Entity object to be updated.</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.SetExoCmdletBase`2.SetIdentity(`0,System.String)">
            <summary>
            Update entity object with identity. Sub classes can override it to update the value directly. 
            </summary>
            <param name="entityObject">Entity object.</param>
            <param name="identity">Identity value to be updated.</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.SetExoCmdletBase`2.GetServiceModel">
            <summary>
            Get OData edm model associated with service.
            </summary>
            <returns>Returns the service model</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.SetExoCmdletBase`2.ReadNonUpdatablePropertiesFromMetadata">
            <summary>
            Helper method to read odata metadata and find all non-updatable fields.
            </summary>
            <returns>Set of non updatable fields in entity.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.SetExoCmdletBase`2.GetNonUpdatableFields(System.Collections.Generic.List{Microsoft.OData.Edm.Vocabularies.IEdmPropertyConstructor},System.String)">
            <summary>
            Read non updatable fields from model update restriction properties.
            </summary>
            <param name="properties">Properties for update restriction annotation.</param>
            <param name="fieldName">Field value to read.</param>
            <returns>Non updatable fields as list of strings.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.SetExoCmdletBase`2.TransformPowershellValueToModelValue(System.Object)">
            <summary>
            Transform parameter value specified in powershell cmdline to OData value.
            </summary>
            <param name="value">Value param</param>
            <returns>Returns the transformed object</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.SetExoCmdletBase`2.TransformModelTypeToPowerShellType(System.Type)">
             <summary>
             Transform model property type to powershell property type.
            
             This method converts bool types to switch parameter.
             </summary>
             <param name="type">OData field type.</param>
             <returns>Converted type.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.SetExoCmdletBase`2.GetAllPropertyDefinitionsFromModel">
            <summary>
            Read all property definitions of current entity.
            </summary>
            <returns>Dictionary with property name to property info.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.SetExoCmdletBase`2.GetEntityName">
            <summary>
            Get current entity name.
            </summary>
            <returns>Entity name</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.SetExoCmdletBase`2.GetModelEntityKeyName">
            <summary>
            Helper method to get key property name of model entity.
            </summary>
            <returns>Key name, empty string if key is not defined for this entity.</returns>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.SetExoMailbox">
            <summary>
            Cmdlet for Set-ExoMailbox.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.SetExoMailbox.GetListOfParamsShouldForceIdentityInXAnchorMailbox">
            <summary>
            Return true if we need to use identity in X-AnchorMailbox header.
            </summary>
            <returns>By default returns false.</returns>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.TypeConverter">
            <summary>
            TypeConverter classes.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.TypeConverter.ConvertByteArrayTypeToListOfByteArray(System.Collections.ObjectModel.ObservableCollection{Microsoft.Exchange.Management.AdminApiProvider.ByteArrayType})">
            <summary>
            Convert Observable Collection of ByteArrayType to List of byte Array.
            </summary>
            <param name="propertyCollection"> ByteArrayType Collection</param>
            <returns>List of byte Array</returns>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Unlimited`1">
            <summary>
            Template provide unlimited feature for value type, similar with System.Nullable
            </summary>
            <typeparam name="T">The unlimited type.</typeparam>
            <remarks>
            <para>
            Unlimited feature means either unlimited or value type instance inside.
            If the value is unlimited, getting Value will be not available.
            </para>
            <para>
            Parse and ToString are provided. 
            ToString will return "unlimited" if the instance represent unlimited value.
            Parse from "unlimited" (case-insensitive) is supported.
            </para>
            </remarks>
            <devdoc>
            If need support Parse for more type, please add code in static constructor,
            Parse() and TryParse().
            </devdoc>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.Unlimited`1.unlimitedString">
            <summary>
            Unlimited string constant
            </summary> 
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.Unlimited`1.validTypeMathOperations">
            <summary>
            Array of valid type math operations
            </summary>  
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.Unlimited`1.limitedValue">
            <summary>
            Limited value param 
            </summary> 
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.Unlimited`1.isUnlimited">
            <summary>
            A boolean whether the value is unlimited or not
            </summary> 
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Unlimited`1.#ctor(`0)">
            <summary>
            public constructor
            </summary>
            <param name="limitedValue">limitedValue param</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Unlimited`1.#ctor(System.String)">
            <summary>
            private constructor
            </summary>
            <param name="expression">Expression param</param>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Unlimited`1.UnlimitedValue">
            <summary>
            Represents the unlimited value. This field is read-only. 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Unlimited`1.UnlimitedString">
            <summary>
            When parsing, if inputted string is UnlimitedString, it is parsed as Unlimited. This field is read-only. 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Unlimited`1.IsUnlimited">
            <summary>
            Return whether the instance is unlimited value
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Unlimited`1.Value">
            <summary>
            Return Value inside
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Unlimited`1.Parse(System.String)">
            <summary>
            Parse a string to Unlimited instance
            </summary>
            <param name="expression">Expression param</param>
            <returns>Returns an instance of Unlimited</returns>
            <remarks>for "unlimited", it is case-insensitive</remarks>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Unlimited`1.TryParse(System.String,Microsoft.Exchange.Management.RestApiClient.Unlimited{`0}@)">
            <summary>
            Try to parse a string to Unlimited instance
            </summary>
            <param name="expression">Expression param</param>
            <param name="result">Result param</param>
            <returns>True if parse can succeed</returns>
            <remarks>for "unlimited", it is case-insensitive</remarks>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Unlimited`1.op_Equality(Microsoft.Exchange.Management.RestApiClient.Unlimited{`0},Microsoft.Exchange.Management.RestApiClient.Unlimited{`0})">
            <summary>
            Override == operator
            </summary>
            <param name="value1">Value 1 to compare</param>
            <param name="value2">Value 2 to compare</param>
            <returns>true if equal, otherwise false</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Unlimited`1.op_Inequality(Microsoft.Exchange.Management.RestApiClient.Unlimited{`0},Microsoft.Exchange.Management.RestApiClient.Unlimited{`0})">
            <summary>
            Override != operator
            </summary>
            <param name="value1">Value 1 to compare</param>
            <param name="value2">Value 2 to compare</param>
            <returns>true if not equal, otherwise false</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Unlimited`1.op_GreaterThan(Microsoft.Exchange.Management.RestApiClient.Unlimited{`0},Microsoft.Exchange.Management.RestApiClient.Unlimited{`0})">
            <summary>
            Override > operator
            </summary>
            <param name="value1">Value 1 to compare</param>
            <param name="value2">Value 2 to compare</param>
            <returns>comparison result</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Unlimited`1.op_GreaterThanOrEqual(Microsoft.Exchange.Management.RestApiClient.Unlimited{`0},Microsoft.Exchange.Management.RestApiClient.Unlimited{`0})">
            <summary>
            Override >= operator
            </summary>
            <param name="value1">Value 1 to compare</param>
            <param name="value2">Value 2 to compare</param>
            <returns>comparison result</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Unlimited`1.op_LessThan(Microsoft.Exchange.Management.RestApiClient.Unlimited{`0},Microsoft.Exchange.Management.RestApiClient.Unlimited{`0})">
            <summary>
            Override &lt; operator
            </summary>
            <param name="value1">Value 1 to compare</param>
            <param name="value2">Value 2 to compare</param>
            <returns>comparison result</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Unlimited`1.op_LessThanOrEqual(Microsoft.Exchange.Management.RestApiClient.Unlimited{`0},Microsoft.Exchange.Management.RestApiClient.Unlimited{`0})">
            <summary>
            Override &lt;= operator
            </summary>
            <param name="value1">Value 1 to compare</param>
            <param name="value2">Value 2 to compare</param>
            <returns>comparison result</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Unlimited`1.op_Division(Microsoft.Exchange.Management.RestApiClient.Unlimited{`0},System.Object)">
            <summary>
            Override '/' Operator
            </summary>
            <param name="value1">Value representing the left side operand</param>
            <param name="value2">Object value for right side operand</param>
            <returns>The Unlimited&lt;T&gt; result of the operation (T / value2)</returns>
            <remarks>
            This method will validate that value2 type matches one of the types
            declared in ValidTypeMathOperations, otherwise an exception is thrown.
            </remarks>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Unlimited`1.op_Multiply(Microsoft.Exchange.Management.RestApiClient.Unlimited{`0},System.Object)">
            <summary>
            Override '*' Operator
            </summary>
            <param name="value1">Value representing the left side operand</param>
            <param name="value2">Object value for right side operand</param>
            <returns>The Unlimited&lt;T&gt; result of the operation (T * value2)</returns>
            <remarks>
            This method will validate that the value2 type matches one of the types
            declared in ValidTypeMathOperations, otherwise an exception is thrown.
            </remarks>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Unlimited`1.op_Addition(Microsoft.Exchange.Management.RestApiClient.Unlimited{`0},System.Object)">
            <summary>
            Override '+' Operator
            </summary>
            <param name="value1">Value representing the left side operand</param>
            <param name="value2">Object value for right side operand</param>
            <returns>The Unlimited&lt;T&gt; result of the operation (T + value2)</returns>
            <remarks>
            This method will validate that the value2 type matches one of the types
            declared in ValidTypeMathOperations, otherwise an exception is thrown.
            </remarks>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Unlimited`1.op_Subtraction(Microsoft.Exchange.Management.RestApiClient.Unlimited{`0},System.Object)">
            <summary>
            Override '-' Operator
            </summary>
            <param name="value1">Value representing the left side operand</param>
            <param name="value2">Object value for right side operand</param>
            <returns>The Unlimited&lt;T&gt; result of the operation (T - value2)</returns>
            <remarks>
            This method will validate that the value2 type matches one of the types
            declared in ValidTypeMathOperations, otherwise an exception is thrown.
            </remarks>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Unlimited`1.op_Implicit(`0)~Microsoft.Exchange.Management.RestApiClient.Unlimited{`0}">
            <summary>
            Cast from value type instance
            </summary>
            <param name="fromValue">From value param</param>
            <returns>Returns an instance of Unlimited</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Unlimited`1.op_Explicit(Microsoft.Exchange.Management.RestApiClient.Unlimited{`0})~`0">
            <summary>
            Cast to value type instance
            </summary>
            <param name="fromValue">fromValue param</param>
            <returns>Returns the value</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Unlimited`1.Equals(System.Object)">
            <summary>
            Return whether equals to other object
            </summary>
            <param name="other">Other param</param>
            <returns>Returns boolean whether the other object is equal or not</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Unlimited`1.Equals(`0)">
            <summary>
            Implemetation of interface IEquatable members
            </summary>
            <param name="other">T instance to verify equality</param>
            <returns>true if T is equals</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Unlimited`1.GetHashCode">
            <summary>
            Returns the hash code for this instance.
            </summary>
            <returns>Returns the hash code</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Unlimited`1.ToString">
            <summary>
            Return string to prepresent this instance
            </summary>
            <returns>Returns the string </returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Unlimited`1.ToString(System.String,System.IFormatProvider)">
            <summary>
            Method for IFormattable
            </summary>
            <param name="format">Format param</param>
            <param name="formatProvider">formatProvider param</param>
            <returns>String representation of object</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Unlimited`1.CompareTo(Microsoft.Exchange.Management.RestApiClient.Unlimited{`0})">
            <summary>
            Compare with same type instance
            </summary>
            <param name="other">Other param</param>
            <returns>Returns by comparing with same type instance</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Unlimited`1.System#IComparable#CompareTo(System.Object)">
            <summary>
            Compare with object
            </summary>
            <param name="other">Other object</param>
            <returns>Compares the object</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Unlimited`1.CompareTo(`0)">
            <summary>
            Implementation of interface IComparable
            </summary>
            <param name="other">T instance to comapre</param>
            <returns>Compares the object</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Unlimited`1.Equals(Microsoft.Exchange.Management.RestApiClient.Unlimited{`0})">
            <summary>
            Return whether equals to other instance
            </summary>
            <param name="other">Other param</param>
            <returns> Returns whether equals to other instance</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Unlimited`1.ExecDynamicOperation(Microsoft.Exchange.Management.RestApiClient.Unlimited{`0},System.Object,System.String)">
            <summary>
            1. Verifies that the operation is valid for the given value1 and value2 types
            2. Executes the DynamicResolveOperation for the operationName between value1.T and value2.
            3. Returns the result of the operation as Unlimited&lt;T&gt;
            </summary>
            <param name="value1">Value1 param</param>
            <param name="value2">Value2 param</param>
            <param name="operationName">Operation name</param>
            <returns>Returns unlimited object</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Unlimited`1.UnBucketT(System.Object)">
            <summary>
            If value is Unlimited&lt;T&gt;, this method returns T otherwise returns value.
            </summary>
            <param name="value">Value param</param>
            <returns>Returns T if value is unlimited else value</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Unlimited`1.IsValidRightOperand(System.Object)">
            <summary>
            Returns true only if value is valid Right operand for
            this Unlimited&lt;T&gt;
            </summary>
            <param name="value">Value param</param>
            <returns>Returns true only if value is valid Right operand for
            this Unlimited</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Unlimited`1.DynamicResolveOperation(Microsoft.Exchange.Management.RestApiClient.Unlimited{`0},System.Object,System.String)">
            <summary>
            Using reflection will try to resolve the following method 
            in T: operationName(typeof(T), typeof(value2))
            The result of calling the method is returned.
            </summary>
            <param name="value1">Left side operand</param>
            <param name="value2">Right side operand</param>
            <param name="operationName">Operation name</param>
            <returns>value1.Value 'operationName' value2</returns>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.VivaInsights.DiscoverTryBuySettings">
            <summary>
            Represents the DiscoverTryBuy settings
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.VivaInsights.DiscoverTryBuySettings.IsInsightsDiscoverTryBuyDisabled">
            <summary>
            Gets or sets the DiscoverTryBuy content control setting.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.VivaInsights.EmfAdminDisplaySettings">
            <summary>
            Represents the output of the cmdlet to display Emf Admin Settings.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.VivaInsights.EmfAdminDisplaySettings.UserId">
            <summary>
            The user id.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.VivaInsights.EmfAdminDisplaySettings.MeetingEffectivenessMode">
            <summary>
            The Meeting Effectiveness Mode, shows whether EMF is enabled by the Admin.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.VivaInsights.EmfAdminMode">
            <summary>
            This enum defines the possible emf admin modes
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.VivaInsights.EmfAdminMode.None">
            <summary>
            default value
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.VivaInsights.EmfAdminMode.Disabled">
            <summary>
            The emf experience is disabled
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.VivaInsights.EmfAdminMode.Enabled">
            <summary>
            The emf experience is enabled
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.VivaInsights.EmfAdminSettings">
            <summary>
            Represents the Emf Admin settings
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.VivaInsights.EmfAdminSettings.AdminMode">
            <summary>
            The user's EMF Admin Mode
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.VivaInsights.FeatureSettings">
            <summary>
            Represents the Feauture settings
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.VivaInsights.FeatureSettings.IsInsightsHeadspaceDisabled">
            <summary>
            Gets or sets the headspace content control setting.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.VivaInsights.FeatureSettings.IsAutoDelayDeliveryOptedIn">
            <summary>
            Gets or sets the delay-delivery content control setting.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.VivaInsights.GetVivaInsightsSettings">
            <summary>
            This class implements "Get-VivaInsightsSettings" Cmdlet
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.VivaInsights.GetVivaInsightsSettings.Feature">
            <summary>
            Gets or sets a value indicating a feature in VivaInsights
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.VivaInsights.GetVivaInsightsSettings.CmdletName">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.VivaInsights.GetVivaInsightsSettings.InternalProcessRecord">
            <summary>
            Because this class overrides InternalProcessRecord(), this method is unused.
            </summary>
            <returns>Throws new exception</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.VivaInsights.GetVivaInsightsSettings.GetUserSettings(System.Object@)">
            <summary>
            Makes the http GET request for User Settings.
            </summary>
            <param name="userDisplaySettings">the User Display Settings</param>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.VivaInsights.MyAUserDisplaySettings">
            <summary>
            Represents the output of the cmdlet to display MyA User Settings.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.VivaInsights.MyAUserDisplaySettings.UserId">
            <summary>
            The user id.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.VivaInsights.MyAUserDisplaySettings.IsInsightsHeadspaceEnabled">
            <summary>
            If Headspace content is enabled.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.VivaInsights.MyAUserDisplaySettings.IsScheduleSendSuggestionsOptedIn">
            <summary>
            If delay-delivery content is opted in.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.VivaInsights.MyAUserDisplaySettings.IsInsightsDiscoverTryBuyEnabled">
            <summary>
            DiscoverTryBuy content is enabled/disabled.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.VivaInsights.MyAUserSettings">
            <summary>
            Represents the MyA User Settings for Viva Insights.
            </summary>
            <remarks>
            This is different from the MyAnalyticsSettings object because these are settings that are part of
            Viva Insights and hence can only be controlled by the Get/Set-VivaInsightsSettings cmdlets.
            </remarks>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.VivaInsights.MyAUserSettings.FeatureSettings">
            <summary>
            FeatureSettings of the user
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.VivaInsights.MyAUserSettings.DiscoverTryBuySettings">
            <summary>
            DiscoverTryBuySettings of the user
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.VivaInsights.MyAUserSettingsBuilder">
            <summary>
            Represents the Builder class of MyA User Settings
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.VivaInsights.MyAUserSettingsBuilder.userSettings">
            <summary>
            internal MyAUserSettings object
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.VivaInsights.MyAUserSettingsBuilder.#ctor">
            <summary>
            Constructor of MyAUserSettingsBuilder
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.VivaInsights.MyAUserSettingsBuilder.SetHeadspaceFeature(System.Boolean)">
            <summary>
            Set Headspace Feature
            </summary>
            <param name="enabled">Enabled Flag</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.VivaInsights.MyAUserSettingsBuilder.SetDelayDeliveryFeature(System.Boolean)">
            <summary>
            Set Delay-delivery Feature
            </summary>
            <param name="enabled">Enabled Flag</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.VivaInsights.MyAUserSettingsBuilder.SetDiscoverTryBuyFeature(System.Boolean)">
            <summary>
            Set DiscoverTryBuySettings
            </summary>
            <param name="enabled">Enabled Flag</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.VivaInsights.MyAUserSettingsBuilder.GetMyAUserSettings">
            <summary>
            Get MyAUserSettings Object
            </summary>
            <returns>Will return MyAUSerSettings with specific feature settings</returns>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.VivaInsights.SetVivaInsightsSettings">
            <summary>
            This class implements "Set-VivaInsightsSettings" Cmdlet
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.VivaInsights.SetVivaInsightsSettings.Enabled">
            <summary>
            Gets or sets a value indicating whether the setting is enabled.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.VivaInsights.SetVivaInsightsSettings.Feature">
            <summary>
            Gets or sets a value indicating a feature in VivaInsights
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.VivaInsights.SetVivaInsightsSettings.CmdletName">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.VivaInsights.SetVivaInsightsSettings.InternalProcessRecord">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.VivaInsights.SetVivaInsightsSettings.IsCommandValid">
            <summary>
            Function to validate the command e.g.
            Set-VivaInsightsSettings -Identity [smtp_addr] -Feature [headspace/schedulesend] -Enabled [$true/$false]
            </summary>
            <returns>Validity of the command</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.VivaInsights.SetVivaInsightsSettings.PatchUserSettings(System.Object@)">
            <summary>
            Makes the http PATCH request for User Settings.
            </summary>
            <param name="userDisplaySettings">the User Display Settings</param>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.VivaInsights.VivaInsightsSettings">
            <summary>
            Represents the all the settings used in VivaInsights
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.VivaInsights.VivaInsightsSettings.MyAUserSettings">
            <summary>
            MyA Settings of the user
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.VivaInsights.VivaInsightsSettings.EmfAdminSettings">
            <summary>
            Emf Admin Settings of the user
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.VivaInsights.VivaInsightsSettingsCmdlet`2">
            <summary>
            The base class for VivaInsights Cmdlets based on Weve implementation.
            </summary>
            <typeparam name="TDomainObject">The type of the domain object.</typeparam>
            <typeparam name="TCmdlet">The type of the cmdlet.</typeparam>
            <seealso cref="!:Microsoft.Exchange.Management.RestApiClient.MyAnalyticsCmdlet&lt;TDomainObject, TCmdlet&gt;" />
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.VivaInsights.VivaInsightsSettingsCmdlet`2.GetEmfAdminSettingsUri">
            <summary>
            Gets the EmfAdminSettings URL.
            
            Our URL has 3  parts : 
            1.  Host :  we get it from ConnectionUri
            2.  ApplicationPath :  We get it from AutoDiscover
            3.  myA settings API path :  we get it from hardcoded string - emfAdminSettingsUrlStem
            
            </summary>
            <returns>The current Emf Admin settings Uri.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.VivaInsights.VivaInsightsSettingsCmdlet`2.GetUserDisplaySettings(Microsoft.Exchange.Management.RestApiClient.VivaInsights.MyAUserSettings,System.String)">
            <summary>
            Converts the API response to the cmdlet output for User Settings
            Sample response :
            UserId                      : xyz
            IsInsightsHeadspaceEnabled  : True
            IsAutoDelayDeliveryOptedIn  : True
            </summary>
            <param name="responseSettings">the response settings</param>
            <param name="feature">parameter of feature</param>
            <returns>The User Display Settings</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.VivaInsights.VivaInsightsSettingsCmdlet`2.GetEmfAdminDisplaySettings(Microsoft.Exchange.Management.RestApiClient.VivaInsights.EmfAdminSettings)">
            <summary>
            Converts the API response to the cmdlet output for Emf Admin Settings
            Sample response :
            UserId                      : xyz
            MeetingEffectivenessMode    : Enabled
            </summary>
            <param name="responseSettings">the response settings</param>
            <returns>The Emf Admin Display Settings</returns>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.VivaInsights.VivaInsightsSettingsConstants">
            <summary>
            Consists of all hard-coded strings
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.VivaInsights.VivaInsightsSettingsConstants.Headspace">
            <summary>
            Headspace feature name
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.VivaInsights.VivaInsightsSettingsConstants.DelayDelivery">
            <summary>
            Delay-delivery feature name
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.VivaInsights.VivaInsightsSettingsConstants.DiscoverTryBuy">
            <summary>
            Discover Try Buy feature name
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.VivaInsights.VivaInsightsSettingsConstants.MeetingEffectivenessSurvey">
            <summary>
            The Meeting Effectiveness Survey feature name
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.VivaInsights.VivaInsightsSettingsConstants.UserSettings">
            <summary>
            The User Settings name
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.VivaInsights.VivaInsightsSettingsConstants.EmfAdminSettings">
            <summary>
            The Emf Admin Settings name
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Viva.Common.CAEException">
            <summary>
            Exception class specifically for Contious Access Evaluation (CAE) failures
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.Common.CAEException.StatusCode">
            <summary>
            The status code of the error response.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.Common.CAEException.#ctor(System.String)">
            <summary>
            Constructor creates an object of type <see cref="T:Microsoft.Exchange.Management.RestApiClient.Viva.Common.CAEException"/>
            </summary>
            <param name="claims">Claims returned by Auth</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.Common.CAEException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Constructor creates an object of type <see cref="T:Microsoft.Exchange.Management.RestApiClient.Viva.Common.CAEException"/>
            </summary>
            <param name="serializationInfo">Serialization info</param>
            <param name="streamingContext">Streaming context</param>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Viva.Common.IVivaHttpProvider">
            <summary>
            Provides a means of sending http requests
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.Common.IVivaHttpProvider.SendHttpRequestAsync(System.Net.Http.HttpRequestMessage)">
            <summary>
            Sends the http request and returns the response.
            </summary>
            <param name="requestMessage">The configured request message to send.</param>
            <returns>The http response message from the sent request.</returns>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Viva.Common.IVivaHttpResponseHandler">
            <summary>
            Interface for handling Viva service Http reponse messages
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.Common.IVivaHttpResponseHandler.HandleGFACServiceResponseAsync``1(System.Net.Http.HttpResponseMessage)">
            <summary>
            Handles the given response and returns the deserialized content.
            </summary>
            <typeparam name="T">The expected return content on a success response.</typeparam>
            <param name="response">The http response to process.</param>
            <returns>The deserialized content in a case of a success response.</returns>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Viva.Common.UriValidator">
            <summary>
            Provides means of validating URI's
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.Common.UriValidator.CreateAndValidateURI(System.Uri,System.Uri)">
            <summary>
            Creates a URI from the provided base and relative URI.
            Validates that the orginal URI strings are well formatted and that the base URI
            is still the  base URI of the final returned URI.
            </summary>
            <param name="baseUri">The base URI of the final URI.</param>
            <param name="relativeUri">The relative URI of the final URI.</param>
            <returns>The combined URI.</returns>
            <exception cref="T:System.InvalidOperationException">Thrown when the URI parameters are not well formatted or the base of the URI is changed in the final URI.</exception>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.AddVivaModuleFeaturePolicy">
            <summary>
            This class implements "Add-VivaModuleFeaturePolicy" Cmdlet, which adds a new Policy to a Module Feature.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.AddVivaModuleFeaturePolicy.ModuleId">
            <summary>
            Gets a value indicating a module in Viva
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.AddVivaModuleFeaturePolicy.FeatureId">
            <summary>
            Gets a value indicating a feature in Viva
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.AddVivaModuleFeaturePolicy.Name">
            <summary>
            The name of the policy
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.AddVivaModuleFeaturePolicy.IsFeatureEnabled">
            <summary>
            Determines whether the policy acts to enable or disable the feature
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.AddVivaModuleFeaturePolicy.IsUserControlEnabled">
            <summary>
            Determines whether the policy acts to enable or disable the user control
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.AddVivaModuleFeaturePolicy.IsUserOptedInByDefault">
            <summary>
            Determines the default opt-in state for users when user control is enabled
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.AddVivaModuleFeaturePolicy.GroupIds">
            <summary>
            The group ids to apply the policy to
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.AddVivaModuleFeaturePolicy.UserIds">
            <summary>
            The user ids to apply the policy to
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.AddVivaModuleFeaturePolicy.Everyone">
            <summary>
            Whether to apply to policy to all users
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.AddVivaModuleFeaturePolicy.InternalProcessRecord">
            <summary>
            The business logic specific to this cmdlet.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.AddVivaModuleFeaturePolicy.CreateModuleFeaturePolicy">
            <summary>
            Create Module Feature Policy
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.GetVivaModuleFeature">
            <summary>
            This class implements "Get-VivaModuleFeature" Cmdlet to fetch the specified feature(s) and display the feature data.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.GetVivaModuleFeature.ModuleId">
            <summary>
            The module identifier of the feature(s) to get.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.GetVivaModuleFeature.FeatureId">
            <summary>
            The feature identifier of the feature to get.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.GetVivaModuleFeature.InternalProcessRecord">
            <summary>
            The business logic specific to this cmdlet.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.GetVivaModuleFeatureEnablement">
            <summary>
            This class implements "Get-VivaModuleFeatureEnablement" Cmdlet. Gets the effective enablement of the user to the specific module feature.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.GetVivaModuleFeatureEnablement.ModuleId">
            <summary>
            The module identifier for which the feature belongs.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.GetVivaModuleFeatureEnablement.FeatureId">
            <summary>
            The feature identifier of which the enablement relates to.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.GetVivaModuleFeatureEnablement.Identity">
            <summary>
            The identity the enablement for the feature refers to.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.GetVivaModuleFeatureEnablement.InternalProcessRecord">
            <summary>
            The business logic specific to this cmdlet.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.GetVivaModuleFeaturePolicy">
            <summary>
            This class implements "Get-VivaModuleFeaturePolicy" Cmdlet, 
            which gets the specific policy or policies and displays the policy data.
            This can either be a feature level policy.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.GetVivaModuleFeaturePolicy.ModuleId">
            <summary>
            The module identifier of the policy or policies to get.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.GetVivaModuleFeaturePolicy.FeatureId">
            <summary>
            The feature identifier of the policy or policies to get.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.GetVivaModuleFeaturePolicy.PolicyId">
            <summary>
            The policy identifier of the policy to get.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.GetVivaModuleFeaturePolicy.InternalProcessRecord">
            <summary>
            The business logic specific to this cmdlet.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.RemoveVivaModuleFeaturePolicy">
            <summary>
            This class implements "Remove-VivaModuleFeaturePolicy" Cmdlet. Removes the specifed policy from the specified module feature.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.RemoveVivaModuleFeaturePolicy.ModuleId">
            <summary>
            The module identifier of the policy to delete.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.RemoveVivaModuleFeaturePolicy.FeatureId">
            <summary>
            The feature identifier of the policy to delete.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.RemoveVivaModuleFeaturePolicy.PolicyId">
            <summary>
            The policy identifier of the policy to delete.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.RemoveVivaModuleFeaturePolicy.InternalProcessRecord">
            <summary>
            The business logic specific to this cmdlet.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.RemoveVivaModuleFeaturePolicy.DeleteModuleFeaturePolicy">
            <summary>
            Delete Module Feature Policy
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.UpdateVivaModuleFeaturePolicy">
            <summary>
            Implements the "Update-VivaModuleFeaturePolicy" Cmdlet. Updates the specified policy with any of the attributes
            provided. Any provided data related to the access control list will override the existing access control list for the policy.
            This can either be a feature level policy.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.UpdateVivaModuleFeaturePolicy.ModuleId">
            <summary>
            The module identifier of the policy to update.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.UpdateVivaModuleFeaturePolicy.FeatureId">
            <summary>
            The feature identifier of the policy to update.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.UpdateVivaModuleFeaturePolicy.PolicyId">
            <summary>
            The policy identifier of the policy to update.
            </summary>        
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.UpdateVivaModuleFeaturePolicy.Name">
            <summary>
            The name the admin would like to change the policy's name to.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.UpdateVivaModuleFeaturePolicy.IsFeatureEnabled">
            <summary>
            The new feature disablement status the admin would like to set for the policy.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.UpdateVivaModuleFeaturePolicy.IsUserControlEnabled">
            <summary>
            Determines whether the policy acts to enable or disable the user control
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.UpdateVivaModuleFeaturePolicy.IsUserOptedInByDefault">
            <summary>
            Determines the default opt-in state for users when user control is enabled
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.UpdateVivaModuleFeaturePolicy.GroupIds">
            <summary>
            The new set of group ids the admin would like to apply the policy to.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.UpdateVivaModuleFeaturePolicy.UserIds">
            <summary>
            The new set of user ids the admin would like to apply the policy to.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.UpdateVivaModuleFeaturePolicy.Everyone">
            <summary>
            Indicates the admin would like to update whether the policy to apply to everyone.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.UpdateVivaModuleFeaturePolicy.InternalProcessRecord">
            <summary>
            The business logic specific to this cmdlet.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.UpdateVivaModuleFeaturePolicy.IsEmptyAccessControlList(Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.AccessInformation[])">
            <summary>
            Determines if the provided access control list is null or empty.
            </summary>
            <param name="accessControlList">The access control list.</param>
            <returns>Whether the provided list is null or empty.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.UpdateVivaModuleFeaturePolicy.IsDisabled(System.Boolean,System.Nullable{System.Boolean})">
            <summary>
            This is to convert the double negative scenario on IsFeatureDisabled and match with service side.
            </summary>
            <param name="oldPolicyIsDisabled">IsFeatureDisabled flag in the old policy</param>
            <param name="newPolicyIsEnabled">IsFeatureEnabled flag in new policy</param>
            <returns>If feature is disabled or not</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Cmdlets.UpdateVivaModuleFeaturePolicy.UpdateModuleFeaturePolicy">
            <summary>
            Update Module Feature Policy
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.GFACConstants">
            <summary>
            Contains the constants for the various endpoints of the GFAC service 
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.GFACConstants.FeaturePolicy">
            <summary>
            The string used for parameter set name
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.GFACConstants.ApiV1">
            <summary>
            The API path prefix for V! of the GFAC API
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.GFACConstants.BaseUri">
            <summary>
            The base URI for the GFAC service.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.GFACConstants.VivaModuleFeatureUri(System.String,System.String)">
            <summary>
            The endpoint to interact with a specific Feature of a Viva Module.
            </summary>
            <param name="moduleId">The module identifier.</param>
            <param name="featureId">The feature identifier.</param>
            <returns>The URI for the endpoint.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.GFACConstants.AllVivaModuleFeaturesUri(System.String)">
            <summary>
            The endpoint to interact with all the Features of a Viva Module.
            </summary>
            <param name="moduleId">The module identifier.</param>
            <returns>The URI for the endpoint.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.GFACConstants.VivaModuleFeaturePolicyUri(System.String,System.String,System.String)">
            <summary>
            The endpoint to interact with a specific Policy of a Viva Module Feature.
            </summary>
            <param name="moduleId">The module identifier.</param>
            <param name="featureId">The feature identifier.</param>
            <param name="policyId">The policy identifier.</param>
            <returns>The URI for the endpoint.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.GFACConstants.AllVivaModuleFeaturePolicyUri(System.String,System.String)">
            <summary>
            The endpoint to interact with all Policies of a Viva Module Feature.
            </summary>
            <param name="moduleId">The module identifier.</param>
            <param name="featureId">The feature identifier.</param>
            <returns>The URI for the endpoint.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.GFACConstants.UsersEffectiveEnablementUri(System.String,System.String,System.String)">
            <summary>
            The endpoint to interact with a specific email's effective enablement for a Module Feature.
            </summary>
            <param name="moduleId">The module identifier.</param>
            <param name="featureId">The feature identifier.</param>
            <param name="email">The user or group's identifier</param>
            <returns>The URI for the endpoint.</returns>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.AccessInformation">
            <summary>
            This is the access information that defines to whom the policy is applied.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.AccessInformation.EveryoneTypeId">
            <summary>
            The Id used if the Access Type of the Access Information is "Everyone".
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.AccessInformation.Type">
            <summary>
            Type of access defined by admins.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.AccessInformation.Id">
            <summary>
            The identifier of the entity to which the access information applies.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.AccessInformation.#ctor(Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.AccessType,System.String)">
            <summary>
            Constructor for the <see cref="T:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.AccessInformation"/> class.
            </summary>
            <param name="type">The type of entity the id refers to.</param>
            <param name="id">The id of the entity the access information refers to.</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.AccessInformation.CreateAccessInformationArray(System.String[],System.String[],System.Nullable{System.Boolean})">
            <summary>
            Creates an array of access information based on the provided access specifications.
            </summary>
            <param name="userIds">The user access types to add to the access information array.</param>
            <param name="groupIds">The group access types to add to the access information array.</param>
            <param name="everyone">Whether to add the everyone access type to the access information array.</param>
            <returns>The array of access information.</returns>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.AccessType">
            <summary>
            Access type.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.AccessType.User">
            <summary>
            Access defined for a specific user.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.AccessType.Group">
            <summary>
            Access defined for a specific group.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.AccessType.Everyone">
            <summary>
            Access defined for all users in the tenant.
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.EffectiveEnablement">
            <summary>
            Represents the effective enablement of a specific Feature.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.EffectiveEnablement.FeatureId">
            <summary>
            The identifier of the feature.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.EffectiveEnablement.Enabled">
            <summary>
            Whether the feature is effectively enabled.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.EffectiveEnablement.#ctor(System.String,System.Boolean)">
            <summary>
            Constructor for the <see cref="T:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.EffectiveEnablement"/> class.
            </summary>
            <param name="featureId">The identifier of the feature.</param>
            <param name="enabled">Whether the feature is effectively enabled.</param>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.Feature">
            <summary>
            Model for Feature.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.Feature.Id">
            <summary>
            The string representing the unique feature. This will be provided by module owner and 
            will be validate at through PR pipeline automation for uniqueness. 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.Feature.Title">
            <summary>
            Title/Name of the feature. This should be localizable. 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.Feature.IsUserControlAvailable">
            <summary>
            If user control is available.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.Feature.IsUserOptedInByDefault">
            <summary>
            If user opted in by default.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.Feature.IsAdminAllowedToToggleUserControl">
            <summary>
            If admin can toggle user control.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.Feature.Description">
            <summary>
            Long description of the feature. Max size is 300 characters. 
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.Feature.ShortDescription">
            <summary>
            Short description of the feature. This will be needed for UX.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.Feature.IsFeatureDisabledByDefault">
            <summary>
            If the feature is disabled by default.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.Feature.PolicyModes">
            <summary>
            List of the policy enablement modes supported by the feature.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.Feature.ParentFeature">
            <summary>
            Parent feature of the feature.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.Feature.ChildFeatures">
            <summary>
            List of child features of the feature.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.Feature.PolicyScopes">
            <summary>
            List of the policy scopes supported by the feature.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.Feature.#ctor(System.String,System.String,System.Boolean,System.Boolean,System.Boolean,System.String,System.String,System.Boolean,System.String[],System.String,System.String[],System.String[])">
            <summary>
            Constructor for the <see cref="T:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.Feature"/> class
            </summary>
            <param name="id">The id of the feature.</param>
            <param name="title">Title/Name of the feature.</param>       
            <param name="isUserControlAvailable">If user control is available.</param>       
            <param name="isUserOptedInByDefault">If user opted in by default.</param>       
            <param name="isAdminAllowedToToggleUserControl">If admin can toggle user control.</param>
            <param name="description">Long description of the feature.</param>
            <param name="shortDescription">Short description of the feature.</param>
            <param name="isFeatureDisabledByDefault">If the feature is disabled by default.</param>
            <param name="policyModes">List of the policy enablement modes supported by the feature.</param>
            <param name="parentFeature">Parent feature of the feature.</param>
            <param name="childFeatures">List of child features of the feature.</param>
            <param name="policyScopes">List of the policy scopes supported by the feature.</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.Feature.CreateFeatureDisplay">
            <summary>
            Creates an object used for friendly display of the policy within PowerShell.
            </summary>
            <returns>The write-friendly object for display in PowerShell.</returns>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.Policy">
            <summary>
            Policy model class.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.Policy.Name">
            <summary>
            Name of the policy.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.Policy.FeatureId">
            <summary>
            Feature id to which the policy is associated with.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.Policy.PolicyId">
            <summary>
            The unique identifier for the policy.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.Policy.ModifiedOn">
            <summary>
            Time when policy was last modified.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.Policy.CreatedOn">
            <summary>
            Time when policy was created.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.Policy.IsFeatureDisabled">
            <summary>
            Whether the policy serves to enable or disable the associated feature for the associated access control list.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.Policy.IsUserControlEnabled">
            <summary>
            Whether the policy serves to enable or disable the user control.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.Policy.IsUserOptedInByDefault">
            <summary>
            Default opt-in state for users when user control is enabled.
            When null, the default from feature configuration is used.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.Policy.AccessControlList">
            <summary>
            Access control information to which this policy is applied.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.Policy.#ctor(System.String,System.String,System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Boolean,System.Nullable{System.Boolean},System.Nullable{System.Boolean},Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.AccessInformation[])">
            <summary>
            Constructor for the <see cref="T:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.Policy"/> class
            </summary>
            <param name="name">Name of the policy.</param>
            <param name="featureId">Feature id to which the policy is associated with.</param>
            <param name="policyId">The unique identifier for the policy.</param>
            <param name="modifiedOn">Time when policy was last modified.</param>
            <param name="createdOn">Time when policy was created.</param>
            <param name="isFeatureDisabled">Whether the policy serves to enable or disable the associated feature for the associated access control list.</param>
            <param name="isUserControlEnabled">Whether the policy serves to enable or disable the user control.</param>
            <param name="isUserOptedInByDefault">Default opt-in state for users when user control is enabled.</param> 
            <param name="accessControlList">Access control information to which this policy is applied.</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.Policy.CreatePolicyDisplay">
            <summary>
            Creates an object used for friendly display of the policy within PowerShell.
            </summary>
            <returns>The write-friendly object for display in PowerShell.</returns>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.PolicyDetail">
            <summary>
            PolicyDetail model class. Represents the subset of policy information that an admin can modify.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.PolicyDetail.Name">
            <summary>
            The name of the policy.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.PolicyDetail.IsFeatureDisabled">
            <summary>
            Whether the policy serves to enable or disable the associated feature for the associated access control list.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.PolicyDetail.IsUserControlEnabled">
            <summary>
            Whether the policy serves to enable or disable the user control.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.PolicyDetail.IsUserOptedInByDefault">
            <summary>
            Default opt-in state for users when user control is enabled.
            When null, the default from feature configuration is used.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.PolicyDetail.AccessControlList">
            <summary>
            Access control information to which this policy is applied.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.PolicyDetail.#ctor(System.String,System.Boolean,System.Nullable{System.Boolean},System.Nullable{System.Boolean},Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.AccessInformation[])">
            <summary>
            Constructor for the <see cref="T:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.PolicyDetail"/> class
            </summary>
            <param name="name">The name of the policy.</param>
            <param name="isFeatureDisabled">Whether the policy serves to enable or disable the associated feature for the associated access control list.</param>
            <param name="isUserControlEnabled">Whether the policy serves to enable or disable the user control.</param>     
            <param name="isUserOptedInByDefault">Default opt-in state for users when user control is enabled.</param>
            <param name="accessControlList">Access control information to which this policy is applied.</param>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Service.VivaGFACHttpProvider">
            <summary>
            Provides an http interface for Viva GFAC
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Service.VivaGFACHttpProvider.SendHttpRequestAsync(System.Net.Http.HttpRequestMessage)">
            <summary>
            Sends the request message and returns the response.
            </summary>
            <param name="requestMessage">The configured request message to send.</param>
            <returns>The response from the http request.</returns>
            <exception cref="T:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Service.VivaGFACServiceException">Thrown when exception encountered while sending the request.</exception>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Service.VivaGFACHttpResponseHandler">
            <summary>
            Handles responses from the Viva GFAC service.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Service.VivaGFACHttpResponseHandler.ExceptionMessagePrefix">
            <summary>
            The prefix used for error messages thrown by the response handler
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Service.VivaGFACHttpResponseHandler.asyncConsoleLogger">
            <summary>
            Logger to log the response
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Service.VivaGFACHttpResponseHandler.#ctor(Microsoft.Exchange.Management.AdminApiProvider.AsyncConsoleLogger)">
            <summary>
            Constructor for the <see cref="T:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Service.VivaGFACHttpResponseHandler"/> class.
            </summary>
            <param name="asyncConsoleLogger">async console logger</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Service.VivaGFACHttpResponseHandler.HandleGFACServiceResponseAsync``1(System.Net.Http.HttpResponseMessage)">
            <summary>
            Handles the given response and returns the deserialized content.
            </summary>
            <typeparam name="T">The expected return content on a success response.</typeparam>
            <param name="responseMessage">The http response to process.</param>
            <returns>The deserialized content in a case of a success response.</returns>
            <exception cref="T:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Service.VivaGFACServiceException">Thrown in cases of error or invalid responses.</exception>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Service.VivaGFACHttpResponseHandler.DeserializeSuccessResponseAsync``1(System.Net.Http.HttpResponseMessage)">
            <summary>
            Deserializes the Json response string into the indicated object type.
            </summary>
            <typeparam name="T">The object type to deserialize to.</typeparam>
            <param name="responseMessage">The success response to deserialize.</param>
            <returns>The deserialized response content.</returns>
            <exception cref="T:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Service.VivaGFACServiceException">Thrown when the object cannot be deserialized properly.</exception>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Service.VivaGFACHttpResponseHandler.CreateVivaGFACServiceExceptionAsync(System.Net.Http.HttpResponseMessage)">
            <summary>
            Creates a <see cref="T:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Service.VivaGFACServiceException"/> based on the response message data.
            </summary>
            <param name="responseMessage">The response message to create the service exception from.</param>
            <returns><see cref="T:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Service.VivaGFACServiceException"/> based on the response message data</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Service.VivaGFACHttpResponseHandler.GetErrorStatusCodeMessage(System.Nullable{System.Net.HttpStatusCode})">
            <summary>
            Creates an error message depending on the provided status code.
            </summary>
            <param name="statusCode">The status code to create the message from.</param>
            <returns>The error message.</returns>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Service.VivaGFACServiceErrorCode">
            <summary>
            Enumeration of service error codes from the GFAC Service
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Service.VivaGFACServiceErrorResponse">
            <summary>
            Class representing the additional content provided as part of unsuccessful GFAC Service responses.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Service.VivaGFACServiceErrorResponse.Code">
            <summary>
            The error code of the response.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Service.VivaGFACServiceErrorResponse.Message">
            <summary>
            A message providing additional context into the error.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Service.VivaGFACServiceErrorResponse.#ctor(System.String,System.String)">
            <summary>
            Constructor for the <see cref="T:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Service.VivaGFACServiceErrorResponse"/> class
            </summary>
            <param name="code">The error code of the error response. Defaults to Unknown.</param>
            <param name="message">The message content of the error response. Defaults to an empty string.</param>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Service.VivaGFACServiceException">
            <summary>
            Exception thrown when errors are encountered while interacting with the Viva GFAC Service.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Service.VivaGFACServiceException.#ctor(System.String)">
            <summary>
            Constructor for the <see cref="T:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Service.VivaGFACServiceException"/> class
            </summary>
            <param name="message">The message content describing the exception.</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Service.VivaGFACServiceException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Without this constructor, deserialization will fail
            </summary>
            <param name="info">SerializationInfo param</param>
            <param name="context">StreamingContext param</param>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Validation.FeatureMetadataValidations">
            <summary>
            Feature metadata validations.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Validation.FeatureMetadataValidations.FeatureIdRegexPattern">
            <summary>
            Regex pattern for id
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Validation.FeatureMetadataValidations.MaxFeatureIdLength">
            <summary>
            Max feature id length.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Validation.FeatureMetadataValidations.MinFeatureIdLength">
            <summary>
            The min length for the feature id
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Validation.FeatureMetadataValidations.ValidateFeatureId(System.String)">
            <summary>
            Throw if the provided feature id is invalid
            </summary>
            <param name="featureId">The feature id to be validated</param>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Validation.ModuleMetadataValidations">
            <summary>
            Module metadata validations.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Validation.ModuleMetadataValidations.MaxModuleIdLength">
            <summary>
            Max module id length.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Validation.ModuleMetadataValidations.MinModuleIdLength">
            <summary>
            Min length for the module id.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Validation.ModuleMetadataValidations.ModuleIdRegexPattern">
            <summary>
            Regex pattern for the module id.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Validation.ModuleMetadataValidations.ValidateModuleId(System.String)">
            <summary>
            Throw if the module id is invalid.
            </summary>
            <param name="moduleId">The module id to be validated.</param>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Validation.PolicyMetadataValidations">
            <summary>
            Policy metadata validations.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Validation.PolicyMetadataValidations.PolicyIdValueLength">
            <summary>
            Policy id length.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Validation.PolicyMetadataValidations.PolicyIdRegex">
            <summary>
            The policy id regex pattern
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Validation.PolicyMetadataValidations.MaxPolicyNameLength">
            <summary>
            Max length for the policy name.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Validation.PolicyMetadataValidations.MinPolicyNameLength">
            <summary>
            Min length for the policy name.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Validation.PolicyMetadataValidations.MaxAccessControlListLength">
            <summary>
            Max length for the access control list.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Validation.PolicyMetadataValidations.MinAccessControlListLength">
            <summary>
            Min length for the access control list.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Validation.PolicyMetadataValidations.PolicyNameRegex">
            <summary>
            Regex pattern for name
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Validation.PolicyMetadataValidations.ValidatePolicyId(System.String)">
            <summary>
            Throw if id is invalid
            </summary>
            <param name="input">input Id</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Validation.PolicyMetadataValidations.ValidateAccessInformationIdAttribute(System.String,Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.AccessType)">
            <summary>
            Throw if the access information id is invalid based on the access type.
            </summary>
            <param name="input">input value</param>
            <param name="accessType">access type of the access information</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Validation.PolicyMetadataValidations.ValidateEmail(System.String)">
            <summary>
            Validates that the user upn input is a properly formatted email.
            </summary>
            <param name="input">The input to be validated as an email.</param>
            <exception cref="T:System.ArgumentException">Thrown if the input is not a valid email.</exception>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Validation.PolicyMetadataValidations.ValidateGroupIdEmail(System.String)">
            <summary>
            Validates that the group upn input is a properly formatted email.
            </summary>
            <param name="input">The input to be validated as an email.</param>
            <exception cref="T:System.ArgumentException">Thrown if the input is not a valid email.</exception>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Validation.PolicyMetadataValidations.ValidateGroupIdGuid(System.String)">
            <summary>
            Validates that the group id input is a properly formatted GUID.
            </summary>
            <param name="input">The input to be validated as a GUID.</param>
            <exception cref="T:System.ArgumentNullException">Thrown if the input is null or empty.</exception>
            <exception cref="T:System.ArgumentException">Thrown if the input is not a valid GUID.</exception>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Validation.PolicyMetadataValidations.ValidateAccessControlList(Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.AccessInformation[])">
            <summary>
            Validated the access control list length.
            </summary>
            <param name="accessInformation">The access information array to be validated.</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Validation.PolicyMetadataValidations.ValidatePolicyName(System.String)">
            <summary>
            Throw if the policy name is invalid
            </summary>
            <param name="policyName">The policy name to be verified.</param>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Validation.ServiceErrorResponseValidations">
            <summary>
            Contains validations for the service error responses
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Validation.ServiceErrorResponseValidations.ErrorMessageRegex">
            <summary>
            Regex pattern for valid messages
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Validation.ServiceErrorResponseValidations.MaxErrorMessageLength">
            <summary>
            Max length accepted for the service error messages
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Validation.ServiceErrorResponseValidations.MinErrorMessageLength">
            <summary>
            Min length accepted for the service error messages
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Validation.ServiceErrorResponseValidations.ValidateServiceErrorMessage(System.String)">
            <summary>
            Throw exception if the message is invalid
            </summary>
            <param name="input">Input to be validation</param>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Validation.ValidationsUtility">
            <summary>
            Validations Utility.
            </summary>
        </member>
        <member name="F:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Validation.ValidationsUtility.RegexTimeout">
            <summary>
            Regex timeout.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Validation.ValidationsUtility.ValidateRegex(System.String,System.String)">
            <summary>
            Validate input for provided pattern.
            </summary>
            <param name="input">input value</param>
            <param name="pattern">pattern to match</param>
            <returns>Whether the provided input matches the full regex pattern.</returns>
        </member>
        <member name="T:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.VivaGFACCmdlet`2">
            <summary>
            Base class for all Viva GFAC cmdlets
            </summary>
            <typeparam name="TDomainObject">The api type</typeparam>
            <typeparam name="TCmdlet">The type of the cmdlet</typeparam>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.VivaGFACCmdlet`2.HttpProvider">
            <summary>
            Provides an interface with which to make http requests.
            </summary>
        </member>
        <member name="P:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.VivaGFACCmdlet`2.ResponseHandler">
            <summary>
            Handles responses from the GFAC Service.
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.VivaGFACCmdlet`2.#ctor">
            <summary>
            Creates an instance of the <see cref="!:VivaGFACCmdlet.cs"/> class
            </summary>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.VivaGFACCmdlet`2.#ctor(Microsoft.Exchange.Management.RestApiClient.Viva.Common.IVivaHttpProvider,Microsoft.Exchange.Management.RestApiClient.Viva.Common.IVivaHttpResponseHandler)">
            <summary>
            Creates an instance of the <see cref="!:VivaGFACCmdlet.cs"/> class
            </summary>
            <param name="httpProvider">Provides an interface with which to make http requests.</param>
            <param name="responseHandler">Handles responses from the GFAC Service.</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.VivaGFACCmdlet`2.GetModuleFeatureAsync(System.String,System.String)">
            <summary>
            Makes the http GET request to fetch details for a specific feature of a module.
            </summary>
            <param name="moduleId">The identifier of the module.</param>
            <param name="featureId">The identifier of the feature.</param>
            <returns>The feature specified by the provided identifiers.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.VivaGFACCmdlet`2.GetAllModuleFeaturesAsync(System.String)">
            <summary>
            Makes the http GET request to fetch details of all of a specific module's features.
            </summary>
            <param name="moduleId">The identifier of the module.</param>
            <returns>The list of features for the specified module.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.VivaGFACCmdlet`2.GetModuleFeaturePolicyAsync(System.String,System.String,System.String)">
            <summary>
            Gets a specific policy of one of a module's features.
            </summary>
            <param name="moduleId">The identifier of the module.</param>
            <param name="featureId">The identifier of the feature.</param>
            <param name="policyId">The identifier of the policy.</param>
            <returns>The policy specified by the provided identifiers.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.VivaGFACCmdlet`2.DeleteModuleFeaturePolicyAsync(System.String,System.String,System.String)">
            <summary>
            Deletes the specified module feature policy.
            </summary>
            <param name="moduleId">The identifier for the module.</param>
            <param name="featureId">The identifier for the feature.</param>
            <param name="policyId">The identifier for the policy.</param>
            <returns>The deleted policy.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.VivaGFACCmdlet`2.UpdateModuleFeaturePolicyAsync(System.String,System.String,System.String,Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.PolicyDetail)">
            <summary>
            Updates the specified module feature policy.
            </summary>
            <param name="moduleId">The identifier for the module.</param>
            <param name="featureId">The identifier for the feature.</param>
            <param name="policyId">The identifier for the policy.</param>
            <param name="policy">The policy details to update the policy with.</param>
            <returns>The updated policy.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.VivaGFACCmdlet`2.CreateModuleFeaturePolicyAsync(System.String,System.String,Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.Models.PolicyDetail)">
            <summary>
            Creates a policy for the specified module feature using the provided policy specification.
            </summary>
            <param name="moduleId">The identifier for the module.</param>
            <param name="featureId">The identifier for the feature.</param>
            <param name="policy">The policy details of the policy to be created.</param>
            <returns>The created policy.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.VivaGFACCmdlet`2.GetAllModuleFeaturePolicyAsync(System.String,System.String)">
            <summary>
            Gets an array of all of the policies of one of a module's features. 
            </summary>
            <param name="moduleId">The identifier of the module.</param>
            <param name="featureId">The identifier of the feature.</param>
            <returns>The policies of the provided module feature.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.VivaGFACCmdlet`2.GetUsersEffectiveEnablementAsync(System.String,System.String,System.String)">
            <summary>
            Gets the effective enablement for the email with regards to a specific module feature.
            </summary>
            <param name="moduleId">The identifier for the module.</param>
            <param name="featureId">The feature of which to fetch the effective enablement.</param>
            <param name="email">The identity of the user or group.</param>
            <returns>The effective enablement for the user for the specified feature.</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.VivaGFACCmdlet`2.ProcessWithRetryAsync``1(System.Func{System.Threading.Tasks.Task{``0}})">
            <summary>
            Retry if the request fails due to CAE failure.
            </summary>
            <typeparam name="T">The return type of the query</typeparam>
            <param name="queryExecutor">The function that needs to be executed</param>
            <returns>The response that comes back from the executed function</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.VivaGFACCmdlet`2.AddAuthorizationHeader(System.Net.Http.HttpRequestMessage)">
            <summary>
            Adds the authorization header to the provided request message.
            </summary>
            <param name="requestMessage">The request message to add the authorization header.</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.VivaGFACCmdlet`2.AddRoutingHeader(System.Net.Http.HttpRequestMessage)">
            <summary>
            Adds the authorization header to the provided request message.
            </summary>
            <param name="requestMessage">The request message to add the authorization header.</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.VivaGFACCmdlet`2.AddUserAgentHeader(System.Net.Http.HttpRequestMessage)">
            <summary>
            Adds the user agent header to the provided request message.
            </summary>
            <param name="requestMessage">The request message to add the user agent header.</param>
        </member>
        <member name="M:Microsoft.Exchange.Management.RestApiClient.Viva.GFAC.VivaGFACCmdlet`2.AddClientRequestIdHeader(System.Net.Http.HttpRequestMessage)">
            <summary>
            Adds the client request id header to the provided request message.
            </summary>
            <param name="requestMessage">The request message to add the user agent header.</param>
        </member>
        <member name="T:Microsoft.Exchange.Management.AdminApiProvider.CtrlCEnabler">
            <summary>
            Enables Crtl+C via SetConsoleCtrlHandler of Win32 API
            </summary>
        </member>
        <member name="T:Microsoft.Exchange.Management.AdminApiProvider.CtrlCEnabler.PHANDLER_ROUTINE">
            <summary>
            A pointer to the application-defined HandlerRoutine function to be added or removed. This parameter can be NULL.
            </summary>
            <param name="ctrlType">ctrlType value</param>
            <returns>Returns a boolean</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.AdminApiProvider.CtrlCEnabler.EnableCtrlC">
            <summary>
            Sets the Crtl Handler
            </summary>
            <returns>Returns bool</returns>
        </member>
        <member name="M:Microsoft.Exchange.Management.AdminApiProvider.CtrlCEnabler.SetConsoleCtrlHandler(Microsoft.Exchange.Management.AdminApiProvider.CtrlCEnabler.PHANDLER_ROUTINE,System.Boolean)">
            <summary>
            Adds or removes an application-defined HandlerRoutine function from the list of handler functions for the calling process.
            If no handler function is specified, the function sets an inheritable attribute that determines whether the calling process ignores CTRL+C signals.
            </summary>
            <param name="handlerRoutine">A pointer to the application-defined HandlerRoutine function to be added or removed.</param>
            <param name="add">If this parameter is TRUE, the handler is added; if it is FALSE, the handler is removed.
            If the HandlerRoutine parameter is NULL, a TRUE value causes the calling process to ignore CTRL+C input, 
            and a FALSE value restores normal processing of CTRL+C input.This attribute of ignoring or processing CTRL+C is inherited by child processes.</param>
            <returns>Returns bool</returns>
        </member>
    </members>
</doc>
