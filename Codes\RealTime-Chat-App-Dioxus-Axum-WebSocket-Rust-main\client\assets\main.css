/* Modern CSS Reset and Base Styles */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  /* Color Scheme */
  --primary-color: #4f46e5;
  --primary-light: #818cf8;
  --primary-dark: #3730a3;
  --secondary-color: #10b981;
  --accent-color: #f59e0b;
  --background-light: #f3f4f6;
  --background-dark: #1f2937;
  --text-light: #f9fafb;
  --text-dark: #111827;
  --text-muted: #6b7280;
  --border-color: #e5e7eb;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

  /* Animation Durations */
  --transition-fast: 150ms;
  --transition-normal: 300ms;
  --transition-slow: 500ms;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;

  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 1rem;
  --radius-full: 9999px;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html, body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  line-height: 1.5;
  color: var(--text-dark);
  background-color: var(--background-light);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Chat Container */
.chat-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: var(--spacing-md);
}

/* Chat Window */
.chat {
  width: 100%;
  max-width: 800px;
  height: 90vh;
  background-color: white;
  position: relative;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.chat:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Chat Header */
.chat-header {
  background-color: var(--primary-color);
  color: var(--text-light);
  padding: var(--spacing-md) var(--spacing-lg);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--primary-dark);
}

.chat-header h1 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.online-indicator {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
}

.online-dot {
  width: 8px;
  height: 8px;
  background-color: var(--secondary-color);
  border-radius: 50%;
  margin-right: var(--spacing-xs);
  display: inline-block;
}

/* Message Container */
.message-container {
  padding: var(--spacing-md);
  height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column-reverse;
  gap: var(--spacing-md);
  scroll-behavior: smooth;
}

/* Message Item */
.message-item {
  position: relative;
  max-width: 80%;
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  font-size: 1rem;
  line-height: 1.5;
  animation: message-appear var(--transition-normal) ease-out;
  transition: opacity var(--transition-normal);
  margin-bottom: var(--spacing-md);
  box-shadow: var(--shadow-sm);
  word-break: break-word;
}

.message-item.expiring {
  animation: message-expire 10s linear forwards;
}

.message-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.message-wrapper.others {
  align-items: flex-start;
}

.message-wrapper.self {
  align-items: flex-end;
}

.others .message-item {
  background-color: var(--primary-light);
  color: var(--text-light);
  border-top-left-radius: 0;
}

.self .message-item {
  background-color: var(--secondary-color);
  color: var(--text-light);
  border-top-right-radius: 0;
}

.message-meta {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
  color: var(--text-muted);
  margin-top: var(--spacing-xs);
  padding: 0 var(--spacing-xs);
}

.message-username {
  font-weight: 600;
  margin-bottom: var(--spacing-xs);
}

.message-time {
  white-space: nowrap;
}

.message-expiry {
  display: inline-block;
  margin-left: var(--spacing-sm);
}

.expiry-progress {
  width: 100%;
  height: 3px;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: var(--radius-full);
  overflow: hidden;
  position: absolute;
  bottom: 0;
  left: 0;
}

.expiry-bar {
  height: 100%;
  background-color: var(--accent-color);
  border-radius: var(--radius-full);
  animation: expiry-countdown 10s linear forwards;
}

/* Input Container */
.input-container {
  padding: var(--spacing-md);
  background-color: white;
  border-top: 1px solid var(--border-color);
  display: flex;
  gap: var(--spacing-sm);
}

input {
  flex: 1;
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: 1rem;
  transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
}

button {
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: background-color var(--transition-fast), transform var(--transition-fast);
}

button:hover:not(:disabled) {
  background-color: var(--primary-dark);
  transform: translateY(-1px);
}

button:active:not(:disabled) {
  transform: translateY(0);
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Login Screen */
.login-container {
  width: 100%;
  max-width: 400px;
  padding: var(--spacing-xl);
  background-color: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  text-align: center;
}

.login-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: var(--spacing-lg);
  color: var(--primary-color);
}

.login-subtitle {
  color: var(--text-muted);
  margin-bottom: var(--spacing-xl);
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.login-input {
  width: 100%;
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: 1rem;
}

.login-button {
  width: 100%;
  padding: var(--spacing-md);
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.login-button:hover:not(:disabled) {
  background-color: var(--primary-dark);
}

/* Animations */
@keyframes message-appear {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes message-expire {
  0% {
    opacity: 1;
  }
  80% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

@keyframes expiry-countdown {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}

/* Sending Message Animation */
.sending-indicator {
  display: inline-block;
  margin-left: var(--spacing-xs);
}

.sending-dot {
  display: inline-block;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: currentColor;
  margin: 0 2px;
  opacity: 0.6;
  animation: sending-dot 1.4s infinite ease-in-out;
}

.sending-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.sending-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes sending-dot {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .chat {
    height: 100vh;
    max-width: 100%;
    border-radius: 0;
  }

  .chat-container {
    padding: 0;
  }

  .message-item {
    max-width: 90%;
  }
}
