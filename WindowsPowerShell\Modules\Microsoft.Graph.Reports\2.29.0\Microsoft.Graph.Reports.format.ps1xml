<?xml version="1.0" encoding="utf-16"?>
<Configuration>
  <ViewDefinitions>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.CollectionOfRelyingPartyDetailedSummary</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.CollectionOfRelyingPartyDetailedSummary</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>OdataNextLink</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>OdataNextLink</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphAdminReportSettings</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphAdminReportSettings</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Id</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>DisplayConcealedNames</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Id</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>DisplayConcealedNames</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphAppIdentity</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphAppIdentity</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>AppId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>DisplayName</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>ServicePrincipalId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>ServicePrincipalName</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>AppId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>DisplayName</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>ServicePrincipalId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>ServicePrincipalName</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphAppliedConditionalAccessPolicy</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphAppliedConditionalAccessPolicy</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>DisplayName</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>EnforcedGrantControls</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>EnforcedSessionControls</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Id</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Result</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>DisplayName</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>EnforcedGrantControls</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>EnforcedSessionControls</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Id</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Result</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphArchivedPrintJob</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphArchivedPrintJob</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>AcquiredByPrinter</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>AcquiredDateTime</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>CompletionDateTime</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>CopiesPrinted</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>CreatedDateTime</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Id</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>PrinterId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>PrinterName</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>ProcessingState</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>AcquiredByPrinter</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>AcquiredDateTime</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>CompletionDateTime</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>CopiesPrinted</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>CreatedDateTime</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Id</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>PrinterId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>PrinterName</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>ProcessingState</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphAttackSimulationRepeatOffender</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphAttackSimulationRepeatOffender</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>RepeatOffenceCount</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>RepeatOffenceCount</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphAttackSimulationUser</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphAttackSimulationUser</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>DisplayName</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Email</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>UserId</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>DisplayName</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Email</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>UserId</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphAttackSimulationUserCoverage</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphAttackSimulationUserCoverage</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>ClickCount</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>CompromisedCount</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>LatestSimulationDateTime</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>SimulationCount</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>ClickCount</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>CompromisedCount</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>LatestSimulationDateTime</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>SimulationCount</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphAuditLogRoot</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphAuditLogRoot</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Id</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Id</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphAuthenticationMethodsRoot</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphAuthenticationMethodsRoot</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Id</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Id</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphDeviceDetail</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphDeviceDetail</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Browser</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>DeviceId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>DisplayName</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>IsCompliant</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>IsManaged</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>OperatingSystem</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>TrustType</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Browser</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>DeviceId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>DisplayName</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>IsCompliant</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>IsManaged</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>OperatingSystem</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>TrustType</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphDeviceManagementExportJob</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphDeviceManagementExportJob</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Id</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>ExpirationDateTime</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Filter</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Format</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>LocalizationType</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>ReportName</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>RequestDateTime</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Select</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>SnapshotId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Status</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Url</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Id</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>ExpirationDateTime</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Filter</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Format</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>LocalizationType</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>ReportName</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>RequestDateTime</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Select</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>SnapshotId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Status</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Url</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphDeviceManagementExportJobCollectionResponse</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphDeviceManagementExportJobCollectionResponse</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>OdataNextLink</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>OdataNextLink</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphDeviceManagementReports</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphDeviceManagementReports</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Id</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Id</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphDirectoryAudit</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphDirectoryAudit</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Id</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>ActivityDateTime</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>ActivityDisplayName</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Category</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>CorrelationId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>LoggedByService</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>OperationType</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Result</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>ResultReason</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Id</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>ActivityDateTime</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>ActivityDisplayName</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Category</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>CorrelationId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>LoggedByService</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>OperationType</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Result</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>ResultReason</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphDirectoryAuditCollectionResponse</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphDirectoryAuditCollectionResponse</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>OdataNextLink</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>OdataNextLink</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphEntity</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphEntity</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Id</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Id</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphGeoCoordinates</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphGeoCoordinates</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Altitude</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Latitude</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Longitude</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Altitude</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Latitude</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Longitude</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphIdentity</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphIdentity</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>DisplayName</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Id</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>DisplayName</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Id</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphInitiator</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphInitiator</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>DisplayName</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Id</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>InitiatorType</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>DisplayName</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Id</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>InitiatorType</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphKeyValue</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphKeyValue</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Key</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Value</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Key</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Value</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphKeyValuePair</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphKeyValuePair</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Name</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Value</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Name</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Value</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphModifiedProperty</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphModifiedProperty</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>DisplayName</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>NewValue</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>OldValue</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>DisplayName</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>NewValue</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>OldValue</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphODataErrorsErrorDetails</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphODataErrorsErrorDetails</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Code</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Message</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Target</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Code</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Message</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Target</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphODataErrorsInnerError</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphODataErrorsInnerError</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>ClientRequestId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Date</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>RequestId</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>ClientRequestId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Date</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>RequestId</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphODataErrorsMainError</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphODataErrorsMainError</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Code</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Message</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Target</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Code</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Message</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Target</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphPartners</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphPartners</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Id</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Id</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphPartnersBilling</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphPartnersBilling</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Id</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Id</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphPartnersBillingAzureUsage</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphPartnersBillingAzureUsage</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Id</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Id</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphPartnersBillingBilledReconciliation</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphPartnersBillingBilledReconciliation</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Id</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Id</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphPartnersBillingBilledUsage</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphPartnersBillingBilledUsage</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Id</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Id</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphPartnersBillingBlob</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphPartnersBillingBlob</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Name</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>PartitionValue</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Name</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>PartitionValue</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphPartnersBillingManifest</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphPartnersBillingManifest</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Id</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>BlobCount</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>CreatedDateTime</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>DataFormat</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>ETag</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>PartitionType</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>PartnerTenantId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>RootDirectory</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>SasToken</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>SchemaVersion</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Id</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>BlobCount</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>CreatedDateTime</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>DataFormat</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>ETag</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>PartitionType</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>PartnerTenantId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>RootDirectory</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>SasToken</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>SchemaVersion</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphPartnersBillingManifestCollectionResponse</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphPartnersBillingManifestCollectionResponse</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>OdataNextLink</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>OdataNextLink</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphPartnersBillingOperation</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphPartnersBillingOperation</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Id</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>CreatedDateTime</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>LastActionDateTime</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Status</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Id</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>CreatedDateTime</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>LastActionDateTime</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Status</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphPartnersBillingOperationCollectionResponse</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphPartnersBillingOperationCollectionResponse</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>OdataNextLink</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>OdataNextLink</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphPartnersBillingReconciliation</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphPartnersBillingReconciliation</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Id</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Id</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphPartnersBillingUnbilledReconciliation</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphPartnersBillingUnbilledReconciliation</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Id</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Id</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphPartnersBillingUnbilledUsage</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphPartnersBillingUnbilledUsage</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Id</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Id</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphPrintUsage</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphPrintUsage</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Id</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>BlackAndWhitePageCount</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>ColorPageCount</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>CompletedBlackAndWhiteJobCount</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>CompletedColorJobCount</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>CompletedJobCount</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>DoubleSidedSheetCount</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>IncompleteJobCount</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>MediaSheetCount</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>PageCount</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>SingleSidedSheetCount</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>UsageDate</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Id</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>BlackAndWhitePageCount</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>ColorPageCount</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>CompletedBlackAndWhiteJobCount</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>CompletedColorJobCount</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>CompletedJobCount</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>DoubleSidedSheetCount</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>IncompleteJobCount</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>MediaSheetCount</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>PageCount</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>SingleSidedSheetCount</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>UsageDate</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphPrintUsageByPrinter</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphPrintUsageByPrinter</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>BlackAndWhitePageCount</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>ColorPageCount</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>CompletedBlackAndWhiteJobCount</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>CompletedColorJobCount</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>CompletedJobCount</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>DoubleSidedSheetCount</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Id</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>IncompleteJobCount</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>MediaSheetCount</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>PageCount</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>SingleSidedSheetCount</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>UsageDate</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>PrinterId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>PrinterName</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>BlackAndWhitePageCount</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>ColorPageCount</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>CompletedBlackAndWhiteJobCount</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>CompletedColorJobCount</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>CompletedJobCount</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>DoubleSidedSheetCount</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Id</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>IncompleteJobCount</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>MediaSheetCount</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>PageCount</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>SingleSidedSheetCount</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>UsageDate</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>PrinterId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>PrinterName</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphPrintUsageByPrinterCollectionResponse</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphPrintUsageByPrinterCollectionResponse</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>OdataNextLink</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>OdataNextLink</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphPrintUsageByUser</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphPrintUsageByUser</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>BlackAndWhitePageCount</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>ColorPageCount</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>CompletedBlackAndWhiteJobCount</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>CompletedColorJobCount</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>CompletedJobCount</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>DoubleSidedSheetCount</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Id</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>IncompleteJobCount</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>MediaSheetCount</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>PageCount</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>SingleSidedSheetCount</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>UsageDate</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>UserPrincipalName</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>BlackAndWhitePageCount</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>ColorPageCount</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>CompletedBlackAndWhiteJobCount</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>CompletedColorJobCount</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>CompletedJobCount</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>DoubleSidedSheetCount</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Id</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>IncompleteJobCount</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>MediaSheetCount</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>PageCount</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>SingleSidedSheetCount</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>UsageDate</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>UserPrincipalName</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphPrintUsageByUserCollectionResponse</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphPrintUsageByUserCollectionResponse</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>OdataNextLink</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>OdataNextLink</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphProvisionedIdentity</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphProvisionedIdentity</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>DisplayName</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Id</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>IdentityType</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>DisplayName</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Id</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>IdentityType</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphProvisioningErrorInfo</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphProvisioningErrorInfo</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>AdditionalDetails</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>ErrorCategory</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>ErrorCode</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Reason</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>RecommendedAction</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>AdditionalDetails</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>ErrorCategory</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>ErrorCode</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Reason</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>RecommendedAction</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphProvisioningObjectSummary</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphProvisioningObjectSummary</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Id</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>ActivityDateTime</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>ChangeId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>CycleId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>DurationInMilliseconds</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>JobId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>ProvisioningAction</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>TenantId</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Id</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>ActivityDateTime</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>ChangeId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>CycleId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>DurationInMilliseconds</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>JobId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>ProvisioningAction</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>TenantId</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphProvisioningObjectSummaryCollectionResponse</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphProvisioningObjectSummaryCollectionResponse</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>OdataNextLink</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>OdataNextLink</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphProvisioningServicePrincipal</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphProvisioningServicePrincipal</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>DisplayName</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Id</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>DisplayName</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Id</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphProvisioningStatusInfo</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphProvisioningStatusInfo</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Status</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Status</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphProvisioningStep</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphProvisioningStep</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Description</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Name</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>ProvisioningStepType</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Status</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Description</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Name</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>ProvisioningStepType</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Status</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphProvisioningSystem</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphProvisioningSystem</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>DisplayName</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Id</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>DisplayName</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Id</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphRelyingPartyDetailedSummary</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphRelyingPartyDetailedSummary</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Id</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>FailedSignInCount</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>MigrationStatus</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>RelyingPartyId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>RelyingPartyName</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>ReplyUrls</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>ServiceId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>SignInSuccessRate</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>SuccessfulSignInCount</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>TotalSignInCount</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>UniqueUserCount</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Id</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>FailedSignInCount</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>MigrationStatus</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>RelyingPartyId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>RelyingPartyName</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>ReplyUrls</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>ServiceId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>SignInSuccessRate</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>SuccessfulSignInCount</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>TotalSignInCount</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>UniqueUserCount</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphReport</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphReport</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Content</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Content</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphSecurityReportsRoot</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphSecurityReportsRoot</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Id</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Id</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphSignIn</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphSignIn</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Id</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>AppDisplayName</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>AppId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>ClientAppUsed</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>ConditionalAccessStatus</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>CorrelationId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>CreatedDateTime</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>IPAddress</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>IsInteractive</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>ResourceDisplayName</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>ResourceId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>RiskDetail</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>RiskEventTypes</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>RiskEventTypesV2</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>RiskLevelAggregated</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>RiskLevelDuringSignIn</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>RiskState</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>UserDisplayName</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>UserId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>UserPrincipalName</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Id</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>AppDisplayName</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>AppId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>ClientAppUsed</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>ConditionalAccessStatus</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>CorrelationId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>CreatedDateTime</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>IPAddress</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>IsInteractive</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>ResourceDisplayName</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>ResourceId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>RiskDetail</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>RiskEventTypes</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>RiskEventTypesV2</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>RiskLevelAggregated</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>RiskLevelDuringSignIn</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>RiskState</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>UserDisplayName</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>UserId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>UserPrincipalName</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphSignInCollectionResponse</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphSignInCollectionResponse</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>OdataNextLink</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>OdataNextLink</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphSignInLocation</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphSignInLocation</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>City</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>CountryOrRegion</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>State</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>City</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>CountryOrRegion</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>State</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphSignInStatus</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphSignInStatus</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>AdditionalDetails</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>ErrorCode</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>FailureReason</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>AdditionalDetails</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>ErrorCode</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>FailureReason</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphTargetResource</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphTargetResource</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>DisplayName</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>GroupType</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Id</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Type</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>UserPrincipalName</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>DisplayName</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>GroupType</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Id</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Type</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>UserPrincipalName</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphUserIdentity</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphUserIdentity</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>DisplayName</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Id</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>IPAddress</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>UserPrincipalName</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>DisplayName</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Id</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>IPAddress</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>UserPrincipalName</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphUserRegistrationDetails</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphUserRegistrationDetails</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Id</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>IsAdmin</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>IsMfaCapable</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>IsMfaRegistered</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>IsPasswordlessCapable</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>IsSsprCapable</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>IsSsprEnabled</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>IsSsprRegistered</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>IsSystemPreferredAuthenticationMethodEnabled</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>LastUpdatedDateTime</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>MethodsRegistered</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>SystemPreferredAuthenticationMethods</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>UserDisplayName</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>UserPreferredMethodForSecondaryAuthentication</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>UserPrincipalName</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>UserType</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Id</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>IsAdmin</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>IsMfaCapable</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>IsMfaRegistered</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>IsPasswordlessCapable</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>IsSsprCapable</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>IsSsprEnabled</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>IsSsprRegistered</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>IsSystemPreferredAuthenticationMethodEnabled</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>LastUpdatedDateTime</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>MethodsRegistered</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>SystemPreferredAuthenticationMethods</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>UserDisplayName</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>UserPreferredMethodForSecondaryAuthentication</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>UserPrincipalName</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>UserType</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphUserRegistrationDetailsCollectionResponse</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphUserRegistrationDetailsCollectionResponse</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>OdataNextLink</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>OdataNextLink</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphUserRegistrationFeatureCount</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphUserRegistrationFeatureCount</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Feature</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>UserCount</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Feature</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>UserCount</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphUserRegistrationFeatureSummary</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphUserRegistrationFeatureSummary</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>TotalUserCount</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>UserRoles</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>UserTypes</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>TotalUserCount</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>UserRoles</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>UserTypes</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphUserRegistrationMethodCount</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphUserRegistrationMethodCount</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>AuthenticationMethod</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>UserCount</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>AuthenticationMethod</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>UserCount</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphUserRegistrationMethodSummary</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphUserRegistrationMethodSummary</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>TotalUserCount</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>UserRoles</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>UserTypes</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>TotalUserCount</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>UserRoles</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>UserTypes</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphUserTrainingStatusInfo</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphUserTrainingStatusInfo</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>AssignedDateTime</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>CompletionDateTime</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>DisplayName</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>TrainingStatus</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>AssignedDateTime</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>CompletionDateTime</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>DisplayName</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>TrainingStatus</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.Paths108Pcs6DevicemanagementReportsMicrosoftGraphGetpolicynoncompliancemetadataPostRequestbodyContentApplicationJsonSchema</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.Paths108Pcs6DevicemanagementReportsMicrosoftGraphGetpolicynoncompliancemetadataPostRequestbodyContentApplicationJsonSchema</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Filter</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>GroupBy</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Name</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>OrderBy</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Search</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Select</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>SessionId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Skip</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Top</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Filter</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>GroupBy</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Name</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>OrderBy</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Search</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Select</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>SessionId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Skip</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Top</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.Paths11Lc6EuDevicemanagementReportsMicrosoftGraphGetnoncompliantdevicesandsettingsreportPostRequestbodyContentApplicationJsonSchema</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.Paths11Lc6EuDevicemanagementReportsMicrosoftGraphGetnoncompliantdevicesandsettingsreportPostRequestbodyContentApplicationJsonSchema</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Filter</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>GroupBy</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Name</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>OrderBy</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Search</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Select</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>SessionId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Skip</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Top</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Filter</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>GroupBy</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Name</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>OrderBy</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Search</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Select</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>SessionId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Skip</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Top</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.Paths12Y1CqcReportsMicrosoftGraphGetuserarchivedprintjobsUseridStartdatetimeEnddatetimeGetResponses2XxContentApplicationJsonSchema</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.Paths12Y1CqcReportsMicrosoftGraphGetuserarchivedprintjobsUseridStartdatetimeEnddatetimeGetResponses2XxContentApplicationJsonSchema</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>OdataNextLink</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>OdataNextLink</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.Paths19DnnqbDevicemanagementReportsMicrosoftGraphGetconfigurationsettingnoncompliancereportPostRequestbodyContentApplicationJsonSchema</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.Paths19DnnqbDevicemanagementReportsMicrosoftGraphGetconfigurationsettingnoncompliancereportPostRequestbodyContentApplicationJsonSchema</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Filter</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>GroupBy</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Name</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>OrderBy</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Search</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Select</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>SessionId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Skip</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Top</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Filter</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>GroupBy</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Name</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>OrderBy</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Search</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Select</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>SessionId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Skip</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Top</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.Paths19Wsha2DevicemanagementReportsMicrosoftGraphGetsettingnoncompliancereportPostRequestbodyContentApplicationJsonSchema</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.Paths19Wsha2DevicemanagementReportsMicrosoftGraphGetsettingnoncompliancereportPostRequestbodyContentApplicationJsonSchema</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Filter</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>GroupBy</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Name</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>OrderBy</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Search</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Select</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>SessionId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Skip</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Top</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Filter</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>GroupBy</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Name</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>OrderBy</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Search</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Select</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>SessionId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Skip</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Top</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.Paths1Ajo9DhReportsPartnersBillingUsageUnbilledMicrosoftGraphPartnersBillingExportPostRequestbodyContentApplicationJsonSchema</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.Paths1Ajo9DhReportsPartnersBillingUsageUnbilledMicrosoftGraphPartnersBillingExportPostRequestbodyContentApplicationJsonSchema</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>AttributeSet</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>BillingPeriod</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>CurrencyCode</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>AttributeSet</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>BillingPeriod</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>CurrencyCode</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.Paths1Bog3HuDevicemanagementReportsMicrosoftGraphRetrievedeviceappinstallationstatusreportPostRequestbodyContentApplicationJsonSchema</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.Paths1Bog3HuDevicemanagementReportsMicrosoftGraphRetrievedeviceappinstallationstatusreportPostRequestbodyContentApplicationJsonSchema</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Filter</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>GroupBy</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Name</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>OrderBy</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Search</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Select</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>SessionId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Skip</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Top</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Filter</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>GroupBy</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Name</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>OrderBy</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Search</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Select</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>SessionId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Skip</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Top</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.Paths1C8L63KDevicemanagementReportsMicrosoftGraphGetcachedreportPostRequestbodyContentApplicationJsonSchema</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.Paths1C8L63KDevicemanagementReportsMicrosoftGraphGetcachedreportPostRequestbodyContentApplicationJsonSchema</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>GroupBy</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Id</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>OrderBy</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Search</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Select</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Skip</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Top</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>GroupBy</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Id</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>OrderBy</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Search</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Select</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Skip</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Top</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.Paths1E3Ye8BReportsMicrosoftGraphGetgrouparchivedprintjobsGroupidStartdatetimeEnddatetimeGetResponses2XxContentApplicationJsonSchema</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.Paths1E3Ye8BReportsMicrosoftGraphGetgrouparchivedprintjobsGroupidStartdatetimeEnddatetimeGetResponses2XxContentApplicationJsonSchema</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>OdataNextLink</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>OdataNextLink</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.Paths1Hds8UqDevicemanagementReportsMicrosoftGraphGetconfigurationpolicynoncompliancereportPostRequestbodyContentApplicationJsonSchema</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.Paths1Hds8UqDevicemanagementReportsMicrosoftGraphGetconfigurationpolicynoncompliancereportPostRequestbodyContentApplicationJsonSchema</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Filter</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>GroupBy</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Name</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>OrderBy</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Search</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Select</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>SessionId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Skip</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Top</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Filter</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>GroupBy</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Name</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>OrderBy</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Search</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Select</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>SessionId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Skip</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Top</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.Paths1I30ObnReportsSecurityMicrosoftGraphGetattacksimulationtrainingusercoverageGetResponses2XxContentApplicationJsonSchema</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.Paths1I30ObnReportsSecurityMicrosoftGraphGetattacksimulationtrainingusercoverageGetResponses2XxContentApplicationJsonSchema</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>OdataNextLink</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>OdataNextLink</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.Paths1Ij96IaReportsPartnersBillingUsageBilledMicrosoftGraphPartnersBillingExportPostRequestbodyContentApplicationJsonSchema</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.Paths1Ij96IaReportsPartnersBillingUsageBilledMicrosoftGraphPartnersBillingExportPostRequestbodyContentApplicationJsonSchema</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>AttributeSet</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>InvoiceId</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>AttributeSet</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>InvoiceId</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.Paths1J4Do9UDevicemanagementReportsMicrosoftGraphGetreportfiltersPostRequestbodyContentApplicationJsonSchema</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.Paths1J4Do9UDevicemanagementReportsMicrosoftGraphGetreportfiltersPostRequestbodyContentApplicationJsonSchema</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Filter</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>GroupBy</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Name</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>OrderBy</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Search</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Select</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>SessionId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Skip</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Top</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Filter</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>GroupBy</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Name</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>OrderBy</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Search</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Select</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>SessionId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Skip</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Top</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.Paths1K0Im7HAuditlogsSigninsMicrosoftGraphDismissPostRequestbodyContentApplicationJsonSchema</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.Paths1K0Im7HAuditlogsSigninsMicrosoftGraphDismissPostRequestbodyContentApplicationJsonSchema</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>RequestIds</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>RequestIds</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.Paths1KwwldyDevicemanagementReportsMicrosoftGraphGetpolicynoncompliancereportPostRequestbodyContentApplicationJsonSchema</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.Paths1KwwldyDevicemanagementReportsMicrosoftGraphGetpolicynoncompliancereportPostRequestbodyContentApplicationJsonSchema</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Filter</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>GroupBy</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Name</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>OrderBy</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Search</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Select</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>SessionId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Skip</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Top</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Filter</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>GroupBy</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Name</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>OrderBy</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Search</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Select</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>SessionId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Skip</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Top</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.Paths1Mia7W1DevicemanagementReportsMicrosoftGraphGethistoricalreportPostRequestbodyContentApplicationJsonSchema</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.Paths1Mia7W1DevicemanagementReportsMicrosoftGraphGethistoricalreportPostRequestbodyContentApplicationJsonSchema</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Filter</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>GroupBy</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Name</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>OrderBy</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Search</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Select</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Skip</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Top</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Filter</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>GroupBy</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Name</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>OrderBy</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Search</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Select</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Skip</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Top</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.Paths1Oi3T48ReportsSecurityMicrosoftGraphGetattacksimulationrepeatoffendersGetResponses2XxContentApplicationJsonSchema</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.Paths1Oi3T48ReportsSecurityMicrosoftGraphGetattacksimulationrepeatoffendersGetResponses2XxContentApplicationJsonSchema</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>OdataNextLink</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>OdataNextLink</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.Paths1Pvn7TwDevicemanagementReportsMicrosoftGraphGetcompliancepolicynoncompliancereportPostRequestbodyContentApplicationJsonSchema</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.Paths1Pvn7TwDevicemanagementReportsMicrosoftGraphGetcompliancepolicynoncompliancereportPostRequestbodyContentApplicationJsonSchema</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Filter</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>GroupBy</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Name</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>OrderBy</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Search</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Select</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>SessionId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Skip</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Top</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Filter</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>GroupBy</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Name</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>OrderBy</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Search</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Select</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>SessionId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Skip</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Top</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.Paths1QqqzoyDevicemanagementReportsMicrosoftGraphGetcompliancesettingnoncompliancereportPostRequestbodyContentApplicationJsonSchema</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.Paths1QqqzoyDevicemanagementReportsMicrosoftGraphGetcompliancesettingnoncompliancereportPostRequestbodyContentApplicationJsonSchema</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Filter</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>GroupBy</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Name</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>OrderBy</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Search</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Select</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>SessionId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Skip</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Top</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Filter</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>GroupBy</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Name</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>OrderBy</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Search</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Select</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>SessionId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Skip</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Top</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.Paths1RxowymDevicemanagementReportsMicrosoftGraphGetdeviceswithoutcompliancepolicyreportPostRequestbodyContentApplicationJsonSchema</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.Paths1RxowymDevicemanagementReportsMicrosoftGraphGetdeviceswithoutcompliancepolicyreportPostRequestbodyContentApplicationJsonSchema</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Filter</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>GroupBy</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Name</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>OrderBy</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Search</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Select</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>SessionId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Skip</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Top</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Filter</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>GroupBy</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Name</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>OrderBy</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Search</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Select</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>SessionId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Skip</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Top</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.Paths1Sa3Fr9DevicemanagementReportsMicrosoftGraphGetdevicemanagementintentpersettingcontributingprofilesPostRequestbodyContentApplicationJsonSchema</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.Paths1Sa3Fr9DevicemanagementReportsMicrosoftGraphGetdevicemanagementintentpersettingcontributingprofilesPostRequestbodyContentApplicationJsonSchema</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Filter</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>GroupBy</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Name</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>OrderBy</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Search</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Select</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>SessionId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Skip</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Top</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Filter</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>GroupBy</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Name</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>OrderBy</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Search</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Select</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>SessionId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Skip</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Top</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.Paths1Yhfzi3AuditlogsSigninsMicrosoftGraphConfirmsafePostRequestbodyContentApplicationJsonSchema</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.Paths1Yhfzi3AuditlogsSigninsMicrosoftGraphConfirmsafePostRequestbodyContentApplicationJsonSchema</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>RequestIds</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>RequestIds</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.Paths6K2Wa1DevicemanagementReportsMicrosoftGraphGetcompliancepolicynoncompliancesummaryreportPostRequestbodyContentApplicationJsonSchema</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.Paths6K2Wa1DevicemanagementReportsMicrosoftGraphGetcompliancepolicynoncompliancesummaryreportPostRequestbodyContentApplicationJsonSchema</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Filter</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>GroupBy</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Name</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>OrderBy</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Search</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Select</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>SessionId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Skip</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Top</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Filter</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>GroupBy</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Name</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>OrderBy</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Search</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Select</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>SessionId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Skip</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Top</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.PathsFucohkReportsPartnersBillingReconciliationUnbilledMicrosoftGraphPartnersBillingExportPostRequestbodyContentApplicationJsonSchema</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.PathsFucohkReportsPartnersBillingReconciliationUnbilledMicrosoftGraphPartnersBillingExportPostRequestbodyContentApplicationJsonSchema</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>AttributeSet</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>BillingPeriod</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>CurrencyCode</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>AttributeSet</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>BillingPeriod</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>CurrencyCode</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.PathsHd8Wo9DevicemanagementReportsMicrosoftGraphGetconfigurationpolicynoncompliancesummaryreportPostRequestbodyContentApplicationJsonSchema</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.PathsHd8Wo9DevicemanagementReportsMicrosoftGraphGetconfigurationpolicynoncompliancesummaryreportPostRequestbodyContentApplicationJsonSchema</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Filter</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>GroupBy</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Name</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>OrderBy</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Search</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Select</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>SessionId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Skip</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Top</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Filter</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>GroupBy</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Name</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>OrderBy</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Search</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Select</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>SessionId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Skip</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Top</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.PathsJ8K6OfDevicemanagementReportsMicrosoftGraphGetpolicynoncompliancesummaryreportPostRequestbodyContentApplicationJsonSchema</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.PathsJ8K6OfDevicemanagementReportsMicrosoftGraphGetpolicynoncompliancesummaryreportPostRequestbodyContentApplicationJsonSchema</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Filter</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>GroupBy</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Name</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>OrderBy</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Search</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Select</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>SessionId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Skip</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Top</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Filter</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>GroupBy</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Name</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>OrderBy</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Search</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Select</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>SessionId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Skip</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Top</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.PathsKfhb9KAuditlogsSigninsMicrosoftGraphConfirmcompromisedPostRequestbodyContentApplicationJsonSchema</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.PathsKfhb9KAuditlogsSigninsMicrosoftGraphConfirmcompromisedPostRequestbodyContentApplicationJsonSchema</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>RequestIds</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>RequestIds</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.PathsOl9O0RDevicemanagementReportsMicrosoftGraphGetdevicenoncompliancereportPostRequestbodyContentApplicationJsonSchema</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.PathsOl9O0RDevicemanagementReportsMicrosoftGraphGetdevicenoncompliancereportPostRequestbodyContentApplicationJsonSchema</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Filter</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>GroupBy</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Name</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>OrderBy</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Search</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Select</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>SessionId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Skip</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Top</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Filter</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>GroupBy</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Name</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>OrderBy</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Search</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Select</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>SessionId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Skip</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Top</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.PathsPo63NoReportsSecurityMicrosoftGraphGetattacksimulationsimulationusercoverageGetResponses2XxContentApplicationJsonSchema</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.PathsPo63NoReportsSecurityMicrosoftGraphGetattacksimulationsimulationusercoverageGetResponses2XxContentApplicationJsonSchema</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>OdataNextLink</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>OdataNextLink</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.PathsTbh0H2DevicemanagementReportsMicrosoftGraphGetdevicemanagementintentsettingsreportPostRequestbodyContentApplicationJsonSchema</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.PathsTbh0H2DevicemanagementReportsMicrosoftGraphGetdevicemanagementintentsettingsreportPostRequestbodyContentApplicationJsonSchema</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Filter</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>GroupBy</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Name</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>OrderBy</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Search</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Select</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>SessionId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Skip</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Top</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Filter</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>GroupBy</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Name</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>OrderBy</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Search</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Select</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>SessionId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Skip</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Top</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.PathsYfiwmwReportsMicrosoftGraphGetprinterarchivedprintjobsPrinteridStartdatetimeEnddatetimeGetResponses2XxContentApplicationJsonSchema</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.PathsYfiwmwReportsMicrosoftGraphGetprinterarchivedprintjobsPrinteridStartdatetimeEnddatetimeGetResponses2XxContentApplicationJsonSchema</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>OdataNextLink</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>OdataNextLink</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.PathsYgrmj4ReportsPartnersBillingReconciliationBilledMicrosoftGraphPartnersBillingExportPostRequestbodyContentApplicationJsonSchema</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.PathsYgrmj4ReportsPartnersBillingReconciliationBilledMicrosoftGraphPartnersBillingExportPostRequestbodyContentApplicationJsonSchema</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>AttributeSet</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>InvoiceId</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>AttributeSet</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>InvoiceId</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.ReportsIdentity</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.ReportsIdentity</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Date</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>DeviceManagementExportJobId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>DirectoryAuditId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>EndDateTime</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Filter</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>GroupId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>IncludedUserRoles</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>IncludedUserTypes</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>ManifestId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>OperationId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Period</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>PrintUsageByPrinterId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>PrintUsageByUserId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>PrinterId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>ProvisioningObjectSummaryId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>SignInId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Skip</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>SkipToken</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>StartDateTime</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Top</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>UserId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>UserRegistrationDetailsId</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Date</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>DeviceManagementExportJobId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>DirectoryAuditId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>EndDateTime</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Filter</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>GroupId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>IncludedUserRoles</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>IncludedUserTypes</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>ManifestId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>OperationId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Period</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>PrintUsageByPrinterId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>PrintUsageByUserId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>PrinterId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>ProvisioningObjectSummaryId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>SignInId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Skip</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>SkipToken</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>StartDateTime</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Top</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>UserId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>UserRegistrationDetailsId</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
  </ViewDefinitions>
</Configuration>
<!-- SIG # Begin signature block -->
<!-- MIIoKgYJKoZIhvcNAQcCoIIoGzCCKBcCAQExDzANBglghkgBZQMEAgEFADB5Bgor -->
<!-- BgEEAYI3AgEEoGswaTA0BgorBgEEAYI3AgEeMCYCAwEAAAQQH8w7YFlLCE63JNLG -->
<!-- KX7zUQIBAAIBAAIBAAIBAAIBADAxMA0GCWCGSAFlAwQCAQUABCB9GFAobkvw7QP/ -->
<!-- WCrm+lWhsFQvBi2c3RBvFzoPYZW4OKCCDXYwggX0MIID3KADAgECAhMzAAAEBGx0 -->
<!-- Bv9XKydyAAAAAAQEMA0GCSqGSIb3DQEBCwUAMH4xCzAJBgNVBAYTAlVTMRMwEQYD -->
<!-- VQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNy -->
<!-- b3NvZnQgQ29ycG9yYXRpb24xKDAmBgNVBAMTH01pY3Jvc29mdCBDb2RlIFNpZ25p -->
<!-- bmcgUENBIDIwMTEwHhcNMjQwOTEyMjAxMTE0WhcNMjUwOTExMjAxMTE0WjB0MQsw -->
<!-- CQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9u -->
<!-- ZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMR4wHAYDVQQDExVNaWNy -->
<!-- b3NvZnQgQ29ycG9yYXRpb24wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIB -->
<!-- AQC0KDfaY50MDqsEGdlIzDHBd6CqIMRQWW9Af1LHDDTuFjfDsvna0nEuDSYJmNyz -->
<!-- NB10jpbg0lhvkT1AzfX2TLITSXwS8D+mBzGCWMM/wTpciWBV/pbjSazbzoKvRrNo -->
<!-- DV/u9omOM2Eawyo5JJJdNkM2d8qzkQ0bRuRd4HarmGunSouyb9NY7egWN5E5lUc3 -->
<!-- a2AROzAdHdYpObpCOdeAY2P5XqtJkk79aROpzw16wCjdSn8qMzCBzR7rvH2WVkvF -->
<!-- HLIxZQET1yhPb6lRmpgBQNnzidHV2Ocxjc8wNiIDzgbDkmlx54QPfw7RwQi8p1fy -->
<!-- 4byhBrTjv568x8NGv3gwb0RbAgMBAAGjggFzMIIBbzAfBgNVHSUEGDAWBgorBgEE -->
<!-- AYI3TAgBBggrBgEFBQcDAzAdBgNVHQ4EFgQU8huhNbETDU+ZWllL4DNMPCijEU4w -->
<!-- RQYDVR0RBD4wPKQ6MDgxHjAcBgNVBAsTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEW -->
<!-- MBQGA1UEBRMNMjMwMDEyKzUwMjkyMzAfBgNVHSMEGDAWgBRIbmTlUAXTgqoXNzci -->
<!-- tW2oynUClTBUBgNVHR8ETTBLMEmgR6BFhkNodHRwOi8vd3d3Lm1pY3Jvc29mdC5j -->
<!-- b20vcGtpb3BzL2NybC9NaWNDb2RTaWdQQ0EyMDExXzIwMTEtMDctMDguY3JsMGEG -->
<!-- CCsGAQUFBwEBBFUwUzBRBggrBgEFBQcwAoZFaHR0cDovL3d3dy5taWNyb3NvZnQu -->
<!-- Y29tL3BraW9wcy9jZXJ0cy9NaWNDb2RTaWdQQ0EyMDExXzIwMTEtMDctMDguY3J0 -->
<!-- MAwGA1UdEwEB/wQCMAAwDQYJKoZIhvcNAQELBQADggIBAIjmD9IpQVvfB1QehvpC -->
<!-- Ge7QeTQkKQ7j3bmDMjwSqFL4ri6ae9IFTdpywn5smmtSIyKYDn3/nHtaEn0X1NBj -->
<!-- L5oP0BjAy1sqxD+uy35B+V8wv5GrxhMDJP8l2QjLtH/UglSTIhLqyt8bUAqVfyfp -->
<!-- h4COMRvwwjTvChtCnUXXACuCXYHWalOoc0OU2oGN+mPJIJJxaNQc1sjBsMbGIWv3 -->
<!-- cmgSHkCEmrMv7yaidpePt6V+yPMik+eXw3IfZ5eNOiNgL1rZzgSJfTnvUqiaEQ0X -->
<!-- dG1HbkDv9fv6CTq6m4Ty3IzLiwGSXYxRIXTxT4TYs5VxHy2uFjFXWVSL0J2ARTYL -->
<!-- E4Oyl1wXDF1PX4bxg1yDMfKPHcE1Ijic5lx1KdK1SkaEJdto4hd++05J9Bf9TAmi -->
<!-- u6EK6C9Oe5vRadroJCK26uCUI4zIjL/qG7mswW+qT0CW0gnR9JHkXCWNbo8ccMk1 -->
<!-- sJatmRoSAifbgzaYbUz8+lv+IXy5GFuAmLnNbGjacB3IMGpa+lbFgih57/fIhamq -->
<!-- 5VhxgaEmn/UjWyr+cPiAFWuTVIpfsOjbEAww75wURNM1Imp9NJKye1O24EspEHmb -->
<!-- DmqCUcq7NqkOKIG4PVm3hDDED/WQpzJDkvu4FrIbvyTGVU01vKsg4UfcdiZ0fQ+/ -->
<!-- V0hf8yrtq9CkB8iIuk5bBxuPMIIHejCCBWKgAwIBAgIKYQ6Q0gAAAAAAAzANBgkq -->
<!-- hkiG9w0BAQsFADCBiDELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldhc2hpbmd0b24x -->
<!-- EDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlv -->
<!-- bjEyMDAGA1UEAxMpTWljcm9zb2Z0IFJvb3QgQ2VydGlmaWNhdGUgQXV0aG9yaXR5 -->
<!-- IDIwMTEwHhcNMTEwNzA4MjA1OTA5WhcNMjYwNzA4MjEwOTA5WjB+MQswCQYDVQQG -->
<!-- EwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwG -->
<!-- A1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSgwJgYDVQQDEx9NaWNyb3NvZnQg -->
<!-- Q29kZSBTaWduaW5nIFBDQSAyMDExMIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIIC -->
<!-- CgKCAgEAq/D6chAcLq3YbqqCEE00uvK2WCGfQhsqa+laUKq4BjgaBEm6f8MMHt03 -->
<!-- a8YS2AvwOMKZBrDIOdUBFDFC04kNeWSHfpRgJGyvnkmc6Whe0t+bU7IKLMOv2akr -->
<!-- rnoJr9eWWcpgGgXpZnboMlImEi/nqwhQz7NEt13YxC4Ddato88tt8zpcoRb0Rrrg -->
<!-- OGSsbmQ1eKagYw8t00CT+OPeBw3VXHmlSSnnDb6gE3e+lD3v++MrWhAfTVYoonpy -->
<!-- 4BI6t0le2O3tQ5GD2Xuye4Yb2T6xjF3oiU+EGvKhL1nkkDstrjNYxbc+/jLTswM9 -->
<!-- sbKvkjh+0p2ALPVOVpEhNSXDOW5kf1O6nA+tGSOEy/S6A4aN91/w0FK/jJSHvMAh -->
<!-- dCVfGCi2zCcoOCWYOUo2z3yxkq4cI6epZuxhH2rhKEmdX4jiJV3TIUs+UsS1Vz8k -->
<!-- A/DRelsv1SPjcF0PUUZ3s/gA4bysAoJf28AVs70b1FVL5zmhD+kjSbwYuER8ReTB -->
<!-- w3J64HLnJN+/RpnF78IcV9uDjexNSTCnq47f7Fufr/zdsGbiwZeBe+3W7UvnSSmn -->
<!-- Eyimp31ngOaKYnhfsi+E11ecXL93KCjx7W3DKI8sj0A3T8HhhUSJxAlMxdSlQy90 -->
<!-- lfdu+HggWCwTXWCVmj5PM4TasIgX3p5O9JawvEagbJjS4NaIjAsCAwEAAaOCAe0w -->
<!-- ggHpMBAGCSsGAQQBgjcVAQQDAgEAMB0GA1UdDgQWBBRIbmTlUAXTgqoXNzcitW2o -->
<!-- ynUClTAZBgkrBgEEAYI3FAIEDB4KAFMAdQBiAEMAQTALBgNVHQ8EBAMCAYYwDwYD -->
<!-- VR0TAQH/BAUwAwEB/zAfBgNVHSMEGDAWgBRyLToCMZBDuRQFTuHqp8cx0SOJNDBa -->
<!-- BgNVHR8EUzBRME+gTaBLhklodHRwOi8vY3JsLm1pY3Jvc29mdC5jb20vcGtpL2Ny -->
<!-- bC9wcm9kdWN0cy9NaWNSb29DZXJBdXQyMDExXzIwMTFfMDNfMjIuY3JsMF4GCCsG -->
<!-- AQUFBwEBBFIwUDBOBggrBgEFBQcwAoZCaHR0cDovL3d3dy5taWNyb3NvZnQuY29t -->
<!-- L3BraS9jZXJ0cy9NaWNSb29DZXJBdXQyMDExXzIwMTFfMDNfMjIuY3J0MIGfBgNV -->
<!-- HSAEgZcwgZQwgZEGCSsGAQQBgjcuAzCBgzA/BggrBgEFBQcCARYzaHR0cDovL3d3 -->
<!-- dy5taWNyb3NvZnQuY29tL3BraW9wcy9kb2NzL3ByaW1hcnljcHMuaHRtMEAGCCsG -->
<!-- AQUFBwICMDQeMiAdAEwAZQBnAGEAbABfAHAAbwBsAGkAYwB5AF8AcwB0AGEAdABl -->
<!-- AG0AZQBuAHQALiAdMA0GCSqGSIb3DQEBCwUAA4ICAQBn8oalmOBUeRou09h0ZyKb -->
<!-- C5YR4WOSmUKWfdJ5DJDBZV8uLD74w3LRbYP+vj/oCso7v0epo/Np22O/IjWll11l -->
<!-- hJB9i0ZQVdgMknzSGksc8zxCi1LQsP1r4z4HLimb5j0bpdS1HXeUOeLpZMlEPXh6 -->
<!-- I/MTfaaQdION9MsmAkYqwooQu6SpBQyb7Wj6aC6VoCo/KmtYSWMfCWluWpiW5IP0 -->
<!-- wI/zRive/DvQvTXvbiWu5a8n7dDd8w6vmSiXmE0OPQvyCInWH8MyGOLwxS3OW560 -->
<!-- STkKxgrCxq2u5bLZ2xWIUUVYODJxJxp/sfQn+N4sOiBpmLJZiWhub6e3dMNABQam -->
<!-- ASooPoI/E01mC8CzTfXhj38cbxV9Rad25UAqZaPDXVJihsMdYzaXht/a8/jyFqGa -->
<!-- J+HNpZfQ7l1jQeNbB5yHPgZ3BtEGsXUfFL5hYbXw3MYbBL7fQccOKO7eZS/sl/ah -->
<!-- XJbYANahRr1Z85elCUtIEJmAH9AAKcWxm6U/RXceNcbSoqKfenoi+kiVH6v7RyOA -->
<!-- 9Z74v2u3S5fi63V4GuzqN5l5GEv/1rMjaHXmr/r8i+sLgOppO6/8MO0ETI7f33Vt -->
<!-- Y5E90Z1WTk+/gFcioXgRMiF670EKsT/7qMykXcGhiJtXcVZOSEXAQsmbdlsKgEhr -->
<!-- /Xmfwb1tbWrJUnMTDXpQzTGCGgowghoGAgEBMIGVMH4xCzAJBgNVBAYTAlVTMRMw -->
<!-- EQYDVQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVN -->
<!-- aWNyb3NvZnQgQ29ycG9yYXRpb24xKDAmBgNVBAMTH01pY3Jvc29mdCBDb2RlIFNp -->
<!-- Z25pbmcgUENBIDIwMTECEzMAAAQEbHQG/1crJ3IAAAAABAQwDQYJYIZIAWUDBAIB -->
<!-- BQCgga4wGQYJKoZIhvcNAQkDMQwGCisGAQQBgjcCAQQwHAYKKwYBBAGCNwIBCzEO -->
<!-- MAwGCisGAQQBgjcCARUwLwYJKoZIhvcNAQkEMSIEIGSf8QLpXj4i9mq2Z7Z08/zl -->
<!-- RgO4fLfKkqmjB5x8gMgdMEIGCisGAQQBgjcCAQwxNDAyoBSAEgBNAGkAYwByAG8A -->
<!-- cwBvAGYAdKEagBhodHRwOi8vd3d3Lm1pY3Jvc29mdC5jb20wDQYJKoZIhvcNAQEB -->
<!-- BQAEggEAqTE1RRQwr8ae/582EDot69mMLHqroYfse8GY8+8Hn0eaX1Xild+vHZtf -->
<!-- sc/8Ybrr8iVox/ZQNY/mMb1d252MTYH/wodleSyJKkE8Gt1doj238JI9eOS5THj4 -->
<!-- sdmZu5xqPjYL6qfjMo5haEUISdlN/h1fGflwOENoQUI9OA2TM8zSOMpgEU0fj/+i -->
<!-- yCDtGup5nA5vl6byIsIqtD52YxHdq+FKDC+wuvawOAcNf4NXrc0IbTT245eWsoqk -->
<!-- 8cJIghuwB8D7fDfanNZlsdRMEDCabOWpqwQK6nHXzeeKZ6ObO4/JLFLco7BBOLay -->
<!-- L+GjBuzF9ZXDIk0VhBXJWTLdr5QR2KGCF5QwgheQBgorBgEEAYI3AwMBMYIXgDCC -->
<!-- F3wGCSqGSIb3DQEHAqCCF20wghdpAgEDMQ8wDQYJYIZIAWUDBAIBBQAwggFSBgsq -->
<!-- hkiG9w0BCRABBKCCAUEEggE9MIIBOQIBAQYKKwYBBAGEWQoDATAxMA0GCWCGSAFl -->
<!-- AwQCAQUABCD9aPZCyAgmpyWtxFiDELShtm+Lm51wgWKauoQdPsKupgIGaErf2xEo -->
<!-- GBMyMDI1MDcwOTExMDcyMy44MzhaMASAAgH0oIHRpIHOMIHLMQswCQYDVQQGEwJV -->
<!-- UzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwGA1UE -->
<!-- ChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSUwIwYDVQQLExxNaWNyb3NvZnQgQW1l -->
<!-- cmljYSBPcGVyYXRpb25zMScwJQYDVQQLEx5uU2hpZWxkIFRTUyBFU046QTAwMC0w -->
<!-- NUUwLUQ5NDcxJTAjBgNVBAMTHE1pY3Jvc29mdCBUaW1lLVN0YW1wIFNlcnZpY2Wg -->
<!-- ghHqMIIHIDCCBQigAwIBAgITMwAAAgh4nVhdksfZUgABAAACCDANBgkqhkiG9w0B -->
<!-- AQsFADB8MQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UE -->
<!-- BxMHUmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSYwJAYD -->
<!-- VQQDEx1NaWNyb3NvZnQgVGltZS1TdGFtcCBQQ0EgMjAxMDAeFw0yNTAxMzAxOTQy -->
<!-- NTNaFw0yNjA0MjIxOTQyNTNaMIHLMQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2Fz -->
<!-- aGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENv -->
<!-- cnBvcmF0aW9uMSUwIwYDVQQLExxNaWNyb3NvZnQgQW1lcmljYSBPcGVyYXRpb25z -->
<!-- MScwJQYDVQQLEx5uU2hpZWxkIFRTUyBFU046QTAwMC0wNUUwLUQ5NDcxJTAjBgNV -->
<!-- BAMTHE1pY3Jvc29mdCBUaW1lLVN0YW1wIFNlcnZpY2UwggIiMA0GCSqGSIb3DQEB -->
<!-- AQUAA4ICDwAwggIKAoICAQC1y3AI5lIz3Ip1nK5BMUUbGRsjSnCz/VGs33zvY0Ne -->
<!-- shsPgfld3/Z3/3dS8WKBLlDlosmXJOZlFSiNXUd6DTJxA9ik/ZbCdWJ78LKjbN3t -->
<!-- FkX2c6RRpRMpA8sq/oBbRryP3c8Q/gxpJAKHHz8cuSn7ewfCLznNmxqliTk3Q5LH -->
<!-- qz2PjeYKD/dbKMBT2TAAWAvum4z/HXIJ6tFdGoNV4WURZswCSt6ROwaqQ1oAYGvE -->
<!-- ndH+DXZq1+bHsgvcPNCdTSIpWobQiJS/UKLiR02KNCqB4I9yajFTSlnMIEMz/Ni5 -->
<!-- 38oGI64phcvNpUe2+qaKWHZ8d4T1KghvRmSSF4YF5DNEJbxaCUwsy7nULmsFnTaO -->
<!-- jVOoTFWWfWXvBuOKkBcQKWGKvrki976j4x+5ezAP36fq3u6dHRJTLZAu4dEuOooU -->
<!-- 3+kMZr+RBYWjTHQCKV+yZ1ST0eGkbHXoA2lyyRDlNjBQcoeZIxWCZts/d3+nf1ji -->
<!-- SLN6f6wdHaUz0ADwOTQ/aEo1IC85eFePvyIKaxFJkGU2Mqa6Xzq3qCq5tokIHtjh -->
<!-- ogsrEgfDKTeFXTtdhl1IPtLcCfMcWOGGAXosVUU7G948F6W96424f2VHD8L3FoyA -->
<!-- I9+r4zyIQUmqiESzuQWeWpTTjFYwCmgXaGOuSDV8cNOVQB6IPzPneZhVTjwxbAZl -->
<!-- aQIDAQABo4IBSTCCAUUwHQYDVR0OBBYEFKMx4vfOqcUTgYOVB9f18/mhegFNMB8G -->
<!-- A1UdIwQYMBaAFJ+nFV0AXmJdg/Tl0mWnG1M1GelyMF8GA1UdHwRYMFYwVKBSoFCG -->
<!-- Tmh0dHA6Ly93d3cubWljcm9zb2Z0LmNvbS9wa2lvcHMvY3JsL01pY3Jvc29mdCUy -->
<!-- MFRpbWUtU3RhbXAlMjBQQ0ElMjAyMDEwKDEpLmNybDBsBggrBgEFBQcBAQRgMF4w -->
<!-- XAYIKwYBBQUHMAKGUGh0dHA6Ly93d3cubWljcm9zb2Z0LmNvbS9wa2lvcHMvY2Vy -->
<!-- dHMvTWljcm9zb2Z0JTIwVGltZS1TdGFtcCUyMFBDQSUyMDIwMTAoMSkuY3J0MAwG -->
<!-- A1UdEwEB/wQCMAAwFgYDVR0lAQH/BAwwCgYIKwYBBQUHAwgwDgYDVR0PAQH/BAQD -->
<!-- AgeAMA0GCSqGSIb3DQEBCwUAA4ICAQBRszKJKwAfswqdaQPFiaYB/ZNAYWDa040X -->
<!-- TcQsCaCua5nsG1IslYaSpH7miTLr6eQEqXczZoqeOa/xvDnMGifGNda0CHbQwtpn -->
<!-- IhsutrKO2jhjEaGwlJgOMql21r7Ik6XnBza0e3hBOu4UBkMl/LEX+AURt7i7+RTN -->
<!-- sGN0cXPwPSbTFE+9z7WagGbY9pwUo/NxkGJseqGCQ/9K2VMU74bw5e7+8IGUhM2x -->
<!-- spJPqnSeHPhYmcB0WclOxcVIfj/ZuQvworPbTEEYDVCzSN37c0yChPMY7FJ+HGFB -->
<!-- NJxwd5lKIr7GYfq8a0gOiC2ljGYlc4rt4cCed1XKg83f0l9aUVimWBYXtfNebhpf -->
<!-- r6Lc3jD8NgsrDhzt0WgnIdnTZCi7jxjsIBilH99pY5/h6bQcLKK/E6KCP9E1YN78 -->
<!-- fLaOXkXMyO6xLrvQZ+uCSi1hdTufFC7oSB/CU5RbfIVHXG0j1o2n1tne4eCbNfKq -->
<!-- UPTE31tNbWBR23Yiy0r3kQmHeYE1GLbL4pwknqaip1BRn6WIUMJtgncawEN33f8A -->
<!-- YGZ4a3NnHopzGVV6neffGVag4Tduy+oy1YF+shChoXdMqfhPWFpHe3uJGT4GJEiN -->
<!-- s4+28a/wHUuF+aRaR0cN5P7XlOwU1360iUCJtQdvKQaNAwGI29KOwS3QGriR9F2j -->
<!-- OGPUAlpeEzCCB3EwggVZoAMCAQICEzMAAAAVxedrngKbSZkAAAAAABUwDQYJKoZI -->
<!-- hvcNAQELBQAwgYgxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNoaW5ndG9uMRAw -->
<!-- DgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNyb3NvZnQgQ29ycG9yYXRpb24x -->
<!-- MjAwBgNVBAMTKU1pY3Jvc29mdCBSb290IENlcnRpZmljYXRlIEF1dGhvcml0eSAy -->
<!-- MDEwMB4XDTIxMDkzMDE4MjIyNVoXDTMwMDkzMDE4MzIyNVowfDELMAkGA1UEBhMC -->
<!-- VVMxEzARBgNVBAgTCldhc2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNV -->
<!-- BAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEmMCQGA1UEAxMdTWljcm9zb2Z0IFRp -->
<!-- bWUtU3RhbXAgUENBIDIwMTAwggIiMA0GCSqGSIb3DQEBAQUAA4ICDwAwggIKAoIC -->
<!-- AQDk4aZM57RyIQt5osvXJHm9DtWC0/3unAcH0qlsTnXIyjVX9gF/bErg4r25Phdg -->
<!-- M/9cT8dm95VTcVrifkpa/rg2Z4VGIwy1jRPPdzLAEBjoYH1qUoNEt6aORmsHFPPF -->
<!-- dvWGUNzBRMhxXFExN6AKOG6N7dcP2CZTfDlhAnrEqv1yaa8dq6z2Nr41JmTamDu6 -->
<!-- GnszrYBbfowQHJ1S/rboYiXcag/PXfT+jlPP1uyFVk3v3byNpOORj7I5LFGc6XBp -->
<!-- Dco2LXCOMcg1KL3jtIckw+DJj361VI/c+gVVmG1oO5pGve2krnopN6zL64NF50Zu -->
<!-- yjLVwIYwXE8s4mKyzbnijYjklqwBSru+cakXW2dg3viSkR4dPf0gz3N9QZpGdc3E -->
<!-- XzTdEonW/aUgfX782Z5F37ZyL9t9X4C626p+Nuw2TPYrbqgSUei/BQOj0XOmTTd0 -->
<!-- lBw0gg/wEPK3Rxjtp+iZfD9M269ewvPV2HM9Q07BMzlMjgK8QmguEOqEUUbi0b1q -->
<!-- GFphAXPKZ6Je1yh2AuIzGHLXpyDwwvoSCtdjbwzJNmSLW6CmgyFdXzB0kZSU2LlQ -->
<!-- +QuJYfM2BjUYhEfb3BvR/bLUHMVr9lxSUV0S2yW6r1AFemzFER1y7435UsSFF5PA -->
<!-- PBXbGjfHCBUYP3irRbb1Hode2o+eFnJpxq57t7c+auIurQIDAQABo4IB3TCCAdkw -->
<!-- EgYJKwYBBAGCNxUBBAUCAwEAATAjBgkrBgEEAYI3FQIEFgQUKqdS/mTEmr6CkTxG -->
<!-- NSnPEP8vBO4wHQYDVR0OBBYEFJ+nFV0AXmJdg/Tl0mWnG1M1GelyMFwGA1UdIARV -->
<!-- MFMwUQYMKwYBBAGCN0yDfQEBMEEwPwYIKwYBBQUHAgEWM2h0dHA6Ly93d3cubWlj -->
<!-- cm9zb2Z0LmNvbS9wa2lvcHMvRG9jcy9SZXBvc2l0b3J5Lmh0bTATBgNVHSUEDDAK -->
<!-- BggrBgEFBQcDCDAZBgkrBgEEAYI3FAIEDB4KAFMAdQBiAEMAQTALBgNVHQ8EBAMC -->
<!-- AYYwDwYDVR0TAQH/BAUwAwEB/zAfBgNVHSMEGDAWgBTV9lbLj+iiXGJo0T2UkFvX -->
<!-- zpoYxDBWBgNVHR8ETzBNMEugSaBHhkVodHRwOi8vY3JsLm1pY3Jvc29mdC5jb20v -->
<!-- cGtpL2NybC9wcm9kdWN0cy9NaWNSb29DZXJBdXRfMjAxMC0wNi0yMy5jcmwwWgYI -->
<!-- KwYBBQUHAQEETjBMMEoGCCsGAQUFBzAChj5odHRwOi8vd3d3Lm1pY3Jvc29mdC5j -->
<!-- b20vcGtpL2NlcnRzL01pY1Jvb0NlckF1dF8yMDEwLTA2LTIzLmNydDANBgkqhkiG -->
<!-- 9w0BAQsFAAOCAgEAnVV9/Cqt4SwfZwExJFvhnnJL/Klv6lwUtj5OR2R4sQaTlz0x -->
<!-- M7U518JxNj/aZGx80HU5bbsPMeTCj/ts0aGUGCLu6WZnOlNN3Zi6th542DYunKmC -->
<!-- VgADsAW+iehp4LoJ7nvfam++Kctu2D9IdQHZGN5tggz1bSNU5HhTdSRXud2f8449 -->
<!-- xvNo32X2pFaq95W2KFUn0CS9QKC/GbYSEhFdPSfgQJY4rPf5KYnDvBewVIVCs/wM -->
<!-- nosZiefwC2qBwoEZQhlSdYo2wh3DYXMuLGt7bj8sCXgU6ZGyqVvfSaN0DLzskYDS -->
<!-- PeZKPmY7T7uG+jIa2Zb0j/aRAfbOxnT99kxybxCrdTDFNLB62FD+CljdQDzHVG2d -->
<!-- Y3RILLFORy3BFARxv2T5JL5zbcqOCb2zAVdJVGTZc9d/HltEAY5aGZFrDZ+kKNxn -->
<!-- GSgkujhLmm77IVRrakURR6nxt67I6IleT53S0Ex2tVdUCbFpAUR+fKFhbHP+Crvs -->
<!-- QWY9af3LwUFJfn6Tvsv4O+S3Fb+0zj6lMVGEvL8CwYKiexcdFYmNcP7ntdAoGokL -->
<!-- jzbaukz5m/8K6TT4JDVnK+ANuOaMmdbhIurwJ0I9JZTmdHRbatGePu1+oDEzfbzL -->
<!-- 6Xu/OHBE0ZDxyKs6ijoIYn/ZcGNTTY3ugm2lBRDBcQZqELQdVTNYs6FwZvKhggNN -->
<!-- MIICNQIBATCB+aGB0aSBzjCByzELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldhc2hp -->
<!-- bmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBDb3Jw -->
<!-- b3JhdGlvbjElMCMGA1UECxMcTWljcm9zb2Z0IEFtZXJpY2EgT3BlcmF0aW9uczEn -->
<!-- MCUGA1UECxMeblNoaWVsZCBUU1MgRVNOOkEwMDAtMDVFMC1EOTQ3MSUwIwYDVQQD -->
<!-- ExxNaWNyb3NvZnQgVGltZS1TdGFtcCBTZXJ2aWNloiMKAQEwBwYFKw4DAhoDFQCN -->
<!-- kvu0NKcSjdYKyrhJZcsyXOUTNKCBgzCBgKR+MHwxCzAJBgNVBAYTAlVTMRMwEQYD -->
<!-- VQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNy -->
<!-- b3NvZnQgQ29ycG9yYXRpb24xJjAkBgNVBAMTHU1pY3Jvc29mdCBUaW1lLVN0YW1w -->
<!-- IFBDQSAyMDEwMA0GCSqGSIb3DQEBCwUAAgUA7BhLyDAiGA8yMDI1MDcwOTAyMDA0 -->
<!-- MFoYDzIwMjUwNzEwMDIwMDQwWjB0MDoGCisGAQQBhFkKBAExLDAqMAoCBQDsGEvI -->
<!-- AgEAMAcCAQACAicsMAcCAQACAhIvMAoCBQDsGZ1IAgEAMDYGCisGAQQBhFkKBAIx -->
<!-- KDAmMAwGCisGAQQBhFkKAwKgCjAIAgEAAgMHoSChCjAIAgEAAgMBhqAwDQYJKoZI -->
<!-- hvcNAQELBQADggEBADnXGmdDdtuw5MGwOaf96lcG8iq3aoRiQjZhOpVbGtu9gArj -->
<!-- t+uv2sUqBZjSzFlwiX1PTSAE3S8UCYzWWLeDPWmFxjIV04oimy5NgmVOGDAv2lvR -->
<!-- iCOG3Vzm317mUdC82WJTGwIYgkrC7ZU5hkXuBODWKwGCuiZ1m+lrCk7NPUg4WXLv -->
<!-- HEn6PU82GrpnaKTOqd+mRx3x8LUGua3J5JD6EoCtSKPWRMjfd6pHuD/FfCHx1m6i -->
<!-- M43oGVpeeCfG443+Rz9RZmfKX+f7N1pIFqBid4bYrv14PcsfJe0t9GZ5NyA5n8R+ -->
<!-- bO0E0hNfYg2EuhrMUU82bexalXDjwJeg/kbOVFsxggQNMIIECQIBATCBkzB8MQsw -->
<!-- CQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9u -->
<!-- ZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSYwJAYDVQQDEx1NaWNy -->
<!-- b3NvZnQgVGltZS1TdGFtcCBQQ0EgMjAxMAITMwAAAgh4nVhdksfZUgABAAACCDAN -->
<!-- BglghkgBZQMEAgEFAKCCAUowGgYJKoZIhvcNAQkDMQ0GCyqGSIb3DQEJEAEEMC8G -->
<!-- CSqGSIb3DQEJBDEiBCCxfJW8LuAANE+qqb9CP16B5UaegJkdLJ+/PbAa3P3txzCB -->
<!-- +gYLKoZIhvcNAQkQAi8xgeowgecwgeQwgb0EII//jm8JHa2W1O9778t9+Ft2Z5Nm -->
<!-- KqttPk6Q+9RRpmepMIGYMIGApH4wfDELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldh -->
<!-- c2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBD -->
<!-- b3Jwb3JhdGlvbjEmMCQGA1UEAxMdTWljcm9zb2Z0IFRpbWUtU3RhbXAgUENBIDIw -->
<!-- MTACEzMAAAIIeJ1YXZLH2VIAAQAAAggwIgQgPL73O+knfnt2bAvd1AuD2lC7DQdu -->
<!-- 5l6OGM83s86H4zEwDQYJKoZIhvcNAQELBQAEggIATio+HPI9gKyqZLX68FakiKaQ -->
<!-- 23LxIvRssgozv5stuqWU9EOax20yaHax18Wy/dFxPmsQVqxv0j9h7KwJz7FwUnKo -->
<!-- XrdFyOSCXG40EfixfrQ8C5dmflNbvq+8u6UOrJ/SX5Ylh9J/pEKmSyKGkz6A4DR7 -->
<!-- JseAhSb0rZuOuRHdc8ygZeXsl/CSN0p7Ozd8q0d7TYJ48cQHkLUCSQbMq+knWvww -->
<!-- USKBDc8gHXFxH+ILSYnsncE8sXNLacPgeN/rFe5HFFoF0Its6B+1W0qMo/rxotrg -->
<!-- no/h3oXJKeBOHS4n8dgXFjyvN/awC5dJhfL4LYmyGeLSKWAiNlYKQaCoG/BAcAwv -->
<!-- wybbHI2Z1GG69iOvL5lm5yH/VI+RgtCebMdlgVj1UAEoLN3hcAy2dCmvGt86B882 -->
<!-- ReoNeXd2plR9D8KFCyWUf0i6oyEyrtSGVFJKFBA44/KBgxp9qdHoHHjI4C/O2Y4X -->
<!-- y184me1dD0tJnKNp+cx2jG/7dYYU+H9FwCNiD7D+03yA/U3VEvYJn+WX2FtBMn/U -->
<!-- W69Fe+DPLX4YnAypPbc0m63RllsBvfm8ZDDoE1xh6Z8puY9KWsuJVsyCBXwqCHZC -->
<!-- /jBZ1rpEX8rAPIYN8NGUWWx2P8wvO6IkvtiBu2RMRlZPr1QxqVrzKD/WXEHu/DVT -->
<!-- vLgbKXFUz4v5bs1gz0E= -->
<!-- SIG # End signature block -->
