# Pfad zur Busylight SDK DLL anpassen:
$dll = ".\BusylightSDK.dll"
if (-not (Test-Path $dll)) {
    Write-Error "BusylightSDK.dll nicht gefunden unter: $dll"
    return
}
Add-Type -Path $dll

# Objekte aus dem SDK
$sdk   = New-Object -TypeName Busylight.SDK
$color = New-Object -TypeName Busylight.BusylightColor

# =============================
# Konfiguration (fein abgestimmt)
# =============================
$speedMs          = 40      # Tickrate; 30–50 ms = sehr weiches Motion-Blur
$hueStep          = 1.0     # Grad pro Tick; kleiner = langsamerer, glatterer Farbverlauf
$saturation       = 0.75    # 0..1 (etwas reduziert -> wirkt „edler“)
$brightness       = 0.22    # 0..1 (dezent/nicht so hell)
$runSecs          = 60      # Laufzeit in Sekunden

# Optionales „Atmen“ als leichte Helligkeitsmodulation (sanft, nicht pumpend)
$enableBreathing  = $true
$breathPeriodSec  = 6.0     # 5–8s fühlt sich hochwertig an
$breathDepth      = 0.15    # 0..0.3 – wie stark die Helligkeit moduliert

# Übergangsglättung (Low-Pass), verhindert abruptes „Flickern“
$lowpassAlpha     = 0.25    # 0..1; 0.15–0.35 ist butterweich

# Gamma-Korrektur (Wahrnehmung): 2.2 ist ein guter Startwert
$gamma            = 2.2

# =============================
# Utilities
# =============================

function Convert-HsvToRgbLinear {
    param(
        [double]$h,  # 0..360
        [double]$s,  # 0..1
        [double]$v   # 0..1 (linear)
    )

    if ($h -ge 360 -or $h -lt 0) { $h = $h % 360 }
    $c = $v * $s
    $x = $c * (1 - [math]::Abs((($h / 60) % 2) - 1))
    $m = $v - $c

    $sector = [int]($h / 60) % 6
    switch ($sector) {
        0 { $r = $c; $g = $x; $b = 0 }
        1 { $r = $x; $g = $c; $b = 0 }
        2 { $r = 0; $g = $c; $b = $x }
        3 { $r = 0; $g = $x; $b = $c }
        4 { $r = $x; $g = 0; $b = $c }
        5 { $r = $c; $g = 0; $b = $x }
    }

    # Linear 0..1
    return [pscustomobject]@{
        R = $r + $m
        G = $g + $m
        B = $b + $m
    }
}

function LinearTo8bitGamma {
    param(
        [double]$lin,   # 0..1 (linear)
        [double]$gamma  # z.B. 2.2
    )
    $lin = [math]::Max(0.0, [math]::Min(1.0, $lin))
    $srgb = [math]::Pow($lin, 1.0 / $gamma)
    return [int]($srgb * 255)
}

function Lerp {
    param(
        [double]$from,
        [double]$to,
        [double]$alpha  # 0..1
    )
    return $from + ($to - $from) * $alpha
}

function EaseInOutSine01 {
    param([double]$t) # 0..1
    # 0..1 mit S-Kurve (sehr weich)
    return 0.5 - 0.5 * [math]::Cos([math]::PI * $t)
}

function FadeOutCurrent {
    param(
        [int]$durationMs = 900,
        [double]$gamma = 2.2,
        [double]$startR,
        [double]$startG,
        [double]$startB,
        $sdk, $color
    )
    $steps = [math]::Max(1, [int]($durationMs / 30))
    for ($i=0; $i -le $steps; $i++) {
        $t = $i / [double]$steps
        $f = 1.0 - (EaseInOutSine01 $t)
        $r8 = LinearTo8bitGamma -lin ($startR * $f) -gamma $gamma
        $g8 = LinearTo8bitGamma -lin ($startG * $f) -gamma $gamma
        $b8 = LinearTo8bitGamma -lin ($startB * $f) -gamma $gamma
        $color.RedRGBValue   = $r8
        $color.GreenRGBValue = $g8
        $color.BlueRGBValue  = $b8
        $sdk.Light($color)
        Start-Sleep -Milliseconds 30
    }
    # sicherheitshalber ganz aus
    $color.RedRGBValue=0; $color.GreenRGBValue=0; $color.BlueRGBValue=0
    $sdk.Light($color)
}

# =============================
# Hauptloop
# =============================

$sw = [System.Diagnostics.Stopwatch]::StartNew()

# Startwerte
$h = 0.0
$lastLin = @{ R = 0.0; G = 0.0; B = 0.0 }  # für Low-Pass
$baseBrightness = [math]::Max(0.0, [math]::Min(1.0, $brightness))

# Sanftes „Anfaden“ aus Schwarz (0.6s)
FadeOutCurrent -durationMs 1 -gamma $gamma -startR 0 -startG 0 -startB 0 -sdk $sdk -color $color | Out-Null  # no-op init
$fadeInSteps = 20
for ($i=0; $i -lt $fadeInSteps; $i++) {
    $factor = EaseInOutSine01(($i+1)/$fadeInSteps) * $baseBrightness
    $rgbLin = Convert-HsvToRgbLinear -h $h -s $saturation -v $factor
    $r8 = LinearTo8bitGamma -lin $rgbLin.R -gamma $gamma
    $g8 = LinearTo8bitGamma -lin $rgbLin.G -gamma $gamma
    $b8 = LinearTo8bitGamma -lin $rgbLin.B -gamma $gamma
    $color.RedRGBValue   = $r8
    $color.GreenRGBValue = $g8
    $color.BlueRGBValue  = $b8
    $sdk.Light($color)
    Start-Sleep -Milliseconds 30
    $h = ($h + $hueStep) % 360
}

try {
    while ($sw.Elapsed.TotalSeconds -lt $runSecs) {
        # sanfter Atem-Effekt als leichte Helligkeitsmodulation
        $vMod = if ($enableBreathing -and $breathPeriodSec -gt 0) {
            $phase = ($sw.Elapsed.TotalSeconds % $breathPeriodSec) / $breathPeriodSec
            $ease  = EaseInOutSine01 $phase             # 0..1
            $minF  = 1.0 - $breathDepth                 # z.B. 0.85
            $maxF  = 1.0 + $breathDepth                 # z.B. 1.15
            $scale = $minF + ($maxF - $minF) * $ease
            $baseBrightness * $scale
        } else {
            $baseBrightness
        }

        # HSV -> linear RGB (0..1)
        $rgbTarget = Convert-HsvToRgbLinear -h $h -s $saturation -v $vMod

        # Low-Pass (weiche Übergänge)
        $linR = Lerp -from $lastLin.R -to $rgbTarget.R -alpha $lowpassAlpha
        $linG = Lerp -from $lastLin.G -to $rgbTarget.G -alpha $lowpassAlpha
        $linB = Lerp -from $lastLin.B -to $rgbTarget.B -alpha $lowpassAlpha
        $lastLin.R = $linR; $lastLin.G = $linG; $lastLin.B = $linB

        # Gamma-Korrektur -> 8-bit
        $r8 = LinearTo8bitGamma -lin $linR -gamma $gamma
        $g8 = LinearTo8bitGamma -lin $linG -gamma $gamma
        $b8 = LinearTo8bitGamma -lin $linB -gamma $gamma

        # Auf das Busylight anwenden
        $color.RedRGBValue   = $r8
        $color.GreenRGBValue = $g8
        $color.BlueRGBValue  = $b8
        $sdk.Light($color)

        Start-Sleep -Milliseconds $speedMs
        $h = ($h + $hueStep) % 360
    }
}
finally {
    # Sanfter Fade-Out aus aktuellem linearen Level
    FadeOutCurrent -durationMs 900 -gamma $gamma -startR $lastLin.R -startG $lastLin.G -startB $lastLin.B -sdk $sdk -color $color
}