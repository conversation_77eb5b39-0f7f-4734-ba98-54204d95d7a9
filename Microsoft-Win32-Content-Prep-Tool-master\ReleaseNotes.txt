1.4
- Microsoft Intune Win32 App Packaging Tool will check the Windows Classic setup files and generate a .intunewin file to be imported into Intune Azure Portal.

1.5
- Fixed the error when processing .msp file as setup file.

1.6
- Add catalog support for Win10 S.

1.7
- Add Unicode support for file and folder name.

1.8
- Fixed the ArgumentOutOfRangeException error when running the tool in Windows Terminal.
- Added some prefix for the log messages for better readability.

1.8.1
- Adding the actual binaries for v1.8. Please do not use v1.8 and use v1.8.1.

1.8.2
- Add support for version parameter (-v)
- Add support for redirection of output to another file

1.8.3
- Fixed the issue where the tool is not working with catalog folders.

1.8.4
- Fixed an issue with version 1.8.3 where the Win32PrepTool will not work with files greater than 4gb.

1.8.5
- Fixed an issue with Win32 content prep tool crashing.

1.8.6
- Fixing an issue where the Win32 content prep tool is crashing.