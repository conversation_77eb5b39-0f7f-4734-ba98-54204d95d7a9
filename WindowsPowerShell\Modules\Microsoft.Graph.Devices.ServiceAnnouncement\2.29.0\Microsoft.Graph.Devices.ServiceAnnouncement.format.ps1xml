<?xml version="1.0" encoding="utf-16"?>
<Configuration>
  <ViewDefinitions>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.DevicesServiceAnnouncementIdentity</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.DevicesServiceAnnouncementIdentity</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>ServiceAnnouncementAttachmentId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>ServiceHealthId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>ServiceHealthIssueId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>ServiceUpdateMessageId</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>ServiceAnnouncementAttachmentId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>ServiceHealthId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>ServiceHealthIssueId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>ServiceUpdateMessageId</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphEntity</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphEntity</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Id</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Id</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphItemBody</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphItemBody</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Content</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>ContentType</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Content</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>ContentType</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphKeyValuePair</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphKeyValuePair</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Name</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Value</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Name</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Value</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphODataErrorsErrorDetails</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphODataErrorsErrorDetails</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Code</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Message</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Target</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Code</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Message</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Target</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphODataErrorsInnerError</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphODataErrorsInnerError</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>ClientRequestId</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Date</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>RequestId</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>ClientRequestId</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Date</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>RequestId</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphODataErrorsMainError</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphODataErrorsMainError</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Code</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Message</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Target</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Code</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Message</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Target</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphServiceAnnouncement</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphServiceAnnouncement</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Id</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Id</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphServiceAnnouncementAttachment</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphServiceAnnouncementAttachment</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Id</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Content</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>ContentType</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>LastModifiedDateTime</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Name</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Size</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Id</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Content</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>ContentType</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>LastModifiedDateTime</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Name</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Size</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphServiceAnnouncementAttachmentCollectionResponse</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphServiceAnnouncementAttachmentCollectionResponse</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>OdataNextLink</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>OdataNextLink</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphServiceAnnouncementBase</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphServiceAnnouncementBase</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Id</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>EndDateTime</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>LastModifiedDateTime</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>StartDateTime</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Title</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Id</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>EndDateTime</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>LastModifiedDateTime</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>StartDateTime</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Title</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphServiceHealth</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphServiceHealth</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Id</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Service</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Status</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Id</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Service</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Status</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphServiceHealthCollectionResponse</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphServiceHealthCollectionResponse</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>OdataNextLink</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>OdataNextLink</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphServiceHealthIssue</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphServiceHealthIssue</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>EndDateTime</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Id</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>LastModifiedDateTime</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>StartDateTime</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Title</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Classification</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Feature</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>FeatureGroup</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>ImpactDescription</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>IsResolved</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Origin</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Service</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Status</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>EndDateTime</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Id</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>LastModifiedDateTime</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>StartDateTime</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Title</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Classification</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Feature</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>FeatureGroup</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>ImpactDescription</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>IsResolved</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Origin</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Service</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Status</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphServiceHealthIssueCollectionResponse</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphServiceHealthIssueCollectionResponse</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>OdataNextLink</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>OdataNextLink</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphServiceHealthIssuePost</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphServiceHealthIssuePost</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>CreatedDateTime</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>PostType</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>CreatedDateTime</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>PostType</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphServiceUpdateMessage</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphServiceUpdateMessage</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>EndDateTime</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Id</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>LastModifiedDateTime</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>StartDateTime</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Title</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>ActionRequiredByDateTime</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>AttachmentsArchive</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Category</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>HasAttachments</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>IsMajorChange</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Services</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Severity</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Tags</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>EndDateTime</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Id</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>LastModifiedDateTime</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>StartDateTime</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Title</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>ActionRequiredByDateTime</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>AttachmentsArchive</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Category</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>HasAttachments</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>IsMajorChange</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Services</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Severity</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Tags</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphServiceUpdateMessageCollectionResponse</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphServiceUpdateMessageCollectionResponse</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>OdataNextLink</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>OdataNextLink</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.MicrosoftGraphServiceUpdateMessageViewpoint</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.MicrosoftGraphServiceUpdateMessageViewpoint</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>IsArchived</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>IsFavorited</Label>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>IsRead</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>IsArchived</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>IsFavorited</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>IsRead</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.Paths19OsejwAdminServiceannouncementMessagesMicrosoftGraphArchivePostRequestbodyContentApplicationJsonSchema</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.Paths19OsejwAdminServiceannouncementMessagesMicrosoftGraphArchivePostRequestbodyContentApplicationJsonSchema</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>MessageIds</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>MessageIds</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.Paths1C7Ez7QAdminServiceannouncementMessagesMicrosoftGraphMarkunreadPostResponses2XxContentApplicationJsonSchema</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.Paths1C7Ez7QAdminServiceannouncementMessagesMicrosoftGraphMarkunreadPostResponses2XxContentApplicationJsonSchema</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Value</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Value</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.Paths1Hqlbr0AdminServiceannouncementMessagesMicrosoftGraphFavoritePostRequestbodyContentApplicationJsonSchema</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.Paths1Hqlbr0AdminServiceannouncementMessagesMicrosoftGraphFavoritePostRequestbodyContentApplicationJsonSchema</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>MessageIds</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>MessageIds</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.Paths1U36FhAdminServiceannouncementMessagesMicrosoftGraphUnfavoritePostRequestbodyContentApplicationJsonSchema</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.Paths1U36FhAdminServiceannouncementMessagesMicrosoftGraphUnfavoritePostRequestbodyContentApplicationJsonSchema</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>MessageIds</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>MessageIds</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.Paths1W5444PAdminServiceannouncementMessagesMicrosoftGraphMarkreadPostResponses2XxContentApplicationJsonSchema</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.Paths1W5444PAdminServiceannouncementMessagesMicrosoftGraphMarkreadPostResponses2XxContentApplicationJsonSchema</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Value</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Value</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.Paths1WyczwcAdminServiceannouncementMessagesMicrosoftGraphFavoritePostResponses2XxContentApplicationJsonSchema</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.Paths1WyczwcAdminServiceannouncementMessagesMicrosoftGraphFavoritePostResponses2XxContentApplicationJsonSchema</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Value</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Value</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.Paths5Dpd6IAdminServiceannouncementMessagesMicrosoftGraphUnfavoritePostResponses2XxContentApplicationJsonSchema</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.Paths5Dpd6IAdminServiceannouncementMessagesMicrosoftGraphUnfavoritePostResponses2XxContentApplicationJsonSchema</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Value</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Value</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.Paths9Q4ErzAdminServiceannouncementMessagesMicrosoftGraphMarkreadPostRequestbodyContentApplicationJsonSchema</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.Paths9Q4ErzAdminServiceannouncementMessagesMicrosoftGraphMarkreadPostRequestbodyContentApplicationJsonSchema</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>MessageIds</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>MessageIds</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.PathsLu4M80AdminServiceannouncementMessagesMicrosoftGraphUnarchivePostResponses2XxContentApplicationJsonSchema</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.PathsLu4M80AdminServiceannouncementMessagesMicrosoftGraphUnarchivePostResponses2XxContentApplicationJsonSchema</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Value</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Value</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.PathsVjmimyAdminServiceannouncementMessagesMicrosoftGraphArchivePostResponses2XxContentApplicationJsonSchema</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.PathsVjmimyAdminServiceannouncementMessagesMicrosoftGraphArchivePostResponses2XxContentApplicationJsonSchema</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Value</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Value</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.PathsX0Td2HAdminServiceannouncementMessagesMicrosoftGraphMarkunreadPostRequestbodyContentApplicationJsonSchema</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.PathsX0Td2HAdminServiceannouncementMessagesMicrosoftGraphMarkunreadPostRequestbodyContentApplicationJsonSchema</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>MessageIds</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>MessageIds</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Microsoft.Graph.PowerShell.Models.PathsYkfdhbAdminServiceannouncementMessagesMicrosoftGraphUnarchivePostRequestbodyContentApplicationJsonSchema</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.Graph.PowerShell.Models.PathsYkfdhbAdminServiceannouncementMessagesMicrosoftGraphUnarchivePostRequestbodyContentApplicationJsonSchema</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>MessageIds</Label>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>MessageIds</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
  </ViewDefinitions>
</Configuration>
<!-- SIG # Begin signature block -->
<!-- MIIoKAYJKoZIhvcNAQcCoIIoGTCCKBUCAQExDzANBglghkgBZQMEAgEFADB5Bgor -->
<!-- BgEEAYI3AgEEoGswaTA0BgorBgEEAYI3AgEeMCYCAwEAAAQQH8w7YFlLCE63JNLG -->
<!-- KX7zUQIBAAIBAAIBAAIBAAIBADAxMA0GCWCGSAFlAwQCAQUABCDOr39vaygpUMP1 -->
<!-- saFSTCvtM7IICkyqaHbjMiSA5zmASKCCDXYwggX0MIID3KADAgECAhMzAAAEBGx0 -->
<!-- Bv9XKydyAAAAAAQEMA0GCSqGSIb3DQEBCwUAMH4xCzAJBgNVBAYTAlVTMRMwEQYD -->
<!-- VQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNy -->
<!-- b3NvZnQgQ29ycG9yYXRpb24xKDAmBgNVBAMTH01pY3Jvc29mdCBDb2RlIFNpZ25p -->
<!-- bmcgUENBIDIwMTEwHhcNMjQwOTEyMjAxMTE0WhcNMjUwOTExMjAxMTE0WjB0MQsw -->
<!-- CQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9u -->
<!-- ZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMR4wHAYDVQQDExVNaWNy -->
<!-- b3NvZnQgQ29ycG9yYXRpb24wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIB -->
<!-- AQC0KDfaY50MDqsEGdlIzDHBd6CqIMRQWW9Af1LHDDTuFjfDsvna0nEuDSYJmNyz -->
<!-- NB10jpbg0lhvkT1AzfX2TLITSXwS8D+mBzGCWMM/wTpciWBV/pbjSazbzoKvRrNo -->
<!-- DV/u9omOM2Eawyo5JJJdNkM2d8qzkQ0bRuRd4HarmGunSouyb9NY7egWN5E5lUc3 -->
<!-- a2AROzAdHdYpObpCOdeAY2P5XqtJkk79aROpzw16wCjdSn8qMzCBzR7rvH2WVkvF -->
<!-- HLIxZQET1yhPb6lRmpgBQNnzidHV2Ocxjc8wNiIDzgbDkmlx54QPfw7RwQi8p1fy -->
<!-- 4byhBrTjv568x8NGv3gwb0RbAgMBAAGjggFzMIIBbzAfBgNVHSUEGDAWBgorBgEE -->
<!-- AYI3TAgBBggrBgEFBQcDAzAdBgNVHQ4EFgQU8huhNbETDU+ZWllL4DNMPCijEU4w -->
<!-- RQYDVR0RBD4wPKQ6MDgxHjAcBgNVBAsTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEW -->
<!-- MBQGA1UEBRMNMjMwMDEyKzUwMjkyMzAfBgNVHSMEGDAWgBRIbmTlUAXTgqoXNzci -->
<!-- tW2oynUClTBUBgNVHR8ETTBLMEmgR6BFhkNodHRwOi8vd3d3Lm1pY3Jvc29mdC5j -->
<!-- b20vcGtpb3BzL2NybC9NaWNDb2RTaWdQQ0EyMDExXzIwMTEtMDctMDguY3JsMGEG -->
<!-- CCsGAQUFBwEBBFUwUzBRBggrBgEFBQcwAoZFaHR0cDovL3d3dy5taWNyb3NvZnQu -->
<!-- Y29tL3BraW9wcy9jZXJ0cy9NaWNDb2RTaWdQQ0EyMDExXzIwMTEtMDctMDguY3J0 -->
<!-- MAwGA1UdEwEB/wQCMAAwDQYJKoZIhvcNAQELBQADggIBAIjmD9IpQVvfB1QehvpC -->
<!-- Ge7QeTQkKQ7j3bmDMjwSqFL4ri6ae9IFTdpywn5smmtSIyKYDn3/nHtaEn0X1NBj -->
<!-- L5oP0BjAy1sqxD+uy35B+V8wv5GrxhMDJP8l2QjLtH/UglSTIhLqyt8bUAqVfyfp -->
<!-- h4COMRvwwjTvChtCnUXXACuCXYHWalOoc0OU2oGN+mPJIJJxaNQc1sjBsMbGIWv3 -->
<!-- cmgSHkCEmrMv7yaidpePt6V+yPMik+eXw3IfZ5eNOiNgL1rZzgSJfTnvUqiaEQ0X -->
<!-- dG1HbkDv9fv6CTq6m4Ty3IzLiwGSXYxRIXTxT4TYs5VxHy2uFjFXWVSL0J2ARTYL -->
<!-- E4Oyl1wXDF1PX4bxg1yDMfKPHcE1Ijic5lx1KdK1SkaEJdto4hd++05J9Bf9TAmi -->
<!-- u6EK6C9Oe5vRadroJCK26uCUI4zIjL/qG7mswW+qT0CW0gnR9JHkXCWNbo8ccMk1 -->
<!-- sJatmRoSAifbgzaYbUz8+lv+IXy5GFuAmLnNbGjacB3IMGpa+lbFgih57/fIhamq -->
<!-- 5VhxgaEmn/UjWyr+cPiAFWuTVIpfsOjbEAww75wURNM1Imp9NJKye1O24EspEHmb -->
<!-- DmqCUcq7NqkOKIG4PVm3hDDED/WQpzJDkvu4FrIbvyTGVU01vKsg4UfcdiZ0fQ+/ -->
<!-- V0hf8yrtq9CkB8iIuk5bBxuPMIIHejCCBWKgAwIBAgIKYQ6Q0gAAAAAAAzANBgkq -->
<!-- hkiG9w0BAQsFADCBiDELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldhc2hpbmd0b24x -->
<!-- EDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlv -->
<!-- bjEyMDAGA1UEAxMpTWljcm9zb2Z0IFJvb3QgQ2VydGlmaWNhdGUgQXV0aG9yaXR5 -->
<!-- IDIwMTEwHhcNMTEwNzA4MjA1OTA5WhcNMjYwNzA4MjEwOTA5WjB+MQswCQYDVQQG -->
<!-- EwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwG -->
<!-- A1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSgwJgYDVQQDEx9NaWNyb3NvZnQg -->
<!-- Q29kZSBTaWduaW5nIFBDQSAyMDExMIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIIC -->
<!-- CgKCAgEAq/D6chAcLq3YbqqCEE00uvK2WCGfQhsqa+laUKq4BjgaBEm6f8MMHt03 -->
<!-- a8YS2AvwOMKZBrDIOdUBFDFC04kNeWSHfpRgJGyvnkmc6Whe0t+bU7IKLMOv2akr -->
<!-- rnoJr9eWWcpgGgXpZnboMlImEi/nqwhQz7NEt13YxC4Ddato88tt8zpcoRb0Rrrg -->
<!-- OGSsbmQ1eKagYw8t00CT+OPeBw3VXHmlSSnnDb6gE3e+lD3v++MrWhAfTVYoonpy -->
<!-- 4BI6t0le2O3tQ5GD2Xuye4Yb2T6xjF3oiU+EGvKhL1nkkDstrjNYxbc+/jLTswM9 -->
<!-- sbKvkjh+0p2ALPVOVpEhNSXDOW5kf1O6nA+tGSOEy/S6A4aN91/w0FK/jJSHvMAh -->
<!-- dCVfGCi2zCcoOCWYOUo2z3yxkq4cI6epZuxhH2rhKEmdX4jiJV3TIUs+UsS1Vz8k -->
<!-- A/DRelsv1SPjcF0PUUZ3s/gA4bysAoJf28AVs70b1FVL5zmhD+kjSbwYuER8ReTB -->
<!-- w3J64HLnJN+/RpnF78IcV9uDjexNSTCnq47f7Fufr/zdsGbiwZeBe+3W7UvnSSmn -->
<!-- Eyimp31ngOaKYnhfsi+E11ecXL93KCjx7W3DKI8sj0A3T8HhhUSJxAlMxdSlQy90 -->
<!-- lfdu+HggWCwTXWCVmj5PM4TasIgX3p5O9JawvEagbJjS4NaIjAsCAwEAAaOCAe0w -->
<!-- ggHpMBAGCSsGAQQBgjcVAQQDAgEAMB0GA1UdDgQWBBRIbmTlUAXTgqoXNzcitW2o -->
<!-- ynUClTAZBgkrBgEEAYI3FAIEDB4KAFMAdQBiAEMAQTALBgNVHQ8EBAMCAYYwDwYD -->
<!-- VR0TAQH/BAUwAwEB/zAfBgNVHSMEGDAWgBRyLToCMZBDuRQFTuHqp8cx0SOJNDBa -->
<!-- BgNVHR8EUzBRME+gTaBLhklodHRwOi8vY3JsLm1pY3Jvc29mdC5jb20vcGtpL2Ny -->
<!-- bC9wcm9kdWN0cy9NaWNSb29DZXJBdXQyMDExXzIwMTFfMDNfMjIuY3JsMF4GCCsG -->
<!-- AQUFBwEBBFIwUDBOBggrBgEFBQcwAoZCaHR0cDovL3d3dy5taWNyb3NvZnQuY29t -->
<!-- L3BraS9jZXJ0cy9NaWNSb29DZXJBdXQyMDExXzIwMTFfMDNfMjIuY3J0MIGfBgNV -->
<!-- HSAEgZcwgZQwgZEGCSsGAQQBgjcuAzCBgzA/BggrBgEFBQcCARYzaHR0cDovL3d3 -->
<!-- dy5taWNyb3NvZnQuY29tL3BraW9wcy9kb2NzL3ByaW1hcnljcHMuaHRtMEAGCCsG -->
<!-- AQUFBwICMDQeMiAdAEwAZQBnAGEAbABfAHAAbwBsAGkAYwB5AF8AcwB0AGEAdABl -->
<!-- AG0AZQBuAHQALiAdMA0GCSqGSIb3DQEBCwUAA4ICAQBn8oalmOBUeRou09h0ZyKb -->
<!-- C5YR4WOSmUKWfdJ5DJDBZV8uLD74w3LRbYP+vj/oCso7v0epo/Np22O/IjWll11l -->
<!-- hJB9i0ZQVdgMknzSGksc8zxCi1LQsP1r4z4HLimb5j0bpdS1HXeUOeLpZMlEPXh6 -->
<!-- I/MTfaaQdION9MsmAkYqwooQu6SpBQyb7Wj6aC6VoCo/KmtYSWMfCWluWpiW5IP0 -->
<!-- wI/zRive/DvQvTXvbiWu5a8n7dDd8w6vmSiXmE0OPQvyCInWH8MyGOLwxS3OW560 -->
<!-- STkKxgrCxq2u5bLZ2xWIUUVYODJxJxp/sfQn+N4sOiBpmLJZiWhub6e3dMNABQam -->
<!-- ASooPoI/E01mC8CzTfXhj38cbxV9Rad25UAqZaPDXVJihsMdYzaXht/a8/jyFqGa -->
<!-- J+HNpZfQ7l1jQeNbB5yHPgZ3BtEGsXUfFL5hYbXw3MYbBL7fQccOKO7eZS/sl/ah -->
<!-- XJbYANahRr1Z85elCUtIEJmAH9AAKcWxm6U/RXceNcbSoqKfenoi+kiVH6v7RyOA -->
<!-- 9Z74v2u3S5fi63V4GuzqN5l5GEv/1rMjaHXmr/r8i+sLgOppO6/8MO0ETI7f33Vt -->
<!-- Y5E90Z1WTk+/gFcioXgRMiF670EKsT/7qMykXcGhiJtXcVZOSEXAQsmbdlsKgEhr -->
<!-- /Xmfwb1tbWrJUnMTDXpQzTGCGggwghoEAgEBMIGVMH4xCzAJBgNVBAYTAlVTMRMw -->
<!-- EQYDVQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVN -->
<!-- aWNyb3NvZnQgQ29ycG9yYXRpb24xKDAmBgNVBAMTH01pY3Jvc29mdCBDb2RlIFNp -->
<!-- Z25pbmcgUENBIDIwMTECEzMAAAQEbHQG/1crJ3IAAAAABAQwDQYJYIZIAWUDBAIB -->
<!-- BQCgga4wGQYJKoZIhvcNAQkDMQwGCisGAQQBgjcCAQQwHAYKKwYBBAGCNwIBCzEO -->
<!-- MAwGCisGAQQBgjcCARUwLwYJKoZIhvcNAQkEMSIEII0kdYXac8juWmFwUd4Qxka6 -->
<!-- +Y62co6J3z2sZFZNhmJqMEIGCisGAQQBgjcCAQwxNDAyoBSAEgBNAGkAYwByAG8A -->
<!-- cwBvAGYAdKEagBhodHRwOi8vd3d3Lm1pY3Jvc29mdC5jb20wDQYJKoZIhvcNAQEB -->
<!-- BQAEggEAYT2H9GiNjgxV2xsAt0Tz+z8VO9tDt8c7zk2mqglz8dLX/QXWNhYu7VSv -->
<!-- Q0fUeP7uEWqgX5ts3R5tCGrXBc+W1slU9oM6izzzdxOpEaZbQFoHtMxtCV5PZUZJ -->
<!-- 9pfe/5+oEaDIdMzdwzFNSiGt1pCA1DqcdV2ykgD970uqcV406d4qW1gXtjUedVaN -->
<!-- 3c3mISfGXQ4Rk2meqPO3vRjSEkO5sjpOD40O0QdzYYcCu3bIH9nByRViQYTyWtUA -->
<!-- 5Z5HhCLEuaZF61AblgQjaWoiR40QeVjjCM+f+NOmzrI6kYYoCW90yldz5wRPkZRr -->
<!-- UfPDtPFRsUGxyO3ZEA3ru5PHkCaqrKGCF5IwgheOBgorBgEEAYI3AwMBMYIXfjCC -->
<!-- F3oGCSqGSIb3DQEHAqCCF2swghdnAgEDMQ8wDQYJYIZIAWUDBAIBBQAwggFQBgsq -->
<!-- hkiG9w0BCRABBKCCAT8EggE7MIIBNwIBAQYKKwYBBAGEWQoDATAxMA0GCWCGSAFl -->
<!-- AwQCAQUABCBh1n9+jVI2lU0U/dlhnV0O++wcueZZqzNw3+FIDoIPyAIGaF2sv6hB -->
<!-- GBEyMDI1MDcwOTExMDcyMi44WjAEgAIB9KCB0aSBzjCByzELMAkGA1UEBhMCVVMx -->
<!-- EzARBgNVBAgTCldhc2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoT -->
<!-- FU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjElMCMGA1UECxMcTWljcm9zb2Z0IEFtZXJp -->
<!-- Y2EgT3BlcmF0aW9uczEnMCUGA1UECxMeblNoaWVsZCBUU1MgRVNOOjhEMDAtMDVF -->
<!-- MC1EOTQ3MSUwIwYDVQQDExxNaWNyb3NvZnQgVGltZS1TdGFtcCBTZXJ2aWNloIIR -->
<!-- 6jCCByAwggUIoAMCAQICEzMAAAINDXe+ezaPf+MAAQAAAg0wDQYJKoZIhvcNAQEL -->
<!-- BQAwfDELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldhc2hpbmd0b24xEDAOBgNVBAcT -->
<!-- B1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEmMCQGA1UE -->
<!-- AxMdTWljcm9zb2Z0IFRpbWUtU3RhbXAgUENBIDIwMTAwHhcNMjUwMTMwMTk0MzAx -->
<!-- WhcNMjYwNDIyMTk0MzAxWjCByzELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldhc2hp -->
<!-- bmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBDb3Jw -->
<!-- b3JhdGlvbjElMCMGA1UECxMcTWljcm9zb2Z0IEFtZXJpY2EgT3BlcmF0aW9uczEn -->
<!-- MCUGA1UECxMeblNoaWVsZCBUU1MgRVNOOjhEMDAtMDVFMC1EOTQ3MSUwIwYDVQQD -->
<!-- ExxNaWNyb3NvZnQgVGltZS1TdGFtcCBTZXJ2aWNlMIICIjANBgkqhkiG9w0BAQEF -->
<!-- AAOCAg8AMIICCgKCAgEAsX+oB93x98IJhTeU1fM0RfzdEyYLllGAq0GT1JsAPkIU -->
<!-- skjae5IhphWXeG+c7AHzdFofj8yCb2LFTIFQmmXG1a/wyQX3scJ0H3Pn4CIzogM2 -->
<!-- Eo4OxLUjnVqC5PNW/5hohiAQvvny3EgM22TtyrVuD0SjkrHQ/+XkXbTXGvxHeBAH -->
<!-- FE99swBkvUqv/+JA0/dihM0TVyY/M9zQy+KBhswkDLwPN7UBuZq53qcRdDrDXyih -->
<!-- 2BzKo8jhD+aU4Cn0+VzH89LBBugEQI5Vye/Uhv46Hrh4fE6gvnCTK8vcAe2S63TA -->
<!-- BcvKxzd1tfpJKcOOyqPvYs3tm5Xvj+YzGqrnytN9wbvthV1UiqYYpdo9KgVFqZ+b -->
<!-- wbX9lvoLFasnQPnrHgR32MXA2ePuaEaMgwINLVvX+/bzgL/dT+G08xvgWTwWwzM0 -->
<!-- g8Jo/AEig5VcK7k3hdXpuzVonnVgAftvYBR/3PzTdbk0C78BgyDsNQ3C5wg9FVzS -->
<!-- +5jr4n2LzVroEwB3DBS1ZQNY3jh9+RPyFvanCiqq2oey8iWsf+jOVB7MELoBoC64 -->
<!-- ojDRL89yKlvgIiCdPe4mMzBs7A6my0lM7SIwxLfcXyBx1LP6d4/QZK9mzz9b+M/L -->
<!-- 6f+OQpN5nxW1thdBLYzgpxjzKyTY4xoRBvcwCtddOQYNpCe0Z/Fk1juSGw/6w7UC -->
<!-- AwEAAaOCAUkwggFFMB0GA1UdDgQWBBQVl46Q5g4Da07CEo9uko1Nf2DDgDAfBgNV -->
<!-- HSMEGDAWgBSfpxVdAF5iXYP05dJlpxtTNRnpcjBfBgNVHR8EWDBWMFSgUqBQhk5o -->
<!-- dHRwOi8vd3d3Lm1pY3Jvc29mdC5jb20vcGtpb3BzL2NybC9NaWNyb3NvZnQlMjBU -->
<!-- aW1lLVN0YW1wJTIwUENBJTIwMjAxMCgxKS5jcmwwbAYIKwYBBQUHAQEEYDBeMFwG -->
<!-- CCsGAQUFBzAChlBodHRwOi8vd3d3Lm1pY3Jvc29mdC5jb20vcGtpb3BzL2NlcnRz -->
<!-- L01pY3Jvc29mdCUyMFRpbWUtU3RhbXAlMjBQQ0ElMjAyMDEwKDEpLmNydDAMBgNV -->
<!-- HRMBAf8EAjAAMBYGA1UdJQEB/wQMMAoGCCsGAQUFBwMIMA4GA1UdDwEB/wQEAwIH -->
<!-- gDANBgkqhkiG9w0BAQsFAAOCAgEAHB/IE+NsFUPBkGfjByFzSfneHUJ9PrzxvwHR -->
<!-- b08ZP7bzjpANou4Z6BDhBXEm80uRHSFQjIuWORIlk/IRCZM7XXquYV7VFrD2PtpW -->
<!-- ElPR3YSg30Q348FWfZHwrYay0Qt48PRWoeTABiPTB0hE6Pa+/nLsfAFKkN4ZmjNo -->
<!-- 5kzVr6xTi2ztNQdXIjRm7xNsSvq28wQYf7lnd5N9KWrui6dGs4O6w2BjBr/I1qLS -->
<!-- C36SjAXgkKLnzLCP4M8aOPpPXamAZAMDJO4dYX9t8nG+Vp4EjEFf1MZd0sW4PuuC -->
<!-- iuhgrE0hus+8zU4Gy+vTzBn8FdnJ2sGPeSMtnICenGzDkUVHGM77LlHfKBEPO3Fu -->
<!-- F6qgS64XnsCY0Tk+mMlq0NqEPo8xitliaBjZ32p5jX6kJ1SqYt9tKJ2oPeSzBY4/ -->
<!-- AcK35WLDvZBDosuyOVJ4DwZhfkR0gr9oRUq/Jyu4LbosvUlDij+hW0Ds8jEbyd1M -->
<!-- bJqx9/OO+18xE2/7WSwI9dE3WBX5I5BXhY5W94jE0rP1ayUhdK9Lzjf6MWaigmPF -->
<!-- mXs/rkD9E68tEEPcEaeQ9hWCfBNbiF/y78LhZAXVn+1+6pW/qc3bz6UgHGgoASFk -->
<!-- 5TJ9m1oJFObj88UTx4f9LvhSO/3kQmRmyzNqp598l0Lzv4fcEJvKSAAUQL6HWold -->
<!-- G1Zh2xEwggdxMIIFWaADAgECAhMzAAAAFcXna54Cm0mZAAAAAAAVMA0GCSqGSIb3 -->
<!-- DQEBCwUAMIGIMQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4G -->
<!-- A1UEBxMHUmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMTIw -->
<!-- MAYDVQQDEylNaWNyb3NvZnQgUm9vdCBDZXJ0aWZpY2F0ZSBBdXRob3JpdHkgMjAx -->
<!-- MDAeFw0yMTA5MzAxODIyMjVaFw0zMDA5MzAxODMyMjVaMHwxCzAJBgNVBAYTAlVT -->
<!-- MRMwEQYDVQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQK -->
<!-- ExVNaWNyb3NvZnQgQ29ycG9yYXRpb24xJjAkBgNVBAMTHU1pY3Jvc29mdCBUaW1l -->
<!-- LVN0YW1wIFBDQSAyMDEwMIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEA -->
<!-- 5OGmTOe0ciELeaLL1yR5vQ7VgtP97pwHB9KpbE51yMo1V/YBf2xK4OK9uT4XYDP/ -->
<!-- XE/HZveVU3Fa4n5KWv64NmeFRiMMtY0Tz3cywBAY6GB9alKDRLemjkZrBxTzxXb1 -->
<!-- hlDcwUTIcVxRMTegCjhuje3XD9gmU3w5YQJ6xKr9cmmvHaus9ja+NSZk2pg7uhp7 -->
<!-- M62AW36MEBydUv626GIl3GoPz130/o5Tz9bshVZN7928jaTjkY+yOSxRnOlwaQ3K -->
<!-- Ni1wjjHINSi947SHJMPgyY9+tVSP3PoFVZhtaDuaRr3tpK56KTesy+uDRedGbsoy -->
<!-- 1cCGMFxPLOJiss254o2I5JasAUq7vnGpF1tnYN74kpEeHT39IM9zfUGaRnXNxF80 -->
<!-- 3RKJ1v2lIH1+/NmeRd+2ci/bfV+AutuqfjbsNkz2K26oElHovwUDo9Fzpk03dJQc -->
<!-- NIIP8BDyt0cY7afomXw/TNuvXsLz1dhzPUNOwTM5TI4CvEJoLhDqhFFG4tG9ahha -->
<!-- YQFzymeiXtcodgLiMxhy16cg8ML6EgrXY28MyTZki1ugpoMhXV8wdJGUlNi5UPkL -->
<!-- iWHzNgY1GIRH29wb0f2y1BzFa/ZcUlFdEtsluq9QBXpsxREdcu+N+VLEhReTwDwV -->
<!-- 2xo3xwgVGD94q0W29R6HXtqPnhZyacaue7e3PmriLq0CAwEAAaOCAd0wggHZMBIG -->
<!-- CSsGAQQBgjcVAQQFAgMBAAEwIwYJKwYBBAGCNxUCBBYEFCqnUv5kxJq+gpE8RjUp -->
<!-- zxD/LwTuMB0GA1UdDgQWBBSfpxVdAF5iXYP05dJlpxtTNRnpcjBcBgNVHSAEVTBT -->
<!-- MFEGDCsGAQQBgjdMg30BATBBMD8GCCsGAQUFBwIBFjNodHRwOi8vd3d3Lm1pY3Jv -->
<!-- c29mdC5jb20vcGtpb3BzL0RvY3MvUmVwb3NpdG9yeS5odG0wEwYDVR0lBAwwCgYI -->
<!-- KwYBBQUHAwgwGQYJKwYBBAGCNxQCBAweCgBTAHUAYgBDAEEwCwYDVR0PBAQDAgGG -->
<!-- MA8GA1UdEwEB/wQFMAMBAf8wHwYDVR0jBBgwFoAU1fZWy4/oolxiaNE9lJBb186a -->
<!-- GMQwVgYDVR0fBE8wTTBLoEmgR4ZFaHR0cDovL2NybC5taWNyb3NvZnQuY29tL3Br -->
<!-- aS9jcmwvcHJvZHVjdHMvTWljUm9vQ2VyQXV0XzIwMTAtMDYtMjMuY3JsMFoGCCsG -->
<!-- AQUFBwEBBE4wTDBKBggrBgEFBQcwAoY+aHR0cDovL3d3dy5taWNyb3NvZnQuY29t -->
<!-- L3BraS9jZXJ0cy9NaWNSb29DZXJBdXRfMjAxMC0wNi0yMy5jcnQwDQYJKoZIhvcN -->
<!-- AQELBQADggIBAJ1VffwqreEsH2cBMSRb4Z5yS/ypb+pcFLY+TkdkeLEGk5c9MTO1 -->
<!-- OdfCcTY/2mRsfNB1OW27DzHkwo/7bNGhlBgi7ulmZzpTTd2YurYeeNg2LpypglYA -->
<!-- A7AFvonoaeC6Ce5732pvvinLbtg/SHUB2RjebYIM9W0jVOR4U3UkV7ndn/OOPcbz -->
<!-- aN9l9qRWqveVtihVJ9AkvUCgvxm2EhIRXT0n4ECWOKz3+SmJw7wXsFSFQrP8DJ6L -->
<!-- GYnn8AtqgcKBGUIZUnWKNsIdw2FzLixre24/LAl4FOmRsqlb30mjdAy87JGA0j3m -->
<!-- Sj5mO0+7hvoyGtmW9I/2kQH2zsZ0/fZMcm8Qq3UwxTSwethQ/gpY3UA8x1RtnWN0 -->
<!-- SCyxTkctwRQEcb9k+SS+c23Kjgm9swFXSVRk2XPXfx5bRAGOWhmRaw2fpCjcZxko -->
<!-- JLo4S5pu+yFUa2pFEUep8beuyOiJXk+d0tBMdrVXVAmxaQFEfnyhYWxz/gq77EFm -->
<!-- PWn9y8FBSX5+k77L+DvktxW/tM4+pTFRhLy/AsGConsXHRWJjXD+57XQKBqJC482 -->
<!-- 2rpM+Zv/Cuk0+CQ1ZyvgDbjmjJnW4SLq8CdCPSWU5nR0W2rRnj7tfqAxM328y+l7 -->
<!-- vzhwRNGQ8cirOoo6CGJ/2XBjU02N7oJtpQUQwXEGahC0HVUzWLOhcGbyoYIDTTCC -->
<!-- AjUCAQEwgfmhgdGkgc4wgcsxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNoaW5n -->
<!-- dG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNyb3NvZnQgQ29ycG9y -->
<!-- YXRpb24xJTAjBgNVBAsTHE1pY3Jvc29mdCBBbWVyaWNhIE9wZXJhdGlvbnMxJzAl -->
<!-- BgNVBAsTHm5TaGllbGQgVFNTIEVTTjo4RDAwLTA1RTAtRDk0NzElMCMGA1UEAxMc -->
<!-- TWljcm9zb2Z0IFRpbWUtU3RhbXAgU2VydmljZaIjCgEBMAcGBSsOAwIaAxUAeyws -->
<!-- KI+ht50OzsYZf6X53uMgEz2ggYMwgYCkfjB8MQswCQYDVQQGEwJVUzETMBEGA1UE -->
<!-- CBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwGA1UEChMVTWljcm9z -->
<!-- b2Z0IENvcnBvcmF0aW9uMSYwJAYDVQQDEx1NaWNyb3NvZnQgVGltZS1TdGFtcCBQ -->
<!-- Q0EgMjAxMDANBgkqhkiG9w0BAQsFAAIFAOwYpP8wIhgPMjAyNTA3MDkwODIxMTla -->
<!-- GA8yMDI1MDcxMDA4MjExOVowdDA6BgorBgEEAYRZCgQBMSwwKjAKAgUA7Bik/wIB -->
<!-- ADAHAgEAAgIenzAHAgEAAgIUQzAKAgUA7Bn2fwIBADA2BgorBgEEAYRZCgQCMSgw -->
<!-- JjAMBgorBgEEAYRZCgMCoAowCAIBAAIDB6EgoQowCAIBAAIDAYagMA0GCSqGSIb3 -->
<!-- DQEBCwUAA4IBAQAIiJc92Xk1FG3dyHFrxDz0CaIzmf0Gyi3koxssYfIJlwBBV2iE -->
<!-- xKhm7TdqXGiaD97EfO91sv8fglTrujb4NEqkodvrjp/KPLlSP0BzcrJCOdmAmIpR -->
<!-- tNd2gNe+kz1KxP5LoVKyIoUcf1GdFLkmZvW+yEZqIo4yAH/FDbkotbY9dpC0A5Op -->
<!-- ZVSzsnkp+8bwywXPWydBn0Wc92M0fwGdvmGov3ZAwL/cH0BJw09YAzsXR/0FvWPx -->
<!-- +wD5oCn5kYiVdFYeRGw9rvfC9fH2RjrhqC+eKgrXmPRZh6/9jKUZIg/3Y5SpmjaK -->
<!-- FAN7z+wEds7GxUjDXuwO3ox/F4Wz1JyhtDEdMYIEDTCCBAkCAQEwgZMwfDELMAkG -->
<!-- A1UEBhMCVVMxEzARBgNVBAgTCldhc2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQx -->
<!-- HjAcBgNVBAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEmMCQGA1UEAxMdTWljcm9z -->
<!-- b2Z0IFRpbWUtU3RhbXAgUENBIDIwMTACEzMAAAINDXe+ezaPf+MAAQAAAg0wDQYJ -->
<!-- YIZIAWUDBAIBBQCgggFKMBoGCSqGSIb3DQEJAzENBgsqhkiG9w0BCRABBDAvBgkq -->
<!-- hkiG9w0BCQQxIgQgfjlX6T4vSXvwMu406ZIaDAF3gteLeIhY6V0nV0O78mIwgfoG -->
<!-- CyqGSIb3DQEJEAIvMYHqMIHnMIHkMIG9BCBj6geU7CRqiPDrpJwSrUr29BX6usOT -->
<!-- UrOb3k1cJGv4QDCBmDCBgKR+MHwxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNo -->
<!-- aW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNyb3NvZnQgQ29y -->
<!-- cG9yYXRpb24xJjAkBgNVBAMTHU1pY3Jvc29mdCBUaW1lLVN0YW1wIFBDQSAyMDEw -->
<!-- AhMzAAACDQ13vns2j3/jAAEAAAINMCIEIF8DZqZlB18a+e5tq+Dqyp9T+sln7IZA -->
<!-- twchNS8qF5WEMA0GCSqGSIb3DQEBCwUABIICAIa7Z4F18D8ghGfKRAG/nhXgYFaF -->
<!-- DaKPzXAqMRbF8XpycM6rXsWGe1g+sUdNdHFSXbR4MYH0UdWMxXvfW7sabAhsnsaz -->
<!-- oN5tUnCCp6m9vlYUltVxIhIEVELnRD/D2ZS/J1xOFlg4H2j14fPM+cW7ku/9rCm9 -->
<!-- Dg6ADZhn2bBO08ndYKUXlqIdiaIUbIMZnKyhhpJXYP4sq8swuIUXnO3MIqibmSbL -->
<!-- 9+RLDCjhwMabc0oeAzZVCAxCkJNe1+Bs0t89GNRaFvDHw7NYp94vV36/eOaYXmpn -->
<!-- f1xRIZMAjqIjAhmdOLQAppT7mSOlePhYbbIpsLNWLNvixDaum9ut6ZSY+NcfuMMK -->
<!-- h06EO+IxKj7dLNMV/yEtYc4vJVJjWRaXJr+KqHv//di6N9dHVNxnGVdxMTy4S2k8 -->
<!-- A0kjqUBDEjxUXXxUjnDMJ+SJk7zeU8UDFp8ZTDKzKTHjkH29U1DvUdlNsh1LVkS/ -->
<!-- 38t+fUQTBnzPI0hVDXldpzsCrYpDxHzM4q7ZdAVq/0g5jM1S+yHySCYu5aL82szT -->
<!-- Jpj8wksM7KIocchR+NMJJ7ydNYGN0AKGIeEZEoRUgTCpFzaz3iaAXy/Urwu0dM4D -->
<!-- ukdYd6eCUd4Dd3TL4qDV+Zwpe8IsznCDwEeZXrPFyyZ+2tOi55/7t3ZI4qeGn9Yz -->
<!-- 1si/X83xcOwd1XFd -->
<!-- SIG # End signature block -->
