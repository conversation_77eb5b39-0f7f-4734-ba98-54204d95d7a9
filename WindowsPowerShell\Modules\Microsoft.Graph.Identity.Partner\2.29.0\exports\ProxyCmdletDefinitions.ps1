
# ----------------------------------------------------------------------------------
# Code generated by Microsoft (R) AutoRest Code Generator (autorest: 3.10.4, generator: @autorest/powershell@)
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# ----------------------------------------------------------------------------------

<#
.Synopsis
Get the number of the resource
.Description
Get the number of the resource
.Example
{{ Add code here }}
.Example
{{ Add code here }}

.Inputs
System.Collections.IDictionary
.Outputs
System.Int32
.Link
https://learn.microsoft.com/powershell/module/microsoft.graph.identity.partner/get-mgtenantrelationshipdelegatedadmincustomercount
#>
function Get-MgTenantRelationshipDelegatedAdminCustomerCount {
[OutputType([System.Int32])]
[CmdletBinding(DefaultParameterSetName='Get', PositionalBinding=$false)]
param(
    [Parameter()]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.String]
    # Filter items by property values
    ${Filter},

    [Parameter()]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.String]
    # Search items by search phrases
    ${Search},

    [Parameter()]
    [Alias('RHV')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # Optional Response Headers Variable.
    ${ResponseHeadersVariable},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Wait for .NET debugger to attach
    ${Break},

    [Parameter(ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Collections.IDictionary]
    # Optional headers that will be added to the request.
    ${Headers},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be appended to the front of the pipeline
    ${HttpPipelineAppend},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be prepended to the front of the pipeline
    ${HttpPipelinePrepend},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Uri]
    # The URI for the proxy server to use
    ${Proxy},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.PSCredential]
    # Credentials for a proxy server to use for the remote call
    ${ProxyCredential},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Use the default credentials for the proxy
    ${ProxyUseDefaultCredentials}
)

begin {
    try {
        $outBuffer = $null
        if ($PSBoundParameters.TryGetValue('OutBuffer', [ref]$outBuffer)) {
            $PSBoundParameters['OutBuffer'] = 1
        }
        $parameterSet = $PSCmdlet.ParameterSetName

        $mapping = @{
            Get = 'Microsoft.Graph.Identity.Partner.private\Get-MgTenantRelationshipDelegatedAdminCustomerCount_Get';
        }
        $cmdInfo = Get-Command -Name $mapping[$parameterSet]
        [Microsoft.Graph.PowerShell.Runtime.MessageAttributeHelper]::ProcessCustomAttributesAtRuntime($cmdInfo, $MyInvocation, $parameterSet, $PSCmdlet)
        $wrappedCmd = $ExecutionContext.InvokeCommand.GetCommand(($mapping[$parameterSet]), [System.Management.Automation.CommandTypes]::Cmdlet)
        $scriptCmd = {& $wrappedCmd @PSBoundParameters}
        $steppablePipeline = $scriptCmd.GetSteppablePipeline($MyInvocation.CommandOrigin)
        $steppablePipeline.Begin($PSCmdlet)
    } catch {

        throw
    }
}

process {
    try {
        $steppablePipeline.Process($_)
    } catch {

        throw
    }

}
end {
    try {
        $steppablePipeline.End()

    } catch {

        throw
    }
} 
}

<#
.Synopsis
Get the number of the resource
.Description
Get the number of the resource
.Example
{{ Add code here }}
.Example
{{ Add code here }}

.Inputs
Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity
.Inputs
System.Collections.IDictionary
.Outputs
System.Int32
.Notes
COMPLEX PARAMETER PROPERTIES

To create the parameters described below, construct a hash table containing the appropriate properties. For information on hash tables, run Get-Help about_Hash_Tables.

INPUTOBJECT <IIdentityPartnerIdentity>: Identity Parameter
  [DelegatedAdminAccessAssignmentId <String>]: The unique identifier of delegatedAdminAccessAssignment
  [DelegatedAdminCustomerId <String>]: The unique identifier of delegatedAdminCustomer
  [DelegatedAdminRelationshipId <String>]: The unique identifier of delegatedAdminRelationship
  [DelegatedAdminRelationshipOperationId <String>]: The unique identifier of delegatedAdminRelationshipOperation
  [DelegatedAdminRelationshipRequestId <String>]: The unique identifier of delegatedAdminRelationshipRequest
  [DelegatedAdminServiceManagementDetailId <String>]: The unique identifier of delegatedAdminServiceManagementDetail
.Link
https://learn.microsoft.com/powershell/module/microsoft.graph.identity.partner/get-mgtenantrelationshipdelegatedadmincustomerservicemanagementdetailcount
#>
function Get-MgTenantRelationshipDelegatedAdminCustomerServiceManagementDetailCount {
[OutputType([System.Int32])]
[CmdletBinding(DefaultParameterSetName='Get', PositionalBinding=$false)]
param(
    [Parameter(ParameterSetName='Get', Mandatory)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [System.String]
    # The unique identifier of delegatedAdminCustomer
    ${DelegatedAdminCustomerId},

    [Parameter(ParameterSetName='GetViaIdentity', Mandatory, ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity]
    # Identity Parameter
    # To construct, see NOTES section for INPUTOBJECT properties and create a hash table.
    ${InputObject},

    [Parameter()]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.String]
    # Filter items by property values
    ${Filter},

    [Parameter()]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.String]
    # Search items by search phrases
    ${Search},

    [Parameter()]
    [Alias('RHV')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # Optional Response Headers Variable.
    ${ResponseHeadersVariable},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Wait for .NET debugger to attach
    ${Break},

    [Parameter(ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Collections.IDictionary]
    # Optional headers that will be added to the request.
    ${Headers},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be appended to the front of the pipeline
    ${HttpPipelineAppend},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be prepended to the front of the pipeline
    ${HttpPipelinePrepend},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Uri]
    # The URI for the proxy server to use
    ${Proxy},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.PSCredential]
    # Credentials for a proxy server to use for the remote call
    ${ProxyCredential},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Use the default credentials for the proxy
    ${ProxyUseDefaultCredentials}
)

begin {
    try {
        $outBuffer = $null
        if ($PSBoundParameters.TryGetValue('OutBuffer', [ref]$outBuffer)) {
            $PSBoundParameters['OutBuffer'] = 1
        }
        $parameterSet = $PSCmdlet.ParameterSetName

        $mapping = @{
            Get = 'Microsoft.Graph.Identity.Partner.private\Get-MgTenantRelationshipDelegatedAdminCustomerServiceManagementDetailCount_Get';
            GetViaIdentity = 'Microsoft.Graph.Identity.Partner.private\Get-MgTenantRelationshipDelegatedAdminCustomerServiceManagementDetailCount_GetViaIdentity';
        }
        $cmdInfo = Get-Command -Name $mapping[$parameterSet]
        [Microsoft.Graph.PowerShell.Runtime.MessageAttributeHelper]::ProcessCustomAttributesAtRuntime($cmdInfo, $MyInvocation, $parameterSet, $PSCmdlet)
        $wrappedCmd = $ExecutionContext.InvokeCommand.GetCommand(($mapping[$parameterSet]), [System.Management.Automation.CommandTypes]::Cmdlet)
        $scriptCmd = {& $wrappedCmd @PSBoundParameters}
        $steppablePipeline = $scriptCmd.GetSteppablePipeline($MyInvocation.CommandOrigin)
        $steppablePipeline.Begin($PSCmdlet)
    } catch {

        throw
    }
}

process {
    try {
        $steppablePipeline.Process($_)
    } catch {

        throw
    }

}
end {
    try {
        $steppablePipeline.End()

    } catch {

        throw
    }
} 
}

<#
.Synopsis
Contains the management details of a service in the customer tenant that's managed by delegated administration.
.Description
Contains the management details of a service in the customer tenant that's managed by delegated administration.
.Example

Import-Module Microsoft.Graph.Identity.Partner

Get-MgTenantRelationshipDelegatedAdminCustomerServiceManagementDetail -DelegatedAdminCustomerId $delegatedAdminCustomerId


.Inputs
Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity
.Inputs
System.Collections.IDictionary
.Outputs
Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminServiceManagementDetail
.Notes
COMPLEX PARAMETER PROPERTIES

To create the parameters described below, construct a hash table containing the appropriate properties. For information on hash tables, run Get-Help about_Hash_Tables.

INPUTOBJECT <IIdentityPartnerIdentity>: Identity Parameter
  [DelegatedAdminAccessAssignmentId <String>]: The unique identifier of delegatedAdminAccessAssignment
  [DelegatedAdminCustomerId <String>]: The unique identifier of delegatedAdminCustomer
  [DelegatedAdminRelationshipId <String>]: The unique identifier of delegatedAdminRelationship
  [DelegatedAdminRelationshipOperationId <String>]: The unique identifier of delegatedAdminRelationshipOperation
  [DelegatedAdminRelationshipRequestId <String>]: The unique identifier of delegatedAdminRelationshipRequest
  [DelegatedAdminServiceManagementDetailId <String>]: The unique identifier of delegatedAdminServiceManagementDetail
.Link
https://learn.microsoft.com/powershell/module/microsoft.graph.identity.partner/get-mgtenantrelationshipdelegatedadmincustomerservicemanagementdetail
.Link
https://learn.microsoft.com/graph/api/delegatedadmincustomer-list-servicemanagementdetails?view=graph-rest-1.0
#>
function Get-MgTenantRelationshipDelegatedAdminCustomerServiceManagementDetail {
[OutputType([Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminServiceManagementDetail])]
[CmdletBinding(DefaultParameterSetName='List', PositionalBinding=$false)]
param(
    [Parameter(ParameterSetName='Get', Mandatory)]
    [Parameter(ParameterSetName='List', Mandatory)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [System.String]
    # The unique identifier of delegatedAdminCustomer
    ${DelegatedAdminCustomerId},

    [Parameter(ParameterSetName='Get', Mandatory)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [System.String]
    # The unique identifier of delegatedAdminServiceManagementDetail
    ${DelegatedAdminServiceManagementDetailId},

    [Parameter(ParameterSetName='GetViaIdentity', Mandatory, ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity]
    # Identity Parameter
    # To construct, see NOTES section for INPUTOBJECT properties and create a hash table.
    ${InputObject},

    [Parameter()]
    [Alias('Expand')]
    [AllowEmptyCollection()]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.String[]]
    # Expand related entities
    ${ExpandProperty},

    [Parameter()]
    [Alias('Select')]
    [AllowEmptyCollection()]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.String[]]
    # Select properties to be returned
    ${Property},

    [Parameter(ParameterSetName='List')]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.String]
    # Filter items by property values
    ${Filter},

    [Parameter(ParameterSetName='List')]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.String]
    # Search items by search phrases
    ${Search},

    [Parameter(ParameterSetName='List')]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.Int32]
    # Skip the first n items
    ${Skip},

    [Parameter(ParameterSetName='List')]
    [Alias('OrderBy')]
    [AllowEmptyCollection()]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.String[]]
    # Order items by property values
    ${Sort},

    [Parameter(ParameterSetName='List')]
    [Alias('Limit')]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.Int32]
    # Show only the first n items
    ${Top},

    [Parameter()]
    [Alias('RHV')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # Optional Response Headers Variable.
    ${ResponseHeadersVariable},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Wait for .NET debugger to attach
    ${Break},

    [Parameter(ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Collections.IDictionary]
    # Optional headers that will be added to the request.
    ${Headers},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be appended to the front of the pipeline
    ${HttpPipelineAppend},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be prepended to the front of the pipeline
    ${HttpPipelinePrepend},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Uri]
    # The URI for the proxy server to use
    ${Proxy},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.PSCredential]
    # Credentials for a proxy server to use for the remote call
    ${ProxyCredential},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Use the default credentials for the proxy
    ${ProxyUseDefaultCredentials},

    [Parameter(ParameterSetName='List')]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Int32]
    # Sets the page size of results.
    ${PageSize},

    [Parameter(ParameterSetName='List')]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # List all pages.
    ${All},

    [Parameter(ParameterSetName='List')]
    [Alias('CV')]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.String]
    # Specifies a count of the total number of items in a collection.
    # By default, this variable will be set in the global scope.
    ${CountVariable}
)

begin {
    try {
        $outBuffer = $null
        if ($PSBoundParameters.TryGetValue('OutBuffer', [ref]$outBuffer)) {
            $PSBoundParameters['OutBuffer'] = 1
        }
        $parameterSet = $PSCmdlet.ParameterSetName

        $mapping = @{
            Get = 'Microsoft.Graph.Identity.Partner.private\Get-MgTenantRelationshipDelegatedAdminCustomerServiceManagementDetail_Get';
            GetViaIdentity = 'Microsoft.Graph.Identity.Partner.private\Get-MgTenantRelationshipDelegatedAdminCustomerServiceManagementDetail_GetViaIdentity';
            List = 'Microsoft.Graph.Identity.Partner.private\Get-MgTenantRelationshipDelegatedAdminCustomerServiceManagementDetail_List';
        }
        $cmdInfo = Get-Command -Name $mapping[$parameterSet]
        [Microsoft.Graph.PowerShell.Runtime.MessageAttributeHelper]::ProcessCustomAttributesAtRuntime($cmdInfo, $MyInvocation, $parameterSet, $PSCmdlet)
        $wrappedCmd = $ExecutionContext.InvokeCommand.GetCommand(($mapping[$parameterSet]), [System.Management.Automation.CommandTypes]::Cmdlet)
        $scriptCmd = {& $wrappedCmd @PSBoundParameters}
        $steppablePipeline = $scriptCmd.GetSteppablePipeline($MyInvocation.CommandOrigin)
        $steppablePipeline.Begin($PSCmdlet)
    } catch {

        throw
    }
}

process {
    try {
        $steppablePipeline.Process($_)
    } catch {

        throw
    }

}
end {
    try {
        $steppablePipeline.End()

    } catch {

        throw
    }
} 
}

<#
.Synopsis
Read the properties of a delegatedAdminCustomer object.
.Description
Read the properties of a delegatedAdminCustomer object.
.Example

Import-Module Microsoft.Graph.Identity.Partner

Get-MgTenantRelationshipDelegatedAdminCustomer


.Inputs
Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity
.Inputs
System.Collections.IDictionary
.Outputs
Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminCustomer
.Notes
COMPLEX PARAMETER PROPERTIES

To create the parameters described below, construct a hash table containing the appropriate properties. For information on hash tables, run Get-Help about_Hash_Tables.

INPUTOBJECT <IIdentityPartnerIdentity>: Identity Parameter
  [DelegatedAdminAccessAssignmentId <String>]: The unique identifier of delegatedAdminAccessAssignment
  [DelegatedAdminCustomerId <String>]: The unique identifier of delegatedAdminCustomer
  [DelegatedAdminRelationshipId <String>]: The unique identifier of delegatedAdminRelationship
  [DelegatedAdminRelationshipOperationId <String>]: The unique identifier of delegatedAdminRelationshipOperation
  [DelegatedAdminRelationshipRequestId <String>]: The unique identifier of delegatedAdminRelationshipRequest
  [DelegatedAdminServiceManagementDetailId <String>]: The unique identifier of delegatedAdminServiceManagementDetail
.Link
https://learn.microsoft.com/powershell/module/microsoft.graph.identity.partner/get-mgtenantrelationshipdelegatedadmincustomer
.Link
https://learn.microsoft.com/graph/api/delegatedadmincustomer-get?view=graph-rest-1.0
.Link
https://learn.microsoft.com/graph/api/tenantrelationship-list-delegatedadmincustomers?view=graph-rest-1.0
#>
function Get-MgTenantRelationshipDelegatedAdminCustomer {
[OutputType([Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminCustomer])]
[CmdletBinding(DefaultParameterSetName='List', PositionalBinding=$false)]
param(
    [Parameter(ParameterSetName='Get', Mandatory)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [System.String]
    # The unique identifier of delegatedAdminCustomer
    ${DelegatedAdminCustomerId},

    [Parameter(ParameterSetName='GetViaIdentity', Mandatory, ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity]
    # Identity Parameter
    # To construct, see NOTES section for INPUTOBJECT properties and create a hash table.
    ${InputObject},

    [Parameter()]
    [Alias('Expand')]
    [AllowEmptyCollection()]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.String[]]
    # Expand related entities
    ${ExpandProperty},

    [Parameter()]
    [Alias('Select')]
    [AllowEmptyCollection()]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.String[]]
    # Select properties to be returned
    ${Property},

    [Parameter(ParameterSetName='List')]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.String]
    # Filter items by property values
    ${Filter},

    [Parameter(ParameterSetName='List')]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.String]
    # Search items by search phrases
    ${Search},

    [Parameter(ParameterSetName='List')]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.Int32]
    # Skip the first n items
    ${Skip},

    [Parameter(ParameterSetName='List')]
    [Alias('OrderBy')]
    [AllowEmptyCollection()]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.String[]]
    # Order items by property values
    ${Sort},

    [Parameter(ParameterSetName='List')]
    [Alias('Limit')]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.Int32]
    # Show only the first n items
    ${Top},

    [Parameter()]
    [Alias('RHV')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # Optional Response Headers Variable.
    ${ResponseHeadersVariable},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Wait for .NET debugger to attach
    ${Break},

    [Parameter(ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Collections.IDictionary]
    # Optional headers that will be added to the request.
    ${Headers},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be appended to the front of the pipeline
    ${HttpPipelineAppend},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be prepended to the front of the pipeline
    ${HttpPipelinePrepend},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Uri]
    # The URI for the proxy server to use
    ${Proxy},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.PSCredential]
    # Credentials for a proxy server to use for the remote call
    ${ProxyCredential},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Use the default credentials for the proxy
    ${ProxyUseDefaultCredentials},

    [Parameter(ParameterSetName='List')]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Int32]
    # Sets the page size of results.
    ${PageSize},

    [Parameter(ParameterSetName='List')]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # List all pages.
    ${All},

    [Parameter(ParameterSetName='List')]
    [Alias('CV')]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.String]
    # Specifies a count of the total number of items in a collection.
    # By default, this variable will be set in the global scope.
    ${CountVariable}
)

begin {
    try {
        $outBuffer = $null
        if ($PSBoundParameters.TryGetValue('OutBuffer', [ref]$outBuffer)) {
            $PSBoundParameters['OutBuffer'] = 1
        }
        $parameterSet = $PSCmdlet.ParameterSetName

        $mapping = @{
            Get = 'Microsoft.Graph.Identity.Partner.private\Get-MgTenantRelationshipDelegatedAdminCustomer_Get';
            GetViaIdentity = 'Microsoft.Graph.Identity.Partner.private\Get-MgTenantRelationshipDelegatedAdminCustomer_GetViaIdentity';
            List = 'Microsoft.Graph.Identity.Partner.private\Get-MgTenantRelationshipDelegatedAdminCustomer_List';
        }
        $cmdInfo = Get-Command -Name $mapping[$parameterSet]
        [Microsoft.Graph.PowerShell.Runtime.MessageAttributeHelper]::ProcessCustomAttributesAtRuntime($cmdInfo, $MyInvocation, $parameterSet, $PSCmdlet)
        $wrappedCmd = $ExecutionContext.InvokeCommand.GetCommand(($mapping[$parameterSet]), [System.Management.Automation.CommandTypes]::Cmdlet)
        $scriptCmd = {& $wrappedCmd @PSBoundParameters}
        $steppablePipeline = $scriptCmd.GetSteppablePipeline($MyInvocation.CommandOrigin)
        $steppablePipeline.Begin($PSCmdlet)
    } catch {

        throw
    }
}

process {
    try {
        $steppablePipeline.Process($_)
    } catch {

        throw
    }

}
end {
    try {
        $steppablePipeline.End()

    } catch {

        throw
    }
} 
}

<#
.Synopsis
Get the number of the resource
.Description
Get the number of the resource
.Example
{{ Add code here }}
.Example
{{ Add code here }}

.Inputs
Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity
.Inputs
System.Collections.IDictionary
.Outputs
System.Int32
.Notes
COMPLEX PARAMETER PROPERTIES

To create the parameters described below, construct a hash table containing the appropriate properties. For information on hash tables, run Get-Help about_Hash_Tables.

INPUTOBJECT <IIdentityPartnerIdentity>: Identity Parameter
  [DelegatedAdminAccessAssignmentId <String>]: The unique identifier of delegatedAdminAccessAssignment
  [DelegatedAdminCustomerId <String>]: The unique identifier of delegatedAdminCustomer
  [DelegatedAdminRelationshipId <String>]: The unique identifier of delegatedAdminRelationship
  [DelegatedAdminRelationshipOperationId <String>]: The unique identifier of delegatedAdminRelationshipOperation
  [DelegatedAdminRelationshipRequestId <String>]: The unique identifier of delegatedAdminRelationshipRequest
  [DelegatedAdminServiceManagementDetailId <String>]: The unique identifier of delegatedAdminServiceManagementDetail
.Link
https://learn.microsoft.com/powershell/module/microsoft.graph.identity.partner/get-mgtenantrelationshipdelegatedadminrelationshipaccessassignmentcount
#>
function Get-MgTenantRelationshipDelegatedAdminRelationshipAccessAssignmentCount {
[OutputType([System.Int32])]
[CmdletBinding(DefaultParameterSetName='Get', PositionalBinding=$false)]
param(
    [Parameter(ParameterSetName='Get', Mandatory)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [System.String]
    # The unique identifier of delegatedAdminRelationship
    ${DelegatedAdminRelationshipId},

    [Parameter(ParameterSetName='GetViaIdentity', Mandatory, ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity]
    # Identity Parameter
    # To construct, see NOTES section for INPUTOBJECT properties and create a hash table.
    ${InputObject},

    [Parameter()]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.String]
    # Filter items by property values
    ${Filter},

    [Parameter()]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.String]
    # Search items by search phrases
    ${Search},

    [Parameter()]
    [Alias('RHV')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # Optional Response Headers Variable.
    ${ResponseHeadersVariable},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Wait for .NET debugger to attach
    ${Break},

    [Parameter(ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Collections.IDictionary]
    # Optional headers that will be added to the request.
    ${Headers},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be appended to the front of the pipeline
    ${HttpPipelineAppend},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be prepended to the front of the pipeline
    ${HttpPipelinePrepend},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Uri]
    # The URI for the proxy server to use
    ${Proxy},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.PSCredential]
    # Credentials for a proxy server to use for the remote call
    ${ProxyCredential},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Use the default credentials for the proxy
    ${ProxyUseDefaultCredentials}
)

begin {
    try {
        $outBuffer = $null
        if ($PSBoundParameters.TryGetValue('OutBuffer', [ref]$outBuffer)) {
            $PSBoundParameters['OutBuffer'] = 1
        }
        $parameterSet = $PSCmdlet.ParameterSetName

        $mapping = @{
            Get = 'Microsoft.Graph.Identity.Partner.private\Get-MgTenantRelationshipDelegatedAdminRelationshipAccessAssignmentCount_Get';
            GetViaIdentity = 'Microsoft.Graph.Identity.Partner.private\Get-MgTenantRelationshipDelegatedAdminRelationshipAccessAssignmentCount_GetViaIdentity';
        }
        $cmdInfo = Get-Command -Name $mapping[$parameterSet]
        [Microsoft.Graph.PowerShell.Runtime.MessageAttributeHelper]::ProcessCustomAttributesAtRuntime($cmdInfo, $MyInvocation, $parameterSet, $PSCmdlet)
        $wrappedCmd = $ExecutionContext.InvokeCommand.GetCommand(($mapping[$parameterSet]), [System.Management.Automation.CommandTypes]::Cmdlet)
        $scriptCmd = {& $wrappedCmd @PSBoundParameters}
        $steppablePipeline = $scriptCmd.GetSteppablePipeline($MyInvocation.CommandOrigin)
        $steppablePipeline.Begin($PSCmdlet)
    } catch {

        throw
    }
}

process {
    try {
        $steppablePipeline.Process($_)
    } catch {

        throw
    }

}
end {
    try {
        $steppablePipeline.End()

    } catch {

        throw
    }
} 
}

<#
.Synopsis
Read the properties of a delegatedAdminAccessAssignment object.
.Description
Read the properties of a delegatedAdminAccessAssignment object.
.Example

Import-Module Microsoft.Graph.Identity.Partner

Get-MgTenantRelationshipDelegatedAdminRelationshipAccessAssignment -DelegatedAdminRelationshipId $delegatedAdminRelationshipId


.Inputs
Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity
.Inputs
System.Collections.IDictionary
.Outputs
Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminAccessAssignment
.Notes
COMPLEX PARAMETER PROPERTIES

To create the parameters described below, construct a hash table containing the appropriate properties. For information on hash tables, run Get-Help about_Hash_Tables.

INPUTOBJECT <IIdentityPartnerIdentity>: Identity Parameter
  [DelegatedAdminAccessAssignmentId <String>]: The unique identifier of delegatedAdminAccessAssignment
  [DelegatedAdminCustomerId <String>]: The unique identifier of delegatedAdminCustomer
  [DelegatedAdminRelationshipId <String>]: The unique identifier of delegatedAdminRelationship
  [DelegatedAdminRelationshipOperationId <String>]: The unique identifier of delegatedAdminRelationshipOperation
  [DelegatedAdminRelationshipRequestId <String>]: The unique identifier of delegatedAdminRelationshipRequest
  [DelegatedAdminServiceManagementDetailId <String>]: The unique identifier of delegatedAdminServiceManagementDetail
.Link
https://learn.microsoft.com/powershell/module/microsoft.graph.identity.partner/get-mgtenantrelationshipdelegatedadminrelationshipaccessassignment
.Link
https://learn.microsoft.com/graph/api/delegatedadminaccessassignment-get?view=graph-rest-1.0
.Link
https://learn.microsoft.com/graph/api/delegatedadminrelationship-list-accessassignments?view=graph-rest-1.0
#>
function Get-MgTenantRelationshipDelegatedAdminRelationshipAccessAssignment {
[OutputType([Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminAccessAssignment])]
[CmdletBinding(DefaultParameterSetName='List', PositionalBinding=$false)]
param(
    [Parameter(ParameterSetName='Get', Mandatory)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [System.String]
    # The unique identifier of delegatedAdminAccessAssignment
    ${DelegatedAdminAccessAssignmentId},

    [Parameter(ParameterSetName='Get', Mandatory)]
    [Parameter(ParameterSetName='List', Mandatory)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [System.String]
    # The unique identifier of delegatedAdminRelationship
    ${DelegatedAdminRelationshipId},

    [Parameter(ParameterSetName='GetViaIdentity', Mandatory, ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity]
    # Identity Parameter
    # To construct, see NOTES section for INPUTOBJECT properties and create a hash table.
    ${InputObject},

    [Parameter()]
    [Alias('Expand')]
    [AllowEmptyCollection()]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.String[]]
    # Expand related entities
    ${ExpandProperty},

    [Parameter()]
    [Alias('Select')]
    [AllowEmptyCollection()]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.String[]]
    # Select properties to be returned
    ${Property},

    [Parameter(ParameterSetName='List')]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.String]
    # Filter items by property values
    ${Filter},

    [Parameter(ParameterSetName='List')]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.String]
    # Search items by search phrases
    ${Search},

    [Parameter(ParameterSetName='List')]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.Int32]
    # Skip the first n items
    ${Skip},

    [Parameter(ParameterSetName='List')]
    [Alias('OrderBy')]
    [AllowEmptyCollection()]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.String[]]
    # Order items by property values
    ${Sort},

    [Parameter(ParameterSetName='List')]
    [Alias('Limit')]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.Int32]
    # Show only the first n items
    ${Top},

    [Parameter()]
    [Alias('RHV')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # Optional Response Headers Variable.
    ${ResponseHeadersVariable},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Wait for .NET debugger to attach
    ${Break},

    [Parameter(ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Collections.IDictionary]
    # Optional headers that will be added to the request.
    ${Headers},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be appended to the front of the pipeline
    ${HttpPipelineAppend},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be prepended to the front of the pipeline
    ${HttpPipelinePrepend},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Uri]
    # The URI for the proxy server to use
    ${Proxy},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.PSCredential]
    # Credentials for a proxy server to use for the remote call
    ${ProxyCredential},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Use the default credentials for the proxy
    ${ProxyUseDefaultCredentials},

    [Parameter(ParameterSetName='List')]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Int32]
    # Sets the page size of results.
    ${PageSize},

    [Parameter(ParameterSetName='List')]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # List all pages.
    ${All},

    [Parameter(ParameterSetName='List')]
    [Alias('CV')]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.String]
    # Specifies a count of the total number of items in a collection.
    # By default, this variable will be set in the global scope.
    ${CountVariable}
)

begin {
    try {
        $outBuffer = $null
        if ($PSBoundParameters.TryGetValue('OutBuffer', [ref]$outBuffer)) {
            $PSBoundParameters['OutBuffer'] = 1
        }
        $parameterSet = $PSCmdlet.ParameterSetName

        $mapping = @{
            Get = 'Microsoft.Graph.Identity.Partner.private\Get-MgTenantRelationshipDelegatedAdminRelationshipAccessAssignment_Get';
            GetViaIdentity = 'Microsoft.Graph.Identity.Partner.private\Get-MgTenantRelationshipDelegatedAdminRelationshipAccessAssignment_GetViaIdentity';
            List = 'Microsoft.Graph.Identity.Partner.private\Get-MgTenantRelationshipDelegatedAdminRelationshipAccessAssignment_List';
        }
        $cmdInfo = Get-Command -Name $mapping[$parameterSet]
        [Microsoft.Graph.PowerShell.Runtime.MessageAttributeHelper]::ProcessCustomAttributesAtRuntime($cmdInfo, $MyInvocation, $parameterSet, $PSCmdlet)
        $wrappedCmd = $ExecutionContext.InvokeCommand.GetCommand(($mapping[$parameterSet]), [System.Management.Automation.CommandTypes]::Cmdlet)
        $scriptCmd = {& $wrappedCmd @PSBoundParameters}
        $steppablePipeline = $scriptCmd.GetSteppablePipeline($MyInvocation.CommandOrigin)
        $steppablePipeline.Begin($PSCmdlet)
    } catch {

        throw
    }
}

process {
    try {
        $steppablePipeline.Process($_)
    } catch {

        throw
    }

}
end {
    try {
        $steppablePipeline.End()

    } catch {

        throw
    }
} 
}

<#
.Synopsis
Get the number of the resource
.Description
Get the number of the resource
.Example
{{ Add code here }}
.Example
{{ Add code here }}

.Inputs
System.Collections.IDictionary
.Outputs
System.Int32
.Link
https://learn.microsoft.com/powershell/module/microsoft.graph.identity.partner/get-mgtenantrelationshipdelegatedadminrelationshipcount
#>
function Get-MgTenantRelationshipDelegatedAdminRelationshipCount {
[OutputType([System.Int32])]
[CmdletBinding(DefaultParameterSetName='Get', PositionalBinding=$false)]
param(
    [Parameter()]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.String]
    # Filter items by property values
    ${Filter},

    [Parameter()]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.String]
    # Search items by search phrases
    ${Search},

    [Parameter()]
    [Alias('RHV')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # Optional Response Headers Variable.
    ${ResponseHeadersVariable},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Wait for .NET debugger to attach
    ${Break},

    [Parameter(ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Collections.IDictionary]
    # Optional headers that will be added to the request.
    ${Headers},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be appended to the front of the pipeline
    ${HttpPipelineAppend},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be prepended to the front of the pipeline
    ${HttpPipelinePrepend},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Uri]
    # The URI for the proxy server to use
    ${Proxy},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.PSCredential]
    # Credentials for a proxy server to use for the remote call
    ${ProxyCredential},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Use the default credentials for the proxy
    ${ProxyUseDefaultCredentials}
)

begin {
    try {
        $outBuffer = $null
        if ($PSBoundParameters.TryGetValue('OutBuffer', [ref]$outBuffer)) {
            $PSBoundParameters['OutBuffer'] = 1
        }
        $parameterSet = $PSCmdlet.ParameterSetName

        $mapping = @{
            Get = 'Microsoft.Graph.Identity.Partner.private\Get-MgTenantRelationshipDelegatedAdminRelationshipCount_Get';
        }
        $cmdInfo = Get-Command -Name $mapping[$parameterSet]
        [Microsoft.Graph.PowerShell.Runtime.MessageAttributeHelper]::ProcessCustomAttributesAtRuntime($cmdInfo, $MyInvocation, $parameterSet, $PSCmdlet)
        $wrappedCmd = $ExecutionContext.InvokeCommand.GetCommand(($mapping[$parameterSet]), [System.Management.Automation.CommandTypes]::Cmdlet)
        $scriptCmd = {& $wrappedCmd @PSBoundParameters}
        $steppablePipeline = $scriptCmd.GetSteppablePipeline($MyInvocation.CommandOrigin)
        $steppablePipeline.Begin($PSCmdlet)
    } catch {

        throw
    }
}

process {
    try {
        $steppablePipeline.Process($_)
    } catch {

        throw
    }

}
end {
    try {
        $steppablePipeline.End()

    } catch {

        throw
    }
} 
}

<#
.Synopsis
Get the number of the resource
.Description
Get the number of the resource
.Example
{{ Add code here }}
.Example
{{ Add code here }}

.Inputs
Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity
.Inputs
System.Collections.IDictionary
.Outputs
System.Int32
.Notes
COMPLEX PARAMETER PROPERTIES

To create the parameters described below, construct a hash table containing the appropriate properties. For information on hash tables, run Get-Help about_Hash_Tables.

INPUTOBJECT <IIdentityPartnerIdentity>: Identity Parameter
  [DelegatedAdminAccessAssignmentId <String>]: The unique identifier of delegatedAdminAccessAssignment
  [DelegatedAdminCustomerId <String>]: The unique identifier of delegatedAdminCustomer
  [DelegatedAdminRelationshipId <String>]: The unique identifier of delegatedAdminRelationship
  [DelegatedAdminRelationshipOperationId <String>]: The unique identifier of delegatedAdminRelationshipOperation
  [DelegatedAdminRelationshipRequestId <String>]: The unique identifier of delegatedAdminRelationshipRequest
  [DelegatedAdminServiceManagementDetailId <String>]: The unique identifier of delegatedAdminServiceManagementDetail
.Link
https://learn.microsoft.com/powershell/module/microsoft.graph.identity.partner/get-mgtenantrelationshipdelegatedadminrelationshipoperationcount
#>
function Get-MgTenantRelationshipDelegatedAdminRelationshipOperationCount {
[OutputType([System.Int32])]
[CmdletBinding(DefaultParameterSetName='Get', PositionalBinding=$false)]
param(
    [Parameter(ParameterSetName='Get', Mandatory)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [System.String]
    # The unique identifier of delegatedAdminRelationship
    ${DelegatedAdminRelationshipId},

    [Parameter(ParameterSetName='GetViaIdentity', Mandatory, ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity]
    # Identity Parameter
    # To construct, see NOTES section for INPUTOBJECT properties and create a hash table.
    ${InputObject},

    [Parameter()]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.String]
    # Filter items by property values
    ${Filter},

    [Parameter()]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.String]
    # Search items by search phrases
    ${Search},

    [Parameter()]
    [Alias('RHV')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # Optional Response Headers Variable.
    ${ResponseHeadersVariable},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Wait for .NET debugger to attach
    ${Break},

    [Parameter(ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Collections.IDictionary]
    # Optional headers that will be added to the request.
    ${Headers},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be appended to the front of the pipeline
    ${HttpPipelineAppend},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be prepended to the front of the pipeline
    ${HttpPipelinePrepend},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Uri]
    # The URI for the proxy server to use
    ${Proxy},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.PSCredential]
    # Credentials for a proxy server to use for the remote call
    ${ProxyCredential},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Use the default credentials for the proxy
    ${ProxyUseDefaultCredentials}
)

begin {
    try {
        $outBuffer = $null
        if ($PSBoundParameters.TryGetValue('OutBuffer', [ref]$outBuffer)) {
            $PSBoundParameters['OutBuffer'] = 1
        }
        $parameterSet = $PSCmdlet.ParameterSetName

        $mapping = @{
            Get = 'Microsoft.Graph.Identity.Partner.private\Get-MgTenantRelationshipDelegatedAdminRelationshipOperationCount_Get';
            GetViaIdentity = 'Microsoft.Graph.Identity.Partner.private\Get-MgTenantRelationshipDelegatedAdminRelationshipOperationCount_GetViaIdentity';
        }
        $cmdInfo = Get-Command -Name $mapping[$parameterSet]
        [Microsoft.Graph.PowerShell.Runtime.MessageAttributeHelper]::ProcessCustomAttributesAtRuntime($cmdInfo, $MyInvocation, $parameterSet, $PSCmdlet)
        $wrappedCmd = $ExecutionContext.InvokeCommand.GetCommand(($mapping[$parameterSet]), [System.Management.Automation.CommandTypes]::Cmdlet)
        $scriptCmd = {& $wrappedCmd @PSBoundParameters}
        $steppablePipeline = $scriptCmd.GetSteppablePipeline($MyInvocation.CommandOrigin)
        $steppablePipeline.Begin($PSCmdlet)
    } catch {

        throw
    }
}

process {
    try {
        $steppablePipeline.Process($_)
    } catch {

        throw
    }

}
end {
    try {
        $steppablePipeline.End()

    } catch {

        throw
    }
} 
}

<#
.Synopsis
Read the properties of a delegatedAdminRelationshipOperation object.
.Description
Read the properties of a delegatedAdminRelationshipOperation object.
.Example

Import-Module Microsoft.Graph.Identity.Partner

Get-MgTenantRelationshipDelegatedAdminRelationshipOperation -DelegatedAdminRelationshipId $delegatedAdminRelationshipId


.Inputs
Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity
.Inputs
System.Collections.IDictionary
.Outputs
Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminRelationshipOperation
.Notes
COMPLEX PARAMETER PROPERTIES

To create the parameters described below, construct a hash table containing the appropriate properties. For information on hash tables, run Get-Help about_Hash_Tables.

INPUTOBJECT <IIdentityPartnerIdentity>: Identity Parameter
  [DelegatedAdminAccessAssignmentId <String>]: The unique identifier of delegatedAdminAccessAssignment
  [DelegatedAdminCustomerId <String>]: The unique identifier of delegatedAdminCustomer
  [DelegatedAdminRelationshipId <String>]: The unique identifier of delegatedAdminRelationship
  [DelegatedAdminRelationshipOperationId <String>]: The unique identifier of delegatedAdminRelationshipOperation
  [DelegatedAdminRelationshipRequestId <String>]: The unique identifier of delegatedAdminRelationshipRequest
  [DelegatedAdminServiceManagementDetailId <String>]: The unique identifier of delegatedAdminServiceManagementDetail
.Link
https://learn.microsoft.com/powershell/module/microsoft.graph.identity.partner/get-mgtenantrelationshipdelegatedadminrelationshipoperation
.Link
https://learn.microsoft.com/graph/api/delegatedadminrelationshipoperation-get?view=graph-rest-1.0
.Link
https://learn.microsoft.com/graph/api/delegatedadminrelationship-list-operations?view=graph-rest-1.0
#>
function Get-MgTenantRelationshipDelegatedAdminRelationshipOperation {
[OutputType([Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminRelationshipOperation])]
[CmdletBinding(DefaultParameterSetName='List', PositionalBinding=$false)]
param(
    [Parameter(ParameterSetName='Get', Mandatory)]
    [Parameter(ParameterSetName='List', Mandatory)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [System.String]
    # The unique identifier of delegatedAdminRelationship
    ${DelegatedAdminRelationshipId},

    [Parameter(ParameterSetName='Get', Mandatory)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [System.String]
    # The unique identifier of delegatedAdminRelationshipOperation
    ${DelegatedAdminRelationshipOperationId},

    [Parameter(ParameterSetName='GetViaIdentity', Mandatory, ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity]
    # Identity Parameter
    # To construct, see NOTES section for INPUTOBJECT properties and create a hash table.
    ${InputObject},

    [Parameter()]
    [Alias('Expand')]
    [AllowEmptyCollection()]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.String[]]
    # Expand related entities
    ${ExpandProperty},

    [Parameter()]
    [Alias('Select')]
    [AllowEmptyCollection()]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.String[]]
    # Select properties to be returned
    ${Property},

    [Parameter(ParameterSetName='List')]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.String]
    # Filter items by property values
    ${Filter},

    [Parameter(ParameterSetName='List')]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.String]
    # Search items by search phrases
    ${Search},

    [Parameter(ParameterSetName='List')]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.Int32]
    # Skip the first n items
    ${Skip},

    [Parameter(ParameterSetName='List')]
    [Alias('OrderBy')]
    [AllowEmptyCollection()]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.String[]]
    # Order items by property values
    ${Sort},

    [Parameter(ParameterSetName='List')]
    [Alias('Limit')]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.Int32]
    # Show only the first n items
    ${Top},

    [Parameter()]
    [Alias('RHV')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # Optional Response Headers Variable.
    ${ResponseHeadersVariable},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Wait for .NET debugger to attach
    ${Break},

    [Parameter(ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Collections.IDictionary]
    # Optional headers that will be added to the request.
    ${Headers},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be appended to the front of the pipeline
    ${HttpPipelineAppend},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be prepended to the front of the pipeline
    ${HttpPipelinePrepend},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Uri]
    # The URI for the proxy server to use
    ${Proxy},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.PSCredential]
    # Credentials for a proxy server to use for the remote call
    ${ProxyCredential},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Use the default credentials for the proxy
    ${ProxyUseDefaultCredentials},

    [Parameter(ParameterSetName='List')]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Int32]
    # Sets the page size of results.
    ${PageSize},

    [Parameter(ParameterSetName='List')]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # List all pages.
    ${All},

    [Parameter(ParameterSetName='List')]
    [Alias('CV')]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.String]
    # Specifies a count of the total number of items in a collection.
    # By default, this variable will be set in the global scope.
    ${CountVariable}
)

begin {
    try {
        $outBuffer = $null
        if ($PSBoundParameters.TryGetValue('OutBuffer', [ref]$outBuffer)) {
            $PSBoundParameters['OutBuffer'] = 1
        }
        $parameterSet = $PSCmdlet.ParameterSetName

        $mapping = @{
            Get = 'Microsoft.Graph.Identity.Partner.private\Get-MgTenantRelationshipDelegatedAdminRelationshipOperation_Get';
            GetViaIdentity = 'Microsoft.Graph.Identity.Partner.private\Get-MgTenantRelationshipDelegatedAdminRelationshipOperation_GetViaIdentity';
            List = 'Microsoft.Graph.Identity.Partner.private\Get-MgTenantRelationshipDelegatedAdminRelationshipOperation_List';
        }
        $cmdInfo = Get-Command -Name $mapping[$parameterSet]
        [Microsoft.Graph.PowerShell.Runtime.MessageAttributeHelper]::ProcessCustomAttributesAtRuntime($cmdInfo, $MyInvocation, $parameterSet, $PSCmdlet)
        $wrappedCmd = $ExecutionContext.InvokeCommand.GetCommand(($mapping[$parameterSet]), [System.Management.Automation.CommandTypes]::Cmdlet)
        $scriptCmd = {& $wrappedCmd @PSBoundParameters}
        $steppablePipeline = $scriptCmd.GetSteppablePipeline($MyInvocation.CommandOrigin)
        $steppablePipeline.Begin($PSCmdlet)
    } catch {

        throw
    }
}

process {
    try {
        $steppablePipeline.Process($_)
    } catch {

        throw
    }

}
end {
    try {
        $steppablePipeline.End()

    } catch {

        throw
    }
} 
}

<#
.Synopsis
Get the number of the resource
.Description
Get the number of the resource
.Example
{{ Add code here }}
.Example
{{ Add code here }}

.Inputs
Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity
.Inputs
System.Collections.IDictionary
.Outputs
System.Int32
.Notes
COMPLEX PARAMETER PROPERTIES

To create the parameters described below, construct a hash table containing the appropriate properties. For information on hash tables, run Get-Help about_Hash_Tables.

INPUTOBJECT <IIdentityPartnerIdentity>: Identity Parameter
  [DelegatedAdminAccessAssignmentId <String>]: The unique identifier of delegatedAdminAccessAssignment
  [DelegatedAdminCustomerId <String>]: The unique identifier of delegatedAdminCustomer
  [DelegatedAdminRelationshipId <String>]: The unique identifier of delegatedAdminRelationship
  [DelegatedAdminRelationshipOperationId <String>]: The unique identifier of delegatedAdminRelationshipOperation
  [DelegatedAdminRelationshipRequestId <String>]: The unique identifier of delegatedAdminRelationshipRequest
  [DelegatedAdminServiceManagementDetailId <String>]: The unique identifier of delegatedAdminServiceManagementDetail
.Link
https://learn.microsoft.com/powershell/module/microsoft.graph.identity.partner/get-mgtenantrelationshipdelegatedadminrelationshiprequestcount
#>
function Get-MgTenantRelationshipDelegatedAdminRelationshipRequestCount {
[OutputType([System.Int32])]
[CmdletBinding(DefaultParameterSetName='Get', PositionalBinding=$false)]
param(
    [Parameter(ParameterSetName='Get', Mandatory)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [System.String]
    # The unique identifier of delegatedAdminRelationship
    ${DelegatedAdminRelationshipId},

    [Parameter(ParameterSetName='GetViaIdentity', Mandatory, ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity]
    # Identity Parameter
    # To construct, see NOTES section for INPUTOBJECT properties and create a hash table.
    ${InputObject},

    [Parameter()]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.String]
    # Filter items by property values
    ${Filter},

    [Parameter()]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.String]
    # Search items by search phrases
    ${Search},

    [Parameter()]
    [Alias('RHV')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # Optional Response Headers Variable.
    ${ResponseHeadersVariable},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Wait for .NET debugger to attach
    ${Break},

    [Parameter(ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Collections.IDictionary]
    # Optional headers that will be added to the request.
    ${Headers},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be appended to the front of the pipeline
    ${HttpPipelineAppend},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be prepended to the front of the pipeline
    ${HttpPipelinePrepend},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Uri]
    # The URI for the proxy server to use
    ${Proxy},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.PSCredential]
    # Credentials for a proxy server to use for the remote call
    ${ProxyCredential},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Use the default credentials for the proxy
    ${ProxyUseDefaultCredentials}
)

begin {
    try {
        $outBuffer = $null
        if ($PSBoundParameters.TryGetValue('OutBuffer', [ref]$outBuffer)) {
            $PSBoundParameters['OutBuffer'] = 1
        }
        $parameterSet = $PSCmdlet.ParameterSetName

        $mapping = @{
            Get = 'Microsoft.Graph.Identity.Partner.private\Get-MgTenantRelationshipDelegatedAdminRelationshipRequestCount_Get';
            GetViaIdentity = 'Microsoft.Graph.Identity.Partner.private\Get-MgTenantRelationshipDelegatedAdminRelationshipRequestCount_GetViaIdentity';
        }
        $cmdInfo = Get-Command -Name $mapping[$parameterSet]
        [Microsoft.Graph.PowerShell.Runtime.MessageAttributeHelper]::ProcessCustomAttributesAtRuntime($cmdInfo, $MyInvocation, $parameterSet, $PSCmdlet)
        $wrappedCmd = $ExecutionContext.InvokeCommand.GetCommand(($mapping[$parameterSet]), [System.Management.Automation.CommandTypes]::Cmdlet)
        $scriptCmd = {& $wrappedCmd @PSBoundParameters}
        $steppablePipeline = $scriptCmd.GetSteppablePipeline($MyInvocation.CommandOrigin)
        $steppablePipeline.Begin($PSCmdlet)
    } catch {

        throw
    }
}

process {
    try {
        $steppablePipeline.Process($_)
    } catch {

        throw
    }

}
end {
    try {
        $steppablePipeline.End()

    } catch {

        throw
    }
} 
}

<#
.Synopsis
Read the properties and relationships of a delegatedAdminRelationshipRequest object.
.Description
Read the properties and relationships of a delegatedAdminRelationshipRequest object.
.Example

Import-Module Microsoft.Graph.Identity.Partner

Get-MgTenantRelationshipDelegatedAdminRelationshipRequest -DelegatedAdminRelationshipId $delegatedAdminRelationshipId


.Inputs
Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity
.Inputs
System.Collections.IDictionary
.Outputs
Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminRelationshipRequest
.Notes
COMPLEX PARAMETER PROPERTIES

To create the parameters described below, construct a hash table containing the appropriate properties. For information on hash tables, run Get-Help about_Hash_Tables.

INPUTOBJECT <IIdentityPartnerIdentity>: Identity Parameter
  [DelegatedAdminAccessAssignmentId <String>]: The unique identifier of delegatedAdminAccessAssignment
  [DelegatedAdminCustomerId <String>]: The unique identifier of delegatedAdminCustomer
  [DelegatedAdminRelationshipId <String>]: The unique identifier of delegatedAdminRelationship
  [DelegatedAdminRelationshipOperationId <String>]: The unique identifier of delegatedAdminRelationshipOperation
  [DelegatedAdminRelationshipRequestId <String>]: The unique identifier of delegatedAdminRelationshipRequest
  [DelegatedAdminServiceManagementDetailId <String>]: The unique identifier of delegatedAdminServiceManagementDetail
.Link
https://learn.microsoft.com/powershell/module/microsoft.graph.identity.partner/get-mgtenantrelationshipdelegatedadminrelationshiprequest
.Link
https://learn.microsoft.com/graph/api/delegatedadminrelationshiprequest-get?view=graph-rest-1.0
.Link
https://learn.microsoft.com/graph/api/delegatedadminrelationship-list-requests?view=graph-rest-1.0
#>
function Get-MgTenantRelationshipDelegatedAdminRelationshipRequest {
[OutputType([Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminRelationshipRequest])]
[CmdletBinding(DefaultParameterSetName='List', PositionalBinding=$false)]
param(
    [Parameter(ParameterSetName='Get', Mandatory)]
    [Parameter(ParameterSetName='List', Mandatory)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [System.String]
    # The unique identifier of delegatedAdminRelationship
    ${DelegatedAdminRelationshipId},

    [Parameter(ParameterSetName='Get', Mandatory)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [System.String]
    # The unique identifier of delegatedAdminRelationshipRequest
    ${DelegatedAdminRelationshipRequestId},

    [Parameter(ParameterSetName='GetViaIdentity', Mandatory, ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity]
    # Identity Parameter
    # To construct, see NOTES section for INPUTOBJECT properties and create a hash table.
    ${InputObject},

    [Parameter()]
    [Alias('Expand')]
    [AllowEmptyCollection()]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.String[]]
    # Expand related entities
    ${ExpandProperty},

    [Parameter()]
    [Alias('Select')]
    [AllowEmptyCollection()]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.String[]]
    # Select properties to be returned
    ${Property},

    [Parameter(ParameterSetName='List')]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.String]
    # Filter items by property values
    ${Filter},

    [Parameter(ParameterSetName='List')]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.String]
    # Search items by search phrases
    ${Search},

    [Parameter(ParameterSetName='List')]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.Int32]
    # Skip the first n items
    ${Skip},

    [Parameter(ParameterSetName='List')]
    [Alias('OrderBy')]
    [AllowEmptyCollection()]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.String[]]
    # Order items by property values
    ${Sort},

    [Parameter(ParameterSetName='List')]
    [Alias('Limit')]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.Int32]
    # Show only the first n items
    ${Top},

    [Parameter()]
    [Alias('RHV')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # Optional Response Headers Variable.
    ${ResponseHeadersVariable},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Wait for .NET debugger to attach
    ${Break},

    [Parameter(ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Collections.IDictionary]
    # Optional headers that will be added to the request.
    ${Headers},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be appended to the front of the pipeline
    ${HttpPipelineAppend},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be prepended to the front of the pipeline
    ${HttpPipelinePrepend},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Uri]
    # The URI for the proxy server to use
    ${Proxy},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.PSCredential]
    # Credentials for a proxy server to use for the remote call
    ${ProxyCredential},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Use the default credentials for the proxy
    ${ProxyUseDefaultCredentials},

    [Parameter(ParameterSetName='List')]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Int32]
    # Sets the page size of results.
    ${PageSize},

    [Parameter(ParameterSetName='List')]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # List all pages.
    ${All},

    [Parameter(ParameterSetName='List')]
    [Alias('CV')]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.String]
    # Specifies a count of the total number of items in a collection.
    # By default, this variable will be set in the global scope.
    ${CountVariable}
)

begin {
    try {
        $outBuffer = $null
        if ($PSBoundParameters.TryGetValue('OutBuffer', [ref]$outBuffer)) {
            $PSBoundParameters['OutBuffer'] = 1
        }
        $parameterSet = $PSCmdlet.ParameterSetName

        $mapping = @{
            Get = 'Microsoft.Graph.Identity.Partner.private\Get-MgTenantRelationshipDelegatedAdminRelationshipRequest_Get';
            GetViaIdentity = 'Microsoft.Graph.Identity.Partner.private\Get-MgTenantRelationshipDelegatedAdminRelationshipRequest_GetViaIdentity';
            List = 'Microsoft.Graph.Identity.Partner.private\Get-MgTenantRelationshipDelegatedAdminRelationshipRequest_List';
        }
        $cmdInfo = Get-Command -Name $mapping[$parameterSet]
        [Microsoft.Graph.PowerShell.Runtime.MessageAttributeHelper]::ProcessCustomAttributesAtRuntime($cmdInfo, $MyInvocation, $parameterSet, $PSCmdlet)
        $wrappedCmd = $ExecutionContext.InvokeCommand.GetCommand(($mapping[$parameterSet]), [System.Management.Automation.CommandTypes]::Cmdlet)
        $scriptCmd = {& $wrappedCmd @PSBoundParameters}
        $steppablePipeline = $scriptCmd.GetSteppablePipeline($MyInvocation.CommandOrigin)
        $steppablePipeline.Begin($PSCmdlet)
    } catch {

        throw
    }
}

process {
    try {
        $steppablePipeline.Process($_)
    } catch {

        throw
    }

}
end {
    try {
        $steppablePipeline.End()

    } catch {

        throw
    }
} 
}

<#
.Synopsis
Read the properties of a delegatedAdminRelationship object.
.Description
Read the properties of a delegatedAdminRelationship object.
.Example

Import-Module Microsoft.Graph.Identity.Partner

Get-MgTenantRelationshipDelegatedAdminRelationship


.Inputs
Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity
.Inputs
System.Collections.IDictionary
.Outputs
Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminRelationship
.Notes
COMPLEX PARAMETER PROPERTIES

To create the parameters described below, construct a hash table containing the appropriate properties. For information on hash tables, run Get-Help about_Hash_Tables.

INPUTOBJECT <IIdentityPartnerIdentity>: Identity Parameter
  [DelegatedAdminAccessAssignmentId <String>]: The unique identifier of delegatedAdminAccessAssignment
  [DelegatedAdminCustomerId <String>]: The unique identifier of delegatedAdminCustomer
  [DelegatedAdminRelationshipId <String>]: The unique identifier of delegatedAdminRelationship
  [DelegatedAdminRelationshipOperationId <String>]: The unique identifier of delegatedAdminRelationshipOperation
  [DelegatedAdminRelationshipRequestId <String>]: The unique identifier of delegatedAdminRelationshipRequest
  [DelegatedAdminServiceManagementDetailId <String>]: The unique identifier of delegatedAdminServiceManagementDetail
.Link
https://learn.microsoft.com/powershell/module/microsoft.graph.identity.partner/get-mgtenantrelationshipdelegatedadminrelationship
.Link
https://learn.microsoft.com/graph/api/delegatedadminrelationship-get?view=graph-rest-1.0
.Link
https://learn.microsoft.com/graph/api/tenantrelationship-list-delegatedadminrelationships?view=graph-rest-1.0
#>
function Get-MgTenantRelationshipDelegatedAdminRelationship {
[OutputType([Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminRelationship])]
[CmdletBinding(DefaultParameterSetName='List', PositionalBinding=$false)]
param(
    [Parameter(ParameterSetName='Get', Mandatory)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [System.String]
    # The unique identifier of delegatedAdminRelationship
    ${DelegatedAdminRelationshipId},

    [Parameter(ParameterSetName='GetViaIdentity', Mandatory, ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity]
    # Identity Parameter
    # To construct, see NOTES section for INPUTOBJECT properties and create a hash table.
    ${InputObject},

    [Parameter()]
    [Alias('Expand')]
    [AllowEmptyCollection()]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.String[]]
    # Expand related entities
    ${ExpandProperty},

    [Parameter()]
    [Alias('Select')]
    [AllowEmptyCollection()]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.String[]]
    # Select properties to be returned
    ${Property},

    [Parameter(ParameterSetName='List')]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.String]
    # Filter items by property values
    ${Filter},

    [Parameter(ParameterSetName='List')]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.String]
    # Search items by search phrases
    ${Search},

    [Parameter(ParameterSetName='List')]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.Int32]
    # Skip the first n items
    ${Skip},

    [Parameter(ParameterSetName='List')]
    [Alias('OrderBy')]
    [AllowEmptyCollection()]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.String[]]
    # Order items by property values
    ${Sort},

    [Parameter(ParameterSetName='List')]
    [Alias('Limit')]
    [Microsoft.Graph.PowerShell.Category('Query')]
    [System.Int32]
    # Show only the first n items
    ${Top},

    [Parameter()]
    [Alias('RHV')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # Optional Response Headers Variable.
    ${ResponseHeadersVariable},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Wait for .NET debugger to attach
    ${Break},

    [Parameter(ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Collections.IDictionary]
    # Optional headers that will be added to the request.
    ${Headers},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be appended to the front of the pipeline
    ${HttpPipelineAppend},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be prepended to the front of the pipeline
    ${HttpPipelinePrepend},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Uri]
    # The URI for the proxy server to use
    ${Proxy},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.PSCredential]
    # Credentials for a proxy server to use for the remote call
    ${ProxyCredential},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Use the default credentials for the proxy
    ${ProxyUseDefaultCredentials},

    [Parameter(ParameterSetName='List')]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Int32]
    # Sets the page size of results.
    ${PageSize},

    [Parameter(ParameterSetName='List')]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # List all pages.
    ${All},

    [Parameter(ParameterSetName='List')]
    [Alias('CV')]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.String]
    # Specifies a count of the total number of items in a collection.
    # By default, this variable will be set in the global scope.
    ${CountVariable}
)

begin {
    try {
        $outBuffer = $null
        if ($PSBoundParameters.TryGetValue('OutBuffer', [ref]$outBuffer)) {
            $PSBoundParameters['OutBuffer'] = 1
        }
        $parameterSet = $PSCmdlet.ParameterSetName

        $mapping = @{
            Get = 'Microsoft.Graph.Identity.Partner.private\Get-MgTenantRelationshipDelegatedAdminRelationship_Get';
            GetViaIdentity = 'Microsoft.Graph.Identity.Partner.private\Get-MgTenantRelationshipDelegatedAdminRelationship_GetViaIdentity';
            List = 'Microsoft.Graph.Identity.Partner.private\Get-MgTenantRelationshipDelegatedAdminRelationship_List';
        }
        $cmdInfo = Get-Command -Name $mapping[$parameterSet]
        [Microsoft.Graph.PowerShell.Runtime.MessageAttributeHelper]::ProcessCustomAttributesAtRuntime($cmdInfo, $MyInvocation, $parameterSet, $PSCmdlet)
        $wrappedCmd = $ExecutionContext.InvokeCommand.GetCommand(($mapping[$parameterSet]), [System.Management.Automation.CommandTypes]::Cmdlet)
        $scriptCmd = {& $wrappedCmd @PSBoundParameters}
        $steppablePipeline = $scriptCmd.GetSteppablePipeline($MyInvocation.CommandOrigin)
        $steppablePipeline.Begin($PSCmdlet)
    } catch {

        throw
    }
}

process {
    try {
        $steppablePipeline.Process($_)
    } catch {

        throw
    }

}
end {
    try {
        $steppablePipeline.End()

    } catch {

        throw
    }
} 
}

<#
.Synopsis
Create new navigation property to serviceManagementDetails for tenantRelationships
.Description
Create new navigation property to serviceManagementDetails for tenantRelationships
.Example
{{ Add code here }}
.Example
{{ Add code here }}

.Inputs
Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity
.Inputs
Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminServiceManagementDetail
.Inputs
System.Collections.IDictionary
.Outputs
Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminServiceManagementDetail
.Notes
COMPLEX PARAMETER PROPERTIES

To create the parameters described below, construct a hash table containing the appropriate properties. For information on hash tables, run Get-Help about_Hash_Tables.

BODYPARAMETER <IMicrosoftGraphDelegatedAdminServiceManagementDetail>: delegatedAdminServiceManagementDetail
  [(Any) <Object>]: This indicates any property can be added to this object.
  [Id <String>]: The unique identifier for an entity. Read-only.
  [ServiceManagementUrl <String>]: The URL of the management portal for the managed service. Read-only.
  [ServiceName <String>]: The name of a managed service. Read-only.

INPUTOBJECT <IIdentityPartnerIdentity>: Identity Parameter
  [DelegatedAdminAccessAssignmentId <String>]: The unique identifier of delegatedAdminAccessAssignment
  [DelegatedAdminCustomerId <String>]: The unique identifier of delegatedAdminCustomer
  [DelegatedAdminRelationshipId <String>]: The unique identifier of delegatedAdminRelationship
  [DelegatedAdminRelationshipOperationId <String>]: The unique identifier of delegatedAdminRelationshipOperation
  [DelegatedAdminRelationshipRequestId <String>]: The unique identifier of delegatedAdminRelationshipRequest
  [DelegatedAdminServiceManagementDetailId <String>]: The unique identifier of delegatedAdminServiceManagementDetail
.Link
https://learn.microsoft.com/powershell/module/microsoft.graph.identity.partner/new-mgtenantrelationshipdelegatedadmincustomerservicemanagementdetail
#>
function New-MgTenantRelationshipDelegatedAdminCustomerServiceManagementDetail {
[OutputType([Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminServiceManagementDetail])]
[CmdletBinding(DefaultParameterSetName='CreateExpanded', PositionalBinding=$false, SupportsShouldProcess, ConfirmImpact='Medium')]
param(
    [Parameter(ParameterSetName='Create', Mandatory)]
    [Parameter(ParameterSetName='CreateExpanded', Mandatory)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [System.String]
    # The unique identifier of delegatedAdminCustomer
    ${DelegatedAdminCustomerId},

    [Parameter(ParameterSetName='CreateViaIdentity', Mandatory, ValueFromPipeline)]
    [Parameter(ParameterSetName='CreateViaIdentityExpanded', Mandatory, ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity]
    # Identity Parameter
    # To construct, see NOTES section for INPUTOBJECT properties and create a hash table.
    ${InputObject},

    [Parameter(ParameterSetName='Create', Mandatory, ValueFromPipeline)]
    [Parameter(ParameterSetName='CreateViaIdentity', Mandatory, ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminServiceManagementDetail]
    # delegatedAdminServiceManagementDetail
    # To construct, see NOTES section for BODYPARAMETER properties and create a hash table.
    ${BodyParameter},

    [Parameter()]
    [Alias('RHV')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # Optional Response Headers Variable.
    ${ResponseHeadersVariable},

    [Parameter(ParameterSetName='CreateExpanded')]
    [Parameter(ParameterSetName='CreateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.Collections.Hashtable]
    # Additional Parameters
    ${AdditionalProperties},

    [Parameter(ParameterSetName='CreateExpanded')]
    [Parameter(ParameterSetName='CreateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # The unique identifier for an entity.
    # Read-only.
    ${Id},

    [Parameter(ParameterSetName='CreateExpanded')]
    [Parameter(ParameterSetName='CreateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # The URL of the management portal for the managed service.
    # Read-only.
    ${ServiceManagementUrl},

    [Parameter(ParameterSetName='CreateExpanded')]
    [Parameter(ParameterSetName='CreateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # The name of a managed service.
    # Read-only.
    ${ServiceName},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Wait for .NET debugger to attach
    ${Break},

    [Parameter(ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Collections.IDictionary]
    # Optional headers that will be added to the request.
    ${Headers},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be appended to the front of the pipeline
    ${HttpPipelineAppend},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be prepended to the front of the pipeline
    ${HttpPipelinePrepend},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Uri]
    # The URI for the proxy server to use
    ${Proxy},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.PSCredential]
    # Credentials for a proxy server to use for the remote call
    ${ProxyCredential},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Use the default credentials for the proxy
    ${ProxyUseDefaultCredentials}
)

begin {
    try {
        $outBuffer = $null
        if ($PSBoundParameters.TryGetValue('OutBuffer', [ref]$outBuffer)) {
            $PSBoundParameters['OutBuffer'] = 1
        }
        $parameterSet = $PSCmdlet.ParameterSetName

        $mapping = @{
            Create = 'Microsoft.Graph.Identity.Partner.private\New-MgTenantRelationshipDelegatedAdminCustomerServiceManagementDetail_Create';
            CreateExpanded = 'Microsoft.Graph.Identity.Partner.private\New-MgTenantRelationshipDelegatedAdminCustomerServiceManagementDetail_CreateExpanded';
            CreateViaIdentity = 'Microsoft.Graph.Identity.Partner.private\New-MgTenantRelationshipDelegatedAdminCustomerServiceManagementDetail_CreateViaIdentity';
            CreateViaIdentityExpanded = 'Microsoft.Graph.Identity.Partner.private\New-MgTenantRelationshipDelegatedAdminCustomerServiceManagementDetail_CreateViaIdentityExpanded';
        }
        $cmdInfo = Get-Command -Name $mapping[$parameterSet]
        [Microsoft.Graph.PowerShell.Runtime.MessageAttributeHelper]::ProcessCustomAttributesAtRuntime($cmdInfo, $MyInvocation, $parameterSet, $PSCmdlet)
        $wrappedCmd = $ExecutionContext.InvokeCommand.GetCommand(($mapping[$parameterSet]), [System.Management.Automation.CommandTypes]::Cmdlet)
        $scriptCmd = {& $wrappedCmd @PSBoundParameters}
        $steppablePipeline = $scriptCmd.GetSteppablePipeline($MyInvocation.CommandOrigin)
        $steppablePipeline.Begin($PSCmdlet)
    } catch {

        throw
    }
}

process {
    try {
        $steppablePipeline.Process($_)
    } catch {

        throw
    }

}
end {
    try {
        $steppablePipeline.End()

    } catch {

        throw
    }
} 
}

<#
.Synopsis
Create new navigation property to delegatedAdminCustomers for tenantRelationships
.Description
Create new navigation property to delegatedAdminCustomers for tenantRelationships
.Example
{{ Add code here }}
.Example
{{ Add code here }}

.Inputs
Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminCustomer
.Inputs
System.Collections.IDictionary
.Outputs
Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminCustomer
.Notes
COMPLEX PARAMETER PROPERTIES

To create the parameters described below, construct a hash table containing the appropriate properties. For information on hash tables, run Get-Help about_Hash_Tables.

BODYPARAMETER <IMicrosoftGraphDelegatedAdminCustomer>: delegatedAdminCustomer
  [(Any) <Object>]: This indicates any property can be added to this object.
  [Id <String>]: The unique identifier for an entity. Read-only.
  [DisplayName <String>]: The Microsoft Entra ID display name of the customer tenant. Read-only. Supports $orderby.
  [ServiceManagementDetails <IMicrosoftGraphDelegatedAdminServiceManagementDetail[]>]: Contains the management details of a service in the customer tenant that's managed by delegated administration.
    [Id <String>]: The unique identifier for an entity. Read-only.
    [ServiceManagementUrl <String>]: The URL of the management portal for the managed service. Read-only.
    [ServiceName <String>]: The name of a managed service. Read-only.
  [TenantId <String>]: The Microsoft Entra ID-assigned tenant ID of the customer. Read-only.

SERVICEMANAGEMENTDETAILS <IMicrosoftGraphDelegatedAdminServiceManagementDetail[]>: Contains the management details of a service in the customer tenant that's managed by delegated administration.
  [Id <String>]: The unique identifier for an entity. Read-only.
  [ServiceManagementUrl <String>]: The URL of the management portal for the managed service. Read-only.
  [ServiceName <String>]: The name of a managed service. Read-only.
.Link
https://learn.microsoft.com/powershell/module/microsoft.graph.identity.partner/new-mgtenantrelationshipdelegatedadmincustomer
#>
function New-MgTenantRelationshipDelegatedAdminCustomer {
[OutputType([Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminCustomer])]
[CmdletBinding(DefaultParameterSetName='CreateExpanded', PositionalBinding=$false, SupportsShouldProcess, ConfirmImpact='Medium')]
param(
    [Parameter(ParameterSetName='Create', Mandatory, ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminCustomer]
    # delegatedAdminCustomer
    # To construct, see NOTES section for BODYPARAMETER properties and create a hash table.
    ${BodyParameter},

    [Parameter()]
    [Alias('RHV')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # Optional Response Headers Variable.
    ${ResponseHeadersVariable},

    [Parameter(ParameterSetName='CreateExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.Collections.Hashtable]
    # Additional Parameters
    ${AdditionalProperties},

    [Parameter(ParameterSetName='CreateExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # The Microsoft Entra ID display name of the customer tenant.
    # Read-only.
    # Supports $orderby.
    ${DisplayName},

    [Parameter(ParameterSetName='CreateExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # The unique identifier for an entity.
    # Read-only.
    ${Id},

    [Parameter(ParameterSetName='CreateExpanded')]
    [AllowEmptyCollection()]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminServiceManagementDetail[]]
    # Contains the management details of a service in the customer tenant that's managed by delegated administration.
    # To construct, see NOTES section for SERVICEMANAGEMENTDETAILS properties and create a hash table.
    ${ServiceManagementDetails},

    [Parameter(ParameterSetName='CreateExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # The Microsoft Entra ID-assigned tenant ID of the customer.
    # Read-only.
    ${TenantId},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Wait for .NET debugger to attach
    ${Break},

    [Parameter(ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Collections.IDictionary]
    # Optional headers that will be added to the request.
    ${Headers},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be appended to the front of the pipeline
    ${HttpPipelineAppend},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be prepended to the front of the pipeline
    ${HttpPipelinePrepend},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Uri]
    # The URI for the proxy server to use
    ${Proxy},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.PSCredential]
    # Credentials for a proxy server to use for the remote call
    ${ProxyCredential},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Use the default credentials for the proxy
    ${ProxyUseDefaultCredentials}
)

begin {
    try {
        $outBuffer = $null
        if ($PSBoundParameters.TryGetValue('OutBuffer', [ref]$outBuffer)) {
            $PSBoundParameters['OutBuffer'] = 1
        }
        $parameterSet = $PSCmdlet.ParameterSetName

        $mapping = @{
            Create = 'Microsoft.Graph.Identity.Partner.private\New-MgTenantRelationshipDelegatedAdminCustomer_Create';
            CreateExpanded = 'Microsoft.Graph.Identity.Partner.private\New-MgTenantRelationshipDelegatedAdminCustomer_CreateExpanded';
        }
        $cmdInfo = Get-Command -Name $mapping[$parameterSet]
        [Microsoft.Graph.PowerShell.Runtime.MessageAttributeHelper]::ProcessCustomAttributesAtRuntime($cmdInfo, $MyInvocation, $parameterSet, $PSCmdlet)
        $wrappedCmd = $ExecutionContext.InvokeCommand.GetCommand(($mapping[$parameterSet]), [System.Management.Automation.CommandTypes]::Cmdlet)
        $scriptCmd = {& $wrappedCmd @PSBoundParameters}
        $steppablePipeline = $scriptCmd.GetSteppablePipeline($MyInvocation.CommandOrigin)
        $steppablePipeline.Begin($PSCmdlet)
    } catch {

        throw
    }
}

process {
    try {
        $steppablePipeline.Process($_)
    } catch {

        throw
    }

}
end {
    try {
        $steppablePipeline.End()

    } catch {

        throw
    }
} 
}

<#
.Synopsis
Create a new delegatedAdminAccessAssignment object.
.Description
Create a new delegatedAdminAccessAssignment object.
.Example

Import-Module Microsoft.Graph.Identity.Partner

$params = @{
	accessContainer = @{
		accessContainerId = "869713c9-0b28-4d08-8949-ae07ae1bf528"
		accessContainerType = "securityGroup"
	}
	accessDetails = @{
		unifiedRoles = @(
			@{
				roleDefinitionId = "29232cdf-9323-42fd-ade2-1d097af3e4de"
			}
			@{
				roleDefinitionId = "f2ef992c-3afb-46b9-b7cf-a126ee74c451"
			}
			@{
				roleDefinitionId = "729827e3-9c14-49f7-bb1b-9608f156bbb8"
			}
			@{
				roleDefinitionId = "3a2c62db-5318-420d-8d74-23affee5d9d5"
			}
		)
	}
}

New-MgTenantRelationshipDelegatedAdminRelationshipAccessAssignment -DelegatedAdminRelationshipId $delegatedAdminRelationshipId -BodyParameter $params


.Inputs
Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity
.Inputs
Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminAccessAssignment
.Inputs
System.Collections.IDictionary
.Outputs
Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminAccessAssignment
.Notes
COMPLEX PARAMETER PROPERTIES

To create the parameters described below, construct a hash table containing the appropriate properties. For information on hash tables, run Get-Help about_Hash_Tables.

ACCESSCONTAINER <IMicrosoftGraphDelegatedAdminAccessContainer>: delegatedAdminAccessContainer
  [(Any) <Object>]: This indicates any property can be added to this object.
  [AccessContainerId <String>]: The identifier of the access container (for example, a security group). For 'securityGroup' access containers, this must be a valid ID of a Microsoft Entra security group in the Microsoft partner's tenant.
  [AccessContainerType <String>]: delegatedAdminAccessContainerType

ACCESSDETAILS <IMicrosoftGraphDelegatedAdminAccessDetails>: delegatedAdminAccessDetails
  [(Any) <Object>]: This indicates any property can be added to this object.
  [UnifiedRoles <IMicrosoftGraphUnifiedRole[]>]: The directory roles that the Microsoft partner is assigned in the customer tenant.
    [RoleDefinitionId <String>]: The unified role definition ID of the directory role. Refer to unifiedRoleDefinition resource.

BODYPARAMETER <IMicrosoftGraphDelegatedAdminAccessAssignment>: delegatedAdminAccessAssignment
  [(Any) <Object>]: This indicates any property can be added to this object.
  [Id <String>]: The unique identifier for an entity. Read-only.
  [AccessContainer <IMicrosoftGraphDelegatedAdminAccessContainer>]: delegatedAdminAccessContainer
    [(Any) <Object>]: This indicates any property can be added to this object.
    [AccessContainerId <String>]: The identifier of the access container (for example, a security group). For 'securityGroup' access containers, this must be a valid ID of a Microsoft Entra security group in the Microsoft partner's tenant.
    [AccessContainerType <String>]: delegatedAdminAccessContainerType
  [AccessDetails <IMicrosoftGraphDelegatedAdminAccessDetails>]: delegatedAdminAccessDetails
    [(Any) <Object>]: This indicates any property can be added to this object.
    [UnifiedRoles <IMicrosoftGraphUnifiedRole[]>]: The directory roles that the Microsoft partner is assigned in the customer tenant.
      [RoleDefinitionId <String>]: The unified role definition ID of the directory role. Refer to unifiedRoleDefinition resource.
  [CreatedDateTime <DateTime?>]: The date and time in ISO 8601 format and in UTC time when the access assignment was created. Read-only.
  [LastModifiedDateTime <DateTime?>]: The date and time in ISO 8601 and in UTC time when this access assignment was last modified. Read-only.
  [Status <String>]: delegatedAdminAccessAssignmentStatus

INPUTOBJECT <IIdentityPartnerIdentity>: Identity Parameter
  [DelegatedAdminAccessAssignmentId <String>]: The unique identifier of delegatedAdminAccessAssignment
  [DelegatedAdminCustomerId <String>]: The unique identifier of delegatedAdminCustomer
  [DelegatedAdminRelationshipId <String>]: The unique identifier of delegatedAdminRelationship
  [DelegatedAdminRelationshipOperationId <String>]: The unique identifier of delegatedAdminRelationshipOperation
  [DelegatedAdminRelationshipRequestId <String>]: The unique identifier of delegatedAdminRelationshipRequest
  [DelegatedAdminServiceManagementDetailId <String>]: The unique identifier of delegatedAdminServiceManagementDetail
.Link
https://learn.microsoft.com/powershell/module/microsoft.graph.identity.partner/new-mgtenantrelationshipdelegatedadminrelationshipaccessassignment
.Link
https://learn.microsoft.com/graph/api/delegatedadminrelationship-post-accessassignments?view=graph-rest-1.0
#>
function New-MgTenantRelationshipDelegatedAdminRelationshipAccessAssignment {
[OutputType([Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminAccessAssignment])]
[CmdletBinding(DefaultParameterSetName='CreateExpanded', PositionalBinding=$false, SupportsShouldProcess, ConfirmImpact='Medium')]
param(
    [Parameter(ParameterSetName='Create', Mandatory)]
    [Parameter(ParameterSetName='CreateExpanded', Mandatory)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [System.String]
    # The unique identifier of delegatedAdminRelationship
    ${DelegatedAdminRelationshipId},

    [Parameter(ParameterSetName='CreateViaIdentity', Mandatory, ValueFromPipeline)]
    [Parameter(ParameterSetName='CreateViaIdentityExpanded', Mandatory, ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity]
    # Identity Parameter
    # To construct, see NOTES section for INPUTOBJECT properties and create a hash table.
    ${InputObject},

    [Parameter(ParameterSetName='Create', Mandatory, ValueFromPipeline)]
    [Parameter(ParameterSetName='CreateViaIdentity', Mandatory, ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminAccessAssignment]
    # delegatedAdminAccessAssignment
    # To construct, see NOTES section for BODYPARAMETER properties and create a hash table.
    ${BodyParameter},

    [Parameter()]
    [Alias('RHV')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # Optional Response Headers Variable.
    ${ResponseHeadersVariable},

    [Parameter(ParameterSetName='CreateExpanded')]
    [Parameter(ParameterSetName='CreateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminAccessContainer]
    # delegatedAdminAccessContainer
    # To construct, see NOTES section for ACCESSCONTAINER properties and create a hash table.
    ${AccessContainer},

    [Parameter(ParameterSetName='CreateExpanded')]
    [Parameter(ParameterSetName='CreateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminAccessDetails]
    # delegatedAdminAccessDetails
    # To construct, see NOTES section for ACCESSDETAILS properties and create a hash table.
    ${AccessDetails},

    [Parameter(ParameterSetName='CreateExpanded')]
    [Parameter(ParameterSetName='CreateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.Collections.Hashtable]
    # Additional Parameters
    ${AdditionalProperties},

    [Parameter(ParameterSetName='CreateExpanded')]
    [Parameter(ParameterSetName='CreateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.DateTime]
    # The date and time in ISO 8601 format and in UTC time when the access assignment was created.
    # Read-only.
    ${CreatedDateTime},

    [Parameter(ParameterSetName='CreateExpanded')]
    [Parameter(ParameterSetName='CreateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # The unique identifier for an entity.
    # Read-only.
    ${Id},

    [Parameter(ParameterSetName='CreateExpanded')]
    [Parameter(ParameterSetName='CreateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.DateTime]
    # The date and time in ISO 8601 and in UTC time when this access assignment was last modified.
    # Read-only.
    ${LastModifiedDateTime},

    [Parameter(ParameterSetName='CreateExpanded')]
    [Parameter(ParameterSetName='CreateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # delegatedAdminAccessAssignmentStatus
    ${Status},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Wait for .NET debugger to attach
    ${Break},

    [Parameter(ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Collections.IDictionary]
    # Optional headers that will be added to the request.
    ${Headers},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be appended to the front of the pipeline
    ${HttpPipelineAppend},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be prepended to the front of the pipeline
    ${HttpPipelinePrepend},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Uri]
    # The URI for the proxy server to use
    ${Proxy},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.PSCredential]
    # Credentials for a proxy server to use for the remote call
    ${ProxyCredential},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Use the default credentials for the proxy
    ${ProxyUseDefaultCredentials}
)

begin {
    try {
        $outBuffer = $null
        if ($PSBoundParameters.TryGetValue('OutBuffer', [ref]$outBuffer)) {
            $PSBoundParameters['OutBuffer'] = 1
        }
        $parameterSet = $PSCmdlet.ParameterSetName

        $mapping = @{
            Create = 'Microsoft.Graph.Identity.Partner.private\New-MgTenantRelationshipDelegatedAdminRelationshipAccessAssignment_Create';
            CreateExpanded = 'Microsoft.Graph.Identity.Partner.private\New-MgTenantRelationshipDelegatedAdminRelationshipAccessAssignment_CreateExpanded';
            CreateViaIdentity = 'Microsoft.Graph.Identity.Partner.private\New-MgTenantRelationshipDelegatedAdminRelationshipAccessAssignment_CreateViaIdentity';
            CreateViaIdentityExpanded = 'Microsoft.Graph.Identity.Partner.private\New-MgTenantRelationshipDelegatedAdminRelationshipAccessAssignment_CreateViaIdentityExpanded';
        }
        $cmdInfo = Get-Command -Name $mapping[$parameterSet]
        [Microsoft.Graph.PowerShell.Runtime.MessageAttributeHelper]::ProcessCustomAttributesAtRuntime($cmdInfo, $MyInvocation, $parameterSet, $PSCmdlet)
        $wrappedCmd = $ExecutionContext.InvokeCommand.GetCommand(($mapping[$parameterSet]), [System.Management.Automation.CommandTypes]::Cmdlet)
        $scriptCmd = {& $wrappedCmd @PSBoundParameters}
        $steppablePipeline = $scriptCmd.GetSteppablePipeline($MyInvocation.CommandOrigin)
        $steppablePipeline.Begin($PSCmdlet)
    } catch {

        throw
    }
}

process {
    try {
        $steppablePipeline.Process($_)
    } catch {

        throw
    }

}
end {
    try {
        $steppablePipeline.End()

    } catch {

        throw
    }
} 
}

<#
.Synopsis
Create new navigation property to operations for tenantRelationships
.Description
Create new navigation property to operations for tenantRelationships
.Example
{{ Add code here }}
.Example
{{ Add code here }}

.Inputs
Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity
.Inputs
Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminRelationshipOperation
.Inputs
System.Collections.IDictionary
.Outputs
Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminRelationshipOperation
.Notes
COMPLEX PARAMETER PROPERTIES

To create the parameters described below, construct a hash table containing the appropriate properties. For information on hash tables, run Get-Help about_Hash_Tables.

BODYPARAMETER <IMicrosoftGraphDelegatedAdminRelationshipOperation>: delegatedAdminRelationshipOperation
  [(Any) <Object>]: This indicates any property can be added to this object.
  [Id <String>]: The unique identifier for an entity. Read-only.
  [CreatedDateTime <DateTime?>]: The time in ISO 8601 format and in UTC time when the long-running operation was created. Read-only.
  [Data <String>]: The data (payload) for the operation. Read-only.
  [LastModifiedDateTime <DateTime?>]: The time in ISO 8601 format and in UTC time when the long-running operation was last modified. Read-only.
  [OperationType <String>]: delegatedAdminRelationshipOperationType
  [Status <String>]: longRunningOperationStatus

INPUTOBJECT <IIdentityPartnerIdentity>: Identity Parameter
  [DelegatedAdminAccessAssignmentId <String>]: The unique identifier of delegatedAdminAccessAssignment
  [DelegatedAdminCustomerId <String>]: The unique identifier of delegatedAdminCustomer
  [DelegatedAdminRelationshipId <String>]: The unique identifier of delegatedAdminRelationship
  [DelegatedAdminRelationshipOperationId <String>]: The unique identifier of delegatedAdminRelationshipOperation
  [DelegatedAdminRelationshipRequestId <String>]: The unique identifier of delegatedAdminRelationshipRequest
  [DelegatedAdminServiceManagementDetailId <String>]: The unique identifier of delegatedAdminServiceManagementDetail
.Link
https://learn.microsoft.com/powershell/module/microsoft.graph.identity.partner/new-mgtenantrelationshipdelegatedadminrelationshipoperation
#>
function New-MgTenantRelationshipDelegatedAdminRelationshipOperation {
[OutputType([Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminRelationshipOperation])]
[CmdletBinding(DefaultParameterSetName='CreateExpanded', PositionalBinding=$false, SupportsShouldProcess, ConfirmImpact='Medium')]
param(
    [Parameter(ParameterSetName='Create', Mandatory)]
    [Parameter(ParameterSetName='CreateExpanded', Mandatory)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [System.String]
    # The unique identifier of delegatedAdminRelationship
    ${DelegatedAdminRelationshipId},

    [Parameter(ParameterSetName='CreateViaIdentity', Mandatory, ValueFromPipeline)]
    [Parameter(ParameterSetName='CreateViaIdentityExpanded', Mandatory, ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity]
    # Identity Parameter
    # To construct, see NOTES section for INPUTOBJECT properties and create a hash table.
    ${InputObject},

    [Parameter(ParameterSetName='Create', Mandatory, ValueFromPipeline)]
    [Parameter(ParameterSetName='CreateViaIdentity', Mandatory, ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminRelationshipOperation]
    # delegatedAdminRelationshipOperation
    # To construct, see NOTES section for BODYPARAMETER properties and create a hash table.
    ${BodyParameter},

    [Parameter()]
    [Alias('RHV')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # Optional Response Headers Variable.
    ${ResponseHeadersVariable},

    [Parameter(ParameterSetName='CreateExpanded')]
    [Parameter(ParameterSetName='CreateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.Collections.Hashtable]
    # Additional Parameters
    ${AdditionalProperties},

    [Parameter(ParameterSetName='CreateExpanded')]
    [Parameter(ParameterSetName='CreateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.DateTime]
    # The time in ISO 8601 format and in UTC time when the long-running operation was created.
    # Read-only.
    ${CreatedDateTime},

    [Parameter(ParameterSetName='CreateExpanded')]
    [Parameter(ParameterSetName='CreateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # The data (payload) for the operation.
    # Read-only.
    ${Data},

    [Parameter(ParameterSetName='CreateExpanded')]
    [Parameter(ParameterSetName='CreateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # The unique identifier for an entity.
    # Read-only.
    ${Id},

    [Parameter(ParameterSetName='CreateExpanded')]
    [Parameter(ParameterSetName='CreateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.DateTime]
    # The time in ISO 8601 format and in UTC time when the long-running operation was last modified.
    # Read-only.
    ${LastModifiedDateTime},

    [Parameter(ParameterSetName='CreateExpanded')]
    [Parameter(ParameterSetName='CreateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # delegatedAdminRelationshipOperationType
    ${OperationType},

    [Parameter(ParameterSetName='CreateExpanded')]
    [Parameter(ParameterSetName='CreateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # longRunningOperationStatus
    ${Status},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Wait for .NET debugger to attach
    ${Break},

    [Parameter(ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Collections.IDictionary]
    # Optional headers that will be added to the request.
    ${Headers},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be appended to the front of the pipeline
    ${HttpPipelineAppend},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be prepended to the front of the pipeline
    ${HttpPipelinePrepend},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Uri]
    # The URI for the proxy server to use
    ${Proxy},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.PSCredential]
    # Credentials for a proxy server to use for the remote call
    ${ProxyCredential},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Use the default credentials for the proxy
    ${ProxyUseDefaultCredentials}
)

begin {
    try {
        $outBuffer = $null
        if ($PSBoundParameters.TryGetValue('OutBuffer', [ref]$outBuffer)) {
            $PSBoundParameters['OutBuffer'] = 1
        }
        $parameterSet = $PSCmdlet.ParameterSetName

        $mapping = @{
            Create = 'Microsoft.Graph.Identity.Partner.private\New-MgTenantRelationshipDelegatedAdminRelationshipOperation_Create';
            CreateExpanded = 'Microsoft.Graph.Identity.Partner.private\New-MgTenantRelationshipDelegatedAdminRelationshipOperation_CreateExpanded';
            CreateViaIdentity = 'Microsoft.Graph.Identity.Partner.private\New-MgTenantRelationshipDelegatedAdminRelationshipOperation_CreateViaIdentity';
            CreateViaIdentityExpanded = 'Microsoft.Graph.Identity.Partner.private\New-MgTenantRelationshipDelegatedAdminRelationshipOperation_CreateViaIdentityExpanded';
        }
        $cmdInfo = Get-Command -Name $mapping[$parameterSet]
        [Microsoft.Graph.PowerShell.Runtime.MessageAttributeHelper]::ProcessCustomAttributesAtRuntime($cmdInfo, $MyInvocation, $parameterSet, $PSCmdlet)
        $wrappedCmd = $ExecutionContext.InvokeCommand.GetCommand(($mapping[$parameterSet]), [System.Management.Automation.CommandTypes]::Cmdlet)
        $scriptCmd = {& $wrappedCmd @PSBoundParameters}
        $steppablePipeline = $scriptCmd.GetSteppablePipeline($MyInvocation.CommandOrigin)
        $steppablePipeline.Begin($PSCmdlet)
    } catch {

        throw
    }
}

process {
    try {
        $steppablePipeline.Process($_)
    } catch {

        throw
    }

}
end {
    try {
        $steppablePipeline.End()

    } catch {

        throw
    }
} 
}

<#
.Synopsis
Create a new delegatedAdminRelationshipRequest object.
.Description
Create a new delegatedAdminRelationshipRequest object.
.Example

Import-Module Microsoft.Graph.Identity.Partner

$params = @{
	action = "lockForApproval"
}

New-MgTenantRelationshipDelegatedAdminRelationshipRequest -DelegatedAdminRelationshipId $delegatedAdminRelationshipId -BodyParameter $params


.Inputs
Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity
.Inputs
Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminRelationshipRequest
.Inputs
System.Collections.IDictionary
.Outputs
Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminRelationshipRequest
.Notes
COMPLEX PARAMETER PROPERTIES

To create the parameters described below, construct a hash table containing the appropriate properties. For information on hash tables, run Get-Help about_Hash_Tables.

BODYPARAMETER <IMicrosoftGraphDelegatedAdminRelationshipRequest>: delegatedAdminRelationshipRequest
  [(Any) <Object>]: This indicates any property can be added to this object.
  [Id <String>]: The unique identifier for an entity. Read-only.
  [Action <String>]: delegatedAdminRelationshipRequestAction
  [CreatedDateTime <DateTime?>]: The date and time in ISO 8601 format and in UTC time when the relationship request was created. Read-only.
  [LastModifiedDateTime <DateTime?>]: The date and time in ISO 8601 format and UTC time when this relationship request was last modified. Read-only.
  [Status <String>]: delegatedAdminRelationshipRequestStatus

INPUTOBJECT <IIdentityPartnerIdentity>: Identity Parameter
  [DelegatedAdminAccessAssignmentId <String>]: The unique identifier of delegatedAdminAccessAssignment
  [DelegatedAdminCustomerId <String>]: The unique identifier of delegatedAdminCustomer
  [DelegatedAdminRelationshipId <String>]: The unique identifier of delegatedAdminRelationship
  [DelegatedAdminRelationshipOperationId <String>]: The unique identifier of delegatedAdminRelationshipOperation
  [DelegatedAdminRelationshipRequestId <String>]: The unique identifier of delegatedAdminRelationshipRequest
  [DelegatedAdminServiceManagementDetailId <String>]: The unique identifier of delegatedAdminServiceManagementDetail
.Link
https://learn.microsoft.com/powershell/module/microsoft.graph.identity.partner/new-mgtenantrelationshipdelegatedadminrelationshiprequest
.Link
https://learn.microsoft.com/graph/api/delegatedadminrelationship-post-requests?view=graph-rest-1.0
#>
function New-MgTenantRelationshipDelegatedAdminRelationshipRequest {
[OutputType([Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminRelationshipRequest])]
[CmdletBinding(DefaultParameterSetName='CreateExpanded', PositionalBinding=$false, SupportsShouldProcess, ConfirmImpact='Medium')]
param(
    [Parameter(ParameterSetName='Create', Mandatory)]
    [Parameter(ParameterSetName='CreateExpanded', Mandatory)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [System.String]
    # The unique identifier of delegatedAdminRelationship
    ${DelegatedAdminRelationshipId},

    [Parameter(ParameterSetName='CreateViaIdentity', Mandatory, ValueFromPipeline)]
    [Parameter(ParameterSetName='CreateViaIdentityExpanded', Mandatory, ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity]
    # Identity Parameter
    # To construct, see NOTES section for INPUTOBJECT properties and create a hash table.
    ${InputObject},

    [Parameter(ParameterSetName='Create', Mandatory, ValueFromPipeline)]
    [Parameter(ParameterSetName='CreateViaIdentity', Mandatory, ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminRelationshipRequest]
    # delegatedAdminRelationshipRequest
    # To construct, see NOTES section for BODYPARAMETER properties and create a hash table.
    ${BodyParameter},

    [Parameter()]
    [Alias('RHV')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # Optional Response Headers Variable.
    ${ResponseHeadersVariable},

    [Parameter(ParameterSetName='CreateExpanded')]
    [Parameter(ParameterSetName='CreateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # delegatedAdminRelationshipRequestAction
    ${Action},

    [Parameter(ParameterSetName='CreateExpanded')]
    [Parameter(ParameterSetName='CreateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.Collections.Hashtable]
    # Additional Parameters
    ${AdditionalProperties},

    [Parameter(ParameterSetName='CreateExpanded')]
    [Parameter(ParameterSetName='CreateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.DateTime]
    # The date and time in ISO 8601 format and in UTC time when the relationship request was created.
    # Read-only.
    ${CreatedDateTime},

    [Parameter(ParameterSetName='CreateExpanded')]
    [Parameter(ParameterSetName='CreateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # The unique identifier for an entity.
    # Read-only.
    ${Id},

    [Parameter(ParameterSetName='CreateExpanded')]
    [Parameter(ParameterSetName='CreateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.DateTime]
    # The date and time in ISO 8601 format and UTC time when this relationship request was last modified.
    # Read-only.
    ${LastModifiedDateTime},

    [Parameter(ParameterSetName='CreateExpanded')]
    [Parameter(ParameterSetName='CreateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # delegatedAdminRelationshipRequestStatus
    ${Status},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Wait for .NET debugger to attach
    ${Break},

    [Parameter(ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Collections.IDictionary]
    # Optional headers that will be added to the request.
    ${Headers},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be appended to the front of the pipeline
    ${HttpPipelineAppend},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be prepended to the front of the pipeline
    ${HttpPipelinePrepend},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Uri]
    # The URI for the proxy server to use
    ${Proxy},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.PSCredential]
    # Credentials for a proxy server to use for the remote call
    ${ProxyCredential},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Use the default credentials for the proxy
    ${ProxyUseDefaultCredentials}
)

begin {
    try {
        $outBuffer = $null
        if ($PSBoundParameters.TryGetValue('OutBuffer', [ref]$outBuffer)) {
            $PSBoundParameters['OutBuffer'] = 1
        }
        $parameterSet = $PSCmdlet.ParameterSetName

        $mapping = @{
            Create = 'Microsoft.Graph.Identity.Partner.private\New-MgTenantRelationshipDelegatedAdminRelationshipRequest_Create';
            CreateExpanded = 'Microsoft.Graph.Identity.Partner.private\New-MgTenantRelationshipDelegatedAdminRelationshipRequest_CreateExpanded';
            CreateViaIdentity = 'Microsoft.Graph.Identity.Partner.private\New-MgTenantRelationshipDelegatedAdminRelationshipRequest_CreateViaIdentity';
            CreateViaIdentityExpanded = 'Microsoft.Graph.Identity.Partner.private\New-MgTenantRelationshipDelegatedAdminRelationshipRequest_CreateViaIdentityExpanded';
        }
        $cmdInfo = Get-Command -Name $mapping[$parameterSet]
        [Microsoft.Graph.PowerShell.Runtime.MessageAttributeHelper]::ProcessCustomAttributesAtRuntime($cmdInfo, $MyInvocation, $parameterSet, $PSCmdlet)
        $wrappedCmd = $ExecutionContext.InvokeCommand.GetCommand(($mapping[$parameterSet]), [System.Management.Automation.CommandTypes]::Cmdlet)
        $scriptCmd = {& $wrappedCmd @PSBoundParameters}
        $steppablePipeline = $scriptCmd.GetSteppablePipeline($MyInvocation.CommandOrigin)
        $steppablePipeline.Begin($PSCmdlet)
    } catch {

        throw
    }
}

process {
    try {
        $steppablePipeline.Process($_)
    } catch {

        throw
    }

}
end {
    try {
        $steppablePipeline.End()

    } catch {

        throw
    }
} 
}

<#
.Synopsis
Create a new delegatedAdminRelationship object.
.Description
Create a new delegatedAdminRelationship object.
.Example

Import-Module Microsoft.Graph.Identity.Partner

$params = @{
	displayName = "Contoso admin relationship"
	duration = "P730D"
	customer = @{
		tenantId = "4b827261-d21f-4aa9-b7db-7fa1f56fb163"
		displayName = "Contoso subsidiary Inc"
	}
	accessDetails = @{
		unifiedRoles = @(
			@{
				roleDefinitionId = "29232cdf-9323-42fd-ade2-1d097af3e4de"
			}
			@{
				roleDefinitionId = "3a2c62db-5318-420d-8d74-23affee5d9d5"
			}
		)
	}
	autoExtendDuration = "P180D"
}

New-MgTenantRelationshipDelegatedAdminRelationship -BodyParameter $params


.Inputs
Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminRelationship
.Inputs
System.Collections.IDictionary
.Outputs
Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminRelationship
.Notes
COMPLEX PARAMETER PROPERTIES

To create the parameters described below, construct a hash table containing the appropriate properties. For information on hash tables, run Get-Help about_Hash_Tables.

ACCESSASSIGNMENTS <IMicrosoftGraphDelegatedAdminAccessAssignment[]>: The access assignments associated with the delegated admin relationship.
  [Id <String>]: The unique identifier for an entity. Read-only.
  [AccessContainer <IMicrosoftGraphDelegatedAdminAccessContainer>]: delegatedAdminAccessContainer
    [(Any) <Object>]: This indicates any property can be added to this object.
    [AccessContainerId <String>]: The identifier of the access container (for example, a security group). For 'securityGroup' access containers, this must be a valid ID of a Microsoft Entra security group in the Microsoft partner's tenant.
    [AccessContainerType <String>]: delegatedAdminAccessContainerType
  [AccessDetails <IMicrosoftGraphDelegatedAdminAccessDetails>]: delegatedAdminAccessDetails
    [(Any) <Object>]: This indicates any property can be added to this object.
    [UnifiedRoles <IMicrosoftGraphUnifiedRole[]>]: The directory roles that the Microsoft partner is assigned in the customer tenant.
      [RoleDefinitionId <String>]: The unified role definition ID of the directory role. Refer to unifiedRoleDefinition resource.
  [CreatedDateTime <DateTime?>]: The date and time in ISO 8601 format and in UTC time when the access assignment was created. Read-only.
  [LastModifiedDateTime <DateTime?>]: The date and time in ISO 8601 and in UTC time when this access assignment was last modified. Read-only.
  [Status <String>]: delegatedAdminAccessAssignmentStatus

ACCESSDETAILS <IMicrosoftGraphDelegatedAdminAccessDetails>: delegatedAdminAccessDetails
  [(Any) <Object>]: This indicates any property can be added to this object.
  [UnifiedRoles <IMicrosoftGraphUnifiedRole[]>]: The directory roles that the Microsoft partner is assigned in the customer tenant.
    [RoleDefinitionId <String>]: The unified role definition ID of the directory role. Refer to unifiedRoleDefinition resource.

BODYPARAMETER <IMicrosoftGraphDelegatedAdminRelationship>: delegatedAdminRelationship
  [(Any) <Object>]: This indicates any property can be added to this object.
  [Id <String>]: The unique identifier for an entity. Read-only.
  [AccessAssignments <IMicrosoftGraphDelegatedAdminAccessAssignment[]>]: The access assignments associated with the delegated admin relationship.
    [Id <String>]: The unique identifier for an entity. Read-only.
    [AccessContainer <IMicrosoftGraphDelegatedAdminAccessContainer>]: delegatedAdminAccessContainer
      [(Any) <Object>]: This indicates any property can be added to this object.
      [AccessContainerId <String>]: The identifier of the access container (for example, a security group). For 'securityGroup' access containers, this must be a valid ID of a Microsoft Entra security group in the Microsoft partner's tenant.
      [AccessContainerType <String>]: delegatedAdminAccessContainerType
    [AccessDetails <IMicrosoftGraphDelegatedAdminAccessDetails>]: delegatedAdminAccessDetails
      [(Any) <Object>]: This indicates any property can be added to this object.
      [UnifiedRoles <IMicrosoftGraphUnifiedRole[]>]: The directory roles that the Microsoft partner is assigned in the customer tenant.
        [RoleDefinitionId <String>]: The unified role definition ID of the directory role. Refer to unifiedRoleDefinition resource.
    [CreatedDateTime <DateTime?>]: The date and time in ISO 8601 format and in UTC time when the access assignment was created. Read-only.
    [LastModifiedDateTime <DateTime?>]: The date and time in ISO 8601 and in UTC time when this access assignment was last modified. Read-only.
    [Status <String>]: delegatedAdminAccessAssignmentStatus
  [AccessDetails <IMicrosoftGraphDelegatedAdminAccessDetails>]: delegatedAdminAccessDetails
  [ActivatedDateTime <DateTime?>]: The date and time in ISO 8601 format and in UTC time when the relationship became active. Read-only.
  [AutoExtendDuration <TimeSpan?>]: The duration by which the validity of the relationship is automatically extended, denoted in ISO 8601 format. Supported values are: P0D, PT0S, P180D. The default value is PT0S. PT0S indicates that the relationship expires when the endDateTime is reached and it isn't automatically extended.
  [CreatedDateTime <DateTime?>]: The date and time in ISO 8601 format and in UTC time when the relationship was created. Read-only.
  [Customer <IMicrosoftGraphDelegatedAdminRelationshipCustomerParticipant>]: delegatedAdminRelationshipCustomerParticipant
    [(Any) <Object>]: This indicates any property can be added to this object.
    [DisplayName <String>]: The display name of the customer tenant as set by Microsoft Entra ID. Read-only
    [TenantId <String>]: The Microsoft Entra ID-assigned tenant ID of the customer tenant.
  [DisplayName <String>]: The display name of the relationship used for ease of identification. Must be unique across all delegated admin relationships of the partner and is set by the partner only when the relationship is in the created status and can't be changed by the customer. Maximum length is 50 characters.
  [Duration <TimeSpan?>]: The duration of the relationship in ISO 8601 format. Must be a value between P1D and P2Y inclusive. This is set by the partner only when the relationship is in the created status and can't be changed by the customer.
  [EndDateTime <DateTime?>]: The date and time in ISO 8601 format and in UTC time when the status of relationship changes to either terminated or expired. Calculated as endDateTime = activatedDateTime + duration. Read-only.
  [LastModifiedDateTime <DateTime?>]: The date and time in ISO 8601 format and in UTC time when the relationship was last modified. Read-only.
  [Operations <IMicrosoftGraphDelegatedAdminRelationshipOperation[]>]: The long running operations associated with the delegated admin relationship.
    [Id <String>]: The unique identifier for an entity. Read-only.
    [CreatedDateTime <DateTime?>]: The time in ISO 8601 format and in UTC time when the long-running operation was created. Read-only.
    [Data <String>]: The data (payload) for the operation. Read-only.
    [LastModifiedDateTime <DateTime?>]: The time in ISO 8601 format and in UTC time when the long-running operation was last modified. Read-only.
    [OperationType <String>]: delegatedAdminRelationshipOperationType
    [Status <String>]: longRunningOperationStatus
  [Requests <IMicrosoftGraphDelegatedAdminRelationshipRequest[]>]: The requests associated with the delegated admin relationship.
    [Id <String>]: The unique identifier for an entity. Read-only.
    [Action <String>]: delegatedAdminRelationshipRequestAction
    [CreatedDateTime <DateTime?>]: The date and time in ISO 8601 format and in UTC time when the relationship request was created. Read-only.
    [LastModifiedDateTime <DateTime?>]: The date and time in ISO 8601 format and UTC time when this relationship request was last modified. Read-only.
    [Status <String>]: delegatedAdminRelationshipRequestStatus
  [Status <String>]: delegatedAdminRelationshipStatus

CUSTOMER <IMicrosoftGraphDelegatedAdminRelationshipCustomerParticipant>: delegatedAdminRelationshipCustomerParticipant
  [(Any) <Object>]: This indicates any property can be added to this object.
  [DisplayName <String>]: The display name of the customer tenant as set by Microsoft Entra ID. Read-only
  [TenantId <String>]: The Microsoft Entra ID-assigned tenant ID of the customer tenant.

OPERATIONS <IMicrosoftGraphDelegatedAdminRelationshipOperation[]>: The long running operations associated with the delegated admin relationship.
  [Id <String>]: The unique identifier for an entity. Read-only.
  [CreatedDateTime <DateTime?>]: The time in ISO 8601 format and in UTC time when the long-running operation was created. Read-only.
  [Data <String>]: The data (payload) for the operation. Read-only.
  [LastModifiedDateTime <DateTime?>]: The time in ISO 8601 format and in UTC time when the long-running operation was last modified. Read-only.
  [OperationType <String>]: delegatedAdminRelationshipOperationType
  [Status <String>]: longRunningOperationStatus

REQUESTS <IMicrosoftGraphDelegatedAdminRelationshipRequest[]>: The requests associated with the delegated admin relationship.
  [Id <String>]: The unique identifier for an entity. Read-only.
  [Action <String>]: delegatedAdminRelationshipRequestAction
  [CreatedDateTime <DateTime?>]: The date and time in ISO 8601 format and in UTC time when the relationship request was created. Read-only.
  [LastModifiedDateTime <DateTime?>]: The date and time in ISO 8601 format and UTC time when this relationship request was last modified. Read-only.
  [Status <String>]: delegatedAdminRelationshipRequestStatus
.Link
https://learn.microsoft.com/powershell/module/microsoft.graph.identity.partner/new-mgtenantrelationshipdelegatedadminrelationship
.Link
https://learn.microsoft.com/graph/api/tenantrelationship-post-delegatedadminrelationships?view=graph-rest-1.0
#>
function New-MgTenantRelationshipDelegatedAdminRelationship {
[OutputType([Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminRelationship])]
[CmdletBinding(DefaultParameterSetName='CreateExpanded', PositionalBinding=$false, SupportsShouldProcess, ConfirmImpact='Medium')]
param(
    [Parameter(ParameterSetName='Create', Mandatory, ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminRelationship]
    # delegatedAdminRelationship
    # To construct, see NOTES section for BODYPARAMETER properties and create a hash table.
    ${BodyParameter},

    [Parameter()]
    [Alias('RHV')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # Optional Response Headers Variable.
    ${ResponseHeadersVariable},

    [Parameter(ParameterSetName='CreateExpanded')]
    [AllowEmptyCollection()]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminAccessAssignment[]]
    # The access assignments associated with the delegated admin relationship.
    # To construct, see NOTES section for ACCESSASSIGNMENTS properties and create a hash table.
    ${AccessAssignments},

    [Parameter(ParameterSetName='CreateExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminAccessDetails]
    # delegatedAdminAccessDetails
    # To construct, see NOTES section for ACCESSDETAILS properties and create a hash table.
    ${AccessDetails},

    [Parameter(ParameterSetName='CreateExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.DateTime]
    # The date and time in ISO 8601 format and in UTC time when the relationship became active.
    # Read-only.
    ${ActivatedDateTime},

    [Parameter(ParameterSetName='CreateExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.Collections.Hashtable]
    # Additional Parameters
    ${AdditionalProperties},

    [Parameter(ParameterSetName='CreateExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.TimeSpan]
    # The duration by which the validity of the relationship is automatically extended, denoted in ISO 8601 format.
    # Supported values are: P0D, PT0S, P180D.
    # The default value is PT0S.
    # PT0S indicates that the relationship expires when the endDateTime is reached and it isn't automatically extended.
    ${AutoExtendDuration},

    [Parameter(ParameterSetName='CreateExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.DateTime]
    # The date and time in ISO 8601 format and in UTC time when the relationship was created.
    # Read-only.
    ${CreatedDateTime},

    [Parameter(ParameterSetName='CreateExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminRelationshipCustomerParticipant]
    # delegatedAdminRelationshipCustomerParticipant
    # To construct, see NOTES section for CUSTOMER properties and create a hash table.
    ${Customer},

    [Parameter(ParameterSetName='CreateExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # The display name of the relationship used for ease of identification.
    # Must be unique across all delegated admin relationships of the partner and is set by the partner only when the relationship is in the created status and can't be changed by the customer.
    # Maximum length is 50 characters.
    ${DisplayName},

    [Parameter(ParameterSetName='CreateExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.TimeSpan]
    # The duration of the relationship in ISO 8601 format.
    # Must be a value between P1D and P2Y inclusive.
    # This is set by the partner only when the relationship is in the created status and can't be changed by the customer.
    ${Duration},

    [Parameter(ParameterSetName='CreateExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.DateTime]
    # The date and time in ISO 8601 format and in UTC time when the status of relationship changes to either terminated or expired.
    # Calculated as endDateTime = activatedDateTime + duration.
    # Read-only.
    ${EndDateTime},

    [Parameter(ParameterSetName='CreateExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # The unique identifier for an entity.
    # Read-only.
    ${Id},

    [Parameter(ParameterSetName='CreateExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.DateTime]
    # The date and time in ISO 8601 format and in UTC time when the relationship was last modified.
    # Read-only.
    ${LastModifiedDateTime},

    [Parameter(ParameterSetName='CreateExpanded')]
    [AllowEmptyCollection()]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminRelationshipOperation[]]
    # The long running operations associated with the delegated admin relationship.
    # To construct, see NOTES section for OPERATIONS properties and create a hash table.
    ${Operations},

    [Parameter(ParameterSetName='CreateExpanded')]
    [AllowEmptyCollection()]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminRelationshipRequest[]]
    # The requests associated with the delegated admin relationship.
    # To construct, see NOTES section for REQUESTS properties and create a hash table.
    ${Requests},

    [Parameter(ParameterSetName='CreateExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # delegatedAdminRelationshipStatus
    ${Status},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Wait for .NET debugger to attach
    ${Break},

    [Parameter(ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Collections.IDictionary]
    # Optional headers that will be added to the request.
    ${Headers},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be appended to the front of the pipeline
    ${HttpPipelineAppend},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be prepended to the front of the pipeline
    ${HttpPipelinePrepend},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Uri]
    # The URI for the proxy server to use
    ${Proxy},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.PSCredential]
    # Credentials for a proxy server to use for the remote call
    ${ProxyCredential},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Use the default credentials for the proxy
    ${ProxyUseDefaultCredentials}
)

begin {
    try {
        $outBuffer = $null
        if ($PSBoundParameters.TryGetValue('OutBuffer', [ref]$outBuffer)) {
            $PSBoundParameters['OutBuffer'] = 1
        }
        $parameterSet = $PSCmdlet.ParameterSetName

        $mapping = @{
            Create = 'Microsoft.Graph.Identity.Partner.private\New-MgTenantRelationshipDelegatedAdminRelationship_Create';
            CreateExpanded = 'Microsoft.Graph.Identity.Partner.private\New-MgTenantRelationshipDelegatedAdminRelationship_CreateExpanded';
        }
        $cmdInfo = Get-Command -Name $mapping[$parameterSet]
        [Microsoft.Graph.PowerShell.Runtime.MessageAttributeHelper]::ProcessCustomAttributesAtRuntime($cmdInfo, $MyInvocation, $parameterSet, $PSCmdlet)
        $wrappedCmd = $ExecutionContext.InvokeCommand.GetCommand(($mapping[$parameterSet]), [System.Management.Automation.CommandTypes]::Cmdlet)
        $scriptCmd = {& $wrappedCmd @PSBoundParameters}
        $steppablePipeline = $scriptCmd.GetSteppablePipeline($MyInvocation.CommandOrigin)
        $steppablePipeline.Begin($PSCmdlet)
    } catch {

        throw
    }
}

process {
    try {
        $steppablePipeline.Process($_)
    } catch {

        throw
    }

}
end {
    try {
        $steppablePipeline.End()

    } catch {

        throw
    }
} 
}

<#
.Synopsis
Delete navigation property serviceManagementDetails for tenantRelationships
.Description
Delete navigation property serviceManagementDetails for tenantRelationships
.Example
{{ Add code here }}
.Example
{{ Add code here }}

.Inputs
Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity
.Inputs
System.Collections.IDictionary
.Outputs
System.Boolean
.Notes
COMPLEX PARAMETER PROPERTIES

To create the parameters described below, construct a hash table containing the appropriate properties. For information on hash tables, run Get-Help about_Hash_Tables.

INPUTOBJECT <IIdentityPartnerIdentity>: Identity Parameter
  [DelegatedAdminAccessAssignmentId <String>]: The unique identifier of delegatedAdminAccessAssignment
  [DelegatedAdminCustomerId <String>]: The unique identifier of delegatedAdminCustomer
  [DelegatedAdminRelationshipId <String>]: The unique identifier of delegatedAdminRelationship
  [DelegatedAdminRelationshipOperationId <String>]: The unique identifier of delegatedAdminRelationshipOperation
  [DelegatedAdminRelationshipRequestId <String>]: The unique identifier of delegatedAdminRelationshipRequest
  [DelegatedAdminServiceManagementDetailId <String>]: The unique identifier of delegatedAdminServiceManagementDetail
.Link
https://learn.microsoft.com/powershell/module/microsoft.graph.identity.partner/remove-mgtenantrelationshipdelegatedadmincustomerservicemanagementdetail
#>
function Remove-MgTenantRelationshipDelegatedAdminCustomerServiceManagementDetail {
[OutputType([System.Boolean])]
[CmdletBinding(DefaultParameterSetName='Delete', PositionalBinding=$false, SupportsShouldProcess, ConfirmImpact='Medium')]
param(
    [Parameter(ParameterSetName='Delete', Mandatory)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [System.String]
    # The unique identifier of delegatedAdminCustomer
    ${DelegatedAdminCustomerId},

    [Parameter(ParameterSetName='Delete', Mandatory)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [System.String]
    # The unique identifier of delegatedAdminServiceManagementDetail
    ${DelegatedAdminServiceManagementDetailId},

    [Parameter(ParameterSetName='DeleteViaIdentity', Mandatory, ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity]
    # Identity Parameter
    # To construct, see NOTES section for INPUTOBJECT properties and create a hash table.
    ${InputObject},

    [Parameter()]
    [Microsoft.Graph.PowerShell.Category('Header')]
    [System.String]
    # ETag
    ${IfMatch},

    [Parameter()]
    [Alias('RHV')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # Optional Response Headers Variable.
    ${ResponseHeadersVariable},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Wait for .NET debugger to attach
    ${Break},

    [Parameter(ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Collections.IDictionary]
    # Optional headers that will be added to the request.
    ${Headers},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be appended to the front of the pipeline
    ${HttpPipelineAppend},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be prepended to the front of the pipeline
    ${HttpPipelinePrepend},

    [Parameter()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Returns true when the command succeeds
    ${PassThru},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Uri]
    # The URI for the proxy server to use
    ${Proxy},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.PSCredential]
    # Credentials for a proxy server to use for the remote call
    ${ProxyCredential},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Use the default credentials for the proxy
    ${ProxyUseDefaultCredentials}
)

begin {
    try {
        $outBuffer = $null
        if ($PSBoundParameters.TryGetValue('OutBuffer', [ref]$outBuffer)) {
            $PSBoundParameters['OutBuffer'] = 1
        }
        $parameterSet = $PSCmdlet.ParameterSetName

        $mapping = @{
            Delete = 'Microsoft.Graph.Identity.Partner.private\Remove-MgTenantRelationshipDelegatedAdminCustomerServiceManagementDetail_Delete';
            DeleteViaIdentity = 'Microsoft.Graph.Identity.Partner.private\Remove-MgTenantRelationshipDelegatedAdminCustomerServiceManagementDetail_DeleteViaIdentity';
        }
        $cmdInfo = Get-Command -Name $mapping[$parameterSet]
        [Microsoft.Graph.PowerShell.Runtime.MessageAttributeHelper]::ProcessCustomAttributesAtRuntime($cmdInfo, $MyInvocation, $parameterSet, $PSCmdlet)
        $wrappedCmd = $ExecutionContext.InvokeCommand.GetCommand(($mapping[$parameterSet]), [System.Management.Automation.CommandTypes]::Cmdlet)
        $scriptCmd = {& $wrappedCmd @PSBoundParameters}
        $steppablePipeline = $scriptCmd.GetSteppablePipeline($MyInvocation.CommandOrigin)
        $steppablePipeline.Begin($PSCmdlet)
    } catch {

        throw
    }
}

process {
    try {
        $steppablePipeline.Process($_)
    } catch {

        throw
    }

}
end {
    try {
        $steppablePipeline.End()

    } catch {

        throw
    }
} 
}

<#
.Synopsis
Delete navigation property delegatedAdminCustomers for tenantRelationships
.Description
Delete navigation property delegatedAdminCustomers for tenantRelationships
.Example
{{ Add code here }}
.Example
{{ Add code here }}

.Inputs
Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity
.Inputs
System.Collections.IDictionary
.Outputs
System.Boolean
.Notes
COMPLEX PARAMETER PROPERTIES

To create the parameters described below, construct a hash table containing the appropriate properties. For information on hash tables, run Get-Help about_Hash_Tables.

INPUTOBJECT <IIdentityPartnerIdentity>: Identity Parameter
  [DelegatedAdminAccessAssignmentId <String>]: The unique identifier of delegatedAdminAccessAssignment
  [DelegatedAdminCustomerId <String>]: The unique identifier of delegatedAdminCustomer
  [DelegatedAdminRelationshipId <String>]: The unique identifier of delegatedAdminRelationship
  [DelegatedAdminRelationshipOperationId <String>]: The unique identifier of delegatedAdminRelationshipOperation
  [DelegatedAdminRelationshipRequestId <String>]: The unique identifier of delegatedAdminRelationshipRequest
  [DelegatedAdminServiceManagementDetailId <String>]: The unique identifier of delegatedAdminServiceManagementDetail
.Link
https://learn.microsoft.com/powershell/module/microsoft.graph.identity.partner/remove-mgtenantrelationshipdelegatedadmincustomer
#>
function Remove-MgTenantRelationshipDelegatedAdminCustomer {
[OutputType([System.Boolean])]
[CmdletBinding(DefaultParameterSetName='Delete', PositionalBinding=$false, SupportsShouldProcess, ConfirmImpact='Medium')]
param(
    [Parameter(ParameterSetName='Delete', Mandatory)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [System.String]
    # The unique identifier of delegatedAdminCustomer
    ${DelegatedAdminCustomerId},

    [Parameter(ParameterSetName='DeleteViaIdentity', Mandatory, ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity]
    # Identity Parameter
    # To construct, see NOTES section for INPUTOBJECT properties and create a hash table.
    ${InputObject},

    [Parameter()]
    [Microsoft.Graph.PowerShell.Category('Header')]
    [System.String]
    # ETag
    ${IfMatch},

    [Parameter()]
    [Alias('RHV')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # Optional Response Headers Variable.
    ${ResponseHeadersVariable},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Wait for .NET debugger to attach
    ${Break},

    [Parameter(ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Collections.IDictionary]
    # Optional headers that will be added to the request.
    ${Headers},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be appended to the front of the pipeline
    ${HttpPipelineAppend},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be prepended to the front of the pipeline
    ${HttpPipelinePrepend},

    [Parameter()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Returns true when the command succeeds
    ${PassThru},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Uri]
    # The URI for the proxy server to use
    ${Proxy},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.PSCredential]
    # Credentials for a proxy server to use for the remote call
    ${ProxyCredential},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Use the default credentials for the proxy
    ${ProxyUseDefaultCredentials}
)

begin {
    try {
        $outBuffer = $null
        if ($PSBoundParameters.TryGetValue('OutBuffer', [ref]$outBuffer)) {
            $PSBoundParameters['OutBuffer'] = 1
        }
        $parameterSet = $PSCmdlet.ParameterSetName

        $mapping = @{
            Delete = 'Microsoft.Graph.Identity.Partner.private\Remove-MgTenantRelationshipDelegatedAdminCustomer_Delete';
            DeleteViaIdentity = 'Microsoft.Graph.Identity.Partner.private\Remove-MgTenantRelationshipDelegatedAdminCustomer_DeleteViaIdentity';
        }
        $cmdInfo = Get-Command -Name $mapping[$parameterSet]
        [Microsoft.Graph.PowerShell.Runtime.MessageAttributeHelper]::ProcessCustomAttributesAtRuntime($cmdInfo, $MyInvocation, $parameterSet, $PSCmdlet)
        $wrappedCmd = $ExecutionContext.InvokeCommand.GetCommand(($mapping[$parameterSet]), [System.Management.Automation.CommandTypes]::Cmdlet)
        $scriptCmd = {& $wrappedCmd @PSBoundParameters}
        $steppablePipeline = $scriptCmd.GetSteppablePipeline($MyInvocation.CommandOrigin)
        $steppablePipeline.Begin($PSCmdlet)
    } catch {

        throw
    }
}

process {
    try {
        $steppablePipeline.Process($_)
    } catch {

        throw
    }

}
end {
    try {
        $steppablePipeline.End()

    } catch {

        throw
    }
} 
}

<#
.Synopsis
Delete a delegatedAdminAccessAssignment object.
.Description
Delete a delegatedAdminAccessAssignment object.
.Example

Import-Module Microsoft.Graph.Identity.Partner

Remove-MgTenantRelationshipDelegatedAdminRelationshipAccessAssignment -DelegatedAdminRelationshipId $delegatedAdminRelationshipId -DelegatedAdminAccessAssignmentId $delegatedAdminAccessAssignmentId -IfMatch W/'"JyI0NzAwNjg0NS0wMDAwLTE5MDAtMDAwMC02MGY0Yjg4MzAwMDAiJw=="' 



.Inputs
Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity
.Inputs
System.Collections.IDictionary
.Outputs
System.Boolean
.Notes
COMPLEX PARAMETER PROPERTIES

To create the parameters described below, construct a hash table containing the appropriate properties. For information on hash tables, run Get-Help about_Hash_Tables.

INPUTOBJECT <IIdentityPartnerIdentity>: Identity Parameter
  [DelegatedAdminAccessAssignmentId <String>]: The unique identifier of delegatedAdminAccessAssignment
  [DelegatedAdminCustomerId <String>]: The unique identifier of delegatedAdminCustomer
  [DelegatedAdminRelationshipId <String>]: The unique identifier of delegatedAdminRelationship
  [DelegatedAdminRelationshipOperationId <String>]: The unique identifier of delegatedAdminRelationshipOperation
  [DelegatedAdminRelationshipRequestId <String>]: The unique identifier of delegatedAdminRelationshipRequest
  [DelegatedAdminServiceManagementDetailId <String>]: The unique identifier of delegatedAdminServiceManagementDetail
.Link
https://learn.microsoft.com/powershell/module/microsoft.graph.identity.partner/remove-mgtenantrelationshipdelegatedadminrelationshipaccessassignment
.Link
https://learn.microsoft.com/graph/api/delegatedadminaccessassignment-delete?view=graph-rest-1.0
#>
function Remove-MgTenantRelationshipDelegatedAdminRelationshipAccessAssignment {
[OutputType([System.Boolean])]
[CmdletBinding(DefaultParameterSetName='Delete', PositionalBinding=$false, SupportsShouldProcess, ConfirmImpact='Medium')]
param(
    [Parameter(ParameterSetName='Delete', Mandatory)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [System.String]
    # The unique identifier of delegatedAdminAccessAssignment
    ${DelegatedAdminAccessAssignmentId},

    [Parameter(ParameterSetName='Delete', Mandatory)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [System.String]
    # The unique identifier of delegatedAdminRelationship
    ${DelegatedAdminRelationshipId},

    [Parameter(ParameterSetName='DeleteViaIdentity', Mandatory, ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity]
    # Identity Parameter
    # To construct, see NOTES section for INPUTOBJECT properties and create a hash table.
    ${InputObject},

    [Parameter()]
    [Microsoft.Graph.PowerShell.Category('Header')]
    [System.String]
    # ETag
    ${IfMatch},

    [Parameter()]
    [Alias('RHV')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # Optional Response Headers Variable.
    ${ResponseHeadersVariable},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Wait for .NET debugger to attach
    ${Break},

    [Parameter(ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Collections.IDictionary]
    # Optional headers that will be added to the request.
    ${Headers},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be appended to the front of the pipeline
    ${HttpPipelineAppend},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be prepended to the front of the pipeline
    ${HttpPipelinePrepend},

    [Parameter()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Returns true when the command succeeds
    ${PassThru},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Uri]
    # The URI for the proxy server to use
    ${Proxy},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.PSCredential]
    # Credentials for a proxy server to use for the remote call
    ${ProxyCredential},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Use the default credentials for the proxy
    ${ProxyUseDefaultCredentials}
)

begin {
    try {
        $outBuffer = $null
        if ($PSBoundParameters.TryGetValue('OutBuffer', [ref]$outBuffer)) {
            $PSBoundParameters['OutBuffer'] = 1
        }
        $parameterSet = $PSCmdlet.ParameterSetName

        $mapping = @{
            Delete = 'Microsoft.Graph.Identity.Partner.private\Remove-MgTenantRelationshipDelegatedAdminRelationshipAccessAssignment_Delete';
            DeleteViaIdentity = 'Microsoft.Graph.Identity.Partner.private\Remove-MgTenantRelationshipDelegatedAdminRelationshipAccessAssignment_DeleteViaIdentity';
        }
        $cmdInfo = Get-Command -Name $mapping[$parameterSet]
        [Microsoft.Graph.PowerShell.Runtime.MessageAttributeHelper]::ProcessCustomAttributesAtRuntime($cmdInfo, $MyInvocation, $parameterSet, $PSCmdlet)
        $wrappedCmd = $ExecutionContext.InvokeCommand.GetCommand(($mapping[$parameterSet]), [System.Management.Automation.CommandTypes]::Cmdlet)
        $scriptCmd = {& $wrappedCmd @PSBoundParameters}
        $steppablePipeline = $scriptCmd.GetSteppablePipeline($MyInvocation.CommandOrigin)
        $steppablePipeline.Begin($PSCmdlet)
    } catch {

        throw
    }
}

process {
    try {
        $steppablePipeline.Process($_)
    } catch {

        throw
    }

}
end {
    try {
        $steppablePipeline.End()

    } catch {

        throw
    }
} 
}

<#
.Synopsis
Delete navigation property operations for tenantRelationships
.Description
Delete navigation property operations for tenantRelationships
.Example
{{ Add code here }}
.Example
{{ Add code here }}

.Inputs
Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity
.Inputs
System.Collections.IDictionary
.Outputs
System.Boolean
.Notes
COMPLEX PARAMETER PROPERTIES

To create the parameters described below, construct a hash table containing the appropriate properties. For information on hash tables, run Get-Help about_Hash_Tables.

INPUTOBJECT <IIdentityPartnerIdentity>: Identity Parameter
  [DelegatedAdminAccessAssignmentId <String>]: The unique identifier of delegatedAdminAccessAssignment
  [DelegatedAdminCustomerId <String>]: The unique identifier of delegatedAdminCustomer
  [DelegatedAdminRelationshipId <String>]: The unique identifier of delegatedAdminRelationship
  [DelegatedAdminRelationshipOperationId <String>]: The unique identifier of delegatedAdminRelationshipOperation
  [DelegatedAdminRelationshipRequestId <String>]: The unique identifier of delegatedAdminRelationshipRequest
  [DelegatedAdminServiceManagementDetailId <String>]: The unique identifier of delegatedAdminServiceManagementDetail
.Link
https://learn.microsoft.com/powershell/module/microsoft.graph.identity.partner/remove-mgtenantrelationshipdelegatedadminrelationshipoperation
#>
function Remove-MgTenantRelationshipDelegatedAdminRelationshipOperation {
[OutputType([System.Boolean])]
[CmdletBinding(DefaultParameterSetName='Delete', PositionalBinding=$false, SupportsShouldProcess, ConfirmImpact='Medium')]
param(
    [Parameter(ParameterSetName='Delete', Mandatory)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [System.String]
    # The unique identifier of delegatedAdminRelationship
    ${DelegatedAdminRelationshipId},

    [Parameter(ParameterSetName='Delete', Mandatory)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [System.String]
    # The unique identifier of delegatedAdminRelationshipOperation
    ${DelegatedAdminRelationshipOperationId},

    [Parameter(ParameterSetName='DeleteViaIdentity', Mandatory, ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity]
    # Identity Parameter
    # To construct, see NOTES section for INPUTOBJECT properties and create a hash table.
    ${InputObject},

    [Parameter()]
    [Microsoft.Graph.PowerShell.Category('Header')]
    [System.String]
    # ETag
    ${IfMatch},

    [Parameter()]
    [Alias('RHV')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # Optional Response Headers Variable.
    ${ResponseHeadersVariable},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Wait for .NET debugger to attach
    ${Break},

    [Parameter(ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Collections.IDictionary]
    # Optional headers that will be added to the request.
    ${Headers},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be appended to the front of the pipeline
    ${HttpPipelineAppend},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be prepended to the front of the pipeline
    ${HttpPipelinePrepend},

    [Parameter()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Returns true when the command succeeds
    ${PassThru},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Uri]
    # The URI for the proxy server to use
    ${Proxy},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.PSCredential]
    # Credentials for a proxy server to use for the remote call
    ${ProxyCredential},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Use the default credentials for the proxy
    ${ProxyUseDefaultCredentials}
)

begin {
    try {
        $outBuffer = $null
        if ($PSBoundParameters.TryGetValue('OutBuffer', [ref]$outBuffer)) {
            $PSBoundParameters['OutBuffer'] = 1
        }
        $parameterSet = $PSCmdlet.ParameterSetName

        $mapping = @{
            Delete = 'Microsoft.Graph.Identity.Partner.private\Remove-MgTenantRelationshipDelegatedAdminRelationshipOperation_Delete';
            DeleteViaIdentity = 'Microsoft.Graph.Identity.Partner.private\Remove-MgTenantRelationshipDelegatedAdminRelationshipOperation_DeleteViaIdentity';
        }
        $cmdInfo = Get-Command -Name $mapping[$parameterSet]
        [Microsoft.Graph.PowerShell.Runtime.MessageAttributeHelper]::ProcessCustomAttributesAtRuntime($cmdInfo, $MyInvocation, $parameterSet, $PSCmdlet)
        $wrappedCmd = $ExecutionContext.InvokeCommand.GetCommand(($mapping[$parameterSet]), [System.Management.Automation.CommandTypes]::Cmdlet)
        $scriptCmd = {& $wrappedCmd @PSBoundParameters}
        $steppablePipeline = $scriptCmd.GetSteppablePipeline($MyInvocation.CommandOrigin)
        $steppablePipeline.Begin($PSCmdlet)
    } catch {

        throw
    }
}

process {
    try {
        $steppablePipeline.Process($_)
    } catch {

        throw
    }

}
end {
    try {
        $steppablePipeline.End()

    } catch {

        throw
    }
} 
}

<#
.Synopsis
Delete navigation property requests for tenantRelationships
.Description
Delete navigation property requests for tenantRelationships
.Example
{{ Add code here }}
.Example
{{ Add code here }}

.Inputs
Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity
.Inputs
System.Collections.IDictionary
.Outputs
System.Boolean
.Notes
COMPLEX PARAMETER PROPERTIES

To create the parameters described below, construct a hash table containing the appropriate properties. For information on hash tables, run Get-Help about_Hash_Tables.

INPUTOBJECT <IIdentityPartnerIdentity>: Identity Parameter
  [DelegatedAdminAccessAssignmentId <String>]: The unique identifier of delegatedAdminAccessAssignment
  [DelegatedAdminCustomerId <String>]: The unique identifier of delegatedAdminCustomer
  [DelegatedAdminRelationshipId <String>]: The unique identifier of delegatedAdminRelationship
  [DelegatedAdminRelationshipOperationId <String>]: The unique identifier of delegatedAdminRelationshipOperation
  [DelegatedAdminRelationshipRequestId <String>]: The unique identifier of delegatedAdminRelationshipRequest
  [DelegatedAdminServiceManagementDetailId <String>]: The unique identifier of delegatedAdminServiceManagementDetail
.Link
https://learn.microsoft.com/powershell/module/microsoft.graph.identity.partner/remove-mgtenantrelationshipdelegatedadminrelationshiprequest
#>
function Remove-MgTenantRelationshipDelegatedAdminRelationshipRequest {
[OutputType([System.Boolean])]
[CmdletBinding(DefaultParameterSetName='Delete', PositionalBinding=$false, SupportsShouldProcess, ConfirmImpact='Medium')]
param(
    [Parameter(ParameterSetName='Delete', Mandatory)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [System.String]
    # The unique identifier of delegatedAdminRelationship
    ${DelegatedAdminRelationshipId},

    [Parameter(ParameterSetName='Delete', Mandatory)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [System.String]
    # The unique identifier of delegatedAdminRelationshipRequest
    ${DelegatedAdminRelationshipRequestId},

    [Parameter(ParameterSetName='DeleteViaIdentity', Mandatory, ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity]
    # Identity Parameter
    # To construct, see NOTES section for INPUTOBJECT properties and create a hash table.
    ${InputObject},

    [Parameter()]
    [Microsoft.Graph.PowerShell.Category('Header')]
    [System.String]
    # ETag
    ${IfMatch},

    [Parameter()]
    [Alias('RHV')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # Optional Response Headers Variable.
    ${ResponseHeadersVariable},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Wait for .NET debugger to attach
    ${Break},

    [Parameter(ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Collections.IDictionary]
    # Optional headers that will be added to the request.
    ${Headers},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be appended to the front of the pipeline
    ${HttpPipelineAppend},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be prepended to the front of the pipeline
    ${HttpPipelinePrepend},

    [Parameter()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Returns true when the command succeeds
    ${PassThru},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Uri]
    # The URI for the proxy server to use
    ${Proxy},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.PSCredential]
    # Credentials for a proxy server to use for the remote call
    ${ProxyCredential},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Use the default credentials for the proxy
    ${ProxyUseDefaultCredentials}
)

begin {
    try {
        $outBuffer = $null
        if ($PSBoundParameters.TryGetValue('OutBuffer', [ref]$outBuffer)) {
            $PSBoundParameters['OutBuffer'] = 1
        }
        $parameterSet = $PSCmdlet.ParameterSetName

        $mapping = @{
            Delete = 'Microsoft.Graph.Identity.Partner.private\Remove-MgTenantRelationshipDelegatedAdminRelationshipRequest_Delete';
            DeleteViaIdentity = 'Microsoft.Graph.Identity.Partner.private\Remove-MgTenantRelationshipDelegatedAdminRelationshipRequest_DeleteViaIdentity';
        }
        $cmdInfo = Get-Command -Name $mapping[$parameterSet]
        [Microsoft.Graph.PowerShell.Runtime.MessageAttributeHelper]::ProcessCustomAttributesAtRuntime($cmdInfo, $MyInvocation, $parameterSet, $PSCmdlet)
        $wrappedCmd = $ExecutionContext.InvokeCommand.GetCommand(($mapping[$parameterSet]), [System.Management.Automation.CommandTypes]::Cmdlet)
        $scriptCmd = {& $wrappedCmd @PSBoundParameters}
        $steppablePipeline = $scriptCmd.GetSteppablePipeline($MyInvocation.CommandOrigin)
        $steppablePipeline.Begin($PSCmdlet)
    } catch {

        throw
    }
}

process {
    try {
        $steppablePipeline.Process($_)
    } catch {

        throw
    }

}
end {
    try {
        $steppablePipeline.End()

    } catch {

        throw
    }
} 
}

<#
.Synopsis
Delete a delegatedAdminRelationship object.
A relationship can only be deleted if it's in the 'created' status.
.Description
Delete a delegatedAdminRelationship object.
A relationship can only be deleted if it's in the 'created' status.
.Example

Import-Module Microsoft.Graph.Identity.Partner

Remove-MgTenantRelationshipDelegatedAdminRelationship -DelegatedAdminRelationshipId $delegatedAdminRelationshipId -IfMatch W/'"JyI0NzAwNjg0NS0wMDAwLTE5MDAtMDAwMC02MGY0Yjg4MzAwMDAiJw=="' 



.Inputs
Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity
.Inputs
System.Collections.IDictionary
.Outputs
System.Boolean
.Notes
COMPLEX PARAMETER PROPERTIES

To create the parameters described below, construct a hash table containing the appropriate properties. For information on hash tables, run Get-Help about_Hash_Tables.

INPUTOBJECT <IIdentityPartnerIdentity>: Identity Parameter
  [DelegatedAdminAccessAssignmentId <String>]: The unique identifier of delegatedAdminAccessAssignment
  [DelegatedAdminCustomerId <String>]: The unique identifier of delegatedAdminCustomer
  [DelegatedAdminRelationshipId <String>]: The unique identifier of delegatedAdminRelationship
  [DelegatedAdminRelationshipOperationId <String>]: The unique identifier of delegatedAdminRelationshipOperation
  [DelegatedAdminRelationshipRequestId <String>]: The unique identifier of delegatedAdminRelationshipRequest
  [DelegatedAdminServiceManagementDetailId <String>]: The unique identifier of delegatedAdminServiceManagementDetail
.Link
https://learn.microsoft.com/powershell/module/microsoft.graph.identity.partner/remove-mgtenantrelationshipdelegatedadminrelationship
.Link
https://learn.microsoft.com/graph/api/delegatedadminrelationship-delete?view=graph-rest-1.0
#>
function Remove-MgTenantRelationshipDelegatedAdminRelationship {
[OutputType([System.Boolean])]
[CmdletBinding(DefaultParameterSetName='Delete', PositionalBinding=$false, SupportsShouldProcess, ConfirmImpact='Medium')]
param(
    [Parameter(ParameterSetName='Delete', Mandatory)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [System.String]
    # The unique identifier of delegatedAdminRelationship
    ${DelegatedAdminRelationshipId},

    [Parameter(ParameterSetName='DeleteViaIdentity', Mandatory, ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity]
    # Identity Parameter
    # To construct, see NOTES section for INPUTOBJECT properties and create a hash table.
    ${InputObject},

    [Parameter()]
    [Microsoft.Graph.PowerShell.Category('Header')]
    [System.String]
    # ETag
    ${IfMatch},

    [Parameter()]
    [Alias('RHV')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # Optional Response Headers Variable.
    ${ResponseHeadersVariable},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Wait for .NET debugger to attach
    ${Break},

    [Parameter(ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Collections.IDictionary]
    # Optional headers that will be added to the request.
    ${Headers},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be appended to the front of the pipeline
    ${HttpPipelineAppend},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be prepended to the front of the pipeline
    ${HttpPipelinePrepend},

    [Parameter()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Returns true when the command succeeds
    ${PassThru},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Uri]
    # The URI for the proxy server to use
    ${Proxy},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.PSCredential]
    # Credentials for a proxy server to use for the remote call
    ${ProxyCredential},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Use the default credentials for the proxy
    ${ProxyUseDefaultCredentials}
)

begin {
    try {
        $outBuffer = $null
        if ($PSBoundParameters.TryGetValue('OutBuffer', [ref]$outBuffer)) {
            $PSBoundParameters['OutBuffer'] = 1
        }
        $parameterSet = $PSCmdlet.ParameterSetName

        $mapping = @{
            Delete = 'Microsoft.Graph.Identity.Partner.private\Remove-MgTenantRelationshipDelegatedAdminRelationship_Delete';
            DeleteViaIdentity = 'Microsoft.Graph.Identity.Partner.private\Remove-MgTenantRelationshipDelegatedAdminRelationship_DeleteViaIdentity';
        }
        $cmdInfo = Get-Command -Name $mapping[$parameterSet]
        [Microsoft.Graph.PowerShell.Runtime.MessageAttributeHelper]::ProcessCustomAttributesAtRuntime($cmdInfo, $MyInvocation, $parameterSet, $PSCmdlet)
        $wrappedCmd = $ExecutionContext.InvokeCommand.GetCommand(($mapping[$parameterSet]), [System.Management.Automation.CommandTypes]::Cmdlet)
        $scriptCmd = {& $wrappedCmd @PSBoundParameters}
        $steppablePipeline = $scriptCmd.GetSteppablePipeline($MyInvocation.CommandOrigin)
        $steppablePipeline.Begin($PSCmdlet)
    } catch {

        throw
    }
}

process {
    try {
        $steppablePipeline.Process($_)
    } catch {

        throw
    }

}
end {
    try {
        $steppablePipeline.End()

    } catch {

        throw
    }
} 
}

<#
.Synopsis
Update the navigation property serviceManagementDetails in tenantRelationships
.Description
Update the navigation property serviceManagementDetails in tenantRelationships
.Example
{{ Add code here }}
.Example
{{ Add code here }}

.Inputs
Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity
.Inputs
Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminServiceManagementDetail
.Inputs
System.Collections.IDictionary
.Outputs
Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminServiceManagementDetail
.Notes
COMPLEX PARAMETER PROPERTIES

To create the parameters described below, construct a hash table containing the appropriate properties. For information on hash tables, run Get-Help about_Hash_Tables.

BODYPARAMETER <IMicrosoftGraphDelegatedAdminServiceManagementDetail>: delegatedAdminServiceManagementDetail
  [(Any) <Object>]: This indicates any property can be added to this object.
  [Id <String>]: The unique identifier for an entity. Read-only.
  [ServiceManagementUrl <String>]: The URL of the management portal for the managed service. Read-only.
  [ServiceName <String>]: The name of a managed service. Read-only.

INPUTOBJECT <IIdentityPartnerIdentity>: Identity Parameter
  [DelegatedAdminAccessAssignmentId <String>]: The unique identifier of delegatedAdminAccessAssignment
  [DelegatedAdminCustomerId <String>]: The unique identifier of delegatedAdminCustomer
  [DelegatedAdminRelationshipId <String>]: The unique identifier of delegatedAdminRelationship
  [DelegatedAdminRelationshipOperationId <String>]: The unique identifier of delegatedAdminRelationshipOperation
  [DelegatedAdminRelationshipRequestId <String>]: The unique identifier of delegatedAdminRelationshipRequest
  [DelegatedAdminServiceManagementDetailId <String>]: The unique identifier of delegatedAdminServiceManagementDetail
.Link
https://learn.microsoft.com/powershell/module/microsoft.graph.identity.partner/update-mgtenantrelationshipdelegatedadmincustomerservicemanagementdetail
#>
function Update-MgTenantRelationshipDelegatedAdminCustomerServiceManagementDetail {
[OutputType([Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminServiceManagementDetail])]
[CmdletBinding(DefaultParameterSetName='UpdateExpanded', PositionalBinding=$false, SupportsShouldProcess, ConfirmImpact='Medium')]
param(
    [Parameter(ParameterSetName='Update', Mandatory)]
    [Parameter(ParameterSetName='UpdateExpanded', Mandatory)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [System.String]
    # The unique identifier of delegatedAdminCustomer
    ${DelegatedAdminCustomerId},

    [Parameter(ParameterSetName='Update', Mandatory)]
    [Parameter(ParameterSetName='UpdateExpanded', Mandatory)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [System.String]
    # The unique identifier of delegatedAdminServiceManagementDetail
    ${DelegatedAdminServiceManagementDetailId},

    [Parameter(ParameterSetName='UpdateViaIdentity', Mandatory, ValueFromPipeline)]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded', Mandatory, ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity]
    # Identity Parameter
    # To construct, see NOTES section for INPUTOBJECT properties and create a hash table.
    ${InputObject},

    [Parameter(ParameterSetName='Update', Mandatory, ValueFromPipeline)]
    [Parameter(ParameterSetName='UpdateViaIdentity', Mandatory, ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminServiceManagementDetail]
    # delegatedAdminServiceManagementDetail
    # To construct, see NOTES section for BODYPARAMETER properties and create a hash table.
    ${BodyParameter},

    [Parameter()]
    [Alias('RHV')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # Optional Response Headers Variable.
    ${ResponseHeadersVariable},

    [Parameter(ParameterSetName='UpdateExpanded')]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.Collections.Hashtable]
    # Additional Parameters
    ${AdditionalProperties},

    [Parameter(ParameterSetName='UpdateExpanded')]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # The unique identifier for an entity.
    # Read-only.
    ${Id},

    [Parameter(ParameterSetName='UpdateExpanded')]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # The URL of the management portal for the managed service.
    # Read-only.
    ${ServiceManagementUrl},

    [Parameter(ParameterSetName='UpdateExpanded')]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # The name of a managed service.
    # Read-only.
    ${ServiceName},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Wait for .NET debugger to attach
    ${Break},

    [Parameter(ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Collections.IDictionary]
    # Optional headers that will be added to the request.
    ${Headers},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be appended to the front of the pipeline
    ${HttpPipelineAppend},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be prepended to the front of the pipeline
    ${HttpPipelinePrepend},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Uri]
    # The URI for the proxy server to use
    ${Proxy},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.PSCredential]
    # Credentials for a proxy server to use for the remote call
    ${ProxyCredential},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Use the default credentials for the proxy
    ${ProxyUseDefaultCredentials}
)

begin {
    try {
        $outBuffer = $null
        if ($PSBoundParameters.TryGetValue('OutBuffer', [ref]$outBuffer)) {
            $PSBoundParameters['OutBuffer'] = 1
        }
        $parameterSet = $PSCmdlet.ParameterSetName

        $mapping = @{
            Update = 'Microsoft.Graph.Identity.Partner.private\Update-MgTenantRelationshipDelegatedAdminCustomerServiceManagementDetail_Update';
            UpdateExpanded = 'Microsoft.Graph.Identity.Partner.private\Update-MgTenantRelationshipDelegatedAdminCustomerServiceManagementDetail_UpdateExpanded';
            UpdateViaIdentity = 'Microsoft.Graph.Identity.Partner.private\Update-MgTenantRelationshipDelegatedAdminCustomerServiceManagementDetail_UpdateViaIdentity';
            UpdateViaIdentityExpanded = 'Microsoft.Graph.Identity.Partner.private\Update-MgTenantRelationshipDelegatedAdminCustomerServiceManagementDetail_UpdateViaIdentityExpanded';
        }
        $cmdInfo = Get-Command -Name $mapping[$parameterSet]
        [Microsoft.Graph.PowerShell.Runtime.MessageAttributeHelper]::ProcessCustomAttributesAtRuntime($cmdInfo, $MyInvocation, $parameterSet, $PSCmdlet)
        $wrappedCmd = $ExecutionContext.InvokeCommand.GetCommand(($mapping[$parameterSet]), [System.Management.Automation.CommandTypes]::Cmdlet)
        $scriptCmd = {& $wrappedCmd @PSBoundParameters}
        $steppablePipeline = $scriptCmd.GetSteppablePipeline($MyInvocation.CommandOrigin)
        $steppablePipeline.Begin($PSCmdlet)
    } catch {

        throw
    }
}

process {
    try {
        $steppablePipeline.Process($_)
    } catch {

        throw
    }

}
end {
    try {
        $steppablePipeline.End()

    } catch {

        throw
    }
} 
}

<#
.Synopsis
Update the navigation property delegatedAdminCustomers in tenantRelationships
.Description
Update the navigation property delegatedAdminCustomers in tenantRelationships
.Example
{{ Add code here }}
.Example
{{ Add code here }}

.Inputs
Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity
.Inputs
Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminCustomer
.Inputs
System.Collections.IDictionary
.Outputs
Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminCustomer
.Notes
COMPLEX PARAMETER PROPERTIES

To create the parameters described below, construct a hash table containing the appropriate properties. For information on hash tables, run Get-Help about_Hash_Tables.

BODYPARAMETER <IMicrosoftGraphDelegatedAdminCustomer>: delegatedAdminCustomer
  [(Any) <Object>]: This indicates any property can be added to this object.
  [Id <String>]: The unique identifier for an entity. Read-only.
  [DisplayName <String>]: The Microsoft Entra ID display name of the customer tenant. Read-only. Supports $orderby.
  [ServiceManagementDetails <IMicrosoftGraphDelegatedAdminServiceManagementDetail[]>]: Contains the management details of a service in the customer tenant that's managed by delegated administration.
    [Id <String>]: The unique identifier for an entity. Read-only.
    [ServiceManagementUrl <String>]: The URL of the management portal for the managed service. Read-only.
    [ServiceName <String>]: The name of a managed service. Read-only.
  [TenantId <String>]: The Microsoft Entra ID-assigned tenant ID of the customer. Read-only.

INPUTOBJECT <IIdentityPartnerIdentity>: Identity Parameter
  [DelegatedAdminAccessAssignmentId <String>]: The unique identifier of delegatedAdminAccessAssignment
  [DelegatedAdminCustomerId <String>]: The unique identifier of delegatedAdminCustomer
  [DelegatedAdminRelationshipId <String>]: The unique identifier of delegatedAdminRelationship
  [DelegatedAdminRelationshipOperationId <String>]: The unique identifier of delegatedAdminRelationshipOperation
  [DelegatedAdminRelationshipRequestId <String>]: The unique identifier of delegatedAdminRelationshipRequest
  [DelegatedAdminServiceManagementDetailId <String>]: The unique identifier of delegatedAdminServiceManagementDetail

SERVICEMANAGEMENTDETAILS <IMicrosoftGraphDelegatedAdminServiceManagementDetail[]>: Contains the management details of a service in the customer tenant that's managed by delegated administration.
  [Id <String>]: The unique identifier for an entity. Read-only.
  [ServiceManagementUrl <String>]: The URL of the management portal for the managed service. Read-only.
  [ServiceName <String>]: The name of a managed service. Read-only.
.Link
https://learn.microsoft.com/powershell/module/microsoft.graph.identity.partner/update-mgtenantrelationshipdelegatedadmincustomer
#>
function Update-MgTenantRelationshipDelegatedAdminCustomer {
[OutputType([Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminCustomer])]
[CmdletBinding(DefaultParameterSetName='UpdateExpanded', PositionalBinding=$false, SupportsShouldProcess, ConfirmImpact='Medium')]
param(
    [Parameter(ParameterSetName='Update', Mandatory)]
    [Parameter(ParameterSetName='UpdateExpanded', Mandatory)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [System.String]
    # The unique identifier of delegatedAdminCustomer
    ${DelegatedAdminCustomerId},

    [Parameter(ParameterSetName='UpdateViaIdentity', Mandatory, ValueFromPipeline)]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded', Mandatory, ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity]
    # Identity Parameter
    # To construct, see NOTES section for INPUTOBJECT properties and create a hash table.
    ${InputObject},

    [Parameter(ParameterSetName='Update', Mandatory, ValueFromPipeline)]
    [Parameter(ParameterSetName='UpdateViaIdentity', Mandatory, ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminCustomer]
    # delegatedAdminCustomer
    # To construct, see NOTES section for BODYPARAMETER properties and create a hash table.
    ${BodyParameter},

    [Parameter()]
    [Alias('RHV')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # Optional Response Headers Variable.
    ${ResponseHeadersVariable},

    [Parameter(ParameterSetName='UpdateExpanded')]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.Collections.Hashtable]
    # Additional Parameters
    ${AdditionalProperties},

    [Parameter(ParameterSetName='UpdateExpanded')]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # The Microsoft Entra ID display name of the customer tenant.
    # Read-only.
    # Supports $orderby.
    ${DisplayName},

    [Parameter(ParameterSetName='UpdateExpanded')]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # The unique identifier for an entity.
    # Read-only.
    ${Id},

    [Parameter(ParameterSetName='UpdateExpanded')]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded')]
    [AllowEmptyCollection()]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminServiceManagementDetail[]]
    # Contains the management details of a service in the customer tenant that's managed by delegated administration.
    # To construct, see NOTES section for SERVICEMANAGEMENTDETAILS properties and create a hash table.
    ${ServiceManagementDetails},

    [Parameter(ParameterSetName='UpdateExpanded')]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # The Microsoft Entra ID-assigned tenant ID of the customer.
    # Read-only.
    ${TenantId},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Wait for .NET debugger to attach
    ${Break},

    [Parameter(ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Collections.IDictionary]
    # Optional headers that will be added to the request.
    ${Headers},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be appended to the front of the pipeline
    ${HttpPipelineAppend},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be prepended to the front of the pipeline
    ${HttpPipelinePrepend},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Uri]
    # The URI for the proxy server to use
    ${Proxy},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.PSCredential]
    # Credentials for a proxy server to use for the remote call
    ${ProxyCredential},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Use the default credentials for the proxy
    ${ProxyUseDefaultCredentials}
)

begin {
    try {
        $outBuffer = $null
        if ($PSBoundParameters.TryGetValue('OutBuffer', [ref]$outBuffer)) {
            $PSBoundParameters['OutBuffer'] = 1
        }
        $parameterSet = $PSCmdlet.ParameterSetName

        $mapping = @{
            Update = 'Microsoft.Graph.Identity.Partner.private\Update-MgTenantRelationshipDelegatedAdminCustomer_Update';
            UpdateExpanded = 'Microsoft.Graph.Identity.Partner.private\Update-MgTenantRelationshipDelegatedAdminCustomer_UpdateExpanded';
            UpdateViaIdentity = 'Microsoft.Graph.Identity.Partner.private\Update-MgTenantRelationshipDelegatedAdminCustomer_UpdateViaIdentity';
            UpdateViaIdentityExpanded = 'Microsoft.Graph.Identity.Partner.private\Update-MgTenantRelationshipDelegatedAdminCustomer_UpdateViaIdentityExpanded';
        }
        $cmdInfo = Get-Command -Name $mapping[$parameterSet]
        [Microsoft.Graph.PowerShell.Runtime.MessageAttributeHelper]::ProcessCustomAttributesAtRuntime($cmdInfo, $MyInvocation, $parameterSet, $PSCmdlet)
        $wrappedCmd = $ExecutionContext.InvokeCommand.GetCommand(($mapping[$parameterSet]), [System.Management.Automation.CommandTypes]::Cmdlet)
        $scriptCmd = {& $wrappedCmd @PSBoundParameters}
        $steppablePipeline = $scriptCmd.GetSteppablePipeline($MyInvocation.CommandOrigin)
        $steppablePipeline.Begin($PSCmdlet)
    } catch {

        throw
    }
}

process {
    try {
        $steppablePipeline.Process($_)
    } catch {

        throw
    }

}
end {
    try {
        $steppablePipeline.End()

    } catch {

        throw
    }
} 
}

<#
.Synopsis
Update the properties of a delegatedAdminAccessAssignment object.
.Description
Update the properties of a delegatedAdminAccessAssignment object.
.Example

Import-Module Microsoft.Graph.Identity.Partner

$params = @{
	accessDetails = @{
		unifiedRoles = @(
			@{
				roleDefinitionId = "88d8e3e3-8f55-4a1e-953a-9b9898b8876b"
			}
			@{
				roleDefinitionId = "44367163-eba1-44c3-98af-f5787879f96a"
			}
			@{
				roleDefinitionId = "729827e3-9c14-49f7-bb1b-9608f156bbb8"
			}
		)
	}
}

Update-MgTenantRelationshipDelegatedAdminRelationshipAccessAssignment -DelegatedAdminRelationshipId $delegatedAdminRelationshipId -DelegatedAdminAccessAssignmentId $delegatedAdminAccessAssignmentId -BodyParameter $params


.Inputs
Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity
.Inputs
Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminAccessAssignment
.Inputs
System.Collections.IDictionary
.Outputs
Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminAccessAssignment
.Notes
COMPLEX PARAMETER PROPERTIES

To create the parameters described below, construct a hash table containing the appropriate properties. For information on hash tables, run Get-Help about_Hash_Tables.

ACCESSCONTAINER <IMicrosoftGraphDelegatedAdminAccessContainer>: delegatedAdminAccessContainer
  [(Any) <Object>]: This indicates any property can be added to this object.
  [AccessContainerId <String>]: The identifier of the access container (for example, a security group). For 'securityGroup' access containers, this must be a valid ID of a Microsoft Entra security group in the Microsoft partner's tenant.
  [AccessContainerType <String>]: delegatedAdminAccessContainerType

ACCESSDETAILS <IMicrosoftGraphDelegatedAdminAccessDetails>: delegatedAdminAccessDetails
  [(Any) <Object>]: This indicates any property can be added to this object.
  [UnifiedRoles <IMicrosoftGraphUnifiedRole[]>]: The directory roles that the Microsoft partner is assigned in the customer tenant.
    [RoleDefinitionId <String>]: The unified role definition ID of the directory role. Refer to unifiedRoleDefinition resource.

BODYPARAMETER <IMicrosoftGraphDelegatedAdminAccessAssignment>: delegatedAdminAccessAssignment
  [(Any) <Object>]: This indicates any property can be added to this object.
  [Id <String>]: The unique identifier for an entity. Read-only.
  [AccessContainer <IMicrosoftGraphDelegatedAdminAccessContainer>]: delegatedAdminAccessContainer
    [(Any) <Object>]: This indicates any property can be added to this object.
    [AccessContainerId <String>]: The identifier of the access container (for example, a security group). For 'securityGroup' access containers, this must be a valid ID of a Microsoft Entra security group in the Microsoft partner's tenant.
    [AccessContainerType <String>]: delegatedAdminAccessContainerType
  [AccessDetails <IMicrosoftGraphDelegatedAdminAccessDetails>]: delegatedAdminAccessDetails
    [(Any) <Object>]: This indicates any property can be added to this object.
    [UnifiedRoles <IMicrosoftGraphUnifiedRole[]>]: The directory roles that the Microsoft partner is assigned in the customer tenant.
      [RoleDefinitionId <String>]: The unified role definition ID of the directory role. Refer to unifiedRoleDefinition resource.
  [CreatedDateTime <DateTime?>]: The date and time in ISO 8601 format and in UTC time when the access assignment was created. Read-only.
  [LastModifiedDateTime <DateTime?>]: The date and time in ISO 8601 and in UTC time when this access assignment was last modified. Read-only.
  [Status <String>]: delegatedAdminAccessAssignmentStatus

INPUTOBJECT <IIdentityPartnerIdentity>: Identity Parameter
  [DelegatedAdminAccessAssignmentId <String>]: The unique identifier of delegatedAdminAccessAssignment
  [DelegatedAdminCustomerId <String>]: The unique identifier of delegatedAdminCustomer
  [DelegatedAdminRelationshipId <String>]: The unique identifier of delegatedAdminRelationship
  [DelegatedAdminRelationshipOperationId <String>]: The unique identifier of delegatedAdminRelationshipOperation
  [DelegatedAdminRelationshipRequestId <String>]: The unique identifier of delegatedAdminRelationshipRequest
  [DelegatedAdminServiceManagementDetailId <String>]: The unique identifier of delegatedAdminServiceManagementDetail
.Link
https://learn.microsoft.com/powershell/module/microsoft.graph.identity.partner/update-mgtenantrelationshipdelegatedadminrelationshipaccessassignment
.Link
https://learn.microsoft.com/graph/api/delegatedadminaccessassignment-update?view=graph-rest-1.0
#>
function Update-MgTenantRelationshipDelegatedAdminRelationshipAccessAssignment {
[OutputType([Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminAccessAssignment])]
[CmdletBinding(DefaultParameterSetName='UpdateExpanded', PositionalBinding=$false, SupportsShouldProcess, ConfirmImpact='Medium')]
param(
    [Parameter(ParameterSetName='Update', Mandatory)]
    [Parameter(ParameterSetName='UpdateExpanded', Mandatory)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [System.String]
    # The unique identifier of delegatedAdminAccessAssignment
    ${DelegatedAdminAccessAssignmentId},

    [Parameter(ParameterSetName='Update', Mandatory)]
    [Parameter(ParameterSetName='UpdateExpanded', Mandatory)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [System.String]
    # The unique identifier of delegatedAdminRelationship
    ${DelegatedAdminRelationshipId},

    [Parameter(ParameterSetName='UpdateViaIdentity', Mandatory, ValueFromPipeline)]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded', Mandatory, ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity]
    # Identity Parameter
    # To construct, see NOTES section for INPUTOBJECT properties and create a hash table.
    ${InputObject},

    [Parameter(ParameterSetName='Update', Mandatory, ValueFromPipeline)]
    [Parameter(ParameterSetName='UpdateViaIdentity', Mandatory, ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminAccessAssignment]
    # delegatedAdminAccessAssignment
    # To construct, see NOTES section for BODYPARAMETER properties and create a hash table.
    ${BodyParameter},

    [Parameter()]
    [Alias('RHV')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # Optional Response Headers Variable.
    ${ResponseHeadersVariable},

    [Parameter(ParameterSetName='UpdateExpanded')]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminAccessContainer]
    # delegatedAdminAccessContainer
    # To construct, see NOTES section for ACCESSCONTAINER properties and create a hash table.
    ${AccessContainer},

    [Parameter(ParameterSetName='UpdateExpanded')]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminAccessDetails]
    # delegatedAdminAccessDetails
    # To construct, see NOTES section for ACCESSDETAILS properties and create a hash table.
    ${AccessDetails},

    [Parameter(ParameterSetName='UpdateExpanded')]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.Collections.Hashtable]
    # Additional Parameters
    ${AdditionalProperties},

    [Parameter(ParameterSetName='UpdateExpanded')]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.DateTime]
    # The date and time in ISO 8601 format and in UTC time when the access assignment was created.
    # Read-only.
    ${CreatedDateTime},

    [Parameter(ParameterSetName='UpdateExpanded')]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # The unique identifier for an entity.
    # Read-only.
    ${Id},

    [Parameter(ParameterSetName='UpdateExpanded')]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.DateTime]
    # The date and time in ISO 8601 and in UTC time when this access assignment was last modified.
    # Read-only.
    ${LastModifiedDateTime},

    [Parameter(ParameterSetName='UpdateExpanded')]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # delegatedAdminAccessAssignmentStatus
    ${Status},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Wait for .NET debugger to attach
    ${Break},

    [Parameter(ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Collections.IDictionary]
    # Optional headers that will be added to the request.
    ${Headers},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be appended to the front of the pipeline
    ${HttpPipelineAppend},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be prepended to the front of the pipeline
    ${HttpPipelinePrepend},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Uri]
    # The URI for the proxy server to use
    ${Proxy},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.PSCredential]
    # Credentials for a proxy server to use for the remote call
    ${ProxyCredential},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Use the default credentials for the proxy
    ${ProxyUseDefaultCredentials}
)

begin {
    try {
        $outBuffer = $null
        if ($PSBoundParameters.TryGetValue('OutBuffer', [ref]$outBuffer)) {
            $PSBoundParameters['OutBuffer'] = 1
        }
        $parameterSet = $PSCmdlet.ParameterSetName

        $mapping = @{
            Update = 'Microsoft.Graph.Identity.Partner.private\Update-MgTenantRelationshipDelegatedAdminRelationshipAccessAssignment_Update';
            UpdateExpanded = 'Microsoft.Graph.Identity.Partner.private\Update-MgTenantRelationshipDelegatedAdminRelationshipAccessAssignment_UpdateExpanded';
            UpdateViaIdentity = 'Microsoft.Graph.Identity.Partner.private\Update-MgTenantRelationshipDelegatedAdminRelationshipAccessAssignment_UpdateViaIdentity';
            UpdateViaIdentityExpanded = 'Microsoft.Graph.Identity.Partner.private\Update-MgTenantRelationshipDelegatedAdminRelationshipAccessAssignment_UpdateViaIdentityExpanded';
        }
        $cmdInfo = Get-Command -Name $mapping[$parameterSet]
        [Microsoft.Graph.PowerShell.Runtime.MessageAttributeHelper]::ProcessCustomAttributesAtRuntime($cmdInfo, $MyInvocation, $parameterSet, $PSCmdlet)
        $wrappedCmd = $ExecutionContext.InvokeCommand.GetCommand(($mapping[$parameterSet]), [System.Management.Automation.CommandTypes]::Cmdlet)
        $scriptCmd = {& $wrappedCmd @PSBoundParameters}
        $steppablePipeline = $scriptCmd.GetSteppablePipeline($MyInvocation.CommandOrigin)
        $steppablePipeline.Begin($PSCmdlet)
    } catch {

        throw
    }
}

process {
    try {
        $steppablePipeline.Process($_)
    } catch {

        throw
    }

}
end {
    try {
        $steppablePipeline.End()

    } catch {

        throw
    }
} 
}

<#
.Synopsis
Update the navigation property operations in tenantRelationships
.Description
Update the navigation property operations in tenantRelationships
.Example
{{ Add code here }}
.Example
{{ Add code here }}

.Inputs
Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity
.Inputs
Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminRelationshipOperation
.Inputs
System.Collections.IDictionary
.Outputs
Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminRelationshipOperation
.Notes
COMPLEX PARAMETER PROPERTIES

To create the parameters described below, construct a hash table containing the appropriate properties. For information on hash tables, run Get-Help about_Hash_Tables.

BODYPARAMETER <IMicrosoftGraphDelegatedAdminRelationshipOperation>: delegatedAdminRelationshipOperation
  [(Any) <Object>]: This indicates any property can be added to this object.
  [Id <String>]: The unique identifier for an entity. Read-only.
  [CreatedDateTime <DateTime?>]: The time in ISO 8601 format and in UTC time when the long-running operation was created. Read-only.
  [Data <String>]: The data (payload) for the operation. Read-only.
  [LastModifiedDateTime <DateTime?>]: The time in ISO 8601 format and in UTC time when the long-running operation was last modified. Read-only.
  [OperationType <String>]: delegatedAdminRelationshipOperationType
  [Status <String>]: longRunningOperationStatus

INPUTOBJECT <IIdentityPartnerIdentity>: Identity Parameter
  [DelegatedAdminAccessAssignmentId <String>]: The unique identifier of delegatedAdminAccessAssignment
  [DelegatedAdminCustomerId <String>]: The unique identifier of delegatedAdminCustomer
  [DelegatedAdminRelationshipId <String>]: The unique identifier of delegatedAdminRelationship
  [DelegatedAdminRelationshipOperationId <String>]: The unique identifier of delegatedAdminRelationshipOperation
  [DelegatedAdminRelationshipRequestId <String>]: The unique identifier of delegatedAdminRelationshipRequest
  [DelegatedAdminServiceManagementDetailId <String>]: The unique identifier of delegatedAdminServiceManagementDetail
.Link
https://learn.microsoft.com/powershell/module/microsoft.graph.identity.partner/update-mgtenantrelationshipdelegatedadminrelationshipoperation
#>
function Update-MgTenantRelationshipDelegatedAdminRelationshipOperation {
[OutputType([Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminRelationshipOperation])]
[CmdletBinding(DefaultParameterSetName='UpdateExpanded', PositionalBinding=$false, SupportsShouldProcess, ConfirmImpact='Medium')]
param(
    [Parameter(ParameterSetName='Update', Mandatory)]
    [Parameter(ParameterSetName='UpdateExpanded', Mandatory)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [System.String]
    # The unique identifier of delegatedAdminRelationship
    ${DelegatedAdminRelationshipId},

    [Parameter(ParameterSetName='Update', Mandatory)]
    [Parameter(ParameterSetName='UpdateExpanded', Mandatory)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [System.String]
    # The unique identifier of delegatedAdminRelationshipOperation
    ${DelegatedAdminRelationshipOperationId},

    [Parameter(ParameterSetName='UpdateViaIdentity', Mandatory, ValueFromPipeline)]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded', Mandatory, ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity]
    # Identity Parameter
    # To construct, see NOTES section for INPUTOBJECT properties and create a hash table.
    ${InputObject},

    [Parameter(ParameterSetName='Update', Mandatory, ValueFromPipeline)]
    [Parameter(ParameterSetName='UpdateViaIdentity', Mandatory, ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminRelationshipOperation]
    # delegatedAdminRelationshipOperation
    # To construct, see NOTES section for BODYPARAMETER properties and create a hash table.
    ${BodyParameter},

    [Parameter()]
    [Alias('RHV')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # Optional Response Headers Variable.
    ${ResponseHeadersVariable},

    [Parameter(ParameterSetName='UpdateExpanded')]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.Collections.Hashtable]
    # Additional Parameters
    ${AdditionalProperties},

    [Parameter(ParameterSetName='UpdateExpanded')]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.DateTime]
    # The time in ISO 8601 format and in UTC time when the long-running operation was created.
    # Read-only.
    ${CreatedDateTime},

    [Parameter(ParameterSetName='UpdateExpanded')]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # The data (payload) for the operation.
    # Read-only.
    ${Data},

    [Parameter(ParameterSetName='UpdateExpanded')]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # The unique identifier for an entity.
    # Read-only.
    ${Id},

    [Parameter(ParameterSetName='UpdateExpanded')]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.DateTime]
    # The time in ISO 8601 format and in UTC time when the long-running operation was last modified.
    # Read-only.
    ${LastModifiedDateTime},

    [Parameter(ParameterSetName='UpdateExpanded')]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # delegatedAdminRelationshipOperationType
    ${OperationType},

    [Parameter(ParameterSetName='UpdateExpanded')]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # longRunningOperationStatus
    ${Status},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Wait for .NET debugger to attach
    ${Break},

    [Parameter(ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Collections.IDictionary]
    # Optional headers that will be added to the request.
    ${Headers},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be appended to the front of the pipeline
    ${HttpPipelineAppend},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be prepended to the front of the pipeline
    ${HttpPipelinePrepend},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Uri]
    # The URI for the proxy server to use
    ${Proxy},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.PSCredential]
    # Credentials for a proxy server to use for the remote call
    ${ProxyCredential},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Use the default credentials for the proxy
    ${ProxyUseDefaultCredentials}
)

begin {
    try {
        $outBuffer = $null
        if ($PSBoundParameters.TryGetValue('OutBuffer', [ref]$outBuffer)) {
            $PSBoundParameters['OutBuffer'] = 1
        }
        $parameterSet = $PSCmdlet.ParameterSetName

        $mapping = @{
            Update = 'Microsoft.Graph.Identity.Partner.private\Update-MgTenantRelationshipDelegatedAdminRelationshipOperation_Update';
            UpdateExpanded = 'Microsoft.Graph.Identity.Partner.private\Update-MgTenantRelationshipDelegatedAdminRelationshipOperation_UpdateExpanded';
            UpdateViaIdentity = 'Microsoft.Graph.Identity.Partner.private\Update-MgTenantRelationshipDelegatedAdminRelationshipOperation_UpdateViaIdentity';
            UpdateViaIdentityExpanded = 'Microsoft.Graph.Identity.Partner.private\Update-MgTenantRelationshipDelegatedAdminRelationshipOperation_UpdateViaIdentityExpanded';
        }
        $cmdInfo = Get-Command -Name $mapping[$parameterSet]
        [Microsoft.Graph.PowerShell.Runtime.MessageAttributeHelper]::ProcessCustomAttributesAtRuntime($cmdInfo, $MyInvocation, $parameterSet, $PSCmdlet)
        $wrappedCmd = $ExecutionContext.InvokeCommand.GetCommand(($mapping[$parameterSet]), [System.Management.Automation.CommandTypes]::Cmdlet)
        $scriptCmd = {& $wrappedCmd @PSBoundParameters}
        $steppablePipeline = $scriptCmd.GetSteppablePipeline($MyInvocation.CommandOrigin)
        $steppablePipeline.Begin($PSCmdlet)
    } catch {

        throw
    }
}

process {
    try {
        $steppablePipeline.Process($_)
    } catch {

        throw
    }

}
end {
    try {
        $steppablePipeline.End()

    } catch {

        throw
    }
} 
}

<#
.Synopsis
Update the navigation property requests in tenantRelationships
.Description
Update the navigation property requests in tenantRelationships
.Example
{{ Add code here }}
.Example
{{ Add code here }}

.Inputs
Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity
.Inputs
Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminRelationshipRequest
.Inputs
System.Collections.IDictionary
.Outputs
Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminRelationshipRequest
.Notes
COMPLEX PARAMETER PROPERTIES

To create the parameters described below, construct a hash table containing the appropriate properties. For information on hash tables, run Get-Help about_Hash_Tables.

BODYPARAMETER <IMicrosoftGraphDelegatedAdminRelationshipRequest>: delegatedAdminRelationshipRequest
  [(Any) <Object>]: This indicates any property can be added to this object.
  [Id <String>]: The unique identifier for an entity. Read-only.
  [Action <String>]: delegatedAdminRelationshipRequestAction
  [CreatedDateTime <DateTime?>]: The date and time in ISO 8601 format and in UTC time when the relationship request was created. Read-only.
  [LastModifiedDateTime <DateTime?>]: The date and time in ISO 8601 format and UTC time when this relationship request was last modified. Read-only.
  [Status <String>]: delegatedAdminRelationshipRequestStatus

INPUTOBJECT <IIdentityPartnerIdentity>: Identity Parameter
  [DelegatedAdminAccessAssignmentId <String>]: The unique identifier of delegatedAdminAccessAssignment
  [DelegatedAdminCustomerId <String>]: The unique identifier of delegatedAdminCustomer
  [DelegatedAdminRelationshipId <String>]: The unique identifier of delegatedAdminRelationship
  [DelegatedAdminRelationshipOperationId <String>]: The unique identifier of delegatedAdminRelationshipOperation
  [DelegatedAdminRelationshipRequestId <String>]: The unique identifier of delegatedAdminRelationshipRequest
  [DelegatedAdminServiceManagementDetailId <String>]: The unique identifier of delegatedAdminServiceManagementDetail
.Link
https://learn.microsoft.com/powershell/module/microsoft.graph.identity.partner/update-mgtenantrelationshipdelegatedadminrelationshiprequest
#>
function Update-MgTenantRelationshipDelegatedAdminRelationshipRequest {
[OutputType([Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminRelationshipRequest])]
[CmdletBinding(DefaultParameterSetName='UpdateExpanded', PositionalBinding=$false, SupportsShouldProcess, ConfirmImpact='Medium')]
param(
    [Parameter(ParameterSetName='Update', Mandatory)]
    [Parameter(ParameterSetName='UpdateExpanded', Mandatory)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [System.String]
    # The unique identifier of delegatedAdminRelationship
    ${DelegatedAdminRelationshipId},

    [Parameter(ParameterSetName='Update', Mandatory)]
    [Parameter(ParameterSetName='UpdateExpanded', Mandatory)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [System.String]
    # The unique identifier of delegatedAdminRelationshipRequest
    ${DelegatedAdminRelationshipRequestId},

    [Parameter(ParameterSetName='UpdateViaIdentity', Mandatory, ValueFromPipeline)]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded', Mandatory, ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity]
    # Identity Parameter
    # To construct, see NOTES section for INPUTOBJECT properties and create a hash table.
    ${InputObject},

    [Parameter(ParameterSetName='Update', Mandatory, ValueFromPipeline)]
    [Parameter(ParameterSetName='UpdateViaIdentity', Mandatory, ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminRelationshipRequest]
    # delegatedAdminRelationshipRequest
    # To construct, see NOTES section for BODYPARAMETER properties and create a hash table.
    ${BodyParameter},

    [Parameter()]
    [Alias('RHV')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # Optional Response Headers Variable.
    ${ResponseHeadersVariable},

    [Parameter(ParameterSetName='UpdateExpanded')]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # delegatedAdminRelationshipRequestAction
    ${Action},

    [Parameter(ParameterSetName='UpdateExpanded')]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.Collections.Hashtable]
    # Additional Parameters
    ${AdditionalProperties},

    [Parameter(ParameterSetName='UpdateExpanded')]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.DateTime]
    # The date and time in ISO 8601 format and in UTC time when the relationship request was created.
    # Read-only.
    ${CreatedDateTime},

    [Parameter(ParameterSetName='UpdateExpanded')]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # The unique identifier for an entity.
    # Read-only.
    ${Id},

    [Parameter(ParameterSetName='UpdateExpanded')]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.DateTime]
    # The date and time in ISO 8601 format and UTC time when this relationship request was last modified.
    # Read-only.
    ${LastModifiedDateTime},

    [Parameter(ParameterSetName='UpdateExpanded')]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # delegatedAdminRelationshipRequestStatus
    ${Status},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Wait for .NET debugger to attach
    ${Break},

    [Parameter(ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Collections.IDictionary]
    # Optional headers that will be added to the request.
    ${Headers},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be appended to the front of the pipeline
    ${HttpPipelineAppend},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be prepended to the front of the pipeline
    ${HttpPipelinePrepend},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Uri]
    # The URI for the proxy server to use
    ${Proxy},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.PSCredential]
    # Credentials for a proxy server to use for the remote call
    ${ProxyCredential},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Use the default credentials for the proxy
    ${ProxyUseDefaultCredentials}
)

begin {
    try {
        $outBuffer = $null
        if ($PSBoundParameters.TryGetValue('OutBuffer', [ref]$outBuffer)) {
            $PSBoundParameters['OutBuffer'] = 1
        }
        $parameterSet = $PSCmdlet.ParameterSetName

        $mapping = @{
            Update = 'Microsoft.Graph.Identity.Partner.private\Update-MgTenantRelationshipDelegatedAdminRelationshipRequest_Update';
            UpdateExpanded = 'Microsoft.Graph.Identity.Partner.private\Update-MgTenantRelationshipDelegatedAdminRelationshipRequest_UpdateExpanded';
            UpdateViaIdentity = 'Microsoft.Graph.Identity.Partner.private\Update-MgTenantRelationshipDelegatedAdminRelationshipRequest_UpdateViaIdentity';
            UpdateViaIdentityExpanded = 'Microsoft.Graph.Identity.Partner.private\Update-MgTenantRelationshipDelegatedAdminRelationshipRequest_UpdateViaIdentityExpanded';
        }
        $cmdInfo = Get-Command -Name $mapping[$parameterSet]
        [Microsoft.Graph.PowerShell.Runtime.MessageAttributeHelper]::ProcessCustomAttributesAtRuntime($cmdInfo, $MyInvocation, $parameterSet, $PSCmdlet)
        $wrappedCmd = $ExecutionContext.InvokeCommand.GetCommand(($mapping[$parameterSet]), [System.Management.Automation.CommandTypes]::Cmdlet)
        $scriptCmd = {& $wrappedCmd @PSBoundParameters}
        $steppablePipeline = $scriptCmd.GetSteppablePipeline($MyInvocation.CommandOrigin)
        $steppablePipeline.Begin($PSCmdlet)
    } catch {

        throw
    }
}

process {
    try {
        $steppablePipeline.Process($_)
    } catch {

        throw
    }

}
end {
    try {
        $steppablePipeline.End()

    } catch {

        throw
    }
} 
}

<#
.Synopsis
Update the properties of a delegatedAdminRelationship object.
The following restrictions apply:\n- You can update this relationship when its status property is created.\n- You can update the autoExtendDuration property when status is either created or active.\n- You can only remove the Microsoft Entra Global Administrator role when the status property is active, which indicates a long-running operation.
.Description
Update the properties of a delegatedAdminRelationship object.
The following restrictions apply:\n- You can update this relationship when its status property is created.\n- You can update the autoExtendDuration property when status is either created or active.\n- You can only remove the Microsoft Entra Global Administrator role when the status property is active, which indicates a long-running operation.
.Example

Import-Module Microsoft.Graph.Identity.Partner

$params = @{
	displayName = "Updated Contoso admin relationship"
	duration = "P31D"
	customer = @{
		tenantId = "52eaad04-13a2-4a2f-9ce8-93a294fadf36"
	}
	accessDetails = @{
		unifiedRoles = @(
			@{
				roleDefinitionId = "44367163-eba1-44c3-98af-f5787879f96a"
			}
			@{
				roleDefinitionId = "29232cdf-9323-42fd-ade2-1d097af3e4de"
			}
			@{
				roleDefinitionId = "69091246-20e8-4a56-aa4d-066075b2a7a8"
			}
			@{
				roleDefinitionId = "3a2c62db-5318-420d-8d74-23affee5d9d5"
			}
		)
	}
	autoExtendDuration = "P180D"
}

Update-MgTenantRelationshipDelegatedAdminRelationship -DelegatedAdminRelationshipId $delegatedAdminRelationshipId -BodyParameter $params


.Inputs
Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity
.Inputs
Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminRelationship
.Inputs
System.Collections.IDictionary
.Outputs
Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminRelationship
.Notes
COMPLEX PARAMETER PROPERTIES

To create the parameters described below, construct a hash table containing the appropriate properties. For information on hash tables, run Get-Help about_Hash_Tables.

ACCESSASSIGNMENTS <IMicrosoftGraphDelegatedAdminAccessAssignment[]>: The access assignments associated with the delegated admin relationship.
  [Id <String>]: The unique identifier for an entity. Read-only.
  [AccessContainer <IMicrosoftGraphDelegatedAdminAccessContainer>]: delegatedAdminAccessContainer
    [(Any) <Object>]: This indicates any property can be added to this object.
    [AccessContainerId <String>]: The identifier of the access container (for example, a security group). For 'securityGroup' access containers, this must be a valid ID of a Microsoft Entra security group in the Microsoft partner's tenant.
    [AccessContainerType <String>]: delegatedAdminAccessContainerType
  [AccessDetails <IMicrosoftGraphDelegatedAdminAccessDetails>]: delegatedAdminAccessDetails
    [(Any) <Object>]: This indicates any property can be added to this object.
    [UnifiedRoles <IMicrosoftGraphUnifiedRole[]>]: The directory roles that the Microsoft partner is assigned in the customer tenant.
      [RoleDefinitionId <String>]: The unified role definition ID of the directory role. Refer to unifiedRoleDefinition resource.
  [CreatedDateTime <DateTime?>]: The date and time in ISO 8601 format and in UTC time when the access assignment was created. Read-only.
  [LastModifiedDateTime <DateTime?>]: The date and time in ISO 8601 and in UTC time when this access assignment was last modified. Read-only.
  [Status <String>]: delegatedAdminAccessAssignmentStatus

ACCESSDETAILS <IMicrosoftGraphDelegatedAdminAccessDetails>: delegatedAdminAccessDetails
  [(Any) <Object>]: This indicates any property can be added to this object.
  [UnifiedRoles <IMicrosoftGraphUnifiedRole[]>]: The directory roles that the Microsoft partner is assigned in the customer tenant.
    [RoleDefinitionId <String>]: The unified role definition ID of the directory role. Refer to unifiedRoleDefinition resource.

BODYPARAMETER <IMicrosoftGraphDelegatedAdminRelationship>: delegatedAdminRelationship
  [(Any) <Object>]: This indicates any property can be added to this object.
  [Id <String>]: The unique identifier for an entity. Read-only.
  [AccessAssignments <IMicrosoftGraphDelegatedAdminAccessAssignment[]>]: The access assignments associated with the delegated admin relationship.
    [Id <String>]: The unique identifier for an entity. Read-only.
    [AccessContainer <IMicrosoftGraphDelegatedAdminAccessContainer>]: delegatedAdminAccessContainer
      [(Any) <Object>]: This indicates any property can be added to this object.
      [AccessContainerId <String>]: The identifier of the access container (for example, a security group). For 'securityGroup' access containers, this must be a valid ID of a Microsoft Entra security group in the Microsoft partner's tenant.
      [AccessContainerType <String>]: delegatedAdminAccessContainerType
    [AccessDetails <IMicrosoftGraphDelegatedAdminAccessDetails>]: delegatedAdminAccessDetails
      [(Any) <Object>]: This indicates any property can be added to this object.
      [UnifiedRoles <IMicrosoftGraphUnifiedRole[]>]: The directory roles that the Microsoft partner is assigned in the customer tenant.
        [RoleDefinitionId <String>]: The unified role definition ID of the directory role. Refer to unifiedRoleDefinition resource.
    [CreatedDateTime <DateTime?>]: The date and time in ISO 8601 format and in UTC time when the access assignment was created. Read-only.
    [LastModifiedDateTime <DateTime?>]: The date and time in ISO 8601 and in UTC time when this access assignment was last modified. Read-only.
    [Status <String>]: delegatedAdminAccessAssignmentStatus
  [AccessDetails <IMicrosoftGraphDelegatedAdminAccessDetails>]: delegatedAdminAccessDetails
  [ActivatedDateTime <DateTime?>]: The date and time in ISO 8601 format and in UTC time when the relationship became active. Read-only.
  [AutoExtendDuration <TimeSpan?>]: The duration by which the validity of the relationship is automatically extended, denoted in ISO 8601 format. Supported values are: P0D, PT0S, P180D. The default value is PT0S. PT0S indicates that the relationship expires when the endDateTime is reached and it isn't automatically extended.
  [CreatedDateTime <DateTime?>]: The date and time in ISO 8601 format and in UTC time when the relationship was created. Read-only.
  [Customer <IMicrosoftGraphDelegatedAdminRelationshipCustomerParticipant>]: delegatedAdminRelationshipCustomerParticipant
    [(Any) <Object>]: This indicates any property can be added to this object.
    [DisplayName <String>]: The display name of the customer tenant as set by Microsoft Entra ID. Read-only
    [TenantId <String>]: The Microsoft Entra ID-assigned tenant ID of the customer tenant.
  [DisplayName <String>]: The display name of the relationship used for ease of identification. Must be unique across all delegated admin relationships of the partner and is set by the partner only when the relationship is in the created status and can't be changed by the customer. Maximum length is 50 characters.
  [Duration <TimeSpan?>]: The duration of the relationship in ISO 8601 format. Must be a value between P1D and P2Y inclusive. This is set by the partner only when the relationship is in the created status and can't be changed by the customer.
  [EndDateTime <DateTime?>]: The date and time in ISO 8601 format and in UTC time when the status of relationship changes to either terminated or expired. Calculated as endDateTime = activatedDateTime + duration. Read-only.
  [LastModifiedDateTime <DateTime?>]: The date and time in ISO 8601 format and in UTC time when the relationship was last modified. Read-only.
  [Operations <IMicrosoftGraphDelegatedAdminRelationshipOperation[]>]: The long running operations associated with the delegated admin relationship.
    [Id <String>]: The unique identifier for an entity. Read-only.
    [CreatedDateTime <DateTime?>]: The time in ISO 8601 format and in UTC time when the long-running operation was created. Read-only.
    [Data <String>]: The data (payload) for the operation. Read-only.
    [LastModifiedDateTime <DateTime?>]: The time in ISO 8601 format and in UTC time when the long-running operation was last modified. Read-only.
    [OperationType <String>]: delegatedAdminRelationshipOperationType
    [Status <String>]: longRunningOperationStatus
  [Requests <IMicrosoftGraphDelegatedAdminRelationshipRequest[]>]: The requests associated with the delegated admin relationship.
    [Id <String>]: The unique identifier for an entity. Read-only.
    [Action <String>]: delegatedAdminRelationshipRequestAction
    [CreatedDateTime <DateTime?>]: The date and time in ISO 8601 format and in UTC time when the relationship request was created. Read-only.
    [LastModifiedDateTime <DateTime?>]: The date and time in ISO 8601 format and UTC time when this relationship request was last modified. Read-only.
    [Status <String>]: delegatedAdminRelationshipRequestStatus
  [Status <String>]: delegatedAdminRelationshipStatus

CUSTOMER <IMicrosoftGraphDelegatedAdminRelationshipCustomerParticipant>: delegatedAdminRelationshipCustomerParticipant
  [(Any) <Object>]: This indicates any property can be added to this object.
  [DisplayName <String>]: The display name of the customer tenant as set by Microsoft Entra ID. Read-only
  [TenantId <String>]: The Microsoft Entra ID-assigned tenant ID of the customer tenant.

INPUTOBJECT <IIdentityPartnerIdentity>: Identity Parameter
  [DelegatedAdminAccessAssignmentId <String>]: The unique identifier of delegatedAdminAccessAssignment
  [DelegatedAdminCustomerId <String>]: The unique identifier of delegatedAdminCustomer
  [DelegatedAdminRelationshipId <String>]: The unique identifier of delegatedAdminRelationship
  [DelegatedAdminRelationshipOperationId <String>]: The unique identifier of delegatedAdminRelationshipOperation
  [DelegatedAdminRelationshipRequestId <String>]: The unique identifier of delegatedAdminRelationshipRequest
  [DelegatedAdminServiceManagementDetailId <String>]: The unique identifier of delegatedAdminServiceManagementDetail

OPERATIONS <IMicrosoftGraphDelegatedAdminRelationshipOperation[]>: The long running operations associated with the delegated admin relationship.
  [Id <String>]: The unique identifier for an entity. Read-only.
  [CreatedDateTime <DateTime?>]: The time in ISO 8601 format and in UTC time when the long-running operation was created. Read-only.
  [Data <String>]: The data (payload) for the operation. Read-only.
  [LastModifiedDateTime <DateTime?>]: The time in ISO 8601 format and in UTC time when the long-running operation was last modified. Read-only.
  [OperationType <String>]: delegatedAdminRelationshipOperationType
  [Status <String>]: longRunningOperationStatus

REQUESTS <IMicrosoftGraphDelegatedAdminRelationshipRequest[]>: The requests associated with the delegated admin relationship.
  [Id <String>]: The unique identifier for an entity. Read-only.
  [Action <String>]: delegatedAdminRelationshipRequestAction
  [CreatedDateTime <DateTime?>]: The date and time in ISO 8601 format and in UTC time when the relationship request was created. Read-only.
  [LastModifiedDateTime <DateTime?>]: The date and time in ISO 8601 format and UTC time when this relationship request was last modified. Read-only.
  [Status <String>]: delegatedAdminRelationshipRequestStatus
.Link
https://learn.microsoft.com/powershell/module/microsoft.graph.identity.partner/update-mgtenantrelationshipdelegatedadminrelationship
.Link
https://learn.microsoft.com/graph/api/delegatedadminrelationship-update?view=graph-rest-1.0
#>
function Update-MgTenantRelationshipDelegatedAdminRelationship {
[OutputType([Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminRelationship])]
[CmdletBinding(DefaultParameterSetName='UpdateExpanded', PositionalBinding=$false, SupportsShouldProcess, ConfirmImpact='Medium')]
param(
    [Parameter(ParameterSetName='Update', Mandatory)]
    [Parameter(ParameterSetName='UpdateExpanded', Mandatory)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [System.String]
    # The unique identifier of delegatedAdminRelationship
    ${DelegatedAdminRelationshipId},

    [Parameter(ParameterSetName='UpdateViaIdentity', Mandatory, ValueFromPipeline)]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded', Mandatory, ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Path')]
    [Microsoft.Graph.PowerShell.Models.IIdentityPartnerIdentity]
    # Identity Parameter
    # To construct, see NOTES section for INPUTOBJECT properties and create a hash table.
    ${InputObject},

    [Parameter(ParameterSetName='Update', Mandatory, ValueFromPipeline)]
    [Parameter(ParameterSetName='UpdateViaIdentity', Mandatory, ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminRelationship]
    # delegatedAdminRelationship
    # To construct, see NOTES section for BODYPARAMETER properties and create a hash table.
    ${BodyParameter},

    [Parameter()]
    [Alias('RHV')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # Optional Response Headers Variable.
    ${ResponseHeadersVariable},

    [Parameter(ParameterSetName='UpdateExpanded')]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded')]
    [AllowEmptyCollection()]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminAccessAssignment[]]
    # The access assignments associated with the delegated admin relationship.
    # To construct, see NOTES section for ACCESSASSIGNMENTS properties and create a hash table.
    ${AccessAssignments},

    [Parameter(ParameterSetName='UpdateExpanded')]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminAccessDetails]
    # delegatedAdminAccessDetails
    # To construct, see NOTES section for ACCESSDETAILS properties and create a hash table.
    ${AccessDetails},

    [Parameter(ParameterSetName='UpdateExpanded')]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.DateTime]
    # The date and time in ISO 8601 format and in UTC time when the relationship became active.
    # Read-only.
    ${ActivatedDateTime},

    [Parameter(ParameterSetName='UpdateExpanded')]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.Collections.Hashtable]
    # Additional Parameters
    ${AdditionalProperties},

    [Parameter(ParameterSetName='UpdateExpanded')]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.TimeSpan]
    # The duration by which the validity of the relationship is automatically extended, denoted in ISO 8601 format.
    # Supported values are: P0D, PT0S, P180D.
    # The default value is PT0S.
    # PT0S indicates that the relationship expires when the endDateTime is reached and it isn't automatically extended.
    ${AutoExtendDuration},

    [Parameter(ParameterSetName='UpdateExpanded')]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.DateTime]
    # The date and time in ISO 8601 format and in UTC time when the relationship was created.
    # Read-only.
    ${CreatedDateTime},

    [Parameter(ParameterSetName='UpdateExpanded')]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminRelationshipCustomerParticipant]
    # delegatedAdminRelationshipCustomerParticipant
    # To construct, see NOTES section for CUSTOMER properties and create a hash table.
    ${Customer},

    [Parameter(ParameterSetName='UpdateExpanded')]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # The display name of the relationship used for ease of identification.
    # Must be unique across all delegated admin relationships of the partner and is set by the partner only when the relationship is in the created status and can't be changed by the customer.
    # Maximum length is 50 characters.
    ${DisplayName},

    [Parameter(ParameterSetName='UpdateExpanded')]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.TimeSpan]
    # The duration of the relationship in ISO 8601 format.
    # Must be a value between P1D and P2Y inclusive.
    # This is set by the partner only when the relationship is in the created status and can't be changed by the customer.
    ${Duration},

    [Parameter(ParameterSetName='UpdateExpanded')]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.DateTime]
    # The date and time in ISO 8601 format and in UTC time when the status of relationship changes to either terminated or expired.
    # Calculated as endDateTime = activatedDateTime + duration.
    # Read-only.
    ${EndDateTime},

    [Parameter(ParameterSetName='UpdateExpanded')]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # The unique identifier for an entity.
    # Read-only.
    ${Id},

    [Parameter(ParameterSetName='UpdateExpanded')]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.DateTime]
    # The date and time in ISO 8601 format and in UTC time when the relationship was last modified.
    # Read-only.
    ${LastModifiedDateTime},

    [Parameter(ParameterSetName='UpdateExpanded')]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded')]
    [AllowEmptyCollection()]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminRelationshipOperation[]]
    # The long running operations associated with the delegated admin relationship.
    # To construct, see NOTES section for OPERATIONS properties and create a hash table.
    ${Operations},

    [Parameter(ParameterSetName='UpdateExpanded')]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded')]
    [AllowEmptyCollection()]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [Microsoft.Graph.PowerShell.Models.IMicrosoftGraphDelegatedAdminRelationshipRequest[]]
    # The requests associated with the delegated admin relationship.
    # To construct, see NOTES section for REQUESTS properties and create a hash table.
    ${Requests},

    [Parameter(ParameterSetName='UpdateExpanded')]
    [Parameter(ParameterSetName='UpdateViaIdentityExpanded')]
    [Microsoft.Graph.PowerShell.Category('Body')]
    [System.String]
    # delegatedAdminRelationshipStatus
    ${Status},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Wait for .NET debugger to attach
    ${Break},

    [Parameter(ValueFromPipeline)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Collections.IDictionary]
    # Optional headers that will be added to the request.
    ${Headers},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be appended to the front of the pipeline
    ${HttpPipelineAppend},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [Microsoft.Graph.PowerShell.Runtime.SendAsyncStep[]]
    # SendAsync Pipeline Steps to be prepended to the front of the pipeline
    ${HttpPipelinePrepend},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Uri]
    # The URI for the proxy server to use
    ${Proxy},

    [Parameter(DontShow)]
    [ValidateNotNull()]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.PSCredential]
    # Credentials for a proxy server to use for the remote call
    ${ProxyCredential},

    [Parameter(DontShow)]
    [Microsoft.Graph.PowerShell.Category('Runtime')]
    [System.Management.Automation.SwitchParameter]
    # Use the default credentials for the proxy
    ${ProxyUseDefaultCredentials}
)

begin {
    try {
        $outBuffer = $null
        if ($PSBoundParameters.TryGetValue('OutBuffer', [ref]$outBuffer)) {
            $PSBoundParameters['OutBuffer'] = 1
        }
        $parameterSet = $PSCmdlet.ParameterSetName

        $mapping = @{
            Update = 'Microsoft.Graph.Identity.Partner.private\Update-MgTenantRelationshipDelegatedAdminRelationship_Update';
            UpdateExpanded = 'Microsoft.Graph.Identity.Partner.private\Update-MgTenantRelationshipDelegatedAdminRelationship_UpdateExpanded';
            UpdateViaIdentity = 'Microsoft.Graph.Identity.Partner.private\Update-MgTenantRelationshipDelegatedAdminRelationship_UpdateViaIdentity';
            UpdateViaIdentityExpanded = 'Microsoft.Graph.Identity.Partner.private\Update-MgTenantRelationshipDelegatedAdminRelationship_UpdateViaIdentityExpanded';
        }
        $cmdInfo = Get-Command -Name $mapping[$parameterSet]
        [Microsoft.Graph.PowerShell.Runtime.MessageAttributeHelper]::ProcessCustomAttributesAtRuntime($cmdInfo, $MyInvocation, $parameterSet, $PSCmdlet)
        $wrappedCmd = $ExecutionContext.InvokeCommand.GetCommand(($mapping[$parameterSet]), [System.Management.Automation.CommandTypes]::Cmdlet)
        $scriptCmd = {& $wrappedCmd @PSBoundParameters}
        $steppablePipeline = $scriptCmd.GetSteppablePipeline($MyInvocation.CommandOrigin)
        $steppablePipeline.Begin($PSCmdlet)
    } catch {

        throw
    }
}

process {
    try {
        $steppablePipeline.Process($_)
    } catch {

        throw
    }

}
end {
    try {
        $steppablePipeline.End()

    } catch {

        throw
    }
} 
}

# SIG # Begin signature block
# MIIoKgYJKoZIhvcNAQcCoIIoGzCCKBcCAQExDzANBglghkgBZQMEAgEFADB5Bgor
# BgEEAYI3AgEEoGswaTA0BgorBgEEAYI3AgEeMCYCAwEAAAQQH8w7YFlLCE63JNLG
# KX7zUQIBAAIBAAIBAAIBAAIBADAxMA0GCWCGSAFlAwQCAQUABCDx2yDxS7lsW7G0
# unRvK+DIMiseC2pVqNDp0y/kz94pkKCCDXYwggX0MIID3KADAgECAhMzAAAEBGx0
# Bv9XKydyAAAAAAQEMA0GCSqGSIb3DQEBCwUAMH4xCzAJBgNVBAYTAlVTMRMwEQYD
# VQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNy
# b3NvZnQgQ29ycG9yYXRpb24xKDAmBgNVBAMTH01pY3Jvc29mdCBDb2RlIFNpZ25p
# bmcgUENBIDIwMTEwHhcNMjQwOTEyMjAxMTE0WhcNMjUwOTExMjAxMTE0WjB0MQsw
# CQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9u
# ZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMR4wHAYDVQQDExVNaWNy
# b3NvZnQgQ29ycG9yYXRpb24wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIB
# AQC0KDfaY50MDqsEGdlIzDHBd6CqIMRQWW9Af1LHDDTuFjfDsvna0nEuDSYJmNyz
# NB10jpbg0lhvkT1AzfX2TLITSXwS8D+mBzGCWMM/wTpciWBV/pbjSazbzoKvRrNo
# DV/u9omOM2Eawyo5JJJdNkM2d8qzkQ0bRuRd4HarmGunSouyb9NY7egWN5E5lUc3
# a2AROzAdHdYpObpCOdeAY2P5XqtJkk79aROpzw16wCjdSn8qMzCBzR7rvH2WVkvF
# HLIxZQET1yhPb6lRmpgBQNnzidHV2Ocxjc8wNiIDzgbDkmlx54QPfw7RwQi8p1fy
# 4byhBrTjv568x8NGv3gwb0RbAgMBAAGjggFzMIIBbzAfBgNVHSUEGDAWBgorBgEE
# AYI3TAgBBggrBgEFBQcDAzAdBgNVHQ4EFgQU8huhNbETDU+ZWllL4DNMPCijEU4w
# RQYDVR0RBD4wPKQ6MDgxHjAcBgNVBAsTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEW
# MBQGA1UEBRMNMjMwMDEyKzUwMjkyMzAfBgNVHSMEGDAWgBRIbmTlUAXTgqoXNzci
# tW2oynUClTBUBgNVHR8ETTBLMEmgR6BFhkNodHRwOi8vd3d3Lm1pY3Jvc29mdC5j
# b20vcGtpb3BzL2NybC9NaWNDb2RTaWdQQ0EyMDExXzIwMTEtMDctMDguY3JsMGEG
# CCsGAQUFBwEBBFUwUzBRBggrBgEFBQcwAoZFaHR0cDovL3d3dy5taWNyb3NvZnQu
# Y29tL3BraW9wcy9jZXJ0cy9NaWNDb2RTaWdQQ0EyMDExXzIwMTEtMDctMDguY3J0
# MAwGA1UdEwEB/wQCMAAwDQYJKoZIhvcNAQELBQADggIBAIjmD9IpQVvfB1QehvpC
# Ge7QeTQkKQ7j3bmDMjwSqFL4ri6ae9IFTdpywn5smmtSIyKYDn3/nHtaEn0X1NBj
# L5oP0BjAy1sqxD+uy35B+V8wv5GrxhMDJP8l2QjLtH/UglSTIhLqyt8bUAqVfyfp
# h4COMRvwwjTvChtCnUXXACuCXYHWalOoc0OU2oGN+mPJIJJxaNQc1sjBsMbGIWv3
# cmgSHkCEmrMv7yaidpePt6V+yPMik+eXw3IfZ5eNOiNgL1rZzgSJfTnvUqiaEQ0X
# dG1HbkDv9fv6CTq6m4Ty3IzLiwGSXYxRIXTxT4TYs5VxHy2uFjFXWVSL0J2ARTYL
# E4Oyl1wXDF1PX4bxg1yDMfKPHcE1Ijic5lx1KdK1SkaEJdto4hd++05J9Bf9TAmi
# u6EK6C9Oe5vRadroJCK26uCUI4zIjL/qG7mswW+qT0CW0gnR9JHkXCWNbo8ccMk1
# sJatmRoSAifbgzaYbUz8+lv+IXy5GFuAmLnNbGjacB3IMGpa+lbFgih57/fIhamq
# 5VhxgaEmn/UjWyr+cPiAFWuTVIpfsOjbEAww75wURNM1Imp9NJKye1O24EspEHmb
# DmqCUcq7NqkOKIG4PVm3hDDED/WQpzJDkvu4FrIbvyTGVU01vKsg4UfcdiZ0fQ+/
# V0hf8yrtq9CkB8iIuk5bBxuPMIIHejCCBWKgAwIBAgIKYQ6Q0gAAAAAAAzANBgkq
# hkiG9w0BAQsFADCBiDELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldhc2hpbmd0b24x
# EDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlv
# bjEyMDAGA1UEAxMpTWljcm9zb2Z0IFJvb3QgQ2VydGlmaWNhdGUgQXV0aG9yaXR5
# IDIwMTEwHhcNMTEwNzA4MjA1OTA5WhcNMjYwNzA4MjEwOTA5WjB+MQswCQYDVQQG
# EwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwG
# A1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSgwJgYDVQQDEx9NaWNyb3NvZnQg
# Q29kZSBTaWduaW5nIFBDQSAyMDExMIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIIC
# CgKCAgEAq/D6chAcLq3YbqqCEE00uvK2WCGfQhsqa+laUKq4BjgaBEm6f8MMHt03
# a8YS2AvwOMKZBrDIOdUBFDFC04kNeWSHfpRgJGyvnkmc6Whe0t+bU7IKLMOv2akr
# rnoJr9eWWcpgGgXpZnboMlImEi/nqwhQz7NEt13YxC4Ddato88tt8zpcoRb0Rrrg
# OGSsbmQ1eKagYw8t00CT+OPeBw3VXHmlSSnnDb6gE3e+lD3v++MrWhAfTVYoonpy
# 4BI6t0le2O3tQ5GD2Xuye4Yb2T6xjF3oiU+EGvKhL1nkkDstrjNYxbc+/jLTswM9
# sbKvkjh+0p2ALPVOVpEhNSXDOW5kf1O6nA+tGSOEy/S6A4aN91/w0FK/jJSHvMAh
# dCVfGCi2zCcoOCWYOUo2z3yxkq4cI6epZuxhH2rhKEmdX4jiJV3TIUs+UsS1Vz8k
# A/DRelsv1SPjcF0PUUZ3s/gA4bysAoJf28AVs70b1FVL5zmhD+kjSbwYuER8ReTB
# w3J64HLnJN+/RpnF78IcV9uDjexNSTCnq47f7Fufr/zdsGbiwZeBe+3W7UvnSSmn
# Eyimp31ngOaKYnhfsi+E11ecXL93KCjx7W3DKI8sj0A3T8HhhUSJxAlMxdSlQy90
# lfdu+HggWCwTXWCVmj5PM4TasIgX3p5O9JawvEagbJjS4NaIjAsCAwEAAaOCAe0w
# ggHpMBAGCSsGAQQBgjcVAQQDAgEAMB0GA1UdDgQWBBRIbmTlUAXTgqoXNzcitW2o
# ynUClTAZBgkrBgEEAYI3FAIEDB4KAFMAdQBiAEMAQTALBgNVHQ8EBAMCAYYwDwYD
# VR0TAQH/BAUwAwEB/zAfBgNVHSMEGDAWgBRyLToCMZBDuRQFTuHqp8cx0SOJNDBa
# BgNVHR8EUzBRME+gTaBLhklodHRwOi8vY3JsLm1pY3Jvc29mdC5jb20vcGtpL2Ny
# bC9wcm9kdWN0cy9NaWNSb29DZXJBdXQyMDExXzIwMTFfMDNfMjIuY3JsMF4GCCsG
# AQUFBwEBBFIwUDBOBggrBgEFBQcwAoZCaHR0cDovL3d3dy5taWNyb3NvZnQuY29t
# L3BraS9jZXJ0cy9NaWNSb29DZXJBdXQyMDExXzIwMTFfMDNfMjIuY3J0MIGfBgNV
# HSAEgZcwgZQwgZEGCSsGAQQBgjcuAzCBgzA/BggrBgEFBQcCARYzaHR0cDovL3d3
# dy5taWNyb3NvZnQuY29tL3BraW9wcy9kb2NzL3ByaW1hcnljcHMuaHRtMEAGCCsG
# AQUFBwICMDQeMiAdAEwAZQBnAGEAbABfAHAAbwBsAGkAYwB5AF8AcwB0AGEAdABl
# AG0AZQBuAHQALiAdMA0GCSqGSIb3DQEBCwUAA4ICAQBn8oalmOBUeRou09h0ZyKb
# C5YR4WOSmUKWfdJ5DJDBZV8uLD74w3LRbYP+vj/oCso7v0epo/Np22O/IjWll11l
# hJB9i0ZQVdgMknzSGksc8zxCi1LQsP1r4z4HLimb5j0bpdS1HXeUOeLpZMlEPXh6
# I/MTfaaQdION9MsmAkYqwooQu6SpBQyb7Wj6aC6VoCo/KmtYSWMfCWluWpiW5IP0
# wI/zRive/DvQvTXvbiWu5a8n7dDd8w6vmSiXmE0OPQvyCInWH8MyGOLwxS3OW560
# STkKxgrCxq2u5bLZ2xWIUUVYODJxJxp/sfQn+N4sOiBpmLJZiWhub6e3dMNABQam
# ASooPoI/E01mC8CzTfXhj38cbxV9Rad25UAqZaPDXVJihsMdYzaXht/a8/jyFqGa
# J+HNpZfQ7l1jQeNbB5yHPgZ3BtEGsXUfFL5hYbXw3MYbBL7fQccOKO7eZS/sl/ah
# XJbYANahRr1Z85elCUtIEJmAH9AAKcWxm6U/RXceNcbSoqKfenoi+kiVH6v7RyOA
# 9Z74v2u3S5fi63V4GuzqN5l5GEv/1rMjaHXmr/r8i+sLgOppO6/8MO0ETI7f33Vt
# Y5E90Z1WTk+/gFcioXgRMiF670EKsT/7qMykXcGhiJtXcVZOSEXAQsmbdlsKgEhr
# /Xmfwb1tbWrJUnMTDXpQzTGCGgowghoGAgEBMIGVMH4xCzAJBgNVBAYTAlVTMRMw
# EQYDVQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVN
# aWNyb3NvZnQgQ29ycG9yYXRpb24xKDAmBgNVBAMTH01pY3Jvc29mdCBDb2RlIFNp
# Z25pbmcgUENBIDIwMTECEzMAAAQEbHQG/1crJ3IAAAAABAQwDQYJYIZIAWUDBAIB
# BQCgga4wGQYJKoZIhvcNAQkDMQwGCisGAQQBgjcCAQQwHAYKKwYBBAGCNwIBCzEO
# MAwGCisGAQQBgjcCARUwLwYJKoZIhvcNAQkEMSIEIAex+M6RZ6rDTujJ0qypYsJX
# 08zFqA4zeG2U+zsL65qEMEIGCisGAQQBgjcCAQwxNDAyoBSAEgBNAGkAYwByAG8A
# cwBvAGYAdKEagBhodHRwOi8vd3d3Lm1pY3Jvc29mdC5jb20wDQYJKoZIhvcNAQEB
# BQAEggEAdOoQEuP2lmVsZeDgSd6kgTBHEWvBx0p/AScL1Elfykyy98l8fJ8UKA+0
# IvfH9JTRBMIvocU2NqWPGLBl0hcO1TK/zQGC0vqPGiFbnFoW7jlGa5L6piGu43Vw
# otq0DauveiXQ7h7WtzxST0s9XbXzwF/sJCzhA0TUQyAQPAg4uo40htWdcnWA3E3C
# Ge92zNmOPJA6jO0tj5XUyj0FiL1ko/EzM6za6aYpkkRk/tlcxV3zDHTc0THzaj+T
# jW/REns1aGIZ8Gt7LyLRSg8437OnitbEVzTQLVldnufeaUiZPVl5EfxV+5otY4Ng
# H2grNVtRIX6eCUU+BKrlLu2gVmT4FKGCF5QwgheQBgorBgEEAYI3AwMBMYIXgDCC
# F3wGCSqGSIb3DQEHAqCCF20wghdpAgEDMQ8wDQYJYIZIAWUDBAIBBQAwggFSBgsq
# hkiG9w0BCRABBKCCAUEEggE9MIIBOQIBAQYKKwYBBAGEWQoDATAxMA0GCWCGSAFl
# AwQCAQUABCCyGxShjOo9f6SvfC76UnefpohxUlqgXuiSpDJxUru8fAIGaEsqPuj1
# GBMyMDI1MDcwOTExMDcyMy41MDhaMASAAgH0oIHRpIHOMIHLMQswCQYDVQQGEwJV
# UzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwGA1UE
# ChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSUwIwYDVQQLExxNaWNyb3NvZnQgQW1l
# cmljYSBPcGVyYXRpb25zMScwJQYDVQQLEx5uU2hpZWxkIFRTUyBFU046MzcwMy0w
# NUUwLUQ5NDcxJTAjBgNVBAMTHE1pY3Jvc29mdCBUaW1lLVN0YW1wIFNlcnZpY2Wg
# ghHqMIIHIDCCBQigAwIBAgITMwAAAgpHshTZ7rKzDwABAAACCjANBgkqhkiG9w0B
# AQsFADB8MQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UE
# BxMHUmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSYwJAYD
# VQQDEx1NaWNyb3NvZnQgVGltZS1TdGFtcCBQQ0EgMjAxMDAeFw0yNTAxMzAxOTQy
# NTdaFw0yNjA0MjIxOTQyNTdaMIHLMQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2Fz
# aGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENv
# cnBvcmF0aW9uMSUwIwYDVQQLExxNaWNyb3NvZnQgQW1lcmljYSBPcGVyYXRpb25z
# MScwJQYDVQQLEx5uU2hpZWxkIFRTUyBFU046MzcwMy0wNUUwLUQ5NDcxJTAjBgNV
# BAMTHE1pY3Jvc29mdCBUaW1lLVN0YW1wIFNlcnZpY2UwggIiMA0GCSqGSIb3DQEB
# AQUAA4ICDwAwggIKAoICAQCy7NzwEpb7BpwAk9LJ00Xq30TcTjcwNZ80TxAtAbhS
# aJ2kwnJA1Au/Do9/fEBjAHv6Mmtt3fmPDeIJnQ7VBeIq8RcfjcjrbPIg3wA5v5MQ
# flPNSBNOvcXRP+fZnAy0ELDzfnJHnCkZNsQUZ7GF7LxULTKOYY2YJw4TrmcHohkY
# 6DjCZyxhqmGQwwdbjoPWRbYu/ozFem/yfJPyjVBql1068bcVh58A8c5CD6TWN/L3
# u+Ny+7O8+Dver6qBT44Ey7pfPZMZ1Hi7yvCLv5LGzSB6o2OD5GIZy7z4kh8UYHdz
# jn9Wx+QZ2233SJQKtZhpI7uHf3oMTg0zanQfz7mgudefmGBrQEg1ox3n+3Tizh0D
# 9zVmNQP9sFjsPQtNGZ9ID9H8A+kFInx4mrSxA2SyGMOQcxlGM30ktIKM3iqCuFEU
# 9CHVMpN94/1fl4T6PonJ+/oWJqFlatYuMKv2Z8uiprnFcAxCpOsDIVBO9K1vHeAM
# iQQUlcE9CD536I1YLnmO2qHagPPmXhdOGrHUnCUtop21elukHh75q/5zH+OnNekp
# 5udpjQNZCviYAZdHsLnkU0NfUAr6r1UqDcSq1yf5RiwimB8SjsdmHll4gPjmqVi0
# /rmnM1oAEQm3PyWcTQQibYLiuKN7Y4io5bJTVwm+vRRbpJ5UL/D33C//7qnHbeoW
# BQIDAQABo4IBSTCCAUUwHQYDVR0OBBYEFAKvF0EEj4AyPfY8W/qrsAvftZwkMB8G
# A1UdIwQYMBaAFJ+nFV0AXmJdg/Tl0mWnG1M1GelyMF8GA1UdHwRYMFYwVKBSoFCG
# Tmh0dHA6Ly93d3cubWljcm9zb2Z0LmNvbS9wa2lvcHMvY3JsL01pY3Jvc29mdCUy
# MFRpbWUtU3RhbXAlMjBQQ0ElMjAyMDEwKDEpLmNybDBsBggrBgEFBQcBAQRgMF4w
# XAYIKwYBBQUHMAKGUGh0dHA6Ly93d3cubWljcm9zb2Z0LmNvbS9wa2lvcHMvY2Vy
# dHMvTWljcm9zb2Z0JTIwVGltZS1TdGFtcCUyMFBDQSUyMDIwMTAoMSkuY3J0MAwG
# A1UdEwEB/wQCMAAwFgYDVR0lAQH/BAwwCgYIKwYBBQUHAwgwDgYDVR0PAQH/BAQD
# AgeAMA0GCSqGSIb3DQEBCwUAA4ICAQCwk3PW0CyjOaqXCMOusTde7ep2CwP/xV1J
# 3o9KAiKSdq8a2UR5RCHYhnJseemweMUH2kNefpnAh2Bn8H2opDztDJkj8OYRd/KQ
# ysE12NwaY3KOwAW8Rg8OdXv5fUZIsOWgprkCQM0VoFHdXYExkJN3EzBbUCUw3yb4
# gAFPK56T+6cPpI8MJLJCQXHNMgti2QZhX9KkfRAffFYMFcpsbI+oziC5Brrk3361
# cJFHhgEJR0J42nqZTGSgUpDGHSZARGqNcAV5h+OQDLeF2p3URx/P6McUg1nJ2gMP
# YBsD+bwd9B0c/XIZ9Mt3ujlELPpkijjCdSZxhzu2M3SZWJr57uY+FC+LspvIOH1O
# pofanh3JGDosNcAEu9yUMWKsEBMngD6VWQSQYZ6X9F80zCoeZwTq0i9AujnYzzx5
# W2fEgZejRu6K1GCASmztNlYJlACjqafWRofTqkJhV/J2v97X3ruDvfpuOuQoUtVA
# wXrDsG2NOBuvVso5KdW54hBSsz/4+ORB4qLnq4/GNtajUHorKRKHGOgFo8DKaXG+
# UNANwhGNxHbILSa59PxExMgCjBRP3828yGKsquSEzzLNWnz5af9ZmeH4809fwItt
# I41JkuiY9X6hmMmLYv8OY34vvOK+zyxkS+9BULVAP6gt+yaHaBlrln8Gi4/dBr2y
# 6Srr/56g0DCCB3EwggVZoAMCAQICEzMAAAAVxedrngKbSZkAAAAAABUwDQYJKoZI
# hvcNAQELBQAwgYgxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNoaW5ndG9uMRAw
# DgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNyb3NvZnQgQ29ycG9yYXRpb24x
# MjAwBgNVBAMTKU1pY3Jvc29mdCBSb290IENlcnRpZmljYXRlIEF1dGhvcml0eSAy
# MDEwMB4XDTIxMDkzMDE4MjIyNVoXDTMwMDkzMDE4MzIyNVowfDELMAkGA1UEBhMC
# VVMxEzARBgNVBAgTCldhc2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNV
# BAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEmMCQGA1UEAxMdTWljcm9zb2Z0IFRp
# bWUtU3RhbXAgUENBIDIwMTAwggIiMA0GCSqGSIb3DQEBAQUAA4ICDwAwggIKAoIC
# AQDk4aZM57RyIQt5osvXJHm9DtWC0/3unAcH0qlsTnXIyjVX9gF/bErg4r25Phdg
# M/9cT8dm95VTcVrifkpa/rg2Z4VGIwy1jRPPdzLAEBjoYH1qUoNEt6aORmsHFPPF
# dvWGUNzBRMhxXFExN6AKOG6N7dcP2CZTfDlhAnrEqv1yaa8dq6z2Nr41JmTamDu6
# GnszrYBbfowQHJ1S/rboYiXcag/PXfT+jlPP1uyFVk3v3byNpOORj7I5LFGc6XBp
# Dco2LXCOMcg1KL3jtIckw+DJj361VI/c+gVVmG1oO5pGve2krnopN6zL64NF50Zu
# yjLVwIYwXE8s4mKyzbnijYjklqwBSru+cakXW2dg3viSkR4dPf0gz3N9QZpGdc3E
# XzTdEonW/aUgfX782Z5F37ZyL9t9X4C626p+Nuw2TPYrbqgSUei/BQOj0XOmTTd0
# lBw0gg/wEPK3Rxjtp+iZfD9M269ewvPV2HM9Q07BMzlMjgK8QmguEOqEUUbi0b1q
# GFphAXPKZ6Je1yh2AuIzGHLXpyDwwvoSCtdjbwzJNmSLW6CmgyFdXzB0kZSU2LlQ
# +QuJYfM2BjUYhEfb3BvR/bLUHMVr9lxSUV0S2yW6r1AFemzFER1y7435UsSFF5PA
# PBXbGjfHCBUYP3irRbb1Hode2o+eFnJpxq57t7c+auIurQIDAQABo4IB3TCCAdkw
# EgYJKwYBBAGCNxUBBAUCAwEAATAjBgkrBgEEAYI3FQIEFgQUKqdS/mTEmr6CkTxG
# NSnPEP8vBO4wHQYDVR0OBBYEFJ+nFV0AXmJdg/Tl0mWnG1M1GelyMFwGA1UdIARV
# MFMwUQYMKwYBBAGCN0yDfQEBMEEwPwYIKwYBBQUHAgEWM2h0dHA6Ly93d3cubWlj
# cm9zb2Z0LmNvbS9wa2lvcHMvRG9jcy9SZXBvc2l0b3J5Lmh0bTATBgNVHSUEDDAK
# BggrBgEFBQcDCDAZBgkrBgEEAYI3FAIEDB4KAFMAdQBiAEMAQTALBgNVHQ8EBAMC
# AYYwDwYDVR0TAQH/BAUwAwEB/zAfBgNVHSMEGDAWgBTV9lbLj+iiXGJo0T2UkFvX
# zpoYxDBWBgNVHR8ETzBNMEugSaBHhkVodHRwOi8vY3JsLm1pY3Jvc29mdC5jb20v
# cGtpL2NybC9wcm9kdWN0cy9NaWNSb29DZXJBdXRfMjAxMC0wNi0yMy5jcmwwWgYI
# KwYBBQUHAQEETjBMMEoGCCsGAQUFBzAChj5odHRwOi8vd3d3Lm1pY3Jvc29mdC5j
# b20vcGtpL2NlcnRzL01pY1Jvb0NlckF1dF8yMDEwLTA2LTIzLmNydDANBgkqhkiG
# 9w0BAQsFAAOCAgEAnVV9/Cqt4SwfZwExJFvhnnJL/Klv6lwUtj5OR2R4sQaTlz0x
# M7U518JxNj/aZGx80HU5bbsPMeTCj/ts0aGUGCLu6WZnOlNN3Zi6th542DYunKmC
# VgADsAW+iehp4LoJ7nvfam++Kctu2D9IdQHZGN5tggz1bSNU5HhTdSRXud2f8449
# xvNo32X2pFaq95W2KFUn0CS9QKC/GbYSEhFdPSfgQJY4rPf5KYnDvBewVIVCs/wM
# nosZiefwC2qBwoEZQhlSdYo2wh3DYXMuLGt7bj8sCXgU6ZGyqVvfSaN0DLzskYDS
# PeZKPmY7T7uG+jIa2Zb0j/aRAfbOxnT99kxybxCrdTDFNLB62FD+CljdQDzHVG2d
# Y3RILLFORy3BFARxv2T5JL5zbcqOCb2zAVdJVGTZc9d/HltEAY5aGZFrDZ+kKNxn
# GSgkujhLmm77IVRrakURR6nxt67I6IleT53S0Ex2tVdUCbFpAUR+fKFhbHP+Crvs
# QWY9af3LwUFJfn6Tvsv4O+S3Fb+0zj6lMVGEvL8CwYKiexcdFYmNcP7ntdAoGokL
# jzbaukz5m/8K6TT4JDVnK+ANuOaMmdbhIurwJ0I9JZTmdHRbatGePu1+oDEzfbzL
# 6Xu/OHBE0ZDxyKs6ijoIYn/ZcGNTTY3ugm2lBRDBcQZqELQdVTNYs6FwZvKhggNN
# MIICNQIBATCB+aGB0aSBzjCByzELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldhc2hp
# bmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBDb3Jw
# b3JhdGlvbjElMCMGA1UECxMcTWljcm9zb2Z0IEFtZXJpY2EgT3BlcmF0aW9uczEn
# MCUGA1UECxMeblNoaWVsZCBUU1MgRVNOOjM3MDMtMDVFMC1EOTQ3MSUwIwYDVQQD
# ExxNaWNyb3NvZnQgVGltZS1TdGFtcCBTZXJ2aWNloiMKAQEwBwYFKw4DAhoDFQDR
# AMVJlA6bKq93Vnu3UkJgm5HlYaCBgzCBgKR+MHwxCzAJBgNVBAYTAlVTMRMwEQYD
# VQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNy
# b3NvZnQgQ29ycG9yYXRpb24xJjAkBgNVBAMTHU1pY3Jvc29mdCBUaW1lLVN0YW1w
# IFBDQSAyMDEwMA0GCSqGSIb3DQEBCwUAAgUA7BiWNjAiGA8yMDI1MDcwOTA3MTgx
# NFoYDzIwMjUwNzEwMDcxODE0WjB0MDoGCisGAQQBhFkKBAExLDAqMAoCBQDsGJY2
# AgEAMAcCAQACAiIlMAcCAQACAhK3MAoCBQDsGee2AgEAMDYGCisGAQQBhFkKBAIx
# KDAmMAwGCisGAQQBhFkKAwKgCjAIAgEAAgMHoSChCjAIAgEAAgMBhqAwDQYJKoZI
# hvcNAQELBQADggEBAFPdUSsscs+owEO1xpkcvunTcGpk1X5o+z3nHztEI2X23rxu
# 1KgvXGKJVzXiaiigOGNxXQTjPod6PhmBZb+Q8uTCh9erfId8UfNKSNp7ylm2IQGE
# Z/ER7Ee+AeBkOhv9OA1EYsKHpn5VTAk/+OQl5ctWvO/jpGxjbYsDv/+CWJyfMVvM
# sH3SOPokjpIDMGbjTfzzYwZz9XiU67yQsLb9aLzNVBjXeAiGIvbKBparccE+mg0Q
# RxMsRSn8camz+K4HIkN+AluvZH0ucF/ikUych28CQIjXmwcbwaXzyQV3efGBlaU+
# ZmmKCzIcC9qdOJfzPGd1dsB6pgRh/eT3wx8B8YAxggQNMIIECQIBATCBkzB8MQsw
# CQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9u
# ZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSYwJAYDVQQDEx1NaWNy
# b3NvZnQgVGltZS1TdGFtcCBQQ0EgMjAxMAITMwAAAgpHshTZ7rKzDwABAAACCjAN
# BglghkgBZQMEAgEFAKCCAUowGgYJKoZIhvcNAQkDMQ0GCyqGSIb3DQEJEAEEMC8G
# CSqGSIb3DQEJBDEiBCDQazTpo5nP0InQuXE382qrERs0evgARiW8pUaKwXjbDDCB
# +gYLKoZIhvcNAQkQAi8xgeowgecwgeQwgb0EIE2ay/y0epK/X3Z03KTcloqE8u9I
# XRtdO7Mex0hw9+SaMIGYMIGApH4wfDELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldh
# c2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBD
# b3Jwb3JhdGlvbjEmMCQGA1UEAxMdTWljcm9zb2Z0IFRpbWUtU3RhbXAgUENBIDIw
# MTACEzMAAAIKR7IU2e6ysw8AAQAAAgowIgQgAXB8Dbb3zDaQOfKpsCPX/+B/UUpi
# yS8VhOnRN7QjpxIwDQYJKoZIhvcNAQELBQAEggIAkNRy6CcfFEcWeMyGc0lIlVSX
# uZz+Af8hoC7jYMbn+BTE9xSEq2/uPCgIAswmh3x/eAVcubvDPxsa+Pw9UKZdQruY
# JBLQ90UvBmV5J5Sh2Gb8FSKffuEvfuy/MNVaBFx8LQ1E4xoqu515RDKugtT4j7gR
# 5bTkGS/EekzFNoVxugfvJuQRjmMFCL0kY7mkOP4Z+3Gkmv1qML5lFXAvu2FCQAht
# sMSu2QcWrJEVKquax8AOnWgRT1riWEretKwKNTo1cYINO7iXgMOobrTX3qhIibaJ
# hIwODix0trkMKQ3IO5ji91t5qAc9dJ7HAcMC0dEfkCPIrT1VhElHC7iLjQ0kNH7r
# z61on6Aa0UUFnOOHXLceib31r8zYbbsi/PPowo7EWLJYUigWMJOrdX1fMLekeUbI
# rlaJt9Q6Vy/5ucV8c8fG2s/9xbWupclB2hz8y1AhumWvL5aHWdrqOksO9lrBBlWE
# yk8Z94YKFLuuZNfsLkNymFqFSmjY1th5QYLACcNmiCAdRPIGAlire7BulbWSNNM/
# mlbiUFo8BWBXDTuYRScRlCfa/Hbs4cqWJ0Dnxc+T5jAZ+pEkb8JOC9EQ+OJrA0j4
# HfewScfyxCfX2XRGgtaRg0mcyAl2IaCWhZOqtf44DgDH5eEbyhfBVnXGRhYZunxZ
# x5cXDMOOfJ2S4ACUC8s=
# SIG # End signature block
