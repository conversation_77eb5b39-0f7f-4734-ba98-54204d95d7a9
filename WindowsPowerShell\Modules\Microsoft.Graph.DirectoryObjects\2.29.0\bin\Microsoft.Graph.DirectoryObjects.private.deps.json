{"runtimeTarget": {"name": ".NETStandard,Version=v2.0/", "signature": ""}, "compilationOptions": {}, "targets": {".NETStandard,Version=v2.0": {}, ".NETStandard,Version=v2.0/": {"Microsoft.Graph.DirectoryObjects.private/2.29.0": {"dependencies": {"Hyak.Common": "1.2.2", "Microsoft.Graph.Authentication": "2.29.0", "NETStandard.Library": "2.0.3", "PowerShellStandard.Library": "5.1.1"}, "runtime": {"Microsoft.Graph.DirectoryObjects.private.dll": {}}}, "Azure.Core/1.44.1": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "System.ClientModel": "1.1.0", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Memory.Data": "6.0.0", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Core.dll": {"assemblyVersion": "1.44.1.0", "fileVersion": "1.4400.124.50905"}}}, "Azure.Identity/1.13.2": {"dependencies": {"Azure.Core": "1.44.1", "Microsoft.Identity.Client": "4.67.2", "Microsoft.Identity.Client.Extensions.Msal": "4.67.2", "System.Memory": "4.5.5", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"assemblyVersion": "1.13.2.0", "fileVersion": "1.1300.225.6404"}}}, "Azure.Identity.Broker/1.2.0": {"dependencies": {"Azure.Identity": "1.13.2", "Microsoft.Identity.Client": "4.67.2", "Microsoft.Identity.Client.Broker": "4.66.1", "Microsoft.Identity.Client.Extensions.Msal": "4.67.2", "System.Memory": "4.5.5", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Identity.Broker.dll": {"assemblyVersion": "1.2.0.0", "fileVersion": "1.200.24.56807"}}}, "Hyak.Common/1.2.2": {"dependencies": {"NETStandard.Library": "2.0.3", "Newtonsoft.Json": "13.0.3", "System.Reflection": "4.3.0", "System.Threading": "4.3.0"}, "runtime": {"lib/netstandard1.4/Hyak.Common.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.2.2.0"}}}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"dependencies": {"System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Bcl.TimeProvider/8.0.1": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Bcl.TimeProvider.dll": {"assemblyVersion": "8.0.0.1", "fileVersion": "8.0.123.58001"}}}, "Microsoft.CSharp/4.5.0": {"runtime": {"lib/netstandard2.0/Microsoft.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}}, "Microsoft.Graph.Core/3.2.2": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.3.0", "Microsoft.IdentityModel.Validators": "8.3.0", "Microsoft.Kiota.Abstractions": "1.16.3", "Microsoft.Kiota.Authentication.Azure": "1.16.3", "Microsoft.Kiota.Http.HttpClientLibrary": "1.16.3", "Microsoft.Kiota.Serialization.Form": "1.16.3", "Microsoft.Kiota.Serialization.Json": "1.16.3", "Microsoft.Kiota.Serialization.Multipart": "1.16.3", "Microsoft.Kiota.Serialization.Text": "1.16.3"}, "runtime": {"lib/netstandard2.0/Microsoft.Graph.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Identity.Client/4.67.2": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.3.0", "System.Diagnostics.DiagnosticSource": "6.0.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Identity.Client.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Identity.Client.Broker/4.66.1": {"dependencies": {"Microsoft.Identity.Client": "4.67.2", "Microsoft.Identity.Client.NativeInterop": "0.16.2"}, "runtime": {"lib/netstandard2.0/Microsoft.Identity.Client.Broker.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Identity.Client.Extensions.Msal/4.67.2": {"dependencies": {"Microsoft.Identity.Client": "4.67.2", "System.IO.FileSystem.AccessControl": "5.0.0", "System.Security.Cryptography.ProtectedData": "4.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Identity.Client.NativeInterop/0.16.2": {"runtime": {"lib/netstandard2.0/Microsoft.Identity.Client.NativeInterop.dll": {"assemblyVersion": "0.16.2.0", "fileVersion": "0.16.2.0"}}}, "Microsoft.IdentityModel.Abstractions/8.3.0": {"runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.3.0.51204"}}}, "Microsoft.IdentityModel.JsonWebTokens/8.3.0": {"dependencies": {"Microsoft.Bcl.TimeProvider": "8.0.1", "Microsoft.IdentityModel.Tokens": "8.3.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "*******", "fileVersion": "8.3.0.51204"}}}, "Microsoft.IdentityModel.Logging/8.3.0": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.3.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "8.3.0.51204"}}}, "Microsoft.IdentityModel.Protocols/8.3.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.3.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "*******", "fileVersion": "8.3.0.51204"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.3.0": {"dependencies": {"Microsoft.IdentityModel.Protocols": "8.3.0", "System.IdentityModel.Tokens.Jwt": "8.3.0", "System.Text.Json": "8.0.5"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "*******", "fileVersion": "8.3.0.51204"}}}, "Microsoft.IdentityModel.Tokens/8.3.0": {"dependencies": {"Microsoft.Bcl.TimeProvider": "8.0.1", "Microsoft.CSharp": "4.5.0", "Microsoft.IdentityModel.Logging": "8.3.0", "System.Security.Cryptography.Cng": "4.5.0", "System.Text.Json": "8.0.5"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "*******", "fileVersion": "8.3.0.51204"}}}, "Microsoft.IdentityModel.Validators/8.3.0": {"dependencies": {"Microsoft.IdentityModel.Protocols": "8.3.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.3.0", "Microsoft.IdentityModel.Tokens": "8.3.0", "System.IdentityModel.Tokens.Jwt": "8.3.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Validators.dll": {"assemblyVersion": "*******", "fileVersion": "8.3.0.51204"}}}, "Microsoft.Kiota.Abstractions/1.16.3": {"dependencies": {"Std.UriTemplate": "2.0.1", "System.Diagnostics.DiagnosticSource": "6.0.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Kiota.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Kiota.Authentication.Azure/1.16.3": {"dependencies": {"Azure.Core": "1.44.1", "Microsoft.Kiota.Abstractions": "1.16.3", "System.Diagnostics.DiagnosticSource": "6.0.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Kiota.Authentication.Azure.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Kiota.Http.HttpClientLibrary/1.16.3": {"dependencies": {"Microsoft.Kiota.Abstractions": "1.16.3", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Text.Json": "8.0.5"}, "runtime": {"lib/netstandard2.0/Microsoft.Kiota.Http.HttpClientLibrary.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Kiota.Serialization.Form/1.16.3": {"dependencies": {"Microsoft.Kiota.Abstractions": "1.16.3"}, "runtime": {"lib/netstandard2.0/Microsoft.Kiota.Serialization.Form.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Kiota.Serialization.Json/1.16.3": {"dependencies": {"Microsoft.Kiota.Abstractions": "1.16.3", "System.Text.Json": "8.0.5"}, "runtime": {"lib/netstandard2.0/Microsoft.Kiota.Serialization.Json.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Kiota.Serialization.Multipart/1.16.3": {"dependencies": {"Microsoft.Kiota.Abstractions": "1.16.3"}, "runtime": {"lib/netstandard2.0/Microsoft.Kiota.Serialization.Multipart.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Kiota.Serialization.Text/1.16.3": {"dependencies": {"Microsoft.Kiota.Abstractions": "1.16.3"}, "runtime": {"lib/netstandard2.0/Microsoft.Kiota.Serialization.Text.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.NETCore.Platforms/1.1.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.Win32.Registry/5.0.0": {"dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.5", "System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "NETStandard.Library/2.0.3": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0"}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.3.27908"}}}, "PowerShellStandard.Library/5.1.1": {"runtime": {"lib/netstandard2.0/System.Management.Automation.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Std.UriTemplate/2.0.1": {"runtime": {"lib/netstandard2.0/Std.UriTemplate.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.Buffers/4.5.1": {"runtime": {"lib/netstandard2.0/System.Buffers.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.28619.1"}}}, "System.ClientModel/1.1.0": {"dependencies": {"System.Memory.Data": "6.0.0", "System.Text.Json": "8.0.5"}, "runtime": {"lib/netstandard2.0/System.ClientModel.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.100.24.46703"}}}, "System.Diagnostics.DiagnosticSource/6.0.1": {"dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/netstandard2.0/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1523.11507"}}}, "System.IdentityModel.Tokens.Jwt/8.3.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.3.0", "Microsoft.IdentityModel.Tokens": "8.3.0"}, "runtime": {"lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "*******", "fileVersion": "8.3.0.51204"}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem.AccessControl/5.0.0": {"dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.5", "System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.IO.FileSystem.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Memory/4.5.5": {"dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/netstandard2.0/System.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.31308.1"}}}, "System.Memory.Data/6.0.0": {"dependencies": {"System.Memory": "4.5.5", "System.Text.Json": "8.0.5"}, "runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Numerics.Vectors/4.5.0": {"runtime": {"lib/netstandard2.0/System.Numerics.Vectors.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"runtime": {"lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Security.AccessControl/5.0.0": {"dependencies": {"System.Security.Principal.Windows": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Security.Cryptography.Cng/4.5.0": {"runtime": {"lib/netstandard2.0/System.Security.Cryptography.Cng.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}}, "System.Security.Cryptography.ProtectedData/4.5.0": {"dependencies": {"System.Memory": "4.5.5"}, "runtime": {"lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}}, "System.Security.Principal.Windows/5.0.0": {"runtime": {"lib/netstandard2.0/System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encodings.Web/8.0.0": {"dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/netstandard2.0/System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Text.Json/8.0.5": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "System.Buffers": "4.5.1", "System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "8.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Threading.dll": {"assemblyVersion": "4.0.12.0", "fileVersion": "4.6.24705.1"}}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks.Extensions/4.5.4": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/netstandard2.0/System.Threading.Tasks.Extensions.dll": {"assemblyVersion": "4.2.0.1", "fileVersion": "4.6.28619.1"}}}, "Microsoft.Graph.Authentication/2.29.0": {"dependencies": {"Microsoft.Graph.Authentication.Core": "2.29.0", "Microsoft.Win32.Registry": "5.0.0", "PowerShellStandard.Library": "5.1.1"}, "runtime": {"Microsoft.Graph.Authentication.dll": {"assemblyVersion": "2.29.0", "fileVersion": "********"}}}, "Microsoft.Graph.Authentication.Core/2.29.0": {"dependencies": {"Azure.Identity": "1.13.2", "Azure.Identity.Broker": "1.2.0", "Microsoft.Graph.Core": "3.2.2", "Newtonsoft.Json": "13.0.3", "System.Text.Json": "8.0.5"}, "runtime": {"Microsoft.Graph.Authentication.Core.dll": {"assemblyVersion": "2.29.0", "fileVersion": "********"}}}}}, "libraries": {"Microsoft.Graph.DirectoryObjects.private/2.29.0": {"type": "project", "serviceable": false, "sha512": ""}, "Azure.Core/1.44.1": {"type": "package", "serviceable": true, "sha512": "sha512-YyznXLQZCregzHvioip07/BkzjuWNXogJEVz9T5W6TwjNr17ax41YGzYMptlo2G10oLCuVPoyva62y0SIRDixg==", "path": "azure.core/1.44.1", "hashPath": "azure.core.1.44.1.nupkg.sha512"}, "Azure.Identity/1.13.2": {"type": "package", "serviceable": true, "sha512": "sha512-CngQVQELdzFmsGSWyGIPIUOCrII7nApMVWxVmJCKQQrWxRXcNquCsZ+njRJRnhFUfD+KMAhpjyRCaceE4EOL6A==", "path": "azure.identity/1.13.2", "hashPath": "azure.identity.1.13.2.nupkg.sha512"}, "Azure.Identity.Broker/1.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-IYN3ZJibTniTUTW84IuTLaiEPxnj8L7JDYQZnauwp3JugXCtpWhsii53dDaFOCiiH+baQq3EUst6z/u8v91h0A==", "path": "azure.identity.broker/1.2.0", "hashPath": "azure.identity.broker.1.2.0.nupkg.sha512"}, "Hyak.Common/1.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-uZpnFn48nSQwHcO0/GSBZ7ExaO0sTXKv8KariXXEWLaB4Q3AeQoprYG4WpKsCT0ByW3YffETivgc5rcH5RRDvQ==", "path": "hyak.common/1.2.2", "hashPath": "hyak.common.1.2.2.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3WA9q9yVqJp222P3x1wYIGDAkpjAku0TMUaaQV22g6L67AI0LdOIrVS7Ht2vJfLHGSPVuqN94vIr15qn+HEkHw==", "path": "microsoft.bcl.asyncinterfaces/8.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.8.0.0.nupkg.sha512"}, "Microsoft.Bcl.TimeProvider/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-C7kWHJnMRY7EvJev2S8+yJHZ1y7A4ZlLbA4NE+O23BDIAN5mHeqND1m+SKv1ChRS5YlCDW7yAMUe7lttRsJaAA==", "path": "microsoft.bcl.timeprovider/8.0.1", "hashPath": "microsoft.bcl.timeprovider.8.0.1.nupkg.sha512"}, "Microsoft.CSharp/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-kaj6Wb4qoMuH3HySFJhxwQfe8R/sJsNJnANrvv8WdFPMoNbKY5htfNscv+LHCu5ipz+49m2e+WQXpLXr9XYemQ==", "path": "microsoft.csharp/4.5.0", "hashPath": "microsoft.csharp.4.5.0.nupkg.sha512"}, "Microsoft.Graph.Core/3.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-SzjZppvER/KtUem8OebnK9ekugl/ExZ57XCQCxxArzYlh0gEMDjoanRvAaq5rGkxNw3OIAqP54K/AoyiTJkVkg==", "path": "microsoft.graph.core/3.2.2", "hashPath": "microsoft.graph.core.3.2.2.nupkg.sha512"}, "Microsoft.Identity.Client/4.67.2": {"type": "package", "serviceable": true, "sha512": "sha512-37t0TfekfG6XM8kue/xNaA66Qjtti5Qe1xA41CK+bEd8VD76/oXJc+meFJHGzygIC485dCpKoamG/pDfb9Qd7Q==", "path": "microsoft.identity.client/4.67.2", "hashPath": "microsoft.identity.client.4.67.2.nupkg.sha512"}, "Microsoft.Identity.Client.Broker/4.66.1": {"type": "package", "serviceable": true, "sha512": "sha512-UN4Lfw7n4SmSuSflZXIlDx04DGEXUvPNjJhyGv5VWMmP8qsh6n1IbCxFwGuYNyfxnzCxALeictVV7Tmdcw0wvw==", "path": "microsoft.identity.client.broker/4.66.1", "hashPath": "microsoft.identity.client.broker.4.66.1.nupkg.sha512"}, "Microsoft.Identity.Client.Extensions.Msal/4.67.2": {"type": "package", "serviceable": true, "sha512": "sha512-DKs+Lva6csEUZabw+JkkjtFgVmcXh4pJeQy5KH5XzPOaKNoZhAMYj1qpKd97qYTZKXIFH12bHPk0DA+6krw+Cw==", "path": "microsoft.identity.client.extensions.msal/4.67.2", "hashPath": "microsoft.identity.client.extensions.msal.4.67.2.nupkg.sha512"}, "Microsoft.Identity.Client.NativeInterop/0.16.2": {"type": "package", "serviceable": true, "sha512": "sha512-hBPb/EjS3PNN+5B3a3PzVHBGb/RWRKY+q+Ib0D40NtdiZekkIApdOcjuJa31h5gVQGuyEkBIi6/PiG1ioqncjQ==", "path": "microsoft.identity.client.nativeinterop/0.16.2", "hashPath": "microsoft.identity.client.nativeinterop.0.16.2.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/8.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-jNin7yvWZu+K3U24q+6kD+LmGSRfbkHl9Px8hN1XrGwq6ZHgKGi/zuTm5m08G27fwqKfVXIWuIcUeq4Y1VQUOg==", "path": "microsoft.identitymodel.abstractions/8.3.0", "hashPath": "microsoft.identitymodel.abstractions.8.3.0.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/8.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-4SVXLT8sDG7CrHiszEBrsDYi+aDW0W9d+fuWUGdZPBdan56aM6fGXJDjbI0TVGEDjJhXbACQd8F/BnC7a+m2RQ==", "path": "microsoft.identitymodel.jsonwebtokens/8.3.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.8.3.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/8.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-4w4pSIGHhCCLTHqtVNR2Cc/zbDIUWIBHTZCu/9ZHm2SVwrXY3RJMcZ7EFGiKqmKZMQZJzA0bpwCZ6R8Yb7i5VQ==", "path": "microsoft.identitymodel.logging/8.3.0", "hashPath": "microsoft.identitymodel.logging.8.3.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/8.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-NNJajNK9cgH61BVY1T/yaHROjCn+KKGQHPk5gxzMBiSit6PW1teiqPBBfSO/y6hVkGMOVKc5hNJfqJsn+5jHjQ==", "path": "microsoft.identitymodel.protocols/8.3.0", "hashPath": "microsoft.identitymodel.protocols.8.3.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kwHswQYvRbggxanPOdE99bkOCELQkgEjg/GNAGTBcrmAZff849DIEBG/utPVNbVDFNDmR+xi0ublSfofWfNTww==", "path": "microsoft.identitymodel.protocols.openidconnect/8.3.0", "hashPath": "microsoft.identitymodel.protocols.openidconnect.8.3.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/8.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yGzqmk+kInH50zeSEH/L1/J0G4/yqTQNq4YmdzOhpE7s/86tz37NS2YbbY2ievbyGjmeBI1mq26QH+yBR6AK3Q==", "path": "microsoft.identitymodel.tokens/8.3.0", "hashPath": "microsoft.identitymodel.tokens.8.3.0.nupkg.sha512"}, "Microsoft.IdentityModel.Validators/8.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-bwjSX8Y8Rv6PyRMSnC2mpVNtr25G2YuQuNEhSQ5943x0FxwdXJYpMvauLw8yyX7rOD+ZhBoFL9P4wdPriY77JA==", "path": "microsoft.identitymodel.validators/8.3.0", "hashPath": "microsoft.identitymodel.validators.8.3.0.nupkg.sha512"}, "Microsoft.Kiota.Abstractions/1.16.3": {"type": "package", "serviceable": true, "sha512": "sha512-JggxduUgWyS+h23qHWcRU0bn4YGO1aM6UKkfkA6WRZwjAyhXvKiP8Wep/o4T62zii/RcH1rOl1XEUpEFP6p6oA==", "path": "microsoft.kiota.abstractions/1.16.3", "hashPath": "microsoft.kiota.abstractions.1.16.3.nupkg.sha512"}, "Microsoft.Kiota.Authentication.Azure/1.16.3": {"type": "package", "serviceable": true, "sha512": "sha512-I66c3nhD+mm6z2YrVqm0BgN1MrD/UWAAl91uwt2+p99W1ZUqp4abfnlf05cAdWo9hPzUxU+46L3Vump8CGij6A==", "path": "microsoft.kiota.authentication.azure/1.16.3", "hashPath": "microsoft.kiota.authentication.azure.1.16.3.nupkg.sha512"}, "Microsoft.Kiota.Http.HttpClientLibrary/1.16.3": {"type": "package", "serviceable": true, "sha512": "sha512-tsk6ykeFvrfMqHfSRX8CEy4yfag0wKQQVkahE5TYs6A+ydKaZDH+R5HyRf9a1NuW6PutHgzBYomvhEMGI4cXEw==", "path": "microsoft.kiota.http.httpclientlibrary/1.16.3", "hashPath": "microsoft.kiota.http.httpclientlibrary.1.16.3.nupkg.sha512"}, "Microsoft.Kiota.Serialization.Form/1.16.3": {"type": "package", "serviceable": true, "sha512": "sha512-/fmyfVLjb1quz4Pov+PSH88LMSC/KulQQxCKAek/GLLckRL8z5Q8z4UNIEb06v3L4XRCL2ay3WGLbrwS9SE/LA==", "path": "microsoft.kiota.serialization.form/1.16.3", "hashPath": "microsoft.kiota.serialization.form.1.16.3.nupkg.sha512"}, "Microsoft.Kiota.Serialization.Json/1.16.3": {"type": "package", "serviceable": true, "sha512": "sha512-gjO27GIB04FIKHUUrJAOS7stRC8d+CGtf824pkzICOOmiqA/XHtqJuu+LQG8lzxPV4gEwYNYP4OnulTY7DFMvQ==", "path": "microsoft.kiota.serialization.json/1.16.3", "hashPath": "microsoft.kiota.serialization.json.1.16.3.nupkg.sha512"}, "Microsoft.Kiota.Serialization.Multipart/1.16.3": {"type": "package", "serviceable": true, "sha512": "sha512-rMeNm7pTHvi+KZurADEWaJQaatAx4TjNLeJwzz3iwFQmMMh6uoQEGpy4DOY+s5osThHvHHcuRLH3aucSB8/apQ==", "path": "microsoft.kiota.serialization.multipart/1.16.3", "hashPath": "microsoft.kiota.serialization.multipart.1.16.3.nupkg.sha512"}, "Microsoft.Kiota.Serialization.Text/1.16.3": {"type": "package", "serviceable": true, "sha512": "sha512-wwSHcR2yJWdsAMbSTGRBDEs8pfGlRqCWLu57qARkleq9upiJHsIjhKTDSNaXzNjDs3jj82PiL1KW6vdVZ3PpaA==", "path": "microsoft.kiota.serialization.text/1.16.3", "hashPath": "microsoft.kiota.serialization.text.1.16.3.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "path": "microsoft.netcore.platforms/1.1.0", "hashPath": "microsoft.netcore.platforms.1.1.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "path": "microsoft.win32.registry/5.0.0", "hashPath": "microsoft.win32.registry.5.0.0.nupkg.sha512"}, "NETStandard.Library/2.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-st47PosZSHrjECdjeIzZQbzivYBJFv6P2nv4cj2ypdI204DO+vZ7l5raGMiX4eXMJ53RfOIg+/s4DHVZ54Nu2A==", "path": "netstandard.library/2.0.3", "hashPath": "netstandard.library.2.0.3.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "PowerShellStandard.Library/5.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-e31xJjG+Kjbv6YF3Yq6D4Dl3or8v7LrNF41k3CXrWozW6hR1zcOe5KYuZJaGSiAgLnwP8wcW+I3+IWEzMPZKXQ==", "path": "powershellstandard.library/5.1.1", "hashPath": "powershellstandard.library.5.1.1.nupkg.sha512"}, "Std.UriTemplate/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Ix5VXZwLfolwVHyGTSSJl6KIJ2le6E9YjLdZBMS1Xxzw7VJankRvQW8JoUL69tEgfcw+0qjgWrlxANrhvS0QCQ==", "path": "std.uritemplate/2.0.1", "hashPath": "std.uritemplate.2.0.1.nupkg.sha512"}, "System.Buffers/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "path": "system.buffers/4.5.1", "hashPath": "system.buffers.4.5.1.nupkg.sha512"}, "System.ClientModel/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-UocOlCkxLZrG2CKMAAImPcldJTxeesHnHGHwhJ0pNlZEvEXcWKuQvVOER2/NiOkJGRJk978SNdw3j6/7O9H1lg==", "path": "system.clientmodel/1.1.0", "hashPath": "system.clientmodel.1.1.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KiLYDu2k2J82Q9BJpWiuQqCkFjRBWVq4jDzKKWawVi9KWzyD0XG3cmfX0vqTQlL14Wi9EufJrbL0+KCLTbqWiQ==", "path": "system.diagnostics.diagnosticsource/6.0.1", "hashPath": "system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/8.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-9GESpDG0Zb17HD5mBW/uEWi2yz/uKPmCthX2UhyLnk42moGH2FpMgXA2Y4l2Qc7P75eXSUTA6wb/c9D9GSVkzw==", "path": "system.identitymodel.tokens.jwt/8.3.0", "hashPath": "system.identitymodel.tokens.jwt.8.3.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.AccessControl/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-SxHB3nuNrpptVk+vZ/F+7OHEpoHUIKKMl02bUmYHQr1r+glbZQxs7pRtsf4ENO29TVm2TH3AEeep2fJcy92oYw==", "path": "system.io.filesystem.accesscontrol/5.0.0", "hashPath": "system.io.filesystem.accesscontrol.5.0.0.nupkg.sha512"}, "System.Memory/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "path": "system.memory/4.5.5", "hashPath": "system.memory.4.5.5.nupkg.sha512"}, "System.Memory.Data/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ntFHArH3I4Lpjf5m4DCXQHJuGwWPNVJPaAvM95Jy/u+2Yzt2ryiyIN04LAogkjP9DeRcEOiviAjQotfmPq/FrQ==", "path": "system.memory.data/6.0.0", "hashPath": "system.memory.data.6.0.0.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Security.AccessControl/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dagJ1mHZO3Ani8GH0PHpPEe/oYO+rVdbQjvjJkBRNQkX4t0r1iaeGn8+/ybkSLEan3/slM0t59SVdHzuHf2jmw==", "path": "system.security.accesscontrol/5.0.0", "hashPath": "system.security.accesscontrol.5.0.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-WG3r7EyjUe9CMPFSs6bty5doUqT+q9pbI80hlNzo2SkPkZ4VTuZkGWjpp77JB8+uaL4DFPRdBsAY+DX3dBK92A==", "path": "system.security.cryptography.cng/4.5.0", "hashPath": "system.security.cryptography.cng.4.5.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-wLBKzFnDCxP12VL9ANydSYhk59fC4cvOr9ypYQLPnAj48NQIhqnjdD2yhP8yEKyBJEjERWS9DisKL7rX5eU25Q==", "path": "system.security.cryptography.protecteddata/4.5.0", "hashPath": "system.security.cryptography.protecteddata.4.5.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "path": "system.text.encodings.web/8.0.0", "hashPath": "system.text.encodings.web.8.0.0.nupkg.sha512"}, "System.Text.Json/8.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-0f1B50Ss7rqxXiaBJyzUu9bWFOO2/zSlifZ/UNMdiIpDYe4cY4LQQicP4nirK1OS31I43rn062UIJ1Q9bpmHpg==", "path": "system.text.json/8.0.5", "hashPath": "system.text.json.8.0.5.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "Microsoft.Graph.Authentication/2.29.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.Graph.Authentication.Core/2.29.0": {"type": "project", "serviceable": false, "sha512": ""}}}