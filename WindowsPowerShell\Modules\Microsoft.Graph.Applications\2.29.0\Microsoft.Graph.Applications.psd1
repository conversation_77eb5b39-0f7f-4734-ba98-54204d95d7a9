#
# Module manifest for module 'Microsoft.Graph.Applications'
#
# Generated by: Microsoft Corporation
#
# Generated on: 7/9/2025
#

@{

# Script module or binary module file associated with this manifest.
RootModule = './Microsoft.Graph.Applications.psm1'

# Version number of this module.
ModuleVersion = '2.29.0'

# Supported PSEditions
CompatiblePSEditions = 'Core', 'Desktop'

# ID used to uniquely identify this module
GUID = '467f54f2-44a8-4993-8e75-b96c3e443098'

# Author of this module
Author = 'Microsoft Corporation'

# Company or vendor of this module
CompanyName = 'Microsoft Corporation'

# Copyright statement for this module
Copyright = 'Microsoft Corporation. All rights reserved.'

# Description of the functionality provided by this module
Description = 'Microsoft Graph PowerShell Cmdlets'

# Minimum version of the PowerShell engine required by this module
PowerShellVersion = '5.1'

# Name of the PowerShell host required by this module
# PowerShellHostName = ''

# Minimum version of the PowerShell host required by this module
# PowerShellHostVersion = ''

# Minimum version of Microsoft .NET Framework required by this module. This prerequisite is valid for the PowerShell Desktop edition only.
DotNetFrameworkVersion = '4.7.2'

# Minimum version of the common language runtime (CLR) required by this module. This prerequisite is valid for the PowerShell Desktop edition only.
# ClrVersion = ''

# Processor architecture (None, X86, Amd64) required by this module
# ProcessorArchitecture = ''

# Modules that must be imported into the global environment prior to importing this module
RequiredModules = @(@{ModuleName = 'Microsoft.Graph.Authentication'; RequiredVersion = '2.29.0'; })

# Assemblies that must be loaded prior to importing this module
RequiredAssemblies = './bin/Microsoft.Graph.Applications.private.dll'

# Script files (.ps1) that are run in the caller's environment prior to importing this module.
# ScriptsToProcess = @()

# Type files (.ps1xml) to be loaded when importing this module
# TypesToProcess = @()

# Format files (.ps1xml) to be loaded when importing this module
FormatsToProcess = './Microsoft.Graph.Applications.format.ps1xml'

# Modules to import as nested modules of the module specified in RootModule/ModuleToProcess
# NestedModules = @()

# Functions to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no functions to export.
FunctionsToExport = 'Add-MgApplicationKey', 'Add-MgApplicationPassword', 
               'Add-MgServicePrincipalKey', 'Add-MgServicePrincipalPassword', 
               'Add-MgServicePrincipalTokenSigningCertificate', 
               'Clear-MgApplicationVerifiedPublisher', 
               'Confirm-MgApplicationMemberGroup', 
               'Confirm-MgApplicationMemberObject', 
               'Confirm-MgServicePrincipalMemberGroup', 
               'Confirm-MgServicePrincipalMemberObject', 
               'Find-MgApplicationSynchronizationJobSchemaDirectory', 
               'Find-MgApplicationSynchronizationTemplateSchemaDirectory', 
               'Find-MgServicePrincipalSynchronizationJobSchemaDirectory', 
               'Find-MgServicePrincipalSynchronizationTemplateSchemaDirectory', 
               'Get-MgApplication', 'Get-MgApplicationAppManagementPolicy', 
               'Get-MgApplicationAppManagementPolicyByRef', 
               'Get-MgApplicationAppManagementPolicyCount', 
               'Get-MgApplicationByAppId', 'Get-MgApplicationById', 
               'Get-MgApplicationByUniqueName', 'Get-MgApplicationCount', 
               'Get-MgApplicationCreatedOnBehalfOf', 'Get-MgApplicationDelta', 
               'Get-MgApplicationExtensionProperty', 
               'Get-MgApplicationExtensionPropertyCount', 
               'Get-MgApplicationFederatedIdentityCredential', 
               'Get-MgApplicationFederatedIdentityCredentialByName', 
               'Get-MgApplicationFederatedIdentityCredentialCount', 
               'Get-MgApplicationHomeRealmDiscoveryPolicy', 
               'Get-MgApplicationHomeRealmDiscoveryPolicyCount', 
               'Get-MgApplicationLogo', 'Get-MgApplicationMemberGroup', 
               'Get-MgApplicationMemberObject', 'Get-MgApplicationOwner', 
               'Get-MgApplicationOwnerAsAppRoleAssignment', 
               'Get-MgApplicationOwnerAsEndpoint', 
               'Get-MgApplicationOwnerAsServicePrincipal', 
               'Get-MgApplicationOwnerAsUser', 'Get-MgApplicationOwnerByRef', 
               'Get-MgApplicationOwnerCount', 
               'Get-MgApplicationOwnerCountAsAppRoleAssignment', 
               'Get-MgApplicationOwnerCountAsEndpoint', 
               'Get-MgApplicationOwnerCountAsServicePrincipal', 
               'Get-MgApplicationOwnerCountAsUser', 
               'Get-MgApplicationSynchronization', 
               'Get-MgApplicationSynchronizationAccessToken', 
               'Get-MgApplicationSynchronizationJob', 
               'Get-MgApplicationSynchronizationJobBulkUpload', 
               'Get-MgApplicationSynchronizationJobBulkUploadContent', 
               'Get-MgApplicationSynchronizationJobCount', 
               'Get-MgApplicationSynchronizationJobSchema', 
               'Get-MgApplicationSynchronizationJobSchemaDirectory', 
               'Get-MgApplicationSynchronizationJobSchemaDirectoryCount', 
               'Get-MgApplicationSynchronizationSecretCount', 
               'Get-MgApplicationSynchronizationTemplate', 
               'Get-MgApplicationSynchronizationTemplateCount', 
               'Get-MgApplicationSynchronizationTemplateSchema', 
               'Get-MgApplicationSynchronizationTemplateSchemaDirectory', 
               'Get-MgApplicationSynchronizationTemplateSchemaDirectoryCount', 
               'Get-MgApplicationTemplate', 'Get-MgApplicationTemplateCount', 
               'Get-MgApplicationTokenIssuancePolicy', 
               'Get-MgApplicationTokenIssuancePolicyByRef', 
               'Get-MgApplicationTokenIssuancePolicyCount', 
               'Get-MgApplicationTokenLifetimePolicy', 
               'Get-MgApplicationTokenLifetimePolicyByRef', 
               'Get-MgApplicationTokenLifetimePolicyCount', 
               'Get-MgGroupAppRoleAssignment', 'Get-MgGroupAppRoleAssignmentCount', 
               'Get-MgServicePrincipal', 
               'Get-MgServicePrincipalAppManagementPolicy', 
               'Get-MgServicePrincipalAppManagementPolicyCount', 
               'Get-MgServicePrincipalAppRoleAssignedTo', 
               'Get-MgServicePrincipalAppRoleAssignedToCount', 
               'Get-MgServicePrincipalAppRoleAssignment', 
               'Get-MgServicePrincipalAppRoleAssignmentCount', 
               'Get-MgServicePrincipalByAppId', 'Get-MgServicePrincipalById', 
               'Get-MgServicePrincipalClaimMappingPolicy', 
               'Get-MgServicePrincipalClaimMappingPolicyByRef', 
               'Get-MgServicePrincipalClaimMappingPolicyCount', 
               'Get-MgServicePrincipalCount', 
               'Get-MgServicePrincipalCreatedObject', 
               'Get-MgServicePrincipalCreatedObjectAsServicePrincipal', 
               'Get-MgServicePrincipalCreatedObjectCount', 
               'Get-MgServicePrincipalCreatedObjectCountAsServicePrincipal', 
               'Get-MgServicePrincipalDelegatedPermissionClassification', 
               'Get-MgServicePrincipalDelegatedPermissionClassificationCount', 
               'Get-MgServicePrincipalDelta', 'Get-MgServicePrincipalEndpoint', 
               'Get-MgServicePrincipalEndpointCount', 
               'Get-MgServicePrincipalHomeRealmDiscoveryPolicy', 
               'Get-MgServicePrincipalHomeRealmDiscoveryPolicyByRef', 
               'Get-MgServicePrincipalHomeRealmDiscoveryPolicyCount', 
               'Get-MgServicePrincipalMemberGroup', 
               'Get-MgServicePrincipalMemberObject', 
               'Get-MgServicePrincipalMemberOf', 
               'Get-MgServicePrincipalMemberOfAsAdministrativeUnit', 
               'Get-MgServicePrincipalMemberOfAsDirectoryRole', 
               'Get-MgServicePrincipalMemberOfAsGroup', 
               'Get-MgServicePrincipalMemberOfCount', 
               'Get-MgServicePrincipalMemberOfCountAsAdministrativeUnit', 
               'Get-MgServicePrincipalMemberOfCountAsDirectoryRole', 
               'Get-MgServicePrincipalMemberOfCountAsGroup', 
               'Get-MgServicePrincipalOauth2PermissionGrant', 
               'Get-MgServicePrincipalOauth2PermissionGrantCount', 
               'Get-MgServicePrincipalOwnedObject', 
               'Get-MgServicePrincipalOwnedObjectAsApplication', 
               'Get-MgServicePrincipalOwnedObjectAsAppRoleAssignment', 
               'Get-MgServicePrincipalOwnedObjectAsEndpoint', 
               'Get-MgServicePrincipalOwnedObjectAsGroup', 
               'Get-MgServicePrincipalOwnedObjectAsServicePrincipal', 
               'Get-MgServicePrincipalOwnedObjectCount', 
               'Get-MgServicePrincipalOwnedObjectCountAsApplication', 
               'Get-MgServicePrincipalOwnedObjectCountAsAppRoleAssignment', 
               'Get-MgServicePrincipalOwnedObjectCountAsEndpoint', 
               'Get-MgServicePrincipalOwnedObjectCountAsGroup', 
               'Get-MgServicePrincipalOwnedObjectCountAsServicePrincipal', 
               'Get-MgServicePrincipalOwner', 
               'Get-MgServicePrincipalOwnerAsAppRoleAssignment', 
               'Get-MgServicePrincipalOwnerAsEndpoint', 
               'Get-MgServicePrincipalOwnerAsServicePrincipal', 
               'Get-MgServicePrincipalOwnerAsUser', 
               'Get-MgServicePrincipalOwnerByRef', 
               'Get-MgServicePrincipalOwnerCount', 
               'Get-MgServicePrincipalOwnerCountAsAppRoleAssignment', 
               'Get-MgServicePrincipalOwnerCountAsEndpoint', 
               'Get-MgServicePrincipalOwnerCountAsServicePrincipal', 
               'Get-MgServicePrincipalOwnerCountAsUser', 
               'Get-MgServicePrincipalRemoteDesktopSecurityConfiguration', 
               'Get-MgServicePrincipalRemoteDesktopSecurityConfigurationTargetDeviceGroup', 
               'Get-MgServicePrincipalRemoteDesktopSecurityConfigurationTargetDeviceGroupCount', 
               'Get-MgServicePrincipalSynchronization', 
               'Get-MgServicePrincipalSynchronizationAccessToken', 
               'Get-MgServicePrincipalSynchronizationJob', 
               'Get-MgServicePrincipalSynchronizationJobBulkUpload', 
               'Get-MgServicePrincipalSynchronizationJobBulkUploadContent', 
               'Get-MgServicePrincipalSynchronizationJobCount', 
               'Get-MgServicePrincipalSynchronizationJobSchema', 
               'Get-MgServicePrincipalSynchronizationJobSchemaDirectory', 
               'Get-MgServicePrincipalSynchronizationJobSchemaDirectoryCount', 
               'Get-MgServicePrincipalSynchronizationSecretCount', 
               'Get-MgServicePrincipalSynchronizationTemplate', 
               'Get-MgServicePrincipalSynchronizationTemplateCount', 
               'Get-MgServicePrincipalSynchronizationTemplateSchema', 
               'Get-MgServicePrincipalSynchronizationTemplateSchemaDirectory', 
               'Get-MgServicePrincipalSynchronizationTemplateSchemaDirectoryCount', 
               'Get-MgServicePrincipalTokenIssuancePolicy', 
               'Get-MgServicePrincipalTokenIssuancePolicyCount', 
               'Get-MgServicePrincipalTokenLifetimePolicy', 
               'Get-MgServicePrincipalTokenLifetimePolicyCount', 
               'Get-MgServicePrincipalTransitiveMemberOf', 
               'Get-MgServicePrincipalTransitiveMemberOfAsAdministrativeUnit', 
               'Get-MgServicePrincipalTransitiveMemberOfAsDirectoryRole', 
               'Get-MgServicePrincipalTransitiveMemberOfAsGroup', 
               'Get-MgServicePrincipalTransitiveMemberOfCount', 
               'Get-MgServicePrincipalTransitiveMemberOfCountAsAdministrativeUnit', 
               'Get-MgServicePrincipalTransitiveMemberOfCountAsDirectoryRole', 
               'Get-MgServicePrincipalTransitiveMemberOfCountAsGroup', 
               'Get-MgUserAppRoleAssignment', 'Get-MgUserAppRoleAssignmentCount', 
               'Invoke-MgFilterApplicationSynchronizationJobSchemaOperator', 
               'Invoke-MgFilterApplicationSynchronizationTemplateSchemaOperator', 
               'Invoke-MgFilterServicePrincipalSynchronizationJobSchemaOperator', 
               'Invoke-MgFilterServicePrincipalSynchronizationTemplateSchemaOperator', 
               'Invoke-MgFunctionApplicationSynchronizationJobSchema', 
               'Invoke-MgFunctionApplicationSynchronizationTemplateSchema', 
               'Invoke-MgFunctionServicePrincipalSynchronizationJobSchema', 
               'Invoke-MgFunctionServicePrincipalSynchronizationTemplateSchema', 
               'Invoke-MgInstantiateApplicationTemplate', 
               'Invoke-MgParseApplicationSynchronizationJobSchemaExpression', 
               'Invoke-MgParseApplicationSynchronizationTemplateSchemaExpression', 
               'Invoke-MgParseServicePrincipalSynchronizationJobSchemaExpression', 
               'Invoke-MgParseServicePrincipalSynchronizationTemplateSchemaExpression', 
               'New-MgApplication', 'New-MgApplicationAppManagementPolicyByRef', 
               'New-MgApplicationExtensionProperty', 
               'New-MgApplicationFederatedIdentityCredential', 
               'New-MgApplicationOwnerByRef', 
               'New-MgApplicationSynchronizationJob', 
               'New-MgApplicationSynchronizationJobOnDemand', 
               'New-MgApplicationSynchronizationJobSchemaDirectory', 
               'New-MgApplicationSynchronizationTemplate', 
               'New-MgApplicationSynchronizationTemplateSchemaDirectory', 
               'New-MgApplicationTokenIssuancePolicyByRef', 
               'New-MgApplicationTokenLifetimePolicyByRef', 
               'New-MgGroupAppRoleAssignment', 'New-MgServicePrincipal', 
               'New-MgServicePrincipalAppRoleAssignedTo', 
               'New-MgServicePrincipalAppRoleAssignment', 
               'New-MgServicePrincipalClaimMappingPolicyByRef', 
               'New-MgServicePrincipalDelegatedPermissionClassification', 
               'New-MgServicePrincipalEndpoint', 
               'New-MgServicePrincipalHomeRealmDiscoveryPolicyByRef', 
               'New-MgServicePrincipalOwnerByRef', 
               'New-MgServicePrincipalRemoteDesktopSecurityConfigurationTargetDeviceGroup', 
               'New-MgServicePrincipalSynchronizationJob', 
               'New-MgServicePrincipalSynchronizationJobOnDemand', 
               'New-MgServicePrincipalSynchronizationJobSchemaDirectory', 
               'New-MgServicePrincipalSynchronizationTemplate', 
               'New-MgServicePrincipalSynchronizationTemplateSchemaDirectory', 
               'New-MgUserAppRoleAssignment', 'Remove-MgApplication', 
               'Remove-MgApplicationAppManagementPolicyAppManagementPolicyByRef', 
               'Remove-MgApplicationByAppId', 'Remove-MgApplicationByUniqueName', 
               'Remove-MgApplicationExtensionProperty', 
               'Remove-MgApplicationFederatedIdentityCredential', 
               'Remove-MgApplicationFederatedIdentityCredentialByName', 
               'Remove-MgApplicationKey', 'Remove-MgApplicationLogo', 
               'Remove-MgApplicationOwnerDirectoryObjectByRef', 
               'Remove-MgApplicationPassword', 
               'Remove-MgApplicationSynchronization', 
               'Remove-MgApplicationSynchronizationJob', 
               'Remove-MgApplicationSynchronizationJobBulkUpload', 
               'Remove-MgApplicationSynchronizationJobBulkUploadContent', 
               'Remove-MgApplicationSynchronizationJobSchema', 
               'Remove-MgApplicationSynchronizationJobSchemaDirectory', 
               'Remove-MgApplicationSynchronizationTemplate', 
               'Remove-MgApplicationSynchronizationTemplateSchema', 
               'Remove-MgApplicationSynchronizationTemplateSchemaDirectory', 
               'Remove-MgApplicationTokenIssuancePolicyTokenIssuancePolicyByRef', 
               'Remove-MgApplicationTokenLifetimePolicyTokenLifetimePolicyByRef', 
               'Remove-MgGroupAppRoleAssignment', 'Remove-MgServicePrincipal', 
               'Remove-MgServicePrincipalAppRoleAssignedTo', 
               'Remove-MgServicePrincipalAppRoleAssignment', 
               'Remove-MgServicePrincipalByAppId', 
               'Remove-MgServicePrincipalClaimMappingPolicyClaimMappingPolicyByRef', 
               'Remove-MgServicePrincipalDelegatedPermissionClassification', 
               'Remove-MgServicePrincipalEndpoint', 
               'Remove-MgServicePrincipalHomeRealmDiscoveryPolicyHomeRealmDiscoveryPolicyByRef', 
               'Remove-MgServicePrincipalKey', 
               'Remove-MgServicePrincipalOwnerDirectoryObjectByRef', 
               'Remove-MgServicePrincipalPassword', 
               'Remove-MgServicePrincipalRemoteDesktopSecurityConfiguration', 
               'Remove-MgServicePrincipalRemoteDesktopSecurityConfigurationTargetDeviceGroup', 
               'Remove-MgServicePrincipalSynchronization', 
               'Remove-MgServicePrincipalSynchronizationJob', 
               'Remove-MgServicePrincipalSynchronizationJobBulkUpload', 
               'Remove-MgServicePrincipalSynchronizationJobBulkUploadContent', 
               'Remove-MgServicePrincipalSynchronizationJobSchema', 
               'Remove-MgServicePrincipalSynchronizationJobSchemaDirectory', 
               'Remove-MgServicePrincipalSynchronizationTemplate', 
               'Remove-MgServicePrincipalSynchronizationTemplateSchema', 
               'Remove-MgServicePrincipalSynchronizationTemplateSchemaDirectory', 
               'Remove-MgUserAppRoleAssignment', 
               'Restart-MgApplicationSynchronizationJob', 
               'Restart-MgServicePrincipalSynchronizationJob', 
               'Set-MgApplicationLogo', 'Set-MgApplicationSynchronization', 
               'Set-MgApplicationSynchronizationJobBulkUploadContent', 
               'Set-MgApplicationSynchronizationSecret', 
               'Set-MgApplicationVerifiedPublisher', 
               'Set-MgServicePrincipalSynchronization', 
               'Set-MgServicePrincipalSynchronizationJobBulkUploadContent', 
               'Set-MgServicePrincipalSynchronizationSecret', 
               'Start-MgApplicationSynchronizationJob', 
               'Start-MgServicePrincipalSynchronizationJob', 
               'Suspend-MgApplicationSynchronizationJob', 
               'Suspend-MgServicePrincipalSynchronizationJob', 
               'Test-MgApplicationProperty', 
               'Test-MgApplicationSynchronizationJobCredential', 
               'Test-MgServicePrincipalProperty', 
               'Test-MgServicePrincipalSynchronizationJobCredential', 
               'Update-MgApplication', 'Update-MgApplicationByAppId', 
               'Update-MgApplicationByUniqueName', 
               'Update-MgApplicationExtensionProperty', 
               'Update-MgApplicationFederatedIdentityCredential', 
               'Update-MgApplicationFederatedIdentityCredentialByName', 
               'Update-MgApplicationSynchronizationJob', 
               'Update-MgApplicationSynchronizationJobBulkUpload', 
               'Update-MgApplicationSynchronizationJobSchema', 
               'Update-MgApplicationSynchronizationJobSchemaDirectory', 
               'Update-MgApplicationSynchronizationTemplate', 
               'Update-MgApplicationSynchronizationTemplateSchema', 
               'Update-MgApplicationSynchronizationTemplateSchemaDirectory', 
               'Update-MgGroupAppRoleAssignment', 'Update-MgServicePrincipal', 
               'Update-MgServicePrincipalAppRoleAssignedTo', 
               'Update-MgServicePrincipalAppRoleAssignment', 
               'Update-MgServicePrincipalByAppId', 
               'Update-MgServicePrincipalDelegatedPermissionClassification', 
               'Update-MgServicePrincipalEndpoint', 
               'Update-MgServicePrincipalRemoteDesktopSecurityConfiguration', 
               'Update-MgServicePrincipalRemoteDesktopSecurityConfigurationTargetDeviceGroup', 
               'Update-MgServicePrincipalSynchronizationJob', 
               'Update-MgServicePrincipalSynchronizationJobBulkUpload', 
               'Update-MgServicePrincipalSynchronizationJobSchema', 
               'Update-MgServicePrincipalSynchronizationJobSchemaDirectory', 
               'Update-MgServicePrincipalSynchronizationTemplate', 
               'Update-MgServicePrincipalSynchronizationTemplateSchema', 
               'Update-MgServicePrincipalSynchronizationTemplateSchemaDirectory', 
               'Update-MgUserAppRoleAssignment'

# Cmdlets to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no cmdlets to export.
CmdletsToExport = @()

# Variables to export from this module
# VariablesToExport = @()

# Aliases to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no aliases to export.
AliasesToExport = 'Remove-MgApplicationAppManagementPolicyByRef', 
               'Remove-MgApplicationOwnerByRef', 
               'Remove-MgApplicationTokenIssuancePolicyByRef', 
               'Remove-MgApplicationTokenLifetimePolicyByRef', 
               'Remove-MgServicePrincipalClaimMappingPolicyByRef', 
               'Remove-MgServicePrincipalHomeRealmDiscoveryPolicyByRef', 
               'Remove-MgServicePrincipalOwnerByRef'

# DSC resources to export from this module
# DscResourcesToExport = @()

# List of all modules packaged with this module
# ModuleList = @()

# List of all files packaged with this module
# FileList = @()

# Private data to pass to the module specified in RootModule/ModuleToProcess. This may also contain a PSData hashtable with additional module metadata used by PowerShell.
PrivateData = @{

    PSData = @{

        # Tags applied to this module. These help with module discovery in online galleries.
        Tags = 'Microsoft','Office365','Graph','PowerShell','PSModule','PSIncludes_Cmdlet'

        # A URL to the license for this module.
        LicenseUri = 'https://aka.ms/devservicesagreement'

        # A URL to the main website for this project.
        ProjectUri = 'https://github.com/microsoftgraph/msgraph-sdk-powershell'

        # A URL to an icon representing this module.
        IconUri = 'https://raw.githubusercontent.com/microsoftgraph/msgraph-sdk-powershell/dev/docs/images/graph_color256.png'

        # ReleaseNotes of this module
        ReleaseNotes = 'See https://aka.ms/GraphPowerShell-Release.'

        # Prerelease string of this module
        # Prerelease = ''

        # Flag to indicate whether the module requires explicit user acceptance for install/update/save
        # RequireLicenseAcceptance = $false

        # External dependent modules of this module
        # ExternalModuleDependencies = @()

    } # End of PSData hashtable

 } # End of PrivateData hashtable

# HelpInfo URI of this module
# HelpInfoURI = ''

# Default prefix for commands exported from this module. Override the default prefix using Import-Module -Prefix.
# DefaultCommandPrefix = ''

}


# SIG # Begin signature block
# MIIoUgYJKoZIhvcNAQcCoIIoQzCCKD8CAQExDzANBglghkgBZQMEAgEFADB5Bgor
# BgEEAYI3AgEEoGswaTA0BgorBgEEAYI3AgEeMCYCAwEAAAQQH8w7YFlLCE63JNLG
# KX7zUQIBAAIBAAIBAAIBAAIBADAxMA0GCWCGSAFlAwQCAQUABCDSF0LpEh5QfraD
# jcCpEeZJbmVF3ZW0LY1c5t5YSNoLTqCCDYUwggYDMIID66ADAgECAhMzAAAEA73V
# lV0POxitAAAAAAQDMA0GCSqGSIb3DQEBCwUAMH4xCzAJBgNVBAYTAlVTMRMwEQYD
# VQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNy
# b3NvZnQgQ29ycG9yYXRpb24xKDAmBgNVBAMTH01pY3Jvc29mdCBDb2RlIFNpZ25p
# bmcgUENBIDIwMTEwHhcNMjQwOTEyMjAxMTEzWhcNMjUwOTExMjAxMTEzWjB0MQsw
# CQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9u
# ZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMR4wHAYDVQQDExVNaWNy
# b3NvZnQgQ29ycG9yYXRpb24wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIB
# AQCfdGddwIOnbRYUyg03O3iz19XXZPmuhEmW/5uyEN+8mgxl+HJGeLGBR8YButGV
# LVK38RxcVcPYyFGQXcKcxgih4w4y4zJi3GvawLYHlsNExQwz+v0jgY/aejBS2EJY
# oUhLVE+UzRihV8ooxoftsmKLb2xb7BoFS6UAo3Zz4afnOdqI7FGoi7g4vx/0MIdi
# kwTn5N56TdIv3mwfkZCFmrsKpN0zR8HD8WYsvH3xKkG7u/xdqmhPPqMmnI2jOFw/
# /n2aL8W7i1Pasja8PnRXH/QaVH0M1nanL+LI9TsMb/enWfXOW65Gne5cqMN9Uofv
# ENtdwwEmJ3bZrcI9u4LZAkujAgMBAAGjggGCMIIBfjAfBgNVHSUEGDAWBgorBgEE
# AYI3TAgBBggrBgEFBQcDAzAdBgNVHQ4EFgQU6m4qAkpz4641iK2irF8eWsSBcBkw
# VAYDVR0RBE0wS6RJMEcxLTArBgNVBAsTJE1pY3Jvc29mdCBJcmVsYW5kIE9wZXJh
# dGlvbnMgTGltaXRlZDEWMBQGA1UEBRMNMjMwMDEyKzUwMjkyNjAfBgNVHSMEGDAW
# gBRIbmTlUAXTgqoXNzcitW2oynUClTBUBgNVHR8ETTBLMEmgR6BFhkNodHRwOi8v
# d3d3Lm1pY3Jvc29mdC5jb20vcGtpb3BzL2NybC9NaWNDb2RTaWdQQ0EyMDExXzIw
# MTEtMDctMDguY3JsMGEGCCsGAQUFBwEBBFUwUzBRBggrBgEFBQcwAoZFaHR0cDov
# L3d3dy5taWNyb3NvZnQuY29tL3BraW9wcy9jZXJ0cy9NaWNDb2RTaWdQQ0EyMDEx
# XzIwMTEtMDctMDguY3J0MAwGA1UdEwEB/wQCMAAwDQYJKoZIhvcNAQELBQADggIB
# AFFo/6E4LX51IqFuoKvUsi80QytGI5ASQ9zsPpBa0z78hutiJd6w154JkcIx/f7r
# EBK4NhD4DIFNfRiVdI7EacEs7OAS6QHF7Nt+eFRNOTtgHb9PExRy4EI/jnMwzQJV
# NokTxu2WgHr/fBsWs6G9AcIgvHjWNN3qRSrhsgEdqHc0bRDUf8UILAdEZOMBvKLC
# rmf+kJPEvPldgK7hFO/L9kmcVe67BnKejDKO73Sa56AJOhM7CkeATrJFxO9GLXos
# oKvrwBvynxAg18W+pagTAkJefzneuWSmniTurPCUE2JnvW7DalvONDOtG01sIVAB
# +ahO2wcUPa2Zm9AiDVBWTMz9XUoKMcvngi2oqbsDLhbK+pYrRUgRpNt0y1sxZsXO
# raGRF8lM2cWvtEkV5UL+TQM1ppv5unDHkW8JS+QnfPbB8dZVRyRmMQ4aY/tx5x5+
# sX6semJ//FbiclSMxSI+zINu1jYerdUwuCi+P6p7SmQmClhDM+6Q+btE2FtpsU0W
# +r6RdYFf/P+nK6j2otl9Nvr3tWLu+WXmz8MGM+18ynJ+lYbSmFWcAj7SYziAfT0s
# IwlQRFkyC71tsIZUhBHtxPliGUu362lIO0Lpe0DOrg8lspnEWOkHnCT5JEnWCbzu
# iVt8RX1IV07uIveNZuOBWLVCzWJjEGa+HhaEtavjy6i7MIIHejCCBWKgAwIBAgIK
# YQ6Q0gAAAAAAAzANBgkqhkiG9w0BAQsFADCBiDELMAkGA1UEBhMCVVMxEzARBgNV
# BAgTCldhc2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jv
# c29mdCBDb3Jwb3JhdGlvbjEyMDAGA1UEAxMpTWljcm9zb2Z0IFJvb3QgQ2VydGlm
# aWNhdGUgQXV0aG9yaXR5IDIwMTEwHhcNMTEwNzA4MjA1OTA5WhcNMjYwNzA4MjEw
# OTA5WjB+MQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UE
# BxMHUmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSgwJgYD
# VQQDEx9NaWNyb3NvZnQgQ29kZSBTaWduaW5nIFBDQSAyMDExMIICIjANBgkqhkiG
# 9w0BAQEFAAOCAg8AMIICCgKCAgEAq/D6chAcLq3YbqqCEE00uvK2WCGfQhsqa+la
# UKq4BjgaBEm6f8MMHt03a8YS2AvwOMKZBrDIOdUBFDFC04kNeWSHfpRgJGyvnkmc
# 6Whe0t+bU7IKLMOv2akrrnoJr9eWWcpgGgXpZnboMlImEi/nqwhQz7NEt13YxC4D
# dato88tt8zpcoRb0RrrgOGSsbmQ1eKagYw8t00CT+OPeBw3VXHmlSSnnDb6gE3e+
# lD3v++MrWhAfTVYoonpy4BI6t0le2O3tQ5GD2Xuye4Yb2T6xjF3oiU+EGvKhL1nk
# kDstrjNYxbc+/jLTswM9sbKvkjh+0p2ALPVOVpEhNSXDOW5kf1O6nA+tGSOEy/S6
# A4aN91/w0FK/jJSHvMAhdCVfGCi2zCcoOCWYOUo2z3yxkq4cI6epZuxhH2rhKEmd
# X4jiJV3TIUs+UsS1Vz8kA/DRelsv1SPjcF0PUUZ3s/gA4bysAoJf28AVs70b1FVL
# 5zmhD+kjSbwYuER8ReTBw3J64HLnJN+/RpnF78IcV9uDjexNSTCnq47f7Fufr/zd
# sGbiwZeBe+3W7UvnSSmnEyimp31ngOaKYnhfsi+E11ecXL93KCjx7W3DKI8sj0A3
# T8HhhUSJxAlMxdSlQy90lfdu+HggWCwTXWCVmj5PM4TasIgX3p5O9JawvEagbJjS
# 4NaIjAsCAwEAAaOCAe0wggHpMBAGCSsGAQQBgjcVAQQDAgEAMB0GA1UdDgQWBBRI
# bmTlUAXTgqoXNzcitW2oynUClTAZBgkrBgEEAYI3FAIEDB4KAFMAdQBiAEMAQTAL
# BgNVHQ8EBAMCAYYwDwYDVR0TAQH/BAUwAwEB/zAfBgNVHSMEGDAWgBRyLToCMZBD
# uRQFTuHqp8cx0SOJNDBaBgNVHR8EUzBRME+gTaBLhklodHRwOi8vY3JsLm1pY3Jv
# c29mdC5jb20vcGtpL2NybC9wcm9kdWN0cy9NaWNSb29DZXJBdXQyMDExXzIwMTFf
# MDNfMjIuY3JsMF4GCCsGAQUFBwEBBFIwUDBOBggrBgEFBQcwAoZCaHR0cDovL3d3
# dy5taWNyb3NvZnQuY29tL3BraS9jZXJ0cy9NaWNSb29DZXJBdXQyMDExXzIwMTFf
# MDNfMjIuY3J0MIGfBgNVHSAEgZcwgZQwgZEGCSsGAQQBgjcuAzCBgzA/BggrBgEF
# BQcCARYzaHR0cDovL3d3dy5taWNyb3NvZnQuY29tL3BraW9wcy9kb2NzL3ByaW1h
# cnljcHMuaHRtMEAGCCsGAQUFBwICMDQeMiAdAEwAZQBnAGEAbABfAHAAbwBsAGkA
# YwB5AF8AcwB0AGEAdABlAG0AZQBuAHQALiAdMA0GCSqGSIb3DQEBCwUAA4ICAQBn
# 8oalmOBUeRou09h0ZyKbC5YR4WOSmUKWfdJ5DJDBZV8uLD74w3LRbYP+vj/oCso7
# v0epo/Np22O/IjWll11lhJB9i0ZQVdgMknzSGksc8zxCi1LQsP1r4z4HLimb5j0b
# pdS1HXeUOeLpZMlEPXh6I/MTfaaQdION9MsmAkYqwooQu6SpBQyb7Wj6aC6VoCo/
# KmtYSWMfCWluWpiW5IP0wI/zRive/DvQvTXvbiWu5a8n7dDd8w6vmSiXmE0OPQvy
# CInWH8MyGOLwxS3OW560STkKxgrCxq2u5bLZ2xWIUUVYODJxJxp/sfQn+N4sOiBp
# mLJZiWhub6e3dMNABQamASooPoI/E01mC8CzTfXhj38cbxV9Rad25UAqZaPDXVJi
# hsMdYzaXht/a8/jyFqGaJ+HNpZfQ7l1jQeNbB5yHPgZ3BtEGsXUfFL5hYbXw3MYb
# BL7fQccOKO7eZS/sl/ahXJbYANahRr1Z85elCUtIEJmAH9AAKcWxm6U/RXceNcbS
# oqKfenoi+kiVH6v7RyOA9Z74v2u3S5fi63V4GuzqN5l5GEv/1rMjaHXmr/r8i+sL
# gOppO6/8MO0ETI7f33VtY5E90Z1WTk+/gFcioXgRMiF670EKsT/7qMykXcGhiJtX
# cVZOSEXAQsmbdlsKgEhr/Xmfwb1tbWrJUnMTDXpQzTGCGiMwghofAgEBMIGVMH4x
# CzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRt
# b25kMR4wHAYDVQQKExVNaWNyb3NvZnQgQ29ycG9yYXRpb24xKDAmBgNVBAMTH01p
# Y3Jvc29mdCBDb2RlIFNpZ25pbmcgUENBIDIwMTECEzMAAAQDvdWVXQ87GK0AAAAA
# BAMwDQYJYIZIAWUDBAIBBQCgga4wGQYJKoZIhvcNAQkDMQwGCisGAQQBgjcCAQQw
# HAYKKwYBBAGCNwIBCzEOMAwGCisGAQQBgjcCARUwLwYJKoZIhvcNAQkEMSIEIHvT
# dKXV4iRYglZ5ZxKDhC3+vd2qV2HszTAynwhY/7reMEIGCisGAQQBgjcCAQwxNDAy
# oBSAEgBNAGkAYwByAG8AcwBvAGYAdKEagBhodHRwOi8vd3d3Lm1pY3Jvc29mdC5j
# b20wDQYJKoZIhvcNAQEBBQAEggEAispcZHKdF28yTjIdi/GrH8XgE5ityTct3kHU
# Py1pO0EAm89NjRGo078/LddLHarRkKyYnxNLtGOeQgdCZ0zy4hUjUFY0LEfhYHSH
# Xr2/TrzHOP7NvxVQjBZqRhq3PVDsHcAk67jZ2FFM205/Bj1upqZjHB8sqwY5ugNI
# oKoWKl9++ghhhJKkQFqUlynPU3VzfXNTk3CdyHcyWQaP3Hi83s7tiWHsirUdscDl
# JBsXtv+ElQWvzzDcVR65LMTJ8TnZtiQiVY/6azbftRWk6dWrDBWMHsDa+QRPQkLS
# jVXkJy80XRlkDZp0dp64JhxBJsG6O+JeEj5z5wppR8hKjsFti6GCF60wghepBgor
# BgEEAYI3AwMBMYIXmTCCF5UGCSqGSIb3DQEHAqCCF4YwgheCAgEDMQ8wDQYJYIZI
# AWUDBAIBBQAwggFaBgsqhkiG9w0BCRABBKCCAUkEggFFMIIBQQIBAQYKKwYBBAGE
# WQoDATAxMA0GCWCGSAFlAwQCAQUABCCoR63pegILF3UXhLn4zsl199YhhOe6FpE8
# cqbd6Bos/gIGaFMNzih1GBMyMDI1MDcwOTExMDcyNS4zMzRaMASAAgH0oIHZpIHW
# MIHTMQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMH
# UmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMS0wKwYDVQQL
# EyRNaWNyb3NvZnQgSXJlbGFuZCBPcGVyYXRpb25zIExpbWl0ZWQxJzAlBgNVBAsT
# Hm5TaGllbGQgVFNTIEVTTjo1NzFBLTA1RTAtRDk0NzElMCMGA1UEAxMcTWljcm9z
# b2Z0IFRpbWUtU3RhbXAgU2VydmljZaCCEfswggcoMIIFEKADAgECAhMzAAAB+8vL
# bDdn5TCVAAEAAAH7MA0GCSqGSIb3DQEBCwUAMHwxCzAJBgNVBAYTAlVTMRMwEQYD
# VQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNy
# b3NvZnQgQ29ycG9yYXRpb24xJjAkBgNVBAMTHU1pY3Jvc29mdCBUaW1lLVN0YW1w
# IFBDQSAyMDEwMB4XDTI0MDcyNTE4MzExM1oXDTI1MTAyMjE4MzExM1owgdMxCzAJ
# BgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25k
# MR4wHAYDVQQKExVNaWNyb3NvZnQgQ29ycG9yYXRpb24xLTArBgNVBAsTJE1pY3Jv
# c29mdCBJcmVsYW5kIE9wZXJhdGlvbnMgTGltaXRlZDEnMCUGA1UECxMeblNoaWVs
# ZCBUU1MgRVNOOjU3MUEtMDVFMC1EOTQ3MSUwIwYDVQQDExxNaWNyb3NvZnQgVGlt
# ZS1TdGFtcCBTZXJ2aWNlMIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEA
# qMJWQeWAq4LwvSjYsjP0Uvhvm0j0aAOJiMLg0sLfxKoTXAdKD6oMuq5rF5oEiOxV
# +9ox0H95Q8fhoZq3x9lxguZyTOK4l2xtcgtJCtjXRllM2bTpjOg35RUrBy0cAloB
# U9GJBs7LBNrcbH6rBiOvqDQNicPRZwq16xyjMidU1J1AJuat9yLn7taifoD58blY
# EcBvkj5dH1la9zU846QDeOoRO6NcqHLsDx8/zVKZxP30mW6Y7RMsqtB8cGCgGwVV
# urOnaNLXs31qTRTyVHX8ppOdoSihCXeqebgJCRzG8zG/e/k0oaBjFFGl+8uFELwC
# yh4wK9Z5+azTzfa2GD4p6ihtskXs3lnW05UKfDJhAADt6viOc0Rk/c8zOiqzh0lK
# pf/eWUY2o/hvcDPZNgLaHvyfDqb8AWaKvO36iRZSXqhSw8SxJo0TCpsbCjmtx0Lp
# Hnqbb1UF7cq09kCcfWTDPcN12pbYLqck0bIIfPKbc7HnrkNQks/mSbVZTnDyT3O8
# zF9q4DCfWesSr1akycDduGxCdKBvgtJh1YxDq1skTweYx5iAWXnB7KMyls3WQZbT
# ubTCLLt8Xn8t+slcKm5DkvobubmHSriuTA3wTyIy4FxamTKm0VDu9mWds8MtjUSJ
# VwNVVlBXaQ3ZMcVjijyVoUNVuBY9McwYcIQK62wQ20ECAwEAAaOCAUkwggFFMB0G
# A1UdDgQWBBRHVSGYUNQ3RwOl71zIAuUjIKg1KjAfBgNVHSMEGDAWgBSfpxVdAF5i
# XYP05dJlpxtTNRnpcjBfBgNVHR8EWDBWMFSgUqBQhk5odHRwOi8vd3d3Lm1pY3Jv
# c29mdC5jb20vcGtpb3BzL2NybC9NaWNyb3NvZnQlMjBUaW1lLVN0YW1wJTIwUENB
# JTIwMjAxMCgxKS5jcmwwbAYIKwYBBQUHAQEEYDBeMFwGCCsGAQUFBzAChlBodHRw
# Oi8vd3d3Lm1pY3Jvc29mdC5jb20vcGtpb3BzL2NlcnRzL01pY3Jvc29mdCUyMFRp
# bWUtU3RhbXAlMjBQQ0ElMjAyMDEwKDEpLmNydDAMBgNVHRMBAf8EAjAAMBYGA1Ud
# JQEB/wQMMAoGCCsGAQUFBwMIMA4GA1UdDwEB/wQEAwIHgDANBgkqhkiG9w0BAQsF
# AAOCAgEAwzoIKOY2dnUjfWuMiGoz/ovoc1e86VwWaZNFdgRmOoQuRe4nLdtZONtT
# HNk3Sj3nkyBszzxSbZEQ0DduyKHHI5P8V87jFttGnlR0wPP22FAebbvAbutkMMVQ
# MFzhVBWiWD0VAnu9x0fjifLKDAVXLwoun5rCFqwbasXFc7H/0DPiC+DBn3tUxefv
# cxUCys4+DC3s8CYp7WWXpZ8Wb/vdBhDliHmB7pWcmsB83uc4/P2GmAI3HMkOEu7f
# CaSYoQhouWOr07l/KM4TndylIirm8f2WwXQcFEzmUvISM6ludUwGlVNfTTJUq2bT
# DEd3tlDKtV9AUY3rrnFwHTwJryLtT4IFhvgBfND3mL1eeSakKf7xTII4Jyt15SXh
# Hd5oI/XGjSgykgJrWA57rGnAC7ru3/ZbFNCMK/Jj6X8X4L6mBOYa2NGKwH4A37YG
# DrecJ/qXXWUYvfLYqHGf8ThYl12Yg1rwSKpWLolA/B1eqBw4TRcvVY0IvNNi5sm+
# //HJ9Aw6NJuR/uDR7X7vDXicpXMlRNgFMyADb8AFIvQPdHqcRpRorY+YUGlvzeJx
# /2gNYyezAokbrFhACsJ2BfyeLyCEo6AuwEHn511PKE8dK4JvlmLSoHj7VFR3NHDk
# 3zRkx0ExkmF8aOdpvoKhuwBCxoZ/JhbzSzrvZ74GVjKKIyt5FA0wggdxMIIFWaAD
# AgECAhMzAAAAFcXna54Cm0mZAAAAAAAVMA0GCSqGSIb3DQEBCwUAMIGIMQswCQYD
# VQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEe
# MBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMTIwMAYDVQQDEylNaWNyb3Nv
# ZnQgUm9vdCBDZXJ0aWZpY2F0ZSBBdXRob3JpdHkgMjAxMDAeFw0yMTA5MzAxODIy
# MjVaFw0zMDA5MzAxODMyMjVaMHwxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNo
# aW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNyb3NvZnQgQ29y
# cG9yYXRpb24xJjAkBgNVBAMTHU1pY3Jvc29mdCBUaW1lLVN0YW1wIFBDQSAyMDEw
# MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEA5OGmTOe0ciELeaLL1yR5
# vQ7VgtP97pwHB9KpbE51yMo1V/YBf2xK4OK9uT4XYDP/XE/HZveVU3Fa4n5KWv64
# NmeFRiMMtY0Tz3cywBAY6GB9alKDRLemjkZrBxTzxXb1hlDcwUTIcVxRMTegCjhu
# je3XD9gmU3w5YQJ6xKr9cmmvHaus9ja+NSZk2pg7uhp7M62AW36MEBydUv626GIl
# 3GoPz130/o5Tz9bshVZN7928jaTjkY+yOSxRnOlwaQ3KNi1wjjHINSi947SHJMPg
# yY9+tVSP3PoFVZhtaDuaRr3tpK56KTesy+uDRedGbsoy1cCGMFxPLOJiss254o2I
# 5JasAUq7vnGpF1tnYN74kpEeHT39IM9zfUGaRnXNxF803RKJ1v2lIH1+/NmeRd+2
# ci/bfV+AutuqfjbsNkz2K26oElHovwUDo9Fzpk03dJQcNIIP8BDyt0cY7afomXw/
# TNuvXsLz1dhzPUNOwTM5TI4CvEJoLhDqhFFG4tG9ahhaYQFzymeiXtcodgLiMxhy
# 16cg8ML6EgrXY28MyTZki1ugpoMhXV8wdJGUlNi5UPkLiWHzNgY1GIRH29wb0f2y
# 1BzFa/ZcUlFdEtsluq9QBXpsxREdcu+N+VLEhReTwDwV2xo3xwgVGD94q0W29R6H
# XtqPnhZyacaue7e3PmriLq0CAwEAAaOCAd0wggHZMBIGCSsGAQQBgjcVAQQFAgMB
# AAEwIwYJKwYBBAGCNxUCBBYEFCqnUv5kxJq+gpE8RjUpzxD/LwTuMB0GA1UdDgQW
# BBSfpxVdAF5iXYP05dJlpxtTNRnpcjBcBgNVHSAEVTBTMFEGDCsGAQQBgjdMg30B
# ATBBMD8GCCsGAQUFBwIBFjNodHRwOi8vd3d3Lm1pY3Jvc29mdC5jb20vcGtpb3Bz
# L0RvY3MvUmVwb3NpdG9yeS5odG0wEwYDVR0lBAwwCgYIKwYBBQUHAwgwGQYJKwYB
# BAGCNxQCBAweCgBTAHUAYgBDAEEwCwYDVR0PBAQDAgGGMA8GA1UdEwEB/wQFMAMB
# Af8wHwYDVR0jBBgwFoAU1fZWy4/oolxiaNE9lJBb186aGMQwVgYDVR0fBE8wTTBL
# oEmgR4ZFaHR0cDovL2NybC5taWNyb3NvZnQuY29tL3BraS9jcmwvcHJvZHVjdHMv
# TWljUm9vQ2VyQXV0XzIwMTAtMDYtMjMuY3JsMFoGCCsGAQUFBwEBBE4wTDBKBggr
# BgEFBQcwAoY+aHR0cDovL3d3dy5taWNyb3NvZnQuY29tL3BraS9jZXJ0cy9NaWNS
# b29DZXJBdXRfMjAxMC0wNi0yMy5jcnQwDQYJKoZIhvcNAQELBQADggIBAJ1Vffwq
# reEsH2cBMSRb4Z5yS/ypb+pcFLY+TkdkeLEGk5c9MTO1OdfCcTY/2mRsfNB1OW27
# DzHkwo/7bNGhlBgi7ulmZzpTTd2YurYeeNg2LpypglYAA7AFvonoaeC6Ce5732pv
# vinLbtg/SHUB2RjebYIM9W0jVOR4U3UkV7ndn/OOPcbzaN9l9qRWqveVtihVJ9Ak
# vUCgvxm2EhIRXT0n4ECWOKz3+SmJw7wXsFSFQrP8DJ6LGYnn8AtqgcKBGUIZUnWK
# NsIdw2FzLixre24/LAl4FOmRsqlb30mjdAy87JGA0j3mSj5mO0+7hvoyGtmW9I/2
# kQH2zsZ0/fZMcm8Qq3UwxTSwethQ/gpY3UA8x1RtnWN0SCyxTkctwRQEcb9k+SS+
# c23Kjgm9swFXSVRk2XPXfx5bRAGOWhmRaw2fpCjcZxkoJLo4S5pu+yFUa2pFEUep
# 8beuyOiJXk+d0tBMdrVXVAmxaQFEfnyhYWxz/gq77EFmPWn9y8FBSX5+k77L+Dvk
# txW/tM4+pTFRhLy/AsGConsXHRWJjXD+57XQKBqJC4822rpM+Zv/Cuk0+CQ1Zyvg
# DbjmjJnW4SLq8CdCPSWU5nR0W2rRnj7tfqAxM328y+l7vzhwRNGQ8cirOoo6CGJ/
# 2XBjU02N7oJtpQUQwXEGahC0HVUzWLOhcGbyoYIDVjCCAj4CAQEwggEBoYHZpIHW
# MIHTMQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMH
# UmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMS0wKwYDVQQL
# EyRNaWNyb3NvZnQgSXJlbGFuZCBPcGVyYXRpb25zIExpbWl0ZWQxJzAlBgNVBAsT
# Hm5TaGllbGQgVFNTIEVTTjo1NzFBLTA1RTAtRDk0NzElMCMGA1UEAxMcTWljcm9z
# b2Z0IFRpbWUtU3RhbXAgU2VydmljZaIjCgEBMAcGBSsOAwIaAxUABHHn7NCGusZz
# 2RfVbyuwYwPykBWggYMwgYCkfjB8MQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2Fz
# aGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENv
# cnBvcmF0aW9uMSYwJAYDVQQDEx1NaWNyb3NvZnQgVGltZS1TdGFtcCBQQ0EgMjAx
# MDANBgkqhkiG9w0BAQsFAAIFAOwYkaEwIhgPMjAyNTA3MDkwNjU4NDFaGA8yMDI1
# MDcxMDA2NTg0MVowdDA6BgorBgEEAYRZCgQBMSwwKjAKAgUA7BiRoQIBADAHAgEA
# AgI0MDAHAgEAAgISGTAKAgUA7BnjIQIBADA2BgorBgEEAYRZCgQCMSgwJjAMBgor
# BgEEAYRZCgMCoAowCAIBAAIDB6EgoQowCAIBAAIDAYagMA0GCSqGSIb3DQEBCwUA
# A4IBAQDa/eJhxwYMgMpgF55MHiHW1H65vDxl41Pcct1QpJhYuqBhIy7AJgYt/XQp
# 0rYZVXV5ZRDRsmZ2vTl7etdGoFQORK/rfgPf4wa+fuC1eXA0E1A1MdoN0NX6nYVI
# Sk+vXE8pr1/oi/V6bOdLAkoAnTzKAgR++yfOuKnkf1rDRyapF30SYkQ7xECZ5WeQ
# ihmDDioLcusC2OGmEwaB8+glQRppTJ6al/bJaCXCQ3j6N6J7XCH+lC8NmDEBhSPG
# uVKKdJDzG4XBP7Jcn91JjR3LIoiWM44cGtnCbPaCnkDRrfGefTEM8NUjlc22i0jW
# jDPPkWmqrXY79idZwVlaitVBm1jpMYIEDTCCBAkCAQEwgZMwfDELMAkGA1UEBhMC
# VVMxEzARBgNVBAgTCldhc2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNV
# BAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEmMCQGA1UEAxMdTWljcm9zb2Z0IFRp
# bWUtU3RhbXAgUENBIDIwMTACEzMAAAH7y8tsN2flMJUAAQAAAfswDQYJYIZIAWUD
# BAIBBQCgggFKMBoGCSqGSIb3DQEJAzENBgsqhkiG9w0BCRABBDAvBgkqhkiG9w0B
# CQQxIgQgbM+1DaNS4UBnoBec81GKCnCEeKtjfUdgKylDNOuM7/owgfoGCyqGSIb3
# DQEJEAIvMYHqMIHnMIHkMIG9BCA52wKr/KCFlVNYiWsCLsB4qhjEYEP3xHqYqDu1
# SSTlGDCBmDCBgKR+MHwxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNoaW5ndG9u
# MRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNyb3NvZnQgQ29ycG9yYXRp
# b24xJjAkBgNVBAMTHU1pY3Jvc29mdCBUaW1lLVN0YW1wIFBDQSAyMDEwAhMzAAAB
# +8vLbDdn5TCVAAEAAAH7MCIEIGFByiJimxdnZDceMzfHoIOQz7Lz+rD4sajRL7Xh
# LDuSMA0GCSqGSIb3DQEBCwUABIICACtOMVTV4ASfKkUpDp/8xakH+7B5+445LmPz
# AaygF45+4bcwiBU6Fyqbeo6JEiFl2Sfs1L1cL/VZF/hM+DeaOSnCAhRfrFAp5rf0
# 3zLUM1+SJ7LnxXAXvCmvYAdJne8vTsM8FOvvG7dV26xgz5bkdiLNS7gfahCw6+vN
# skXqUkUkPDz+u1k5KlQj3bZm7RggZcxCEzalWewBeolHCEUFxBLot+PWMZi4YNft
# ZJWFU4b/KCMLTN5Z3bnuJx9PrY600aujRjw+Gc+UFhgzGD/c/3GEQcfJ3dVLLr8V
# wwlVVj538mU6+Bh1iQetAFg9fqBiwCO7tMPTgVC7EyIKed4Megued/j8cVydFkY3
# XuQeWzC5ucUD6uXd0WeqmLEFYu3Lo5moogK73z1I85j7Ezbq6FF3J9BXi4+v75S0
# DdpZfdgCDKSZ2qOmE1b9gVCxXWVBDXyEU+ZPkNn7Jq4lqB55n7/JemAkDjOjRe7P
# U8csJXkqXjD+o7hWPGygMy13vaV65cdcKECGbxFl2THTK8r+LAMpDwzLcGOHB58l
# bB4vNFcDM7qV+RQihX5IjlojlnNJKbspwWqnBqpsmrwc+s73/Bw7gti8PZ+jhX6z
# itKEM3gKnzC5RwKvFs3mZtiJjRTHV+9mdg2op9hK0iOY8nFqlrSbXFFZJ9KGGGcU
# lzzVc+sF
# SIG # End signature block
