#
# Module manifest for module 'Microsoft.Graph.DeviceManagement'
#
# Generated by: Microsoft Corporation
#
# Generated on: 7/9/2025
#

@{

# Script module or binary module file associated with this manifest.
RootModule = './Microsoft.Graph.DeviceManagement.psm1'

# Version number of this module.
ModuleVersion = '2.29.0'

# Supported PSEditions
CompatiblePSEditions = 'Core', 'Desktop'

# ID used to uniquely identify this module
GUID = '4131557d-8635-4903-9cfd-d59ddef4a597'

# Author of this module
Author = 'Microsoft Corporation'

# Company or vendor of this module
CompanyName = 'Microsoft Corporation'

# Copyright statement for this module
Copyright = 'Microsoft Corporation. All rights reserved.'

# Description of the functionality provided by this module
Description = 'Microsoft Graph PowerShell Cmdlets'

# Minimum version of the PowerShell engine required by this module
PowerShellVersion = '5.1'

# Name of the PowerShell host required by this module
# PowerShellHostName = ''

# Minimum version of the PowerShell host required by this module
# PowerShellHostVersion = ''

# Minimum version of Microsoft .NET Framework required by this module. This prerequisite is valid for the PowerShell Desktop edition only.
DotNetFrameworkVersion = '4.7.2'

# Minimum version of the common language runtime (CLR) required by this module. This prerequisite is valid for the PowerShell Desktop edition only.
# ClrVersion = ''

# Processor architecture (None, X86, Amd64) required by this module
# ProcessorArchitecture = ''

# Modules that must be imported into the global environment prior to importing this module
RequiredModules = @(@{ModuleName = 'Microsoft.Graph.Authentication'; RequiredVersion = '2.29.0'; })

# Assemblies that must be loaded prior to importing this module
RequiredAssemblies = './bin/Microsoft.Graph.DeviceManagement.private.dll'

# Script files (.ps1) that are run in the caller's environment prior to importing this module.
# ScriptsToProcess = @()

# Type files (.ps1xml) to be loaded when importing this module
# TypesToProcess = @()

# Format files (.ps1xml) to be loaded when importing this module
FormatsToProcess = './Microsoft.Graph.DeviceManagement.format.ps1xml'

# Modules to import as nested modules of the module specified in RootModule/ModuleToProcess
# NestedModules = @()

# Functions to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no functions to export.
FunctionsToExport = 'Clear-MgDeviceManagementManagedDevice', 
               'Disable-MgDeviceManagementManagedDeviceLostMode', 
               'Find-MgDeviceManagementManagedDevice', 'Get-MgAdminEdge', 
               'Get-MgAdminEdgeInternetExplorerMode', 
               'Get-MgAdminEdgeInternetExplorerModeSiteList', 
               'Get-MgAdminEdgeInternetExplorerModeSiteListCount', 
               'Get-MgAdminEdgeInternetExplorerModeSiteListSharedCookie', 
               'Get-MgAdminEdgeInternetExplorerModeSiteListSharedCookieCount', 
               'Get-MgAdminEdgeInternetExplorerModeSiteListSite', 
               'Get-MgAdminEdgeInternetExplorerModeSiteListSiteCount', 
               'Get-MgDeviceManagement', 'Get-MgDeviceManagementDetectedApp', 
               'Get-MgDeviceManagementDetectedAppCount', 
               'Get-MgDeviceManagementDetectedAppManagedDevice', 
               'Get-MgDeviceManagementDetectedAppManagedDeviceCount', 
               'Get-MgDeviceManagementDeviceCategory', 
               'Get-MgDeviceManagementDeviceCategoryCount', 
               'Get-MgDeviceManagementDeviceCompliancePolicy', 
               'Get-MgDeviceManagementDeviceCompliancePolicyAssignment', 
               'Get-MgDeviceManagementDeviceCompliancePolicyAssignmentCount', 
               'Get-MgDeviceManagementDeviceCompliancePolicyCount', 
               'Get-MgDeviceManagementDeviceCompliancePolicyDeviceSettingStateSummary', 
               'Get-MgDeviceManagementDeviceCompliancePolicyDeviceSettingStateSummaryCount', 
               'Get-MgDeviceManagementDeviceCompliancePolicyDeviceStateSummary', 
               'Get-MgDeviceManagementDeviceCompliancePolicyDeviceStatus', 
               'Get-MgDeviceManagementDeviceCompliancePolicyDeviceStatusCount', 
               'Get-MgDeviceManagementDeviceCompliancePolicyDeviceStatusOverview', 
               'Get-MgDeviceManagementDeviceCompliancePolicyScheduledActionForRule', 
               'Get-MgDeviceManagementDeviceCompliancePolicyScheduledActionForRuleCount', 
               'Get-MgDeviceManagementDeviceCompliancePolicyScheduledActionForRuleScheduledActionConfiguration', 
               'Get-MgDeviceManagementDeviceCompliancePolicyScheduledActionForRuleScheduledActionConfigurationCount', 
               'Get-MgDeviceManagementDeviceCompliancePolicySettingStateSummary', 
               'Get-MgDeviceManagementDeviceCompliancePolicySettingStateSummaryCount', 
               'Get-MgDeviceManagementDeviceCompliancePolicySettingStateSummaryDeviceComplianceSettingState', 
               'Get-MgDeviceManagementDeviceCompliancePolicySettingStateSummaryDeviceComplianceSettingStateCount', 
               'Get-MgDeviceManagementDeviceCompliancePolicyUserStatus', 
               'Get-MgDeviceManagementDeviceCompliancePolicyUserStatusCount', 
               'Get-MgDeviceManagementDeviceCompliancePolicyUserStatusOverview', 
               'Get-MgDeviceManagementDeviceConfiguration', 
               'Get-MgDeviceManagementDeviceConfigurationAssignment', 
               'Get-MgDeviceManagementDeviceConfigurationAssignmentCount', 
               'Get-MgDeviceManagementDeviceConfigurationCount', 
               'Get-MgDeviceManagementDeviceConfigurationDeviceSettingStateSummary', 
               'Get-MgDeviceManagementDeviceConfigurationDeviceSettingStateSummaryCount', 
               'Get-MgDeviceManagementDeviceConfigurationDeviceStateSummary', 
               'Get-MgDeviceManagementDeviceConfigurationDeviceStatus', 
               'Get-MgDeviceManagementDeviceConfigurationDeviceStatusCount', 
               'Get-MgDeviceManagementDeviceConfigurationDeviceStatusOverview', 
               'Get-MgDeviceManagementDeviceConfigurationOmaSettingPlainTextValue', 
               'Get-MgDeviceManagementDeviceConfigurationUserStatus', 
               'Get-MgDeviceManagementDeviceConfigurationUserStatusCount', 
               'Get-MgDeviceManagementDeviceConfigurationUserStatusOverview', 
               'Get-MgDeviceManagementManagedDevice', 
               'Get-MgDeviceManagementManagedDeviceCategory', 
               'Get-MgDeviceManagementManagedDeviceCategoryByRef', 
               'Get-MgDeviceManagementManagedDeviceCompliancePolicyState', 
               'Get-MgDeviceManagementManagedDeviceCompliancePolicyStateCount', 
               'Get-MgDeviceManagementManagedDeviceConfigurationState', 
               'Get-MgDeviceManagementManagedDeviceConfigurationStateCount', 
               'Get-MgDeviceManagementManagedDeviceCount', 
               'Get-MgDeviceManagementManagedDeviceLogCollectionRequest', 
               'Get-MgDeviceManagementManagedDeviceLogCollectionRequestCount', 
               'Get-MgDeviceManagementManagedDeviceOverview', 
               'Get-MgDeviceManagementManagedDeviceUser', 
               'Get-MgDeviceManagementManagedDeviceWindowsProtectionState', 
               'Get-MgDeviceManagementManagedDeviceWindowsProtectionStateDetectedMalwareState', 
               'Get-MgDeviceManagementManagedDeviceWindowsProtectionStateDetectedMalwareStateCount', 
               'Get-MgDeviceManagementMobileAppTroubleshootingEvent', 
               'Get-MgDeviceManagementMobileAppTroubleshootingEventAppLogCollectionRequest', 
               'Get-MgDeviceManagementMobileAppTroubleshootingEventAppLogCollectionRequestCount', 
               'Get-MgDeviceManagementMobileAppTroubleshootingEventCount', 
               'Get-MgDeviceManagementNotificationMessageTemplate', 
               'Get-MgDeviceManagementNotificationMessageTemplateCount', 
               'Get-MgDeviceManagementNotificationMessageTemplateLocalizedNotificationMessage', 
               'Get-MgDeviceManagementNotificationMessageTemplateLocalizedNotificationMessageCount', 
               'Get-MgDeviceManagementSoftwareUpdateStatusSummary', 
               'Get-MgDeviceManagementTroubleshootingEvent', 
               'Get-MgDeviceManagementTroubleshootingEventCount', 
               'Get-MgDeviceManagementWindowsInformationProtectionAppLearningSummary', 
               'Get-MgDeviceManagementWindowsInformationProtectionAppLearningSummaryCount', 
               'Get-MgDeviceManagementWindowsInformationProtectionNetworkLearningSummary', 
               'Get-MgDeviceManagementWindowsInformationProtectionNetworkLearningSummaryCount', 
               'Get-MgDeviceManagementWindowsMalwareInformation', 
               'Get-MgDeviceManagementWindowsMalwareInformationCount', 
               'Get-MgDeviceManagementWindowsMalwareInformationDeviceMalwareState', 
               'Get-MgDeviceManagementWindowsMalwareInformationDeviceMalwareStateCount', 
               'Invoke-MgCleanDeviceManagementManagedDeviceWindowsDevice', 
               'Invoke-MgDownDeviceManagementManagedDeviceShut', 
               'Invoke-MgLogoutDeviceManagementManagedDeviceSharedAppleDeviceActiveUser', 
               'Invoke-MgRetireDeviceManagementManagedDevice', 
               'Invoke-MgScanDeviceManagementManagedDeviceWindowsDefender', 
               'Invoke-MgScheduleDeviceManagementDeviceCompliancePolicyActionForRule', 
               'Lock-MgDeviceManagementManagedDeviceRemote', 
               'New-MgAdminEdgeInternetExplorerModeSiteList', 
               'New-MgAdminEdgeInternetExplorerModeSiteListSharedCookie', 
               'New-MgAdminEdgeInternetExplorerModeSiteListSite', 
               'New-MgDeviceManagementDetectedApp', 
               'New-MgDeviceManagementDeviceCategory', 
               'New-MgDeviceManagementDeviceCompliancePolicy', 
               'New-MgDeviceManagementDeviceCompliancePolicyAssignment', 
               'New-MgDeviceManagementDeviceCompliancePolicyDeviceSettingStateSummary', 
               'New-MgDeviceManagementDeviceCompliancePolicyDeviceStatus', 
               'New-MgDeviceManagementDeviceCompliancePolicyScheduledActionForRule', 
               'New-MgDeviceManagementDeviceCompliancePolicyScheduledActionForRuleScheduledActionConfiguration', 
               'New-MgDeviceManagementDeviceCompliancePolicySettingStateSummary', 
               'New-MgDeviceManagementDeviceCompliancePolicySettingStateSummaryDeviceComplianceSettingState', 
               'New-MgDeviceManagementDeviceCompliancePolicyUserStatus', 
               'New-MgDeviceManagementDeviceConfiguration', 
               'New-MgDeviceManagementDeviceConfigurationAssignment', 
               'New-MgDeviceManagementDeviceConfigurationDeviceSettingStateSummary', 
               'New-MgDeviceManagementDeviceConfigurationDeviceStatus', 
               'New-MgDeviceManagementDeviceConfigurationUserStatus', 
               'New-MgDeviceManagementManagedDevice', 
               'New-MgDeviceManagementManagedDeviceCompliancePolicyState', 
               'New-MgDeviceManagementManagedDeviceConfigurationState', 
               'New-MgDeviceManagementManagedDeviceLogCollectionRequestDownloadUrl', 
               'New-MgDeviceManagementManagedDeviceWindowsProtectionStateDetectedMalwareState', 
               'New-MgDeviceManagementMobileAppTroubleshootingEvent', 
               'New-MgDeviceManagementMobileAppTroubleshootingEventAppLogCollectionRequest', 
               'New-MgDeviceManagementMobileAppTroubleshootingEventAppLogCollectionRequestDownloadUrl', 
               'New-MgDeviceManagementNotificationMessageTemplate', 
               'New-MgDeviceManagementNotificationMessageTemplateLocalizedNotificationMessage', 
               'New-MgDeviceManagementTroubleshootingEvent', 
               'New-MgDeviceManagementWindowsInformationProtectionAppLearningSummary', 
               'New-MgDeviceManagementWindowsInformationProtectionNetworkLearningSummary', 
               'New-MgDeviceManagementWindowsMalwareInformation', 
               'New-MgDeviceManagementWindowsMalwareInformationDeviceMalwareState', 
               'Publish-MgAdminEdgeInternetExplorerModeSiteList', 
               'Remove-MgAdminEdge', 'Remove-MgAdminEdgeInternetExplorerMode', 
               'Remove-MgAdminEdgeInternetExplorerModeSiteList', 
               'Remove-MgAdminEdgeInternetExplorerModeSiteListSharedCookie', 
               'Remove-MgAdminEdgeInternetExplorerModeSiteListSite', 
               'Remove-MgDeviceManagementDetectedApp', 
               'Remove-MgDeviceManagementDeviceCategory', 
               'Remove-MgDeviceManagementDeviceCompliancePolicy', 
               'Remove-MgDeviceManagementDeviceCompliancePolicyAssignment', 
               'Remove-MgDeviceManagementDeviceCompliancePolicyDeviceSettingStateSummary', 
               'Remove-MgDeviceManagementDeviceCompliancePolicyDeviceStateSummary', 
               'Remove-MgDeviceManagementDeviceCompliancePolicyDeviceStatus', 
               'Remove-MgDeviceManagementDeviceCompliancePolicyDeviceStatusOverview', 
               'Remove-MgDeviceManagementDeviceCompliancePolicyScheduledActionForRule', 
               'Remove-MgDeviceManagementDeviceCompliancePolicyScheduledActionForRuleScheduledActionConfiguration', 
               'Remove-MgDeviceManagementDeviceCompliancePolicySettingStateSummary', 
               'Remove-MgDeviceManagementDeviceCompliancePolicySettingStateSummaryDeviceComplianceSettingState', 
               'Remove-MgDeviceManagementDeviceCompliancePolicyUserStatus', 
               'Remove-MgDeviceManagementDeviceCompliancePolicyUserStatusOverview', 
               'Remove-MgDeviceManagementDeviceConfiguration', 
               'Remove-MgDeviceManagementDeviceConfigurationAssignment', 
               'Remove-MgDeviceManagementDeviceConfigurationDeviceSettingStateSummary', 
               'Remove-MgDeviceManagementDeviceConfigurationDeviceStateSummary', 
               'Remove-MgDeviceManagementDeviceConfigurationDeviceStatus', 
               'Remove-MgDeviceManagementDeviceConfigurationDeviceStatusOverview', 
               'Remove-MgDeviceManagementDeviceConfigurationUserStatus', 
               'Remove-MgDeviceManagementDeviceConfigurationUserStatusOverview', 
               'Remove-MgDeviceManagementManagedDevice', 
               'Remove-MgDeviceManagementManagedDeviceCategory', 
               'Remove-MgDeviceManagementManagedDeviceCategoryByRef', 
               'Remove-MgDeviceManagementManagedDeviceCompliancePolicyState', 
               'Remove-MgDeviceManagementManagedDeviceConfigurationState', 
               'Remove-MgDeviceManagementManagedDeviceLogCollectionRequest', 
               'Remove-MgDeviceManagementManagedDeviceUserFromSharedAppleDevice', 
               'Remove-MgDeviceManagementManagedDeviceWindowsProtectionState', 
               'Remove-MgDeviceManagementManagedDeviceWindowsProtectionStateDetectedMalwareState', 
               'Remove-MgDeviceManagementMobileAppTroubleshootingEvent', 
               'Remove-MgDeviceManagementMobileAppTroubleshootingEventAppLogCollectionRequest', 
               'Remove-MgDeviceManagementNotificationMessageTemplate', 
               'Remove-MgDeviceManagementNotificationMessageTemplateLocalizedNotificationMessage', 
               'Remove-MgDeviceManagementTroubleshootingEvent', 
               'Remove-MgDeviceManagementWindowsInformationProtectionAppLearningSummary', 
               'Remove-MgDeviceManagementWindowsInformationProtectionNetworkLearningSummary', 
               'Remove-MgDeviceManagementWindowsMalwareInformation', 
               'Remove-MgDeviceManagementWindowsMalwareInformationDeviceMalwareState', 
               'Request-MgDeviceManagementManagedDeviceRemoteAssistance', 
               'Reset-MgDeviceManagementManagedDevicePasscode', 
               'Restart-MgDeviceManagementManagedDeviceNow', 
               'Restore-MgDeviceManagementManagedDevicePasscode', 
               'Send-MgDeviceManagementNotificationMessageTemplateTestMessage', 
               'Set-MgDeviceManagementDeviceCompliancePolicy', 
               'Set-MgDeviceManagementDeviceConfiguration', 
               'Set-MgDeviceManagementManagedDeviceCategoryByRef', 
               'Skip-MgDeviceManagementManagedDeviceActivationLock', 
               'Sync-MgDeviceManagementManagedDevice', 'Update-MgAdminEdge', 
               'Update-MgAdminEdgeInternetExplorerMode', 
               'Update-MgAdminEdgeInternetExplorerModeSiteList', 
               'Update-MgAdminEdgeInternetExplorerModeSiteListSharedCookie', 
               'Update-MgAdminEdgeInternetExplorerModeSiteListSite', 
               'Update-MgDeviceManagement', 'Update-MgDeviceManagementDetectedApp', 
               'Update-MgDeviceManagementDeviceCategory', 
               'Update-MgDeviceManagementDeviceCompliancePolicy', 
               'Update-MgDeviceManagementDeviceCompliancePolicyAssignment', 
               'Update-MgDeviceManagementDeviceCompliancePolicyDeviceSettingStateSummary', 
               'Update-MgDeviceManagementDeviceCompliancePolicyDeviceStateSummary', 
               'Update-MgDeviceManagementDeviceCompliancePolicyDeviceStatus', 
               'Update-MgDeviceManagementDeviceCompliancePolicyDeviceStatusOverview', 
               'Update-MgDeviceManagementDeviceCompliancePolicyScheduledActionForRule', 
               'Update-MgDeviceManagementDeviceCompliancePolicyScheduledActionForRuleScheduledActionConfiguration', 
               'Update-MgDeviceManagementDeviceCompliancePolicySettingStateSummary', 
               'Update-MgDeviceManagementDeviceCompliancePolicySettingStateSummaryDeviceComplianceSettingState', 
               'Update-MgDeviceManagementDeviceCompliancePolicyUserStatus', 
               'Update-MgDeviceManagementDeviceCompliancePolicyUserStatusOverview', 
               'Update-MgDeviceManagementDeviceConfiguration', 
               'Update-MgDeviceManagementDeviceConfigurationAssignment', 
               'Update-MgDeviceManagementDeviceConfigurationDeviceSettingStateSummary', 
               'Update-MgDeviceManagementDeviceConfigurationDeviceStateSummary', 
               'Update-MgDeviceManagementDeviceConfigurationDeviceStatus', 
               'Update-MgDeviceManagementDeviceConfigurationDeviceStatusOverview', 
               'Update-MgDeviceManagementDeviceConfigurationUserStatus', 
               'Update-MgDeviceManagementDeviceConfigurationUserStatusOverview', 
               'Update-MgDeviceManagementManagedDevice', 
               'Update-MgDeviceManagementManagedDeviceCategory', 
               'Update-MgDeviceManagementManagedDeviceCompliancePolicyState', 
               'Update-MgDeviceManagementManagedDeviceConfigurationState', 
               'Update-MgDeviceManagementManagedDeviceLogCollectionRequest', 
               'Update-MgDeviceManagementManagedDeviceWindowsDeviceAccount', 
               'Update-MgDeviceManagementManagedDeviceWindowsProtectionState', 
               'Update-MgDeviceManagementManagedDeviceWindowsProtectionStateDetectedMalwareState', 
               'Update-MgDeviceManagementMobileAppTroubleshootingEvent', 
               'Update-MgDeviceManagementMobileAppTroubleshootingEventAppLogCollectionRequest', 
               'Update-MgDeviceManagementNotificationMessageTemplate', 
               'Update-MgDeviceManagementNotificationMessageTemplateLocalizedNotificationMessage', 
               'Update-MgDeviceManagementTroubleshootingEvent', 
               'Update-MgDeviceManagementWindowsInformationProtectionAppLearningSummary', 
               'Update-MgDeviceManagementWindowsInformationProtectionNetworkLearningSummary', 
               'Update-MgDeviceManagementWindowsMalwareInformation', 
               'Update-MgDeviceManagementWindowsMalwareInformationDeviceMalwareState'

# Cmdlets to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no cmdlets to export.
CmdletsToExport = @()

# Variables to export from this module
# VariablesToExport = @()

# Aliases to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no aliases to export.
AliasesToExport = '*'

# DSC resources to export from this module
# DscResourcesToExport = @()

# List of all modules packaged with this module
# ModuleList = @()

# List of all files packaged with this module
# FileList = @()

# Private data to pass to the module specified in RootModule/ModuleToProcess. This may also contain a PSData hashtable with additional module metadata used by PowerShell.
PrivateData = @{

    PSData = @{

        # Tags applied to this module. These help with module discovery in online galleries.
        Tags = 'Microsoft','Office365','Graph','PowerShell','PSModule','PSIncludes_Cmdlet'

        # A URL to the license for this module.
        LicenseUri = 'https://aka.ms/devservicesagreement'

        # A URL to the main website for this project.
        ProjectUri = 'https://github.com/microsoftgraph/msgraph-sdk-powershell'

        # A URL to an icon representing this module.
        IconUri = 'https://raw.githubusercontent.com/microsoftgraph/msgraph-sdk-powershell/dev/docs/images/graph_color256.png'

        # ReleaseNotes of this module
        ReleaseNotes = 'See https://aka.ms/GraphPowerShell-Release.'

        # Prerelease string of this module
        # Prerelease = ''

        # Flag to indicate whether the module requires explicit user acceptance for install/update/save
        # RequireLicenseAcceptance = $false

        # External dependent modules of this module
        # ExternalModuleDependencies = @()

    } # End of PSData hashtable

 } # End of PrivateData hashtable

# HelpInfo URI of this module
# HelpInfoURI = ''

# Default prefix for commands exported from this module. Override the default prefix using Import-Module -Prefix.
# DefaultCommandPrefix = ''

}


# SIG # Begin signature block
# MIIoKgYJKoZIhvcNAQcCoIIoGzCCKBcCAQExDzANBglghkgBZQMEAgEFADB5Bgor
# BgEEAYI3AgEEoGswaTA0BgorBgEEAYI3AgEeMCYCAwEAAAQQH8w7YFlLCE63JNLG
# KX7zUQIBAAIBAAIBAAIBAAIBADAxMA0GCWCGSAFlAwQCAQUABCDQcpdE3veFsKZz
# jPgN/igNsaGV5m73bw+qkGc/n4jvlKCCDXYwggX0MIID3KADAgECAhMzAAAEBGx0
# Bv9XKydyAAAAAAQEMA0GCSqGSIb3DQEBCwUAMH4xCzAJBgNVBAYTAlVTMRMwEQYD
# VQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNy
# b3NvZnQgQ29ycG9yYXRpb24xKDAmBgNVBAMTH01pY3Jvc29mdCBDb2RlIFNpZ25p
# bmcgUENBIDIwMTEwHhcNMjQwOTEyMjAxMTE0WhcNMjUwOTExMjAxMTE0WjB0MQsw
# CQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9u
# ZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMR4wHAYDVQQDExVNaWNy
# b3NvZnQgQ29ycG9yYXRpb24wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIB
# AQC0KDfaY50MDqsEGdlIzDHBd6CqIMRQWW9Af1LHDDTuFjfDsvna0nEuDSYJmNyz
# NB10jpbg0lhvkT1AzfX2TLITSXwS8D+mBzGCWMM/wTpciWBV/pbjSazbzoKvRrNo
# DV/u9omOM2Eawyo5JJJdNkM2d8qzkQ0bRuRd4HarmGunSouyb9NY7egWN5E5lUc3
# a2AROzAdHdYpObpCOdeAY2P5XqtJkk79aROpzw16wCjdSn8qMzCBzR7rvH2WVkvF
# HLIxZQET1yhPb6lRmpgBQNnzidHV2Ocxjc8wNiIDzgbDkmlx54QPfw7RwQi8p1fy
# 4byhBrTjv568x8NGv3gwb0RbAgMBAAGjggFzMIIBbzAfBgNVHSUEGDAWBgorBgEE
# AYI3TAgBBggrBgEFBQcDAzAdBgNVHQ4EFgQU8huhNbETDU+ZWllL4DNMPCijEU4w
# RQYDVR0RBD4wPKQ6MDgxHjAcBgNVBAsTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEW
# MBQGA1UEBRMNMjMwMDEyKzUwMjkyMzAfBgNVHSMEGDAWgBRIbmTlUAXTgqoXNzci
# tW2oynUClTBUBgNVHR8ETTBLMEmgR6BFhkNodHRwOi8vd3d3Lm1pY3Jvc29mdC5j
# b20vcGtpb3BzL2NybC9NaWNDb2RTaWdQQ0EyMDExXzIwMTEtMDctMDguY3JsMGEG
# CCsGAQUFBwEBBFUwUzBRBggrBgEFBQcwAoZFaHR0cDovL3d3dy5taWNyb3NvZnQu
# Y29tL3BraW9wcy9jZXJ0cy9NaWNDb2RTaWdQQ0EyMDExXzIwMTEtMDctMDguY3J0
# MAwGA1UdEwEB/wQCMAAwDQYJKoZIhvcNAQELBQADggIBAIjmD9IpQVvfB1QehvpC
# Ge7QeTQkKQ7j3bmDMjwSqFL4ri6ae9IFTdpywn5smmtSIyKYDn3/nHtaEn0X1NBj
# L5oP0BjAy1sqxD+uy35B+V8wv5GrxhMDJP8l2QjLtH/UglSTIhLqyt8bUAqVfyfp
# h4COMRvwwjTvChtCnUXXACuCXYHWalOoc0OU2oGN+mPJIJJxaNQc1sjBsMbGIWv3
# cmgSHkCEmrMv7yaidpePt6V+yPMik+eXw3IfZ5eNOiNgL1rZzgSJfTnvUqiaEQ0X
# dG1HbkDv9fv6CTq6m4Ty3IzLiwGSXYxRIXTxT4TYs5VxHy2uFjFXWVSL0J2ARTYL
# E4Oyl1wXDF1PX4bxg1yDMfKPHcE1Ijic5lx1KdK1SkaEJdto4hd++05J9Bf9TAmi
# u6EK6C9Oe5vRadroJCK26uCUI4zIjL/qG7mswW+qT0CW0gnR9JHkXCWNbo8ccMk1
# sJatmRoSAifbgzaYbUz8+lv+IXy5GFuAmLnNbGjacB3IMGpa+lbFgih57/fIhamq
# 5VhxgaEmn/UjWyr+cPiAFWuTVIpfsOjbEAww75wURNM1Imp9NJKye1O24EspEHmb
# DmqCUcq7NqkOKIG4PVm3hDDED/WQpzJDkvu4FrIbvyTGVU01vKsg4UfcdiZ0fQ+/
# V0hf8yrtq9CkB8iIuk5bBxuPMIIHejCCBWKgAwIBAgIKYQ6Q0gAAAAAAAzANBgkq
# hkiG9w0BAQsFADCBiDELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldhc2hpbmd0b24x
# EDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlv
# bjEyMDAGA1UEAxMpTWljcm9zb2Z0IFJvb3QgQ2VydGlmaWNhdGUgQXV0aG9yaXR5
# IDIwMTEwHhcNMTEwNzA4MjA1OTA5WhcNMjYwNzA4MjEwOTA5WjB+MQswCQYDVQQG
# EwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwG
# A1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSgwJgYDVQQDEx9NaWNyb3NvZnQg
# Q29kZSBTaWduaW5nIFBDQSAyMDExMIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIIC
# CgKCAgEAq/D6chAcLq3YbqqCEE00uvK2WCGfQhsqa+laUKq4BjgaBEm6f8MMHt03
# a8YS2AvwOMKZBrDIOdUBFDFC04kNeWSHfpRgJGyvnkmc6Whe0t+bU7IKLMOv2akr
# rnoJr9eWWcpgGgXpZnboMlImEi/nqwhQz7NEt13YxC4Ddato88tt8zpcoRb0Rrrg
# OGSsbmQ1eKagYw8t00CT+OPeBw3VXHmlSSnnDb6gE3e+lD3v++MrWhAfTVYoonpy
# 4BI6t0le2O3tQ5GD2Xuye4Yb2T6xjF3oiU+EGvKhL1nkkDstrjNYxbc+/jLTswM9
# sbKvkjh+0p2ALPVOVpEhNSXDOW5kf1O6nA+tGSOEy/S6A4aN91/w0FK/jJSHvMAh
# dCVfGCi2zCcoOCWYOUo2z3yxkq4cI6epZuxhH2rhKEmdX4jiJV3TIUs+UsS1Vz8k
# A/DRelsv1SPjcF0PUUZ3s/gA4bysAoJf28AVs70b1FVL5zmhD+kjSbwYuER8ReTB
# w3J64HLnJN+/RpnF78IcV9uDjexNSTCnq47f7Fufr/zdsGbiwZeBe+3W7UvnSSmn
# Eyimp31ngOaKYnhfsi+E11ecXL93KCjx7W3DKI8sj0A3T8HhhUSJxAlMxdSlQy90
# lfdu+HggWCwTXWCVmj5PM4TasIgX3p5O9JawvEagbJjS4NaIjAsCAwEAAaOCAe0w
# ggHpMBAGCSsGAQQBgjcVAQQDAgEAMB0GA1UdDgQWBBRIbmTlUAXTgqoXNzcitW2o
# ynUClTAZBgkrBgEEAYI3FAIEDB4KAFMAdQBiAEMAQTALBgNVHQ8EBAMCAYYwDwYD
# VR0TAQH/BAUwAwEB/zAfBgNVHSMEGDAWgBRyLToCMZBDuRQFTuHqp8cx0SOJNDBa
# BgNVHR8EUzBRME+gTaBLhklodHRwOi8vY3JsLm1pY3Jvc29mdC5jb20vcGtpL2Ny
# bC9wcm9kdWN0cy9NaWNSb29DZXJBdXQyMDExXzIwMTFfMDNfMjIuY3JsMF4GCCsG
# AQUFBwEBBFIwUDBOBggrBgEFBQcwAoZCaHR0cDovL3d3dy5taWNyb3NvZnQuY29t
# L3BraS9jZXJ0cy9NaWNSb29DZXJBdXQyMDExXzIwMTFfMDNfMjIuY3J0MIGfBgNV
# HSAEgZcwgZQwgZEGCSsGAQQBgjcuAzCBgzA/BggrBgEFBQcCARYzaHR0cDovL3d3
# dy5taWNyb3NvZnQuY29tL3BraW9wcy9kb2NzL3ByaW1hcnljcHMuaHRtMEAGCCsG
# AQUFBwICMDQeMiAdAEwAZQBnAGEAbABfAHAAbwBsAGkAYwB5AF8AcwB0AGEAdABl
# AG0AZQBuAHQALiAdMA0GCSqGSIb3DQEBCwUAA4ICAQBn8oalmOBUeRou09h0ZyKb
# C5YR4WOSmUKWfdJ5DJDBZV8uLD74w3LRbYP+vj/oCso7v0epo/Np22O/IjWll11l
# hJB9i0ZQVdgMknzSGksc8zxCi1LQsP1r4z4HLimb5j0bpdS1HXeUOeLpZMlEPXh6
# I/MTfaaQdION9MsmAkYqwooQu6SpBQyb7Wj6aC6VoCo/KmtYSWMfCWluWpiW5IP0
# wI/zRive/DvQvTXvbiWu5a8n7dDd8w6vmSiXmE0OPQvyCInWH8MyGOLwxS3OW560
# STkKxgrCxq2u5bLZ2xWIUUVYODJxJxp/sfQn+N4sOiBpmLJZiWhub6e3dMNABQam
# ASooPoI/E01mC8CzTfXhj38cbxV9Rad25UAqZaPDXVJihsMdYzaXht/a8/jyFqGa
# J+HNpZfQ7l1jQeNbB5yHPgZ3BtEGsXUfFL5hYbXw3MYbBL7fQccOKO7eZS/sl/ah
# XJbYANahRr1Z85elCUtIEJmAH9AAKcWxm6U/RXceNcbSoqKfenoi+kiVH6v7RyOA
# 9Z74v2u3S5fi63V4GuzqN5l5GEv/1rMjaHXmr/r8i+sLgOppO6/8MO0ETI7f33Vt
# Y5E90Z1WTk+/gFcioXgRMiF670EKsT/7qMykXcGhiJtXcVZOSEXAQsmbdlsKgEhr
# /Xmfwb1tbWrJUnMTDXpQzTGCGgowghoGAgEBMIGVMH4xCzAJBgNVBAYTAlVTMRMw
# EQYDVQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVN
# aWNyb3NvZnQgQ29ycG9yYXRpb24xKDAmBgNVBAMTH01pY3Jvc29mdCBDb2RlIFNp
# Z25pbmcgUENBIDIwMTECEzMAAAQEbHQG/1crJ3IAAAAABAQwDQYJYIZIAWUDBAIB
# BQCgga4wGQYJKoZIhvcNAQkDMQwGCisGAQQBgjcCAQQwHAYKKwYBBAGCNwIBCzEO
# MAwGCisGAQQBgjcCARUwLwYJKoZIhvcNAQkEMSIEIFodaY5SqXJ/kGV0GGP7NNlS
# Vt3YPwXAJs3nMCb141cFMEIGCisGAQQBgjcCAQwxNDAyoBSAEgBNAGkAYwByAG8A
# cwBvAGYAdKEagBhodHRwOi8vd3d3Lm1pY3Jvc29mdC5jb20wDQYJKoZIhvcNAQEB
# BQAEggEAptkda+wyjXprxhdzTmCju98L6BVIESOmJ4aHf3WkLnb6RXWqIu84ILKb
# yI5Kk0qY6QyxHzrKALjZNcejcWyCm9dRlouExf9/MT3lxAVXWqTtfSOPN7ABCnXL
# U8jg2CYQvVErJ0wHJLhVg7dekE2AppwMdpR1L8F3qFtbMWFJKHIvqIMomVLGZWbi
# r+SQzRXjgo8nQ989MPKnGElj55zPeXpjikLfe3UrHskc53I+tIZUfmYen9YTXpyP
# NagBoh5sSZKT8jwFRnzVSWjhl2U3s7sMrELnbUOGtHGYeaD5Qzq6pHaYCHjyV241
# NqI+T/JsOGuuUoaWMr0yus9473QDlqGCF5QwgheQBgorBgEEAYI3AwMBMYIXgDCC
# F3wGCSqGSIb3DQEHAqCCF20wghdpAgEDMQ8wDQYJYIZIAWUDBAIBBQAwggFSBgsq
# hkiG9w0BCRABBKCCAUEEggE9MIIBOQIBAQYKKwYBBAGEWQoDATAxMA0GCWCGSAFl
# AwQCAQUABCDqNz+gE1zNgz28MoQP8WCAE/JvZX6XLARe6l38vh3QqQIGaEsqPulb
# GBMyMDI1MDcwOTExMDcyNy4wODNaMASAAgH0oIHRpIHOMIHLMQswCQYDVQQGEwJV
# UzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwGA1UE
# ChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSUwIwYDVQQLExxNaWNyb3NvZnQgQW1l
# cmljYSBPcGVyYXRpb25zMScwJQYDVQQLEx5uU2hpZWxkIFRTUyBFU046MzcwMy0w
# NUUwLUQ5NDcxJTAjBgNVBAMTHE1pY3Jvc29mdCBUaW1lLVN0YW1wIFNlcnZpY2Wg
# ghHqMIIHIDCCBQigAwIBAgITMwAAAgpHshTZ7rKzDwABAAACCjANBgkqhkiG9w0B
# AQsFADB8MQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UE
# BxMHUmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSYwJAYD
# VQQDEx1NaWNyb3NvZnQgVGltZS1TdGFtcCBQQ0EgMjAxMDAeFw0yNTAxMzAxOTQy
# NTdaFw0yNjA0MjIxOTQyNTdaMIHLMQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2Fz
# aGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENv
# cnBvcmF0aW9uMSUwIwYDVQQLExxNaWNyb3NvZnQgQW1lcmljYSBPcGVyYXRpb25z
# MScwJQYDVQQLEx5uU2hpZWxkIFRTUyBFU046MzcwMy0wNUUwLUQ5NDcxJTAjBgNV
# BAMTHE1pY3Jvc29mdCBUaW1lLVN0YW1wIFNlcnZpY2UwggIiMA0GCSqGSIb3DQEB
# AQUAA4ICDwAwggIKAoICAQCy7NzwEpb7BpwAk9LJ00Xq30TcTjcwNZ80TxAtAbhS
# aJ2kwnJA1Au/Do9/fEBjAHv6Mmtt3fmPDeIJnQ7VBeIq8RcfjcjrbPIg3wA5v5MQ
# flPNSBNOvcXRP+fZnAy0ELDzfnJHnCkZNsQUZ7GF7LxULTKOYY2YJw4TrmcHohkY
# 6DjCZyxhqmGQwwdbjoPWRbYu/ozFem/yfJPyjVBql1068bcVh58A8c5CD6TWN/L3
# u+Ny+7O8+Dver6qBT44Ey7pfPZMZ1Hi7yvCLv5LGzSB6o2OD5GIZy7z4kh8UYHdz
# jn9Wx+QZ2233SJQKtZhpI7uHf3oMTg0zanQfz7mgudefmGBrQEg1ox3n+3Tizh0D
# 9zVmNQP9sFjsPQtNGZ9ID9H8A+kFInx4mrSxA2SyGMOQcxlGM30ktIKM3iqCuFEU
# 9CHVMpN94/1fl4T6PonJ+/oWJqFlatYuMKv2Z8uiprnFcAxCpOsDIVBO9K1vHeAM
# iQQUlcE9CD536I1YLnmO2qHagPPmXhdOGrHUnCUtop21elukHh75q/5zH+OnNekp
# 5udpjQNZCviYAZdHsLnkU0NfUAr6r1UqDcSq1yf5RiwimB8SjsdmHll4gPjmqVi0
# /rmnM1oAEQm3PyWcTQQibYLiuKN7Y4io5bJTVwm+vRRbpJ5UL/D33C//7qnHbeoW
# BQIDAQABo4IBSTCCAUUwHQYDVR0OBBYEFAKvF0EEj4AyPfY8W/qrsAvftZwkMB8G
# A1UdIwQYMBaAFJ+nFV0AXmJdg/Tl0mWnG1M1GelyMF8GA1UdHwRYMFYwVKBSoFCG
# Tmh0dHA6Ly93d3cubWljcm9zb2Z0LmNvbS9wa2lvcHMvY3JsL01pY3Jvc29mdCUy
# MFRpbWUtU3RhbXAlMjBQQ0ElMjAyMDEwKDEpLmNybDBsBggrBgEFBQcBAQRgMF4w
# XAYIKwYBBQUHMAKGUGh0dHA6Ly93d3cubWljcm9zb2Z0LmNvbS9wa2lvcHMvY2Vy
# dHMvTWljcm9zb2Z0JTIwVGltZS1TdGFtcCUyMFBDQSUyMDIwMTAoMSkuY3J0MAwG
# A1UdEwEB/wQCMAAwFgYDVR0lAQH/BAwwCgYIKwYBBQUHAwgwDgYDVR0PAQH/BAQD
# AgeAMA0GCSqGSIb3DQEBCwUAA4ICAQCwk3PW0CyjOaqXCMOusTde7ep2CwP/xV1J
# 3o9KAiKSdq8a2UR5RCHYhnJseemweMUH2kNefpnAh2Bn8H2opDztDJkj8OYRd/KQ
# ysE12NwaY3KOwAW8Rg8OdXv5fUZIsOWgprkCQM0VoFHdXYExkJN3EzBbUCUw3yb4
# gAFPK56T+6cPpI8MJLJCQXHNMgti2QZhX9KkfRAffFYMFcpsbI+oziC5Brrk3361
# cJFHhgEJR0J42nqZTGSgUpDGHSZARGqNcAV5h+OQDLeF2p3URx/P6McUg1nJ2gMP
# YBsD+bwd9B0c/XIZ9Mt3ujlELPpkijjCdSZxhzu2M3SZWJr57uY+FC+LspvIOH1O
# pofanh3JGDosNcAEu9yUMWKsEBMngD6VWQSQYZ6X9F80zCoeZwTq0i9AujnYzzx5
# W2fEgZejRu6K1GCASmztNlYJlACjqafWRofTqkJhV/J2v97X3ruDvfpuOuQoUtVA
# wXrDsG2NOBuvVso5KdW54hBSsz/4+ORB4qLnq4/GNtajUHorKRKHGOgFo8DKaXG+
# UNANwhGNxHbILSa59PxExMgCjBRP3828yGKsquSEzzLNWnz5af9ZmeH4809fwItt
# I41JkuiY9X6hmMmLYv8OY34vvOK+zyxkS+9BULVAP6gt+yaHaBlrln8Gi4/dBr2y
# 6Srr/56g0DCCB3EwggVZoAMCAQICEzMAAAAVxedrngKbSZkAAAAAABUwDQYJKoZI
# hvcNAQELBQAwgYgxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNoaW5ndG9uMRAw
# DgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNyb3NvZnQgQ29ycG9yYXRpb24x
# MjAwBgNVBAMTKU1pY3Jvc29mdCBSb290IENlcnRpZmljYXRlIEF1dGhvcml0eSAy
# MDEwMB4XDTIxMDkzMDE4MjIyNVoXDTMwMDkzMDE4MzIyNVowfDELMAkGA1UEBhMC
# VVMxEzARBgNVBAgTCldhc2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNV
# BAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEmMCQGA1UEAxMdTWljcm9zb2Z0IFRp
# bWUtU3RhbXAgUENBIDIwMTAwggIiMA0GCSqGSIb3DQEBAQUAA4ICDwAwggIKAoIC
# AQDk4aZM57RyIQt5osvXJHm9DtWC0/3unAcH0qlsTnXIyjVX9gF/bErg4r25Phdg
# M/9cT8dm95VTcVrifkpa/rg2Z4VGIwy1jRPPdzLAEBjoYH1qUoNEt6aORmsHFPPF
# dvWGUNzBRMhxXFExN6AKOG6N7dcP2CZTfDlhAnrEqv1yaa8dq6z2Nr41JmTamDu6
# GnszrYBbfowQHJ1S/rboYiXcag/PXfT+jlPP1uyFVk3v3byNpOORj7I5LFGc6XBp
# Dco2LXCOMcg1KL3jtIckw+DJj361VI/c+gVVmG1oO5pGve2krnopN6zL64NF50Zu
# yjLVwIYwXE8s4mKyzbnijYjklqwBSru+cakXW2dg3viSkR4dPf0gz3N9QZpGdc3E
# XzTdEonW/aUgfX782Z5F37ZyL9t9X4C626p+Nuw2TPYrbqgSUei/BQOj0XOmTTd0
# lBw0gg/wEPK3Rxjtp+iZfD9M269ewvPV2HM9Q07BMzlMjgK8QmguEOqEUUbi0b1q
# GFphAXPKZ6Je1yh2AuIzGHLXpyDwwvoSCtdjbwzJNmSLW6CmgyFdXzB0kZSU2LlQ
# +QuJYfM2BjUYhEfb3BvR/bLUHMVr9lxSUV0S2yW6r1AFemzFER1y7435UsSFF5PA
# PBXbGjfHCBUYP3irRbb1Hode2o+eFnJpxq57t7c+auIurQIDAQABo4IB3TCCAdkw
# EgYJKwYBBAGCNxUBBAUCAwEAATAjBgkrBgEEAYI3FQIEFgQUKqdS/mTEmr6CkTxG
# NSnPEP8vBO4wHQYDVR0OBBYEFJ+nFV0AXmJdg/Tl0mWnG1M1GelyMFwGA1UdIARV
# MFMwUQYMKwYBBAGCN0yDfQEBMEEwPwYIKwYBBQUHAgEWM2h0dHA6Ly93d3cubWlj
# cm9zb2Z0LmNvbS9wa2lvcHMvRG9jcy9SZXBvc2l0b3J5Lmh0bTATBgNVHSUEDDAK
# BggrBgEFBQcDCDAZBgkrBgEEAYI3FAIEDB4KAFMAdQBiAEMAQTALBgNVHQ8EBAMC
# AYYwDwYDVR0TAQH/BAUwAwEB/zAfBgNVHSMEGDAWgBTV9lbLj+iiXGJo0T2UkFvX
# zpoYxDBWBgNVHR8ETzBNMEugSaBHhkVodHRwOi8vY3JsLm1pY3Jvc29mdC5jb20v
# cGtpL2NybC9wcm9kdWN0cy9NaWNSb29DZXJBdXRfMjAxMC0wNi0yMy5jcmwwWgYI
# KwYBBQUHAQEETjBMMEoGCCsGAQUFBzAChj5odHRwOi8vd3d3Lm1pY3Jvc29mdC5j
# b20vcGtpL2NlcnRzL01pY1Jvb0NlckF1dF8yMDEwLTA2LTIzLmNydDANBgkqhkiG
# 9w0BAQsFAAOCAgEAnVV9/Cqt4SwfZwExJFvhnnJL/Klv6lwUtj5OR2R4sQaTlz0x
# M7U518JxNj/aZGx80HU5bbsPMeTCj/ts0aGUGCLu6WZnOlNN3Zi6th542DYunKmC
# VgADsAW+iehp4LoJ7nvfam++Kctu2D9IdQHZGN5tggz1bSNU5HhTdSRXud2f8449
# xvNo32X2pFaq95W2KFUn0CS9QKC/GbYSEhFdPSfgQJY4rPf5KYnDvBewVIVCs/wM
# nosZiefwC2qBwoEZQhlSdYo2wh3DYXMuLGt7bj8sCXgU6ZGyqVvfSaN0DLzskYDS
# PeZKPmY7T7uG+jIa2Zb0j/aRAfbOxnT99kxybxCrdTDFNLB62FD+CljdQDzHVG2d
# Y3RILLFORy3BFARxv2T5JL5zbcqOCb2zAVdJVGTZc9d/HltEAY5aGZFrDZ+kKNxn
# GSgkujhLmm77IVRrakURR6nxt67I6IleT53S0Ex2tVdUCbFpAUR+fKFhbHP+Crvs
# QWY9af3LwUFJfn6Tvsv4O+S3Fb+0zj6lMVGEvL8CwYKiexcdFYmNcP7ntdAoGokL
# jzbaukz5m/8K6TT4JDVnK+ANuOaMmdbhIurwJ0I9JZTmdHRbatGePu1+oDEzfbzL
# 6Xu/OHBE0ZDxyKs6ijoIYn/ZcGNTTY3ugm2lBRDBcQZqELQdVTNYs6FwZvKhggNN
# MIICNQIBATCB+aGB0aSBzjCByzELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldhc2hp
# bmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBDb3Jw
# b3JhdGlvbjElMCMGA1UECxMcTWljcm9zb2Z0IEFtZXJpY2EgT3BlcmF0aW9uczEn
# MCUGA1UECxMeblNoaWVsZCBUU1MgRVNOOjM3MDMtMDVFMC1EOTQ3MSUwIwYDVQQD
# ExxNaWNyb3NvZnQgVGltZS1TdGFtcCBTZXJ2aWNloiMKAQEwBwYFKw4DAhoDFQDR
# AMVJlA6bKq93Vnu3UkJgm5HlYaCBgzCBgKR+MHwxCzAJBgNVBAYTAlVTMRMwEQYD
# VQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNy
# b3NvZnQgQ29ycG9yYXRpb24xJjAkBgNVBAMTHU1pY3Jvc29mdCBUaW1lLVN0YW1w
# IFBDQSAyMDEwMA0GCSqGSIb3DQEBCwUAAgUA7BiWNjAiGA8yMDI1MDcwOTA3MTgx
# NFoYDzIwMjUwNzEwMDcxODE0WjB0MDoGCisGAQQBhFkKBAExLDAqMAoCBQDsGJY2
# AgEAMAcCAQACAiIlMAcCAQACAhK3MAoCBQDsGee2AgEAMDYGCisGAQQBhFkKBAIx
# KDAmMAwGCisGAQQBhFkKAwKgCjAIAgEAAgMHoSChCjAIAgEAAgMBhqAwDQYJKoZI
# hvcNAQELBQADggEBAFPdUSsscs+owEO1xpkcvunTcGpk1X5o+z3nHztEI2X23rxu
# 1KgvXGKJVzXiaiigOGNxXQTjPod6PhmBZb+Q8uTCh9erfId8UfNKSNp7ylm2IQGE
# Z/ER7Ee+AeBkOhv9OA1EYsKHpn5VTAk/+OQl5ctWvO/jpGxjbYsDv/+CWJyfMVvM
# sH3SOPokjpIDMGbjTfzzYwZz9XiU67yQsLb9aLzNVBjXeAiGIvbKBparccE+mg0Q
# RxMsRSn8camz+K4HIkN+AluvZH0ucF/ikUych28CQIjXmwcbwaXzyQV3efGBlaU+
# ZmmKCzIcC9qdOJfzPGd1dsB6pgRh/eT3wx8B8YAxggQNMIIECQIBATCBkzB8MQsw
# CQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9u
# ZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSYwJAYDVQQDEx1NaWNy
# b3NvZnQgVGltZS1TdGFtcCBQQ0EgMjAxMAITMwAAAgpHshTZ7rKzDwABAAACCjAN
# BglghkgBZQMEAgEFAKCCAUowGgYJKoZIhvcNAQkDMQ0GCyqGSIb3DQEJEAEEMC8G
# CSqGSIb3DQEJBDEiBCCTmjRYVFpImbS2B7wMm8rJhA74ukxL/+xjGhGGF/VIoTCB
# +gYLKoZIhvcNAQkQAi8xgeowgecwgeQwgb0EIE2ay/y0epK/X3Z03KTcloqE8u9I
# XRtdO7Mex0hw9+SaMIGYMIGApH4wfDELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldh
# c2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBD
# b3Jwb3JhdGlvbjEmMCQGA1UEAxMdTWljcm9zb2Z0IFRpbWUtU3RhbXAgUENBIDIw
# MTACEzMAAAIKR7IU2e6ysw8AAQAAAgowIgQgAXB8Dbb3zDaQOfKpsCPX/+B/UUpi
# yS8VhOnRN7QjpxIwDQYJKoZIhvcNAQELBQAEggIAex9CWxzw2fBZHxp65P5YMWs3
# X/vBnttcM49OaibnDbIvNdHLaxpmfoKaivl27LNtrg6aKdsN8QEz5tCmQ6hHemHw
# 2W3xyhIXwOAmQQEiOZcZB0SsrWIqFWSEKU3zmmBr9aoK16zBZvoXXVCocX99vZ+b
# qUVc6j0cjyO8TzMbI7cLay7i2mLDwktgBZAzkAG3qL/20g6qfmd5jlCiuP5AclB6
# YRLLys0Qiqe73RdHYaIcDBL7G/iXLvoqUIQGQNlzLwvaDB2uw3Ztrh4o6YyejO7z
# Bt5Fzjy2PeqwN9qG4xBL0giEPEZtoPiAiMyqEVg8sWUVIhwJZFlkhgUQrxWksBcn
# EslaEl37n+qWZ+ptvgVecxdzTQnw/H7KUeJ7As24AA0CQzeP9CdS+pWuid/6Zv4V
# DCYGrknl+qZrzrZsWLfiy0nzm81F91ty/hH8RFDK4ZPhcrOSNVZsssamSXLvb9kx
# /NXu1Vy5V7+zVf8iAdFH806b/Gf1pMXMbe1KYMNatT43/H7tbxuZCLaee+ESvCS8
# wrv5swiIwRo3SdTK7Zk3+3PaJjH39SBj4uH19MQYbsIoTQSnFwvdW/uP+yOkeKGh
# G0tilLcQLpyKbS3Rg1e/SuKyXA9D+fvo44Q+u7kro+f/8PJBS4NFVFqVHvQ6OZkP
# g/NdsaexK08x9Jjioeg=
# SIG # End signature block
