#
# Module manifest for module 'Microsoft.Graph.Security'
#
# Generated by: Microsoft Corporation
#
# Generated on: 7/9/2025
#

@{

# Script module or binary module file associated with this manifest.
RootModule = './Microsoft.Graph.Security.psm1'

# Version number of this module.
ModuleVersion = '2.29.0'

# Supported PSEditions
CompatiblePSEditions = 'Core', 'Desktop'

# ID used to uniquely identify this module
GUID = '06b0769e-2c63-4d60-9fb4-9ca0ec87e0d7'

# Author of this module
Author = 'Microsoft Corporation'

# Company or vendor of this module
CompanyName = 'Microsoft Corporation'

# Copyright statement for this module
Copyright = 'Microsoft Corporation. All rights reserved.'

# Description of the functionality provided by this module
Description = 'Microsoft Graph PowerShell Cmdlets'

# Minimum version of the PowerShell engine required by this module
PowerShellVersion = '5.1'

# Name of the PowerShell host required by this module
# PowerShellHostName = ''

# Minimum version of the PowerShell host required by this module
# PowerShellHostVersion = ''

# Minimum version of Microsoft .NET Framework required by this module. This prerequisite is valid for the PowerShell Desktop edition only.
DotNetFrameworkVersion = '4.7.2'

# Minimum version of the common language runtime (CLR) required by this module. This prerequisite is valid for the PowerShell Desktop edition only.
# ClrVersion = ''

# Processor architecture (None, X86, Amd64) required by this module
# ProcessorArchitecture = ''

# Modules that must be imported into the global environment prior to importing this module
RequiredModules = @(@{ModuleName = 'Microsoft.Graph.Authentication'; RequiredVersion = '2.29.0'; })

# Assemblies that must be loaded prior to importing this module
RequiredAssemblies = './bin/Microsoft.Graph.Security.private.dll'

# Script files (.ps1) that are run in the caller's environment prior to importing this module.
# ScriptsToProcess = @()

# Type files (.ps1xml) to be loaded when importing this module
# TypesToProcess = @()

# Format files (.ps1xml) to be loaded when importing this module
FormatsToProcess = './Microsoft.Graph.Security.format.ps1xml'

# Modules to import as nested modules of the module specified in RootModule/ModuleToProcess
# NestedModules = @()

# Functions to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no functions to export.
FunctionsToExport = 'Add-MgSecurityCaseEdiscoveryCaseCustodianHold', 
               'Add-MgSecurityCaseEdiscoveryCaseNoncustodialDataSourceHold', 
               'Add-MgSecurityCaseEdiscoveryCaseReviewSetQueryTag', 
               'Add-MgSecurityCaseEdiscoveryCaseReviewSetToReviewSet', 
               'Clear-MgSecurityCaseEdiscoveryCaseSearchData', 
               'Close-MgSecurityCaseEdiscoveryCase', 
               'Export-MgSecurityCaseEdiscoveryCaseReviewSet', 
               'Export-MgSecurityCaseEdiscoveryCaseReviewSetQuery', 
               'Export-MgSecurityCaseEdiscoveryCaseSearchReport', 
               'Export-MgSecurityCaseEdiscoveryCaseSearchResult', 
               'Get-MgSecurityAlert', 'Get-MgSecurityAlertCount', 
               'Get-MgSecurityAlertV2', 'Get-MgSecurityAlertV2Count', 
               'Get-MgSecurityAttackSimulation', 
               'Get-MgSecurityAttackSimulationAutomation', 
               'Get-MgSecurityAttackSimulationAutomationCount', 
               'Get-MgSecurityAttackSimulationAutomationRun', 
               'Get-MgSecurityAttackSimulationAutomationRunCount', 
               'Get-MgSecurityAttackSimulationCount', 
               'Get-MgSecurityAttackSimulationEndUserNotification', 
               'Get-MgSecurityAttackSimulationEndUserNotificationCount', 
               'Get-MgSecurityAttackSimulationEndUserNotificationDetail', 
               'Get-MgSecurityAttackSimulationEndUserNotificationDetailCount', 
               'Get-MgSecurityAttackSimulationLandingPage', 
               'Get-MgSecurityAttackSimulationLandingPageCount', 
               'Get-MgSecurityAttackSimulationLandingPageDetail', 
               'Get-MgSecurityAttackSimulationLandingPageDetailCount', 
               'Get-MgSecurityAttackSimulationLoginPage', 
               'Get-MgSecurityAttackSimulationLoginPageCount', 
               'Get-MgSecurityAttackSimulationOperation', 
               'Get-MgSecurityAttackSimulationOperationCount', 
               'Get-MgSecurityAttackSimulationPayload', 
               'Get-MgSecurityAttackSimulationPayloadCount', 
               'Get-MgSecurityAttackSimulationTraining', 
               'Get-MgSecurityAttackSimulationTrainingCount', 
               'Get-MgSecurityAttackSimulationTrainingLanguageDetail', 
               'Get-MgSecurityAttackSimulationTrainingLanguageDetailCount', 
               'Get-MgSecurityCase', 'Get-MgSecurityCaseEdiscoveryCase', 
               'Get-MgSecurityCaseEdiscoveryCaseCount', 
               'Get-MgSecurityCaseEdiscoveryCaseCustodian', 
               'Get-MgSecurityCaseEdiscoveryCaseCustodianCount', 
               'Get-MgSecurityCaseEdiscoveryCaseCustodianLastIndexOperation', 
               'Get-MgSecurityCaseEdiscoveryCaseCustodianSiteSource', 
               'Get-MgSecurityCaseEdiscoveryCaseCustodianSiteSourceCount', 
               'Get-MgSecurityCaseEdiscoveryCaseCustodianSiteSourceSite', 
               'Get-MgSecurityCaseEdiscoveryCaseCustodianUnifiedGroupSource', 
               'Get-MgSecurityCaseEdiscoveryCaseCustodianUnifiedGroupSourceCount', 
               'Get-MgSecurityCaseEdiscoveryCaseCustodianUnifiedGroupSourceGroup', 
               'Get-MgSecurityCaseEdiscoveryCaseCustodianUnifiedGroupSourceGroupServiceProvisioningError', 
               'Get-MgSecurityCaseEdiscoveryCaseCustodianUnifiedGroupSourceGroupServiceProvisioningErrorCount', 
               'Get-MgSecurityCaseEdiscoveryCaseCustodianUserSource', 
               'Get-MgSecurityCaseEdiscoveryCaseCustodianUserSourceCount', 
               'Get-MgSecurityCaseEdiscoveryCaseNoncustodialDataSource', 
               'Get-MgSecurityCaseEdiscoveryCaseNoncustodialDataSourceCount', 
               'Get-MgSecurityCaseEdiscoveryCaseNoncustodialDataSourceLastIndexOperation', 
               'Get-MgSecurityCaseEdiscoveryCaseOperation', 
               'Get-MgSecurityCaseEdiscoveryCaseOperationCount', 
               'Get-MgSecurityCaseEdiscoveryCaseReviewSet', 
               'Get-MgSecurityCaseEdiscoveryCaseReviewSetCount', 
               'Get-MgSecurityCaseEdiscoveryCaseReviewSetQuery', 
               'Get-MgSecurityCaseEdiscoveryCaseReviewSetQueryCount', 
               'Get-MgSecurityCaseEdiscoveryCaseSearch', 
               'Get-MgSecurityCaseEdiscoveryCaseSearchAdditionalSource', 
               'Get-MgSecurityCaseEdiscoveryCaseSearchAdditionalSourceCount', 
               'Get-MgSecurityCaseEdiscoveryCaseSearchAddToReviewSetOperation', 
               'Get-MgSecurityCaseEdiscoveryCaseSearchCount', 
               'Get-MgSecurityCaseEdiscoveryCaseSearchCustodianSource', 
               'Get-MgSecurityCaseEdiscoveryCaseSearchCustodianSourceCount', 
               'Get-MgSecurityCaseEdiscoveryCaseSearchLastEstimateStatisticsOperation', 
               'Get-MgSecurityCaseEdiscoveryCaseSearchNoncustodialSource', 
               'Get-MgSecurityCaseEdiscoveryCaseSearchNoncustodialSourceCount', 
               'Get-MgSecurityCaseEdiscoveryCaseSetting', 
               'Get-MgSecurityCaseEdiscoveryCaseTag', 
               'Get-MgSecurityCaseEdiscoveryCaseTagChildTag', 
               'Get-MgSecurityCaseEdiscoveryCaseTagChildTagCount', 
               'Get-MgSecurityCaseEdiscoveryCaseTagCount', 
               'Get-MgSecurityCaseEdiscoveryCaseTagParent', 
               'Get-MgSecurityDataSecurityAndGovernance', 
               'Get-MgSecurityDataSecurityAndGovernanceProtectionScope', 
               'Get-MgSecurityIdentity', 'Get-MgSecurityIdentityHealthIssue', 
               'Get-MgSecurityIdentityHealthIssueCount', 
               'Get-MgSecurityIdentitySensor', 'Get-MgSecurityIdentitySensorCount', 
               'Get-MgSecurityIdentitySensorDeploymentAccessKey', 
               'Get-MgSecurityIdentitySensorDeploymentPackageUri', 
               'Get-MgSecurityIdentitySensorHealthIssue', 
               'Get-MgSecurityIdentitySensorHealthIssueCount', 
               'Get-MgSecurityIncident', 'Get-MgSecurityIncidentAlert', 
               'Get-MgSecurityIncidentAlertCommentCount', 
               'Get-MgSecurityIncidentAlertCount', 'Get-MgSecurityIncidentCount', 
               'Get-MgSecurityLabel', 'Get-MgSecurityLabelAuthority', 
               'Get-MgSecurityLabelAuthorityCount', 'Get-MgSecurityLabelCategory', 
               'Get-MgSecurityLabelCategoryCount', 
               'Get-MgSecurityLabelCategorySubcategory', 
               'Get-MgSecurityLabelCategorySubcategoryCount', 
               'Get-MgSecurityLabelCitation', 'Get-MgSecurityLabelCitationCount', 
               'Get-MgSecurityLabelDepartment', 
               'Get-MgSecurityLabelDepartmentCount', 
               'Get-MgSecurityLabelFilePlanReference', 
               'Get-MgSecurityLabelFilePlanReferenceCount', 
               'Get-MgSecurityLabelRetentionEventType', 
               'Get-MgSecurityLabelRetentionLabel', 
               'Get-MgSecurityLabelRetentionLabelCount', 
               'Get-MgSecurityLabelRetentionLabelDescriptor', 
               'Get-MgSecurityLabelRetentionLabelDescriptorAuthorityTemplate', 
               'Get-MgSecurityLabelRetentionLabelDescriptorCategoryTemplate', 
               'Get-MgSecurityLabelRetentionLabelDescriptorCitationTemplate', 
               'Get-MgSecurityLabelRetentionLabelDescriptorDepartmentTemplate', 
               'Get-MgSecurityLabelRetentionLabelDescriptorFilePlanReferenceTemplate', 
               'Get-MgSecurityLabelRetentionLabelDispositionReviewStage', 
               'Get-MgSecurityLabelRetentionLabelDispositionReviewStageCount', 
               'Get-MgSecuritySecureScore', 
               'Get-MgSecuritySecureScoreControlProfile', 
               'Get-MgSecuritySecureScoreControlProfileCount', 
               'Get-MgSecuritySecureScoreCount', 
               'Get-MgSecuritySubjectRightsRequest', 
               'Get-MgSecuritySubjectRightsRequestApprover', 
               'Get-MgSecuritySubjectRightsRequestApproverByUserPrincipalName', 
               'Get-MgSecuritySubjectRightsRequestApproverCount', 
               'Get-MgSecuritySubjectRightsRequestApproverMailboxSetting', 
               'Get-MgSecuritySubjectRightsRequestApproverServiceProvisioningError', 
               'Get-MgSecuritySubjectRightsRequestApproverServiceProvisioningErrorCount', 
               'Get-MgSecuritySubjectRightsRequestCollaborator', 
               'Get-MgSecuritySubjectRightsRequestCollaboratorByUserPrincipalName', 
               'Get-MgSecuritySubjectRightsRequestCollaboratorCount', 
               'Get-MgSecuritySubjectRightsRequestCollaboratorMailboxSetting', 
               'Get-MgSecuritySubjectRightsRequestCollaboratorServiceProvisioningError', 
               'Get-MgSecuritySubjectRightsRequestCollaboratorServiceProvisioningErrorCount', 
               'Get-MgSecuritySubjectRightsRequestCount', 
               'Get-MgSecuritySubjectRightsRequestFinalAttachment', 
               'Get-MgSecuritySubjectRightsRequestFinalReport', 
               'Get-MgSecuritySubjectRightsRequestNote', 
               'Get-MgSecuritySubjectRightsRequestNoteCount', 
               'Get-MgSecuritySubjectRightsRequestTeam', 
               'Get-MgSecurityThreatIntelligence', 
               'Get-MgSecurityThreatIntelligenceArticle', 
               'Get-MgSecurityThreatIntelligenceArticleCount', 
               'Get-MgSecurityThreatIntelligenceArticleIndicator', 
               'Get-MgSecurityThreatIntelligenceArticleIndicatorArtifact', 
               'Get-MgSecurityThreatIntelligenceArticleIndicatorCount', 
               'Get-MgSecurityThreatIntelligenceHost', 
               'Get-MgSecurityThreatIntelligenceHostChildHostPair', 
               'Get-MgSecurityThreatIntelligenceHostChildHostPairCount', 
               'Get-MgSecurityThreatIntelligenceHostComponent', 
               'Get-MgSecurityThreatIntelligenceHostComponentCount', 
               'Get-MgSecurityThreatIntelligenceHostComponentHost', 
               'Get-MgSecurityThreatIntelligenceHostCookie', 
               'Get-MgSecurityThreatIntelligenceHostCookieCount', 
               'Get-MgSecurityThreatIntelligenceHostCookieHost', 
               'Get-MgSecurityThreatIntelligenceHostCount', 
               'Get-MgSecurityThreatIntelligenceHostPair', 
               'Get-MgSecurityThreatIntelligenceHostPairChildHost', 
               'Get-MgSecurityThreatIntelligenceHostPairCount', 
               'Get-MgSecurityThreatIntelligenceHostPairParentHost', 
               'Get-MgSecurityThreatIntelligenceHostParentHostPair', 
               'Get-MgSecurityThreatIntelligenceHostParentHostPairCount', 
               'Get-MgSecurityThreatIntelligenceHostPassiveDns', 
               'Get-MgSecurityThreatIntelligenceHostPassiveDnsCount', 
               'Get-MgSecurityThreatIntelligenceHostPassiveDnsReverse', 
               'Get-MgSecurityThreatIntelligenceHostPassiveDnsReverseCount', 
               'Get-MgSecurityThreatIntelligenceHostPort', 
               'Get-MgSecurityThreatIntelligenceHostPortCount', 
               'Get-MgSecurityThreatIntelligenceHostPortHost', 
               'Get-MgSecurityThreatIntelligenceHostPortMostRecentSslCertificate', 
               'Get-MgSecurityThreatIntelligenceHostReputation', 
               'Get-MgSecurityThreatIntelligenceHostSslCertificate', 
               'Get-MgSecurityThreatIntelligenceHostSslCertificateCount', 
               'Get-MgSecurityThreatIntelligenceHostSslCertificateHost', 
               'Get-MgSecurityThreatIntelligenceHostSubdomain', 
               'Get-MgSecurityThreatIntelligenceHostSubdomainCount', 
               'Get-MgSecurityThreatIntelligenceHostTracker', 
               'Get-MgSecurityThreatIntelligenceHostTrackerCount', 
               'Get-MgSecurityThreatIntelligenceHostTrackerHost', 
               'Get-MgSecurityThreatIntelligenceHostWhoi', 
               'Get-MgSecurityThreatIntelligenceIntelProfile', 
               'Get-MgSecurityThreatIntelligenceIntelProfileCount', 
               'Get-MgSecurityThreatIntelligenceIntelProfileIndicator', 
               'Get-MgSecurityThreatIntelligenceIntelProfileIndicatorCount', 
               'Get-MgSecurityThreatIntelligencePassiveDnsRecord', 
               'Get-MgSecurityThreatIntelligencePassiveDnsRecordArtifact', 
               'Get-MgSecurityThreatIntelligencePassiveDnsRecordCount', 
               'Get-MgSecurityThreatIntelligencePassiveDnsRecordParentHost', 
               'Get-MgSecurityThreatIntelligenceProfileIndicator', 
               'Get-MgSecurityThreatIntelligenceProfileIndicatorArtifact', 
               'Get-MgSecurityThreatIntelligenceProfileIndicatorCount', 
               'Get-MgSecurityThreatIntelligenceSslCertificate', 
               'Get-MgSecurityThreatIntelligenceSslCertificateCount', 
               'Get-MgSecurityThreatIntelligenceSslCertificateRelatedHost', 
               'Get-MgSecurityThreatIntelligenceSslCertificateRelatedHostCount', 
               'Get-MgSecurityThreatIntelligenceSubdomain', 
               'Get-MgSecurityThreatIntelligenceSubdomainCount', 
               'Get-MgSecurityThreatIntelligenceSubdomainHost', 
               'Get-MgSecurityThreatIntelligenceVulnerability', 
               'Get-MgSecurityThreatIntelligenceVulnerabilityArticle', 
               'Get-MgSecurityThreatIntelligenceVulnerabilityArticleCount', 
               'Get-MgSecurityThreatIntelligenceVulnerabilityComponent', 
               'Get-MgSecurityThreatIntelligenceVulnerabilityComponentCount', 
               'Get-MgSecurityThreatIntelligenceVulnerabilityCount', 
               'Get-MgSecurityThreatIntelligenceWhoisHistoryRecord', 
               'Get-MgSecurityThreatIntelligenceWhoisHistoryRecordCount', 
               'Get-MgSecurityThreatIntelligenceWhoisHistoryRecordHost', 
               'Get-MgSecurityThreatIntelligenceWhoisRecord', 
               'Get-MgSecurityThreatIntelligenceWhoisRecordCount', 
               'Get-MgSecurityThreatIntelligenceWhoisRecordHistory', 
               'Get-MgSecurityThreatIntelligenceWhoisRecordHistoryCount', 
               'Get-MgSecurityThreatIntelligenceWhoisRecordHost', 
               'Get-MgSecurityTrigger', 'Get-MgSecurityTriggerRetentionEvent', 
               'Get-MgSecurityTriggerRetentionEventCount', 
               'Get-MgSecurityTriggerRetentionEventType', 
               'Get-MgSecurityTriggerType', 
               'Get-MgSecurityTriggerTypeRetentionEventType', 
               'Get-MgSecurityTriggerTypeRetentionEventTypeCount', 
               'Initialize-MgSecurityCaseEdiscoveryCaseCustodian', 
               'Invoke-MgAsSecurityCaseEdiscoveryCaseTagHierarchy', 
               'Invoke-MgCommentSecurityAlert', 
               'Invoke-MgComputeSecurityDataSecurityAndGovernanceProtectionScope', 
               'Invoke-MgEstimateSecurityCaseEdiscoveryCaseSearchStatistics', 
               'Invoke-MgProcessSecurityDataSecurityAndGovernanceContentAsync', 
               'Invoke-MgReopenSecurityCaseEdiscoveryCase', 'New-MgSecurityAlert', 
               'New-MgSecurityAlertV2', 'New-MgSecurityAttackSimulation', 
               'New-MgSecurityAttackSimulationAutomation', 
               'New-MgSecurityAttackSimulationAutomationRun', 
               'New-MgSecurityAttackSimulationEndUserNotification', 
               'New-MgSecurityAttackSimulationEndUserNotificationDetail', 
               'New-MgSecurityAttackSimulationLandingPage', 
               'New-MgSecurityAttackSimulationLandingPageDetail', 
               'New-MgSecurityAttackSimulationLoginPage', 
               'New-MgSecurityAttackSimulationOperation', 
               'New-MgSecurityAttackSimulationPayload', 
               'New-MgSecurityAttackSimulationTraining', 
               'New-MgSecurityAttackSimulationTrainingLanguageDetail', 
               'New-MgSecurityCaseEdiscoveryCase', 
               'New-MgSecurityCaseEdiscoveryCaseCustodian', 
               'New-MgSecurityCaseEdiscoveryCaseCustodianSiteSource', 
               'New-MgSecurityCaseEdiscoveryCaseCustodianUnifiedGroupSource', 
               'New-MgSecurityCaseEdiscoveryCaseCustodianUserSource', 
               'New-MgSecurityCaseEdiscoveryCaseNoncustodialDataSource', 
               'New-MgSecurityCaseEdiscoveryCaseOperation', 
               'New-MgSecurityCaseEdiscoveryCaseReviewSet', 
               'New-MgSecurityCaseEdiscoveryCaseReviewSetQuery', 
               'New-MgSecurityCaseEdiscoveryCaseSearch', 
               'New-MgSecurityCaseEdiscoveryCaseSearchAdditionalSource', 
               'New-MgSecurityCaseEdiscoveryCaseTag', 
               'New-MgSecurityIdentityHealthIssue', 'New-MgSecurityIdentitySensor', 
               'New-MgSecurityIdentitySensorDeploymentAccessKey', 
               'New-MgSecurityIncident', 'New-MgSecurityLabelAuthority', 
               'New-MgSecurityLabelCategory', 
               'New-MgSecurityLabelCategorySubcategory', 
               'New-MgSecurityLabelCitation', 'New-MgSecurityLabelDepartment', 
               'New-MgSecurityLabelFilePlanReference', 
               'New-MgSecurityLabelRetentionLabel', 
               'New-MgSecurityLabelRetentionLabelDispositionReviewStage', 
               'New-MgSecuritySecureScore', 
               'New-MgSecuritySecureScoreControlProfile', 
               'New-MgSecuritySubjectRightsRequest', 
               'New-MgSecuritySubjectRightsRequestNote', 
               'New-MgSecurityThreatIntelligenceArticle', 
               'New-MgSecurityThreatIntelligenceArticleIndicator', 
               'New-MgSecurityThreatIntelligenceHost', 
               'New-MgSecurityThreatIntelligenceHostComponent', 
               'New-MgSecurityThreatIntelligenceHostCookie', 
               'New-MgSecurityThreatIntelligenceHostPair', 
               'New-MgSecurityThreatIntelligenceHostPort', 
               'New-MgSecurityThreatIntelligenceHostSslCertificate', 
               'New-MgSecurityThreatIntelligenceHostTracker', 
               'New-MgSecurityThreatIntelligenceIntelProfile', 
               'New-MgSecurityThreatIntelligencePassiveDnsRecord', 
               'New-MgSecurityThreatIntelligenceProfileIndicator', 
               'New-MgSecurityThreatIntelligenceSslCertificate', 
               'New-MgSecurityThreatIntelligenceSubdomain', 
               'New-MgSecurityThreatIntelligenceVulnerability', 
               'New-MgSecurityThreatIntelligenceVulnerabilityComponent', 
               'New-MgSecurityThreatIntelligenceWhoisHistoryRecord', 
               'New-MgSecurityThreatIntelligenceWhoisRecord', 
               'New-MgSecurityTriggerRetentionEvent', 
               'New-MgSecurityTriggerTypeRetentionEventType', 
               'Publish-MgSecurityCaseEdiscoveryCaseCustodian', 
               'Publish-MgSecurityCaseEdiscoveryCaseNoncustodialDataSource', 
               'Remove-MgSecurityAlertV2', 'Remove-MgSecurityAttackSimulation', 
               'Remove-MgSecurityAttackSimulationAutomation', 
               'Remove-MgSecurityAttackSimulationAutomationRun', 
               'Remove-MgSecurityAttackSimulationEndUserNotification', 
               'Remove-MgSecurityAttackSimulationEndUserNotificationDetail', 
               'Remove-MgSecurityAttackSimulationLandingPage', 
               'Remove-MgSecurityAttackSimulationLandingPageDetail', 
               'Remove-MgSecurityAttackSimulationLoginPage', 
               'Remove-MgSecurityAttackSimulationOperation', 
               'Remove-MgSecurityAttackSimulationPayload', 
               'Remove-MgSecurityAttackSimulationTraining', 
               'Remove-MgSecurityAttackSimulationTrainingLanguageDetail', 
               'Remove-MgSecurityCase', 'Remove-MgSecurityCaseEdiscoveryCase', 
               'Remove-MgSecurityCaseEdiscoveryCaseCustodian', 
               'Remove-MgSecurityCaseEdiscoveryCaseCustodianHold', 
               'Remove-MgSecurityCaseEdiscoveryCaseCustodianSiteSource', 
               'Remove-MgSecurityCaseEdiscoveryCaseCustodianUnifiedGroupSource', 
               'Remove-MgSecurityCaseEdiscoveryCaseCustodianUserSource', 
               'Remove-MgSecurityCaseEdiscoveryCaseNoncustodialDataSource', 
               'Remove-MgSecurityCaseEdiscoveryCaseNoncustodialDataSourceHold', 
               'Remove-MgSecurityCaseEdiscoveryCaseOperation', 
               'Remove-MgSecurityCaseEdiscoveryCaseReviewSet', 
               'Remove-MgSecurityCaseEdiscoveryCaseReviewSetQuery', 
               'Remove-MgSecurityCaseEdiscoveryCaseSearch', 
               'Remove-MgSecurityCaseEdiscoveryCaseSearchAdditionalSource', 
               'Remove-MgSecurityCaseEdiscoveryCaseSetting', 
               'Remove-MgSecurityCaseEdiscoveryCaseTag', 
               'Remove-MgSecurityDataSecurityAndGovernance', 
               'Remove-MgSecurityDataSecurityAndGovernanceProtectionScope', 
               'Remove-MgSecurityIdentity', 'Remove-MgSecurityIdentityHealthIssue', 
               'Remove-MgSecurityIdentitySensor', 'Remove-MgSecurityIncident', 
               'Remove-MgSecurityLabel', 'Remove-MgSecurityLabelAuthority', 
               'Remove-MgSecurityLabelCategory', 
               'Remove-MgSecurityLabelCategorySubcategory', 
               'Remove-MgSecurityLabelCitation', 
               'Remove-MgSecurityLabelDepartment', 
               'Remove-MgSecurityLabelFilePlanReference', 
               'Remove-MgSecurityLabelRetentionLabel', 
               'Remove-MgSecurityLabelRetentionLabelDescriptor', 
               'Remove-MgSecurityLabelRetentionLabelDispositionReviewStage', 
               'Remove-MgSecuritySecureScore', 
               'Remove-MgSecuritySecureScoreControlProfile', 
               'Remove-MgSecuritySubjectRightsRequest', 
               'Remove-MgSecuritySubjectRightsRequestNote', 
               'Remove-MgSecurityThreatIntelligence', 
               'Remove-MgSecurityThreatIntelligenceArticle', 
               'Remove-MgSecurityThreatIntelligenceArticleIndicator', 
               'Remove-MgSecurityThreatIntelligenceHost', 
               'Remove-MgSecurityThreatIntelligenceHostComponent', 
               'Remove-MgSecurityThreatIntelligenceHostCookie', 
               'Remove-MgSecurityThreatIntelligenceHostPair', 
               'Remove-MgSecurityThreatIntelligenceHostPort', 
               'Remove-MgSecurityThreatIntelligenceHostReputation', 
               'Remove-MgSecurityThreatIntelligenceHostSslCertificate', 
               'Remove-MgSecurityThreatIntelligenceHostTracker', 
               'Remove-MgSecurityThreatIntelligenceIntelProfile', 
               'Remove-MgSecurityThreatIntelligencePassiveDnsRecord', 
               'Remove-MgSecurityThreatIntelligenceProfileIndicator', 
               'Remove-MgSecurityThreatIntelligenceSslCertificate', 
               'Remove-MgSecurityThreatIntelligenceSubdomain', 
               'Remove-MgSecurityThreatIntelligenceVulnerability', 
               'Remove-MgSecurityThreatIntelligenceVulnerabilityComponent', 
               'Remove-MgSecurityThreatIntelligenceWhoisHistoryRecord', 
               'Remove-MgSecurityThreatIntelligenceWhoisRecord', 
               'Remove-MgSecurityTrigger', 
               'Remove-MgSecurityTriggerRetentionEvent', 
               'Remove-MgSecurityTriggerType', 
               'Remove-MgSecurityTriggerTypeRetentionEventType', 
               'Reset-MgSecurityCaseEdiscoveryCaseSettingToDefault', 
               'Set-MgSecurityAlert', 'Set-MgSecurityIncidentAlertComment', 
               'Start-MgSecurityHuntingQuery', 'Update-MgSecurityAlert', 
               'Update-MgSecurityAlertV2', 
               'Update-MgSecurityAttackSimulationAutomation', 
               'Update-MgSecurityAttackSimulationAutomationRun', 
               'Update-MgSecurityAttackSimulationEndUserNotification', 
               'Update-MgSecurityAttackSimulationEndUserNotificationDetail', 
               'Update-MgSecurityAttackSimulationLandingPage', 
               'Update-MgSecurityAttackSimulationLandingPageDetail', 
               'Update-MgSecurityAttackSimulationLoginPage', 
               'Update-MgSecurityAttackSimulationOperation', 
               'Update-MgSecurityAttackSimulationPayload', 
               'Update-MgSecurityAttackSimulationTraining', 
               'Update-MgSecurityAttackSimulationTrainingLanguageDetail', 
               'Update-MgSecurityCase', 'Update-MgSecurityCaseEdiscoveryCase', 
               'Update-MgSecurityCaseEdiscoveryCaseCustodian', 
               'Update-MgSecurityCaseEdiscoveryCaseCustodianIndex', 
               'Update-MgSecurityCaseEdiscoveryCaseCustodianSiteSource', 
               'Update-MgSecurityCaseEdiscoveryCaseCustodianUnifiedGroupSource', 
               'Update-MgSecurityCaseEdiscoveryCaseCustodianUserSource', 
               'Update-MgSecurityCaseEdiscoveryCaseNoncustodialDataSource', 
               'Update-MgSecurityCaseEdiscoveryCaseNoncustodialDataSourceIndex', 
               'Update-MgSecurityCaseEdiscoveryCaseOperation', 
               'Update-MgSecurityCaseEdiscoveryCaseReviewSet', 
               'Update-MgSecurityCaseEdiscoveryCaseReviewSetQuery', 
               'Update-MgSecurityCaseEdiscoveryCaseSearch', 
               'Update-MgSecurityCaseEdiscoveryCaseSearchAdditionalSource', 
               'Update-MgSecurityCaseEdiscoveryCaseSetting', 
               'Update-MgSecurityCaseEdiscoveryCaseTag', 
               'Update-MgSecurityDataSecurityAndGovernance', 
               'Update-MgSecurityDataSecurityAndGovernanceProtectionScope', 
               'Update-MgSecurityIdentity', 'Update-MgSecurityIdentityHealthIssue', 
               'Update-MgSecurityIdentitySensor', 'Update-MgSecurityIncident', 
               'Update-MgSecurityLabel', 'Update-MgSecurityLabelAuthority', 
               'Update-MgSecurityLabelCategory', 
               'Update-MgSecurityLabelCategorySubcategory', 
               'Update-MgSecurityLabelCitation', 
               'Update-MgSecurityLabelDepartment', 
               'Update-MgSecurityLabelFilePlanReference', 
               'Update-MgSecurityLabelRetentionLabel', 
               'Update-MgSecurityLabelRetentionLabelDescriptor', 
               'Update-MgSecurityLabelRetentionLabelDispositionReviewStage', 
               'Update-MgSecuritySecureScore', 
               'Update-MgSecuritySecureScoreControlProfile', 
               'Update-MgSecuritySubjectRightsRequest', 
               'Update-MgSecuritySubjectRightsRequestApproverMailboxSetting', 
               'Update-MgSecuritySubjectRightsRequestCollaboratorMailboxSetting', 
               'Update-MgSecuritySubjectRightsRequestNote', 
               'Update-MgSecurityThreatIntelligence', 
               'Update-MgSecurityThreatIntelligenceArticle', 
               'Update-MgSecurityThreatIntelligenceArticleIndicator', 
               'Update-MgSecurityThreatIntelligenceHost', 
               'Update-MgSecurityThreatIntelligenceHostComponent', 
               'Update-MgSecurityThreatIntelligenceHostCookie', 
               'Update-MgSecurityThreatIntelligenceHostPair', 
               'Update-MgSecurityThreatIntelligenceHostPort', 
               'Update-MgSecurityThreatIntelligenceHostReputation', 
               'Update-MgSecurityThreatIntelligenceHostSslCertificate', 
               'Update-MgSecurityThreatIntelligenceHostTracker', 
               'Update-MgSecurityThreatIntelligenceIntelProfile', 
               'Update-MgSecurityThreatIntelligencePassiveDnsRecord', 
               'Update-MgSecurityThreatIntelligenceProfileIndicator', 
               'Update-MgSecurityThreatIntelligenceSslCertificate', 
               'Update-MgSecurityThreatIntelligenceSubdomain', 
               'Update-MgSecurityThreatIntelligenceVulnerability', 
               'Update-MgSecurityThreatIntelligenceVulnerabilityComponent', 
               'Update-MgSecurityThreatIntelligenceWhoisHistoryRecord', 
               'Update-MgSecurityThreatIntelligenceWhoisRecord', 
               'Update-MgSecurityTrigger', 
               'Update-MgSecurityTriggerRetentionEvent', 
               'Update-MgSecurityTriggerType', 
               'Update-MgSecurityTriggerTypeRetentionEventType'

# Cmdlets to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no cmdlets to export.
CmdletsToExport = @()

# Variables to export from this module
# VariablesToExport = @()

# Aliases to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no aliases to export.
AliasesToExport = '*'

# DSC resources to export from this module
# DscResourcesToExport = @()

# List of all modules packaged with this module
# ModuleList = @()

# List of all files packaged with this module
# FileList = @()

# Private data to pass to the module specified in RootModule/ModuleToProcess. This may also contain a PSData hashtable with additional module metadata used by PowerShell.
PrivateData = @{

    PSData = @{

        # Tags applied to this module. These help with module discovery in online galleries.
        Tags = 'Microsoft','Office365','Graph','PowerShell','PSModule','PSIncludes_Cmdlet'

        # A URL to the license for this module.
        LicenseUri = 'https://aka.ms/devservicesagreement'

        # A URL to the main website for this project.
        ProjectUri = 'https://github.com/microsoftgraph/msgraph-sdk-powershell'

        # A URL to an icon representing this module.
        IconUri = 'https://raw.githubusercontent.com/microsoftgraph/msgraph-sdk-powershell/dev/docs/images/graph_color256.png'

        # ReleaseNotes of this module
        ReleaseNotes = 'See https://aka.ms/GraphPowerShell-Release.'

        # Prerelease string of this module
        # Prerelease = ''

        # Flag to indicate whether the module requires explicit user acceptance for install/update/save
        # RequireLicenseAcceptance = $false

        # External dependent modules of this module
        # ExternalModuleDependencies = @()

    } # End of PSData hashtable

 } # End of PrivateData hashtable

# HelpInfo URI of this module
# HelpInfoURI = ''

# Default prefix for commands exported from this module. Override the default prefix using Import-Module -Prefix.
# DefaultCommandPrefix = ''

}


# SIG # Begin signature block
# MIIoQwYJKoZIhvcNAQcCoIIoNDCCKDACAQExDzANBglghkgBZQMEAgEFADB5Bgor
# BgEEAYI3AgEEoGswaTA0BgorBgEEAYI3AgEeMCYCAwEAAAQQH8w7YFlLCE63JNLG
# KX7zUQIBAAIBAAIBAAIBAAIBADAxMA0GCWCGSAFlAwQCAQUABCAVqN9UFu82k2ET
# r90ud7jASzojBuVNAkm2NZCcdL9Ct6CCDXYwggX0MIID3KADAgECAhMzAAAEBGx0
# Bv9XKydyAAAAAAQEMA0GCSqGSIb3DQEBCwUAMH4xCzAJBgNVBAYTAlVTMRMwEQYD
# VQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNy
# b3NvZnQgQ29ycG9yYXRpb24xKDAmBgNVBAMTH01pY3Jvc29mdCBDb2RlIFNpZ25p
# bmcgUENBIDIwMTEwHhcNMjQwOTEyMjAxMTE0WhcNMjUwOTExMjAxMTE0WjB0MQsw
# CQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9u
# ZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMR4wHAYDVQQDExVNaWNy
# b3NvZnQgQ29ycG9yYXRpb24wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIB
# AQC0KDfaY50MDqsEGdlIzDHBd6CqIMRQWW9Af1LHDDTuFjfDsvna0nEuDSYJmNyz
# NB10jpbg0lhvkT1AzfX2TLITSXwS8D+mBzGCWMM/wTpciWBV/pbjSazbzoKvRrNo
# DV/u9omOM2Eawyo5JJJdNkM2d8qzkQ0bRuRd4HarmGunSouyb9NY7egWN5E5lUc3
# a2AROzAdHdYpObpCOdeAY2P5XqtJkk79aROpzw16wCjdSn8qMzCBzR7rvH2WVkvF
# HLIxZQET1yhPb6lRmpgBQNnzidHV2Ocxjc8wNiIDzgbDkmlx54QPfw7RwQi8p1fy
# 4byhBrTjv568x8NGv3gwb0RbAgMBAAGjggFzMIIBbzAfBgNVHSUEGDAWBgorBgEE
# AYI3TAgBBggrBgEFBQcDAzAdBgNVHQ4EFgQU8huhNbETDU+ZWllL4DNMPCijEU4w
# RQYDVR0RBD4wPKQ6MDgxHjAcBgNVBAsTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEW
# MBQGA1UEBRMNMjMwMDEyKzUwMjkyMzAfBgNVHSMEGDAWgBRIbmTlUAXTgqoXNzci
# tW2oynUClTBUBgNVHR8ETTBLMEmgR6BFhkNodHRwOi8vd3d3Lm1pY3Jvc29mdC5j
# b20vcGtpb3BzL2NybC9NaWNDb2RTaWdQQ0EyMDExXzIwMTEtMDctMDguY3JsMGEG
# CCsGAQUFBwEBBFUwUzBRBggrBgEFBQcwAoZFaHR0cDovL3d3dy5taWNyb3NvZnQu
# Y29tL3BraW9wcy9jZXJ0cy9NaWNDb2RTaWdQQ0EyMDExXzIwMTEtMDctMDguY3J0
# MAwGA1UdEwEB/wQCMAAwDQYJKoZIhvcNAQELBQADggIBAIjmD9IpQVvfB1QehvpC
# Ge7QeTQkKQ7j3bmDMjwSqFL4ri6ae9IFTdpywn5smmtSIyKYDn3/nHtaEn0X1NBj
# L5oP0BjAy1sqxD+uy35B+V8wv5GrxhMDJP8l2QjLtH/UglSTIhLqyt8bUAqVfyfp
# h4COMRvwwjTvChtCnUXXACuCXYHWalOoc0OU2oGN+mPJIJJxaNQc1sjBsMbGIWv3
# cmgSHkCEmrMv7yaidpePt6V+yPMik+eXw3IfZ5eNOiNgL1rZzgSJfTnvUqiaEQ0X
# dG1HbkDv9fv6CTq6m4Ty3IzLiwGSXYxRIXTxT4TYs5VxHy2uFjFXWVSL0J2ARTYL
# E4Oyl1wXDF1PX4bxg1yDMfKPHcE1Ijic5lx1KdK1SkaEJdto4hd++05J9Bf9TAmi
# u6EK6C9Oe5vRadroJCK26uCUI4zIjL/qG7mswW+qT0CW0gnR9JHkXCWNbo8ccMk1
# sJatmRoSAifbgzaYbUz8+lv+IXy5GFuAmLnNbGjacB3IMGpa+lbFgih57/fIhamq
# 5VhxgaEmn/UjWyr+cPiAFWuTVIpfsOjbEAww75wURNM1Imp9NJKye1O24EspEHmb
# DmqCUcq7NqkOKIG4PVm3hDDED/WQpzJDkvu4FrIbvyTGVU01vKsg4UfcdiZ0fQ+/
# V0hf8yrtq9CkB8iIuk5bBxuPMIIHejCCBWKgAwIBAgIKYQ6Q0gAAAAAAAzANBgkq
# hkiG9w0BAQsFADCBiDELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldhc2hpbmd0b24x
# EDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlv
# bjEyMDAGA1UEAxMpTWljcm9zb2Z0IFJvb3QgQ2VydGlmaWNhdGUgQXV0aG9yaXR5
# IDIwMTEwHhcNMTEwNzA4MjA1OTA5WhcNMjYwNzA4MjEwOTA5WjB+MQswCQYDVQQG
# EwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwG
# A1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSgwJgYDVQQDEx9NaWNyb3NvZnQg
# Q29kZSBTaWduaW5nIFBDQSAyMDExMIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIIC
# CgKCAgEAq/D6chAcLq3YbqqCEE00uvK2WCGfQhsqa+laUKq4BjgaBEm6f8MMHt03
# a8YS2AvwOMKZBrDIOdUBFDFC04kNeWSHfpRgJGyvnkmc6Whe0t+bU7IKLMOv2akr
# rnoJr9eWWcpgGgXpZnboMlImEi/nqwhQz7NEt13YxC4Ddato88tt8zpcoRb0Rrrg
# OGSsbmQ1eKagYw8t00CT+OPeBw3VXHmlSSnnDb6gE3e+lD3v++MrWhAfTVYoonpy
# 4BI6t0le2O3tQ5GD2Xuye4Yb2T6xjF3oiU+EGvKhL1nkkDstrjNYxbc+/jLTswM9
# sbKvkjh+0p2ALPVOVpEhNSXDOW5kf1O6nA+tGSOEy/S6A4aN91/w0FK/jJSHvMAh
# dCVfGCi2zCcoOCWYOUo2z3yxkq4cI6epZuxhH2rhKEmdX4jiJV3TIUs+UsS1Vz8k
# A/DRelsv1SPjcF0PUUZ3s/gA4bysAoJf28AVs70b1FVL5zmhD+kjSbwYuER8ReTB
# w3J64HLnJN+/RpnF78IcV9uDjexNSTCnq47f7Fufr/zdsGbiwZeBe+3W7UvnSSmn
# Eyimp31ngOaKYnhfsi+E11ecXL93KCjx7W3DKI8sj0A3T8HhhUSJxAlMxdSlQy90
# lfdu+HggWCwTXWCVmj5PM4TasIgX3p5O9JawvEagbJjS4NaIjAsCAwEAAaOCAe0w
# ggHpMBAGCSsGAQQBgjcVAQQDAgEAMB0GA1UdDgQWBBRIbmTlUAXTgqoXNzcitW2o
# ynUClTAZBgkrBgEEAYI3FAIEDB4KAFMAdQBiAEMAQTALBgNVHQ8EBAMCAYYwDwYD
# VR0TAQH/BAUwAwEB/zAfBgNVHSMEGDAWgBRyLToCMZBDuRQFTuHqp8cx0SOJNDBa
# BgNVHR8EUzBRME+gTaBLhklodHRwOi8vY3JsLm1pY3Jvc29mdC5jb20vcGtpL2Ny
# bC9wcm9kdWN0cy9NaWNSb29DZXJBdXQyMDExXzIwMTFfMDNfMjIuY3JsMF4GCCsG
# AQUFBwEBBFIwUDBOBggrBgEFBQcwAoZCaHR0cDovL3d3dy5taWNyb3NvZnQuY29t
# L3BraS9jZXJ0cy9NaWNSb29DZXJBdXQyMDExXzIwMTFfMDNfMjIuY3J0MIGfBgNV
# HSAEgZcwgZQwgZEGCSsGAQQBgjcuAzCBgzA/BggrBgEFBQcCARYzaHR0cDovL3d3
# dy5taWNyb3NvZnQuY29tL3BraW9wcy9kb2NzL3ByaW1hcnljcHMuaHRtMEAGCCsG
# AQUFBwICMDQeMiAdAEwAZQBnAGEAbABfAHAAbwBsAGkAYwB5AF8AcwB0AGEAdABl
# AG0AZQBuAHQALiAdMA0GCSqGSIb3DQEBCwUAA4ICAQBn8oalmOBUeRou09h0ZyKb
# C5YR4WOSmUKWfdJ5DJDBZV8uLD74w3LRbYP+vj/oCso7v0epo/Np22O/IjWll11l
# hJB9i0ZQVdgMknzSGksc8zxCi1LQsP1r4z4HLimb5j0bpdS1HXeUOeLpZMlEPXh6
# I/MTfaaQdION9MsmAkYqwooQu6SpBQyb7Wj6aC6VoCo/KmtYSWMfCWluWpiW5IP0
# wI/zRive/DvQvTXvbiWu5a8n7dDd8w6vmSiXmE0OPQvyCInWH8MyGOLwxS3OW560
# STkKxgrCxq2u5bLZ2xWIUUVYODJxJxp/sfQn+N4sOiBpmLJZiWhub6e3dMNABQam
# ASooPoI/E01mC8CzTfXhj38cbxV9Rad25UAqZaPDXVJihsMdYzaXht/a8/jyFqGa
# J+HNpZfQ7l1jQeNbB5yHPgZ3BtEGsXUfFL5hYbXw3MYbBL7fQccOKO7eZS/sl/ah
# XJbYANahRr1Z85elCUtIEJmAH9AAKcWxm6U/RXceNcbSoqKfenoi+kiVH6v7RyOA
# 9Z74v2u3S5fi63V4GuzqN5l5GEv/1rMjaHXmr/r8i+sLgOppO6/8MO0ETI7f33Vt
# Y5E90Z1WTk+/gFcioXgRMiF670EKsT/7qMykXcGhiJtXcVZOSEXAQsmbdlsKgEhr
# /Xmfwb1tbWrJUnMTDXpQzTGCGiMwghofAgEBMIGVMH4xCzAJBgNVBAYTAlVTMRMw
# EQYDVQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVN
# aWNyb3NvZnQgQ29ycG9yYXRpb24xKDAmBgNVBAMTH01pY3Jvc29mdCBDb2RlIFNp
# Z25pbmcgUENBIDIwMTECEzMAAAQEbHQG/1crJ3IAAAAABAQwDQYJYIZIAWUDBAIB
# BQCgga4wGQYJKoZIhvcNAQkDMQwGCisGAQQBgjcCAQQwHAYKKwYBBAGCNwIBCzEO
# MAwGCisGAQQBgjcCARUwLwYJKoZIhvcNAQkEMSIEIAxGLRjKLLe6iZeVQUsx/7AS
# rWDDNW4qTelOpoaINoLhMEIGCisGAQQBgjcCAQwxNDAyoBSAEgBNAGkAYwByAG8A
# cwBvAGYAdKEagBhodHRwOi8vd3d3Lm1pY3Jvc29mdC5jb20wDQYJKoZIhvcNAQEB
# BQAEggEAKpocMvELqGt3C1NcHVukOuigTbevD1dnM8MnZjByBqMbOMj7twpM8c8U
# XDwcXfjeT1D1ArdzvKB8VOFYp43rYiBoA0xpIFMW9xJfzZhphoUHmYdxWh339uyv
# U6T6kRbdSNHJYHIGK6J+1H9DkCVvCQb/P+wALNNYvHC/PgB50oDZc+fa6UpvRcUS
# PwZ9G0/qqu2aSxyMKYbDZ9QasRBCpAMQmv/Rrj4UJZ/DYRo99Vixu0ZUsUHvMkZO
# J8qihcqiWQdJdKqH6vamlQQanNNVR5oFWC0CnchbF6imkToLQz1wVW9F96+876Be
# OGcU35DVOeOhdO9AGBNMYhOjtBYP1qGCF60wghepBgorBgEEAYI3AwMBMYIXmTCC
# F5UGCSqGSIb3DQEHAqCCF4YwgheCAgEDMQ8wDQYJYIZIAWUDBAIBBQAwggFaBgsq
# hkiG9w0BCRABBKCCAUkEggFFMIIBQQIBAQYKKwYBBAGEWQoDATAxMA0GCWCGSAFl
# AwQCAQUABCC444jrMF3TrFujO0xWjCOeI/nqbXpcwJ4HyiEsb2iJ/wIGaFK+5DtB
# GBMyMDI1MDcwOTExMDgwMC43NjdaMASAAgH0oIHZpIHWMIHTMQswCQYDVQQGEwJV
# UzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwGA1UE
# ChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMS0wKwYDVQQLEyRNaWNyb3NvZnQgSXJl
# bGFuZCBPcGVyYXRpb25zIExpbWl0ZWQxJzAlBgNVBAsTHm5TaGllbGQgVFNTIEVT
# TjoyRDFBLTA1RTAtRDk0NzElMCMGA1UEAxMcTWljcm9zb2Z0IFRpbWUtU3RhbXAg
# U2VydmljZaCCEfswggcoMIIFEKADAgECAhMzAAAB/XP5aFrNDGHtAAEAAAH9MA0G
# CSqGSIb3DQEBCwUAMHwxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNoaW5ndG9u
# MRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNyb3NvZnQgQ29ycG9yYXRp
# b24xJjAkBgNVBAMTHU1pY3Jvc29mdCBUaW1lLVN0YW1wIFBDQSAyMDEwMB4XDTI0
# MDcyNTE4MzExNloXDTI1MTAyMjE4MzExNlowgdMxCzAJBgNVBAYTAlVTMRMwEQYD
# VQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNy
# b3NvZnQgQ29ycG9yYXRpb24xLTArBgNVBAsTJE1pY3Jvc29mdCBJcmVsYW5kIE9w
# ZXJhdGlvbnMgTGltaXRlZDEnMCUGA1UECxMeblNoaWVsZCBUU1MgRVNOOjJEMUEt
# MDVFMC1EOTQ3MSUwIwYDVQQDExxNaWNyb3NvZnQgVGltZS1TdGFtcCBTZXJ2aWNl
# MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAoWWs+D+Ou4JjYnRHRedu
# 0MTFYzNJEVPnILzc02R3qbnujvhZgkhp+p/lymYLzkQyG2zpxYceTjIF7HiQWbt6
# FW3ARkBrthJUz05ZnKpcF31lpUEb8gUXiD2xIpo8YM+SD0S+hTP1TCA/we38yZ3B
# EtmZtcVnaLRp/Avsqg+5KI0Kw6TDJpKwTLl0VW0/23sKikeWDSnHQeTprO0zIm/b
# tagSYm3V/8zXlfxy7s/EVFdSglHGsUq8EZupUO8XbHzz7tURyiD3kOxNnw5ox1eZ
# X/c/XmW4H6b4yNmZF0wTZuw37yA1PJKOySSrXrWEh+H6++Wb6+1ltMCPoMJHUtPP
# 3Cn0CNcNvrPyJtDacqjnITrLzrsHdOLqjsH229Zkvndk0IqxBDZgMoY+Ef7ffFRP
# 2pPkrF1F9IcBkYz8hL+QjX+u4y4Uqq4UtT7VRnsqvR/x/+QLE0pcSEh/XE1w1fcp
# 6Jmq8RnHEXikycMLN/a/KYxpSP3FfFbLZuf+qIryFL0gEDytapGn1ONjVkiKpVP2
# uqVIYj4ViCjy5pLUceMeqiKgYqhpmUHCE2WssLLhdQBHdpl28+k+ZY6m4dPFnEoG
# cJHuMcIZnw4cOwixojROr+Nq71cJj7Q4L0XwPvuTHQt0oH7RKMQgmsy7CVD7v55d
# OhdHXdYsyO69dAdK+nWlyYcCAwEAAaOCAUkwggFFMB0GA1UdDgQWBBTpDMXA4ZW8
# +yL2+3vA6RmU7oEKpDAfBgNVHSMEGDAWgBSfpxVdAF5iXYP05dJlpxtTNRnpcjBf
# BgNVHR8EWDBWMFSgUqBQhk5odHRwOi8vd3d3Lm1pY3Jvc29mdC5jb20vcGtpb3Bz
# L2NybC9NaWNyb3NvZnQlMjBUaW1lLVN0YW1wJTIwUENBJTIwMjAxMCgxKS5jcmww
# bAYIKwYBBQUHAQEEYDBeMFwGCCsGAQUFBzAChlBodHRwOi8vd3d3Lm1pY3Jvc29m
# dC5jb20vcGtpb3BzL2NlcnRzL01pY3Jvc29mdCUyMFRpbWUtU3RhbXAlMjBQQ0El
# MjAyMDEwKDEpLmNydDAMBgNVHRMBAf8EAjAAMBYGA1UdJQEB/wQMMAoGCCsGAQUF
# BwMIMA4GA1UdDwEB/wQEAwIHgDANBgkqhkiG9w0BAQsFAAOCAgEAY9hYX+T5AmCr
# YGaH96TdR5T52/PNOG7ySYeopv4flnDWQLhBlravAg+pjlNv5XSXZrKGv8e4s5dJ
# 5WdhfC9ywFQq4TmXnUevPXtlubZk+02BXK6/23hM0TSKs2KlhYiqzbRe8QbMfKXE
# DtvMoHSZT7r+wI2IgjYQwka+3P9VXgERwu46/czz8IR/Zq+vO5523Jld6ssVuzs9
# uwIrJhfcYBj50mXWRBcMhzajLjWDgcih0DuykPcBpoTLlOL8LpXooqnr+QLYE4Bp
# Uep3JySMYfPz2hfOL3g02WEfsOxp8ANbcdiqM31dm3vSheEkmjHA2zuM+Tgn4j5n
# +Any7IODYQkIrNVhLdML09eu1dIPhp24lFtnWTYNaFTOfMqFa3Ab8KDKicmp0Ath
# RNZVg0BPAL58+B0UcoBGKzS9jscwOTu1JmNlisOKkVUVkSJ5Fo/ctfDSPdCTVaIX
# XF7l40k1cM/X2O0JdAS97T78lYjtw/PybuzX5shxBh/RqTPvCyAhIxBVKfN/hfs4
# CIoFaqWJ0r/8SB1CGsyyIcPfEgMo8ceq1w5Zo0JfnyFi6Guo+z3LPFl/exQaRubE
# rsAUTfyBY5/5liyvjAgyDYnEB8vHO7c7Fg2tGd5hGgYs+AOoWx24+XcyxpUkAajD
# hky9Dl+8JZTjts6BcT9sYTmOodk/SgIwggdxMIIFWaADAgECAhMzAAAAFcXna54C
# m0mZAAAAAAAVMA0GCSqGSIb3DQEBCwUAMIGIMQswCQYDVQQGEwJVUzETMBEGA1UE
# CBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwGA1UEChMVTWljcm9z
# b2Z0IENvcnBvcmF0aW9uMTIwMAYDVQQDEylNaWNyb3NvZnQgUm9vdCBDZXJ0aWZp
# Y2F0ZSBBdXRob3JpdHkgMjAxMDAeFw0yMTA5MzAxODIyMjVaFw0zMDA5MzAxODMy
# MjVaMHwxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQH
# EwdSZWRtb25kMR4wHAYDVQQKExVNaWNyb3NvZnQgQ29ycG9yYXRpb24xJjAkBgNV
# BAMTHU1pY3Jvc29mdCBUaW1lLVN0YW1wIFBDQSAyMDEwMIICIjANBgkqhkiG9w0B
# AQEFAAOCAg8AMIICCgKCAgEA5OGmTOe0ciELeaLL1yR5vQ7VgtP97pwHB9KpbE51
# yMo1V/YBf2xK4OK9uT4XYDP/XE/HZveVU3Fa4n5KWv64NmeFRiMMtY0Tz3cywBAY
# 6GB9alKDRLemjkZrBxTzxXb1hlDcwUTIcVxRMTegCjhuje3XD9gmU3w5YQJ6xKr9
# cmmvHaus9ja+NSZk2pg7uhp7M62AW36MEBydUv626GIl3GoPz130/o5Tz9bshVZN
# 7928jaTjkY+yOSxRnOlwaQ3KNi1wjjHINSi947SHJMPgyY9+tVSP3PoFVZhtaDua
# Rr3tpK56KTesy+uDRedGbsoy1cCGMFxPLOJiss254o2I5JasAUq7vnGpF1tnYN74
# kpEeHT39IM9zfUGaRnXNxF803RKJ1v2lIH1+/NmeRd+2ci/bfV+AutuqfjbsNkz2
# K26oElHovwUDo9Fzpk03dJQcNIIP8BDyt0cY7afomXw/TNuvXsLz1dhzPUNOwTM5
# TI4CvEJoLhDqhFFG4tG9ahhaYQFzymeiXtcodgLiMxhy16cg8ML6EgrXY28MyTZk
# i1ugpoMhXV8wdJGUlNi5UPkLiWHzNgY1GIRH29wb0f2y1BzFa/ZcUlFdEtsluq9Q
# BXpsxREdcu+N+VLEhReTwDwV2xo3xwgVGD94q0W29R6HXtqPnhZyacaue7e3Pmri
# Lq0CAwEAAaOCAd0wggHZMBIGCSsGAQQBgjcVAQQFAgMBAAEwIwYJKwYBBAGCNxUC
# BBYEFCqnUv5kxJq+gpE8RjUpzxD/LwTuMB0GA1UdDgQWBBSfpxVdAF5iXYP05dJl
# pxtTNRnpcjBcBgNVHSAEVTBTMFEGDCsGAQQBgjdMg30BATBBMD8GCCsGAQUFBwIB
# FjNodHRwOi8vd3d3Lm1pY3Jvc29mdC5jb20vcGtpb3BzL0RvY3MvUmVwb3NpdG9y
# eS5odG0wEwYDVR0lBAwwCgYIKwYBBQUHAwgwGQYJKwYBBAGCNxQCBAweCgBTAHUA
# YgBDAEEwCwYDVR0PBAQDAgGGMA8GA1UdEwEB/wQFMAMBAf8wHwYDVR0jBBgwFoAU
# 1fZWy4/oolxiaNE9lJBb186aGMQwVgYDVR0fBE8wTTBLoEmgR4ZFaHR0cDovL2Ny
# bC5taWNyb3NvZnQuY29tL3BraS9jcmwvcHJvZHVjdHMvTWljUm9vQ2VyQXV0XzIw
# MTAtMDYtMjMuY3JsMFoGCCsGAQUFBwEBBE4wTDBKBggrBgEFBQcwAoY+aHR0cDov
# L3d3dy5taWNyb3NvZnQuY29tL3BraS9jZXJ0cy9NaWNSb29DZXJBdXRfMjAxMC0w
# Ni0yMy5jcnQwDQYJKoZIhvcNAQELBQADggIBAJ1VffwqreEsH2cBMSRb4Z5yS/yp
# b+pcFLY+TkdkeLEGk5c9MTO1OdfCcTY/2mRsfNB1OW27DzHkwo/7bNGhlBgi7ulm
# ZzpTTd2YurYeeNg2LpypglYAA7AFvonoaeC6Ce5732pvvinLbtg/SHUB2RjebYIM
# 9W0jVOR4U3UkV7ndn/OOPcbzaN9l9qRWqveVtihVJ9AkvUCgvxm2EhIRXT0n4ECW
# OKz3+SmJw7wXsFSFQrP8DJ6LGYnn8AtqgcKBGUIZUnWKNsIdw2FzLixre24/LAl4
# FOmRsqlb30mjdAy87JGA0j3mSj5mO0+7hvoyGtmW9I/2kQH2zsZ0/fZMcm8Qq3Uw
# xTSwethQ/gpY3UA8x1RtnWN0SCyxTkctwRQEcb9k+SS+c23Kjgm9swFXSVRk2XPX
# fx5bRAGOWhmRaw2fpCjcZxkoJLo4S5pu+yFUa2pFEUep8beuyOiJXk+d0tBMdrVX
# VAmxaQFEfnyhYWxz/gq77EFmPWn9y8FBSX5+k77L+DvktxW/tM4+pTFRhLy/AsGC
# onsXHRWJjXD+57XQKBqJC4822rpM+Zv/Cuk0+CQ1ZyvgDbjmjJnW4SLq8CdCPSWU
# 5nR0W2rRnj7tfqAxM328y+l7vzhwRNGQ8cirOoo6CGJ/2XBjU02N7oJtpQUQwXEG
# ahC0HVUzWLOhcGbyoYIDVjCCAj4CAQEwggEBoYHZpIHWMIHTMQswCQYDVQQGEwJV
# UzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwGA1UE
# ChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMS0wKwYDVQQLEyRNaWNyb3NvZnQgSXJl
# bGFuZCBPcGVyYXRpb25zIExpbWl0ZWQxJzAlBgNVBAsTHm5TaGllbGQgVFNTIEVT
# TjoyRDFBLTA1RTAtRDk0NzElMCMGA1UEAxMcTWljcm9zb2Z0IFRpbWUtU3RhbXAg
# U2VydmljZaIjCgEBMAcGBSsOAwIaAxUAoj0WtVVQUNSKoqtrjinRAsBUdoOggYMw
# gYCkfjB8MQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UE
# BxMHUmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSYwJAYD
# VQQDEx1NaWNyb3NvZnQgVGltZS1TdGFtcCBQQ0EgMjAxMDANBgkqhkiG9w0BAQsF
# AAIFAOwYQrEwIhgPMjAyNTA3MDkwMTIxNTNaGA8yMDI1MDcxMDAxMjE1M1owdDA6
# BgorBgEEAYRZCgQBMSwwKjAKAgUA7BhCsQIBADAHAgEAAgIJITAHAgEAAgIToTAK
# AgUA7BmUMQIBADA2BgorBgEEAYRZCgQCMSgwJjAMBgorBgEEAYRZCgMCoAowCAIB
# AAIDB6EgoQowCAIBAAIDAYagMA0GCSqGSIb3DQEBCwUAA4IBAQBDiVELrZGoiWbc
# vbrTNa2t9WrzxQ2nlpefBE7+Wa8WF6OYPOwWVCkBuFrbLGX+gCHjWA/28qr2Va0Z
# Esb469Tndovh1cVTQPqyPgGTdivl5LUAJFzty97W7gi8Xoc750ropPhP4dtJGpkK
# 9ghcjpvJMHsWLKlFObx3b4SKMnJtxBgTjdm+kpWIJrVApdmTye3wdWubYX7zjKyC
# 4vYiaEkMQHFrj2hsHsMYqHak+EpBZSpOdQ9S0ZfsT+zAT7p0HVbap/W9A6oCiOfI
# kIUOyVwNm6wsO8ZDxz5oI7fWs2F+Q41fzbchv0cP6PeC0F8FqKfXtefPh8UuDD5f
# TeP5EdmCMYIEDTCCBAkCAQEwgZMwfDELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldh
# c2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBD
# b3Jwb3JhdGlvbjEmMCQGA1UEAxMdTWljcm9zb2Z0IFRpbWUtU3RhbXAgUENBIDIw
# MTACEzMAAAH9c/loWs0MYe0AAQAAAf0wDQYJYIZIAWUDBAIBBQCgggFKMBoGCSqG
# SIb3DQEJAzENBgsqhkiG9w0BCRABBDAvBgkqhkiG9w0BCQQxIgQgMBvWFOrKKdvJ
# +cP3qqmWMUf2mmP+M5hlhdJvLpmDW9owgfoGCyqGSIb3DQEJEAIvMYHqMIHnMIHk
# MIG9BCCAKEgNyUowvIfx/eDfYSupHkeF1p6GFwjKBs8lRB4NRzCBmDCBgKR+MHwx
# CzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRt
# b25kMR4wHAYDVQQKExVNaWNyb3NvZnQgQ29ycG9yYXRpb24xJjAkBgNVBAMTHU1p
# Y3Jvc29mdCBUaW1lLVN0YW1wIFBDQSAyMDEwAhMzAAAB/XP5aFrNDGHtAAEAAAH9
# MCIEIKDk9h9/GV6ek7y9IxKVwwDlqN3INC1QF391/S8D7htWMA0GCSqGSIb3DQEB
# CwUABIICAJyiL+4Ls7F3CddZlBA1+a7lNIwMBsdDIqaomBR02RJ9AUvOOa3fLQfD
# JteREB0rZP9GWCcffz+rIM5Hcy3KKeYV7icBlJCHpAKWzppck519wwifRrON8kAv
# nPp7TPB2rLVHRwFUkUcMivizdd2MGzvOWjwzsYmnDk57Xk03HsQE/bwf9x1cRsDp
# 4qit7R4e0qJnnjV2boioeztBsVmnRO2Iauk7yFmy+D72jgJDFnsc5lILWcDloRWw
# EjB+MU0h1lFt+J32Nth0Dp1gbBWqmb6b37NWBs7+OlyFjXgHfsT6n4J6Rjveond5
# Ixwb8y9ERcuuSd9TE6P3b3uIIRfLJj1V9WUmQxhmxqIAaMr1PrAO3p1lPgHepm6N
# 7dEe2/B54/3l3BwKKwsfH5IwVNquzir/d83EwHY2pRiEEa2w/XQJf7hUcq2/QPx7
# 5+iCUQwHP0hgiCE7ldNJUhBDElTzaYeUQwanOyJ9xS8rGqQx232VN6yEZvkkjGwi
# M952xYNEVWlNNKwPlrxNwri8EKpFy17ZxPoYpEVOpXCl7VLftYajFHvztt6PYfQY
# oteNNwUWq6tXw5bLjROPIBplKiFnXU1i3sHqKsfffO+Po1e1gTFpaqvJNWRrCeXb
# LcD8tq9oByAfA+vAPGtd4GS09v1bp+8qWukWRCw48EhM3o9RZvx+
# SIG # End signature block
