#pragma namespace("\\\\.\\root\\default")
instance of __namespace{ name="MS_409";};
#pragma namespace("\\\\.\\root\\default\\MS_409")

[AMENDMENT, LOCALE("MS_409")]
class MSFT_PSModule : OMI_BaseResource
{
  [Key, Description("Specifies the name of the repository to manage.") : Amended] String Name;
  [Description("If the repository should be present or absent on the server being configured. Default values is 'Present'.") : Amended] String Ensure;
  [Description("Specifies the URI for discovering and installing modules from this repository. A URI can be a NuGet server feed, HTTP, HTTPS, FTP or file location.") : Amended] String SourceLocation;
  [Description("Specifies the URI for the script source location.") : Amended] String ScriptSourceLocation;
  [Description("Specifies the URI of the publish location. For example, for NuGet-based repositories, the publish location is similar to http://someNuGetUrl.com/api/v2/Packages.") : Amended] String PublishLocation;
  [Description("Specifies the URI for the script publish location.") : Amended] String ScriptPublishLocation;
  [Description("Specifies the installation policy. Valid values are 'Trusted' or 'Untrusted'. The default value is 'Untrusted'.") : Amended] String InstallationPolicy;
  [Description("Specifies a OneGet package provider. Default value is 'NuGet'.") : Amended] String PackageManagementProvider;
  [Description("Specifies if the repository is trusted.") : Amended] Boolean Trusted;
  [Description("Specifies if the repository is registered.") : Amended] Boolean Registered;
};
