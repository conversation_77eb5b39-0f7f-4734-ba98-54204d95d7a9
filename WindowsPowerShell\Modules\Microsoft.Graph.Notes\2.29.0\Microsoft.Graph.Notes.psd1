#
# Module manifest for module 'Microsoft.Graph.Notes'
#
# Generated by: Microsoft Corporation
#
# Generated on: 7/9/2025
#

@{

# Script module or binary module file associated with this manifest.
RootModule = './Microsoft.Graph.Notes.psm1'

# Version number of this module.
ModuleVersion = '2.29.0'

# Supported PSEditions
CompatiblePSEditions = 'Core', 'Desktop'

# ID used to uniquely identify this module
GUID = '21a6a116-6d91-49d3-a034-45218f5b5e72'

# Author of this module
Author = 'Microsoft Corporation'

# Company or vendor of this module
CompanyName = 'Microsoft Corporation'

# Copyright statement for this module
Copyright = 'Microsoft Corporation. All rights reserved.'

# Description of the functionality provided by this module
Description = 'Microsoft Graph PowerShell Cmdlets'

# Minimum version of the PowerShell engine required by this module
PowerShellVersion = '5.1'

# Name of the PowerShell host required by this module
# PowerShellHostName = ''

# Minimum version of the PowerShell host required by this module
# PowerShellHostVersion = ''

# Minimum version of Microsoft .NET Framework required by this module. This prerequisite is valid for the PowerShell Desktop edition only.
DotNetFrameworkVersion = '4.7.2'

# Minimum version of the common language runtime (CLR) required by this module. This prerequisite is valid for the PowerShell Desktop edition only.
# ClrVersion = ''

# Processor architecture (None, X86, Amd64) required by this module
# ProcessorArchitecture = ''

# Modules that must be imported into the global environment prior to importing this module
RequiredModules = @(@{ModuleName = 'Microsoft.Graph.Authentication'; RequiredVersion = '2.29.0'; })

# Assemblies that must be loaded prior to importing this module
RequiredAssemblies = './bin/Microsoft.Graph.Notes.private.dll'

# Script files (.ps1) that are run in the caller's environment prior to importing this module.
# ScriptsToProcess = @()

# Type files (.ps1xml) to be loaded when importing this module
# TypesToProcess = @()

# Format files (.ps1xml) to be loaded when importing this module
FormatsToProcess = './Microsoft.Graph.Notes.format.ps1xml'

# Modules to import as nested modules of the module specified in RootModule/ModuleToProcess
# NestedModules = @()

# Functions to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no functions to export.
FunctionsToExport = 'Copy-MgGroupOnenoteNotebook', 
               'Copy-MgGroupOnenoteNotebookSectionGroupSectionPageToSection', 
               'Copy-MgGroupOnenoteNotebookSectionGroupSectionToNotebook', 
               'Copy-MgGroupOnenoteNotebookSectionGroupSectionToSectionGroup', 
               'Copy-MgGroupOnenoteNotebookSectionPageToSection', 
               'Copy-MgGroupOnenoteNotebookSectionToNotebook', 
               'Copy-MgGroupOnenoteNotebookSectionToSectionGroup', 
               'Copy-MgGroupOnenotePageToSection', 
               'Copy-MgGroupOnenoteSectionGroupSectionPageToSection', 
               'Copy-MgGroupOnenoteSectionGroupSectionToNotebook', 
               'Copy-MgGroupOnenoteSectionGroupSectionToSectionGroup', 
               'Copy-MgGroupOnenoteSectionPageToSection', 
               'Copy-MgGroupOnenoteSectionToNotebook', 
               'Copy-MgGroupOnenoteSectionToSectionGroup', 
               'Copy-MgSiteOnenoteNotebook', 
               'Copy-MgSiteOnenoteNotebookSectionGroupSectionPageToSection', 
               'Copy-MgSiteOnenoteNotebookSectionGroupSectionToNotebook', 
               'Copy-MgSiteOnenoteNotebookSectionGroupSectionToSectionGroup', 
               'Copy-MgSiteOnenoteNotebookSectionPageToSection', 
               'Copy-MgSiteOnenoteNotebookSectionToNotebook', 
               'Copy-MgSiteOnenoteNotebookSectionToSectionGroup', 
               'Copy-MgSiteOnenotePageToSection', 
               'Copy-MgSiteOnenoteSectionGroupSectionPageToSection', 
               'Copy-MgSiteOnenoteSectionGroupSectionToNotebook', 
               'Copy-MgSiteOnenoteSectionGroupSectionToSectionGroup', 
               'Copy-MgSiteOnenoteSectionPageToSection', 
               'Copy-MgSiteOnenoteSectionToNotebook', 
               'Copy-MgSiteOnenoteSectionToSectionGroup', 
               'Copy-MgUserOnenoteNotebook', 
               'Copy-MgUserOnenoteNotebookSectionGroupSectionPageToSection', 
               'Copy-MgUserOnenoteNotebookSectionGroupSectionToNotebook', 
               'Copy-MgUserOnenoteNotebookSectionGroupSectionToSectionGroup', 
               'Copy-MgUserOnenoteNotebookSectionPageToSection', 
               'Copy-MgUserOnenoteNotebookSectionToNotebook', 
               'Copy-MgUserOnenoteNotebookSectionToSectionGroup', 
               'Copy-MgUserOnenotePageToSection', 
               'Copy-MgUserOnenoteSectionGroupSectionPageToSection', 
               'Copy-MgUserOnenoteSectionGroupSectionToNotebook', 
               'Copy-MgUserOnenoteSectionGroupSectionToSectionGroup', 
               'Copy-MgUserOnenoteSectionPageToSection', 
               'Copy-MgUserOnenoteSectionToNotebook', 
               'Copy-MgUserOnenoteSectionToSectionGroup', 'Get-MgGroupOnenote', 
               'Get-MgGroupOnenoteNotebook', 'Get-MgGroupOnenoteNotebookCount', 
               'Get-MgGroupOnenoteNotebookFromWebUrl', 
               'Get-MgGroupOnenoteNotebookSection', 
               'Get-MgGroupOnenoteNotebookSectionCount', 
               'Get-MgGroupOnenoteNotebookSectionGroup', 
               'Get-MgGroupOnenoteNotebookSectionGroupCount', 
               'Get-MgGroupOnenoteNotebookSectionGroupParentNotebook', 
               'Get-MgGroupOnenoteNotebookSectionGroupParentSectionGroup', 
               'Get-MgGroupOnenoteNotebookSectionGroupSection', 
               'Get-MgGroupOnenoteNotebookSectionGroupSectionCount', 
               'Get-MgGroupOnenoteNotebookSectionGroupSectionPage', 
               'Get-MgGroupOnenoteNotebookSectionGroupSectionPageContent', 
               'Get-MgGroupOnenoteNotebookSectionGroupSectionPageCount', 
               'Get-MgGroupOnenoteNotebookSectionGroupSectionPageParentNotebook', 
               'Get-MgGroupOnenoteNotebookSectionGroupSectionPageParentSection', 
               'Get-MgGroupOnenoteNotebookSectionGroupSectionParentNotebook', 
               'Get-MgGroupOnenoteNotebookSectionGroupSectionParentSectionGroup', 
               'Get-MgGroupOnenoteNotebookSectionPage', 
               'Get-MgGroupOnenoteNotebookSectionPageContent', 
               'Get-MgGroupOnenoteNotebookSectionPageCount', 
               'Get-MgGroupOnenoteNotebookSectionPageParentNotebook', 
               'Get-MgGroupOnenoteNotebookSectionPageParentSection', 
               'Get-MgGroupOnenoteNotebookSectionParentNotebook', 
               'Get-MgGroupOnenoteNotebookSectionParentSectionGroup', 
               'Get-MgGroupOnenoteOperation', 'Get-MgGroupOnenoteOperationCount', 
               'Get-MgGroupOnenotePage', 'Get-MgGroupOnenotePageContent', 
               'Get-MgGroupOnenotePageCount', 
               'Get-MgGroupOnenotePageParentNotebook', 
               'Get-MgGroupOnenotePageParentSection', 
               'Get-MgGroupOnenoteRecentNotebook', 'Get-MgGroupOnenoteResource', 
               'Get-MgGroupOnenoteResourceContent', 
               'Get-MgGroupOnenoteResourceCount', 'Get-MgGroupOnenoteSection', 
               'Get-MgGroupOnenoteSectionCount', 'Get-MgGroupOnenoteSectionGroup', 
               'Get-MgGroupOnenoteSectionGroupCount', 
               'Get-MgGroupOnenoteSectionGroupParentNotebook', 
               'Get-MgGroupOnenoteSectionGroupParentSectionGroup', 
               'Get-MgGroupOnenoteSectionGroupSection', 
               'Get-MgGroupOnenoteSectionGroupSectionCount', 
               'Get-MgGroupOnenoteSectionGroupSectionPage', 
               'Get-MgGroupOnenoteSectionGroupSectionPageContent', 
               'Get-MgGroupOnenoteSectionGroupSectionPageCount', 
               'Get-MgGroupOnenoteSectionGroupSectionPageParentNotebook', 
               'Get-MgGroupOnenoteSectionGroupSectionPageParentSection', 
               'Get-MgGroupOnenoteSectionGroupSectionParentNotebook', 
               'Get-MgGroupOnenoteSectionGroupSectionParentSectionGroup', 
               'Get-MgGroupOnenoteSectionPage', 
               'Get-MgGroupOnenoteSectionPageContent', 
               'Get-MgGroupOnenoteSectionPageCount', 
               'Get-MgGroupOnenoteSectionPageParentNotebook', 
               'Get-MgGroupOnenoteSectionPageParentSection', 
               'Get-MgGroupOnenoteSectionParentNotebook', 
               'Get-MgGroupOnenoteSectionParentSectionGroup', 
               'Get-MgSiteGetByPathOnenote', 'Get-MgSiteOnenote', 
               'Get-MgSiteOnenoteNotebook', 'Get-MgSiteOnenoteNotebookCount', 
               'Get-MgSiteOnenoteNotebookFromWebUrl', 
               'Get-MgSiteOnenoteNotebookRecentNotebook', 
               'Get-MgSiteOnenoteNotebookSection', 
               'Get-MgSiteOnenoteNotebookSectionCount', 
               'Get-MgSiteOnenoteNotebookSectionGroup', 
               'Get-MgSiteOnenoteNotebookSectionGroupCount', 
               'Get-MgSiteOnenoteNotebookSectionGroupParentNotebook', 
               'Get-MgSiteOnenoteNotebookSectionGroupParentSectionGroup', 
               'Get-MgSiteOnenoteNotebookSectionGroupSection', 
               'Get-MgSiteOnenoteNotebookSectionGroupSectionCount', 
               'Get-MgSiteOnenoteNotebookSectionGroupSectionPage', 
               'Get-MgSiteOnenoteNotebookSectionGroupSectionPageContent', 
               'Get-MgSiteOnenoteNotebookSectionGroupSectionPageCount', 
               'Get-MgSiteOnenoteNotebookSectionGroupSectionPageParentNotebook', 
               'Get-MgSiteOnenoteNotebookSectionGroupSectionPageParentSection', 
               'Get-MgSiteOnenoteNotebookSectionGroupSectionParentNotebook', 
               'Get-MgSiteOnenoteNotebookSectionGroupSectionParentSectionGroup', 
               'Get-MgSiteOnenoteNotebookSectionPage', 
               'Get-MgSiteOnenoteNotebookSectionPageContent', 
               'Get-MgSiteOnenoteNotebookSectionPageCount', 
               'Get-MgSiteOnenoteNotebookSectionPageParentNotebook', 
               'Get-MgSiteOnenoteNotebookSectionPageParentSection', 
               'Get-MgSiteOnenoteNotebookSectionParentNotebook', 
               'Get-MgSiteOnenoteNotebookSectionParentSectionGroup', 
               'Get-MgSiteOnenoteOperation', 'Get-MgSiteOnenoteOperationCount', 
               'Get-MgSiteOnenotePage', 'Get-MgSiteOnenotePageContent', 
               'Get-MgSiteOnenotePageCount', 'Get-MgSiteOnenotePageParentNotebook', 
               'Get-MgSiteOnenotePageParentSection', 'Get-MgSiteOnenoteResource', 
               'Get-MgSiteOnenoteResourceContent', 
               'Get-MgSiteOnenoteResourceCount', 'Get-MgSiteOnenoteSection', 
               'Get-MgSiteOnenoteSectionCount', 'Get-MgSiteOnenoteSectionGroup', 
               'Get-MgSiteOnenoteSectionGroupCount', 
               'Get-MgSiteOnenoteSectionGroupParentNotebook', 
               'Get-MgSiteOnenoteSectionGroupParentSectionGroup', 
               'Get-MgSiteOnenoteSectionGroupSection', 
               'Get-MgSiteOnenoteSectionGroupSectionCount', 
               'Get-MgSiteOnenoteSectionGroupSectionPage', 
               'Get-MgSiteOnenoteSectionGroupSectionPageContent', 
               'Get-MgSiteOnenoteSectionGroupSectionPageCount', 
               'Get-MgSiteOnenoteSectionGroupSectionPageParentNotebook', 
               'Get-MgSiteOnenoteSectionGroupSectionPageParentSection', 
               'Get-MgSiteOnenoteSectionGroupSectionParentNotebook', 
               'Get-MgSiteOnenoteSectionGroupSectionParentSectionGroup', 
               'Get-MgSiteOnenoteSectionPage', 
               'Get-MgSiteOnenoteSectionPageContent', 
               'Get-MgSiteOnenoteSectionPageCount', 
               'Get-MgSiteOnenoteSectionPageParentNotebook', 
               'Get-MgSiteOnenoteSectionPageParentSection', 
               'Get-MgSiteOnenoteSectionParentNotebook', 
               'Get-MgSiteOnenoteSectionParentSectionGroup', 'Get-MgUserOnenote', 
               'Get-MgUserOnenoteNotebook', 'Get-MgUserOnenoteNotebookCount', 
               'Get-MgUserOnenoteNotebookFromWebUrl', 
               'Get-MgUserOnenoteNotebookRecentNotebook', 
               'Get-MgUserOnenoteNotebookSection', 
               'Get-MgUserOnenoteNotebookSectionCount', 
               'Get-MgUserOnenoteNotebookSectionGroup', 
               'Get-MgUserOnenoteNotebookSectionGroupCount', 
               'Get-MgUserOnenoteNotebookSectionGroupParentNotebook', 
               'Get-MgUserOnenoteNotebookSectionGroupParentSectionGroup', 
               'Get-MgUserOnenoteNotebookSectionGroupSection', 
               'Get-MgUserOnenoteNotebookSectionGroupSectionCount', 
               'Get-MgUserOnenoteNotebookSectionGroupSectionPage', 
               'Get-MgUserOnenoteNotebookSectionGroupSectionPageContent', 
               'Get-MgUserOnenoteNotebookSectionGroupSectionPageCount', 
               'Get-MgUserOnenoteNotebookSectionGroupSectionPageParentNotebook', 
               'Get-MgUserOnenoteNotebookSectionGroupSectionPageParentSection', 
               'Get-MgUserOnenoteNotebookSectionGroupSectionParentNotebook', 
               'Get-MgUserOnenoteNotebookSectionGroupSectionParentSectionGroup', 
               'Get-MgUserOnenoteNotebookSectionPage', 
               'Get-MgUserOnenoteNotebookSectionPageContent', 
               'Get-MgUserOnenoteNotebookSectionPageCount', 
               'Get-MgUserOnenoteNotebookSectionPageParentNotebook', 
               'Get-MgUserOnenoteNotebookSectionPageParentSection', 
               'Get-MgUserOnenoteNotebookSectionParentNotebook', 
               'Get-MgUserOnenoteNotebookSectionParentSectionGroup', 
               'Get-MgUserOnenoteOperation', 'Get-MgUserOnenoteOperationCount', 
               'Get-MgUserOnenotePage', 'Get-MgUserOnenotePageContent', 
               'Get-MgUserOnenotePageCount', 'Get-MgUserOnenotePageParentNotebook', 
               'Get-MgUserOnenotePageParentSection', 'Get-MgUserOnenoteResource', 
               'Get-MgUserOnenoteResourceContent', 
               'Get-MgUserOnenoteResourceCount', 'Get-MgUserOnenoteSection', 
               'Get-MgUserOnenoteSectionCount', 'Get-MgUserOnenoteSectionGroup', 
               'Get-MgUserOnenoteSectionGroupCount', 
               'Get-MgUserOnenoteSectionGroupParentNotebook', 
               'Get-MgUserOnenoteSectionGroupParentSectionGroup', 
               'Get-MgUserOnenoteSectionGroupSection', 
               'Get-MgUserOnenoteSectionGroupSectionCount', 
               'Get-MgUserOnenoteSectionGroupSectionPage', 
               'Get-MgUserOnenoteSectionGroupSectionPageContent', 
               'Get-MgUserOnenoteSectionGroupSectionPageCount', 
               'Get-MgUserOnenoteSectionGroupSectionPageParentNotebook', 
               'Get-MgUserOnenoteSectionGroupSectionPageParentSection', 
               'Get-MgUserOnenoteSectionGroupSectionParentNotebook', 
               'Get-MgUserOnenoteSectionGroupSectionParentSectionGroup', 
               'Get-MgUserOnenoteSectionPage', 
               'Get-MgUserOnenoteSectionPageContent', 
               'Get-MgUserOnenoteSectionPageCount', 
               'Get-MgUserOnenoteSectionPageParentNotebook', 
               'Get-MgUserOnenoteSectionPageParentSection', 
               'Get-MgUserOnenoteSectionParentNotebook', 
               'Get-MgUserOnenoteSectionParentSectionGroup', 
               'Invoke-MgPreviewGroupOnenoteNotebookSectionGroupSectionPage', 
               'Invoke-MgPreviewGroupOnenoteNotebookSectionPage', 
               'Invoke-MgPreviewGroupOnenotePage', 
               'Invoke-MgPreviewGroupOnenoteSectionGroupSectionPage', 
               'Invoke-MgPreviewGroupOnenoteSectionPage', 
               'Invoke-MgPreviewSiteOnenoteNotebookSectionGroupSectionPage', 
               'Invoke-MgPreviewSiteOnenoteNotebookSectionPage', 
               'Invoke-MgPreviewSiteOnenotePage', 
               'Invoke-MgPreviewSiteOnenoteSectionGroupSectionPage', 
               'Invoke-MgPreviewSiteOnenoteSectionPage', 
               'Invoke-MgPreviewUserOnenoteNotebookSectionGroupSectionPage', 
               'Invoke-MgPreviewUserOnenoteNotebookSectionPage', 
               'Invoke-MgPreviewUserOnenotePage', 
               'Invoke-MgPreviewUserOnenoteSectionGroupSectionPage', 
               'Invoke-MgPreviewUserOnenoteSectionPage', 
               'New-MgGroupOnenoteNotebook', 'New-MgGroupOnenoteNotebookSection', 
               'New-MgGroupOnenoteNotebookSectionGroup', 
               'New-MgGroupOnenoteNotebookSectionGroupSection', 
               'New-MgGroupOnenoteNotebookSectionGroupSectionPage', 
               'New-MgGroupOnenoteNotebookSectionPage', 
               'New-MgGroupOnenoteOperation', 'New-MgGroupOnenotePage', 
               'New-MgGroupOnenoteResource', 'New-MgGroupOnenoteSection', 
               'New-MgGroupOnenoteSectionGroup', 
               'New-MgGroupOnenoteSectionGroupSection', 
               'New-MgGroupOnenoteSectionGroupSectionPage', 
               'New-MgGroupOnenoteSectionPage', 'New-MgSiteOnenoteNotebook', 
               'New-MgSiteOnenoteNotebookSection', 
               'New-MgSiteOnenoteNotebookSectionGroup', 
               'New-MgSiteOnenoteNotebookSectionGroupSection', 
               'New-MgSiteOnenoteNotebookSectionGroupSectionPage', 
               'New-MgSiteOnenoteNotebookSectionPage', 
               'New-MgSiteOnenoteOperation', 'New-MgSiteOnenotePage', 
               'New-MgSiteOnenoteResource', 'New-MgSiteOnenoteSection', 
               'New-MgSiteOnenoteSectionGroup', 
               'New-MgSiteOnenoteSectionGroupSection', 
               'New-MgSiteOnenoteSectionGroupSectionPage', 
               'New-MgSiteOnenoteSectionPage', 'New-MgUserOnenoteNotebook', 
               'New-MgUserOnenoteNotebookSection', 
               'New-MgUserOnenoteNotebookSectionGroup', 
               'New-MgUserOnenoteNotebookSectionGroupSection', 
               'New-MgUserOnenoteNotebookSectionGroupSectionPage', 
               'New-MgUserOnenoteNotebookSectionPage', 
               'New-MgUserOnenoteOperation', 'New-MgUserOnenotePage', 
               'New-MgUserOnenoteResource', 'New-MgUserOnenoteSection', 
               'New-MgUserOnenoteSectionGroup', 
               'New-MgUserOnenoteSectionGroupSection', 
               'New-MgUserOnenoteSectionGroupSectionPage', 
               'New-MgUserOnenoteSectionPage', 'Remove-MgGroupOnenote', 
               'Remove-MgGroupOnenoteNotebook', 
               'Remove-MgGroupOnenoteNotebookSection', 
               'Remove-MgGroupOnenoteNotebookSectionGroup', 
               'Remove-MgGroupOnenoteNotebookSectionGroupSection', 
               'Remove-MgGroupOnenoteNotebookSectionGroupSectionPage', 
               'Remove-MgGroupOnenoteNotebookSectionGroupSectionPageContent', 
               'Remove-MgGroupOnenoteNotebookSectionPage', 
               'Remove-MgGroupOnenoteNotebookSectionPageContent', 
               'Remove-MgGroupOnenoteOperation', 'Remove-MgGroupOnenotePage', 
               'Remove-MgGroupOnenotePageContent', 'Remove-MgGroupOnenoteResource', 
               'Remove-MgGroupOnenoteResourceContent', 
               'Remove-MgGroupOnenoteSection', 'Remove-MgGroupOnenoteSectionGroup', 
               'Remove-MgGroupOnenoteSectionGroupSection', 
               'Remove-MgGroupOnenoteSectionGroupSectionPage', 
               'Remove-MgGroupOnenoteSectionGroupSectionPageContent', 
               'Remove-MgGroupOnenoteSectionPage', 
               'Remove-MgGroupOnenoteSectionPageContent', 
               'Remove-MgSiteGetByPathOnenote', 'Remove-MgSiteOnenote', 
               'Remove-MgSiteOnenoteNotebook', 
               'Remove-MgSiteOnenoteNotebookSection', 
               'Remove-MgSiteOnenoteNotebookSectionGroup', 
               'Remove-MgSiteOnenoteNotebookSectionGroupSection', 
               'Remove-MgSiteOnenoteNotebookSectionGroupSectionPage', 
               'Remove-MgSiteOnenoteNotebookSectionPage', 
               'Remove-MgSiteOnenoteOperation', 'Remove-MgSiteOnenotePage', 
               'Remove-MgSiteOnenoteResource', 'Remove-MgSiteOnenoteSection', 
               'Remove-MgSiteOnenoteSectionGroup', 
               'Remove-MgSiteOnenoteSectionGroupSection', 
               'Remove-MgSiteOnenoteSectionGroupSectionPage', 
               'Remove-MgSiteOnenoteSectionPage', 'Remove-MgUserOnenote', 
               'Remove-MgUserOnenoteNotebook', 
               'Remove-MgUserOnenoteNotebookSection', 
               'Remove-MgUserOnenoteNotebookSectionGroup', 
               'Remove-MgUserOnenoteNotebookSectionGroupSection', 
               'Remove-MgUserOnenoteNotebookSectionGroupSectionPage', 
               'Remove-MgUserOnenoteNotebookSectionGroupSectionPageContent', 
               'Remove-MgUserOnenoteNotebookSectionPage', 
               'Remove-MgUserOnenoteNotebookSectionPageContent', 
               'Remove-MgUserOnenoteOperation', 'Remove-MgUserOnenotePage', 
               'Remove-MgUserOnenotePageContent', 'Remove-MgUserOnenoteResource', 
               'Remove-MgUserOnenoteResourceContent', 
               'Remove-MgUserOnenoteSection', 'Remove-MgUserOnenoteSectionGroup', 
               'Remove-MgUserOnenoteSectionGroupSection', 
               'Remove-MgUserOnenoteSectionGroupSectionPage', 
               'Remove-MgUserOnenoteSectionGroupSectionPageContent', 
               'Remove-MgUserOnenoteSectionPage', 
               'Remove-MgUserOnenoteSectionPageContent', 
               'Set-MgGroupOnenoteNotebookSectionGroupSectionPageContent', 
               'Set-MgGroupOnenoteNotebookSectionPageContent', 
               'Set-MgGroupOnenotePageContent', 
               'Set-MgGroupOnenoteResourceContent', 
               'Set-MgGroupOnenoteSectionGroupSectionPageContent', 
               'Set-MgGroupOnenoteSectionPageContent', 
               'Set-MgSiteOnenoteNotebookSectionGroupSectionPageContent', 
               'Set-MgSiteOnenoteNotebookSectionPageContent', 
               'Set-MgSiteOnenotePageContent', 'Set-MgSiteOnenoteResourceContent', 
               'Set-MgSiteOnenoteSectionGroupSectionPageContent', 
               'Set-MgSiteOnenoteSectionPageContent', 
               'Set-MgUserOnenoteNotebookSectionGroupSectionPageContent', 
               'Set-MgUserOnenoteNotebookSectionPageContent', 
               'Set-MgUserOnenotePageContent', 'Set-MgUserOnenoteResourceContent', 
               'Set-MgUserOnenoteSectionGroupSectionPageContent', 
               'Set-MgUserOnenoteSectionPageContent', 'Update-MgGroupOnenote', 
               'Update-MgGroupOnenoteNotebook', 
               'Update-MgGroupOnenoteNotebookSection', 
               'Update-MgGroupOnenoteNotebookSectionGroup', 
               'Update-MgGroupOnenoteNotebookSectionGroupSection', 
               'Update-MgGroupOnenoteNotebookSectionGroupSectionPageContent', 
               'Update-MgGroupOnenoteNotebookSectionPageContent', 
               'Update-MgGroupOnenoteOperation', 
               'Update-MgGroupOnenotePageContent', 'Update-MgGroupOnenoteResource', 
               'Update-MgGroupOnenoteSection', 'Update-MgGroupOnenoteSectionGroup', 
               'Update-MgGroupOnenoteSectionGroupSection', 
               'Update-MgGroupOnenoteSectionGroupSectionPageContent', 
               'Update-MgGroupOnenoteSectionPageContent', 
               'Update-MgSiteGetByPathOnenote', 'Update-MgSiteOnenoteContent', 
               'Update-MgSiteOnenoteNotebookContent', 
               'Update-MgSiteOnenoteNotebookSectionContent', 
               'Update-MgSiteOnenoteNotebookSectionGroupContent', 
               'Update-MgSiteOnenoteNotebookSectionGroupSectionContent', 
               'Update-MgSiteOnenoteNotebookSectionGroupSectionPageContent', 
               'Update-MgSiteOnenoteNotebookSectionPageContent', 
               'Update-MgSiteOnenoteOperationContent', 
               'Update-MgSiteOnenotePageContent', 
               'Update-MgSiteOnenoteResourceContent', 
               'Update-MgSiteOnenoteSectionContent', 
               'Update-MgSiteOnenoteSectionGroupContent', 
               'Update-MgSiteOnenoteSectionGroupSectionContent', 
               'Update-MgSiteOnenoteSectionGroupSectionPageContent', 
               'Update-MgSiteOnenoteSectionPageContent', 'Update-MgUserOnenote', 
               'Update-MgUserOnenoteNotebook', 
               'Update-MgUserOnenoteNotebookSection', 
               'Update-MgUserOnenoteNotebookSectionGroup', 
               'Update-MgUserOnenoteNotebookSectionGroupSection', 
               'Update-MgUserOnenoteNotebookSectionGroupSectionPage', 
               'Update-MgUserOnenoteNotebookSectionPage', 
               'Update-MgUserOnenoteOperation', 'Update-MgUserOnenotePage', 
               'Update-MgUserOnenoteResource', 'Update-MgUserOnenoteSection', 
               'Update-MgUserOnenoteSectionGroup', 
               'Update-MgUserOnenoteSectionGroupSection', 
               'Update-MgUserOnenoteSectionGroupSectionPage', 
               'Update-MgUserOnenoteSectionPage'

# Cmdlets to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no cmdlets to export.
CmdletsToExport = @()

# Variables to export from this module
# VariablesToExport = @()

# Aliases to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no aliases to export.
AliasesToExport = 'Get-MgSiteRecentNotebook', 'Get-MgUserOnenoteRecentNotebook'

# DSC resources to export from this module
# DscResourcesToExport = @()

# List of all modules packaged with this module
# ModuleList = @()

# List of all files packaged with this module
# FileList = @()

# Private data to pass to the module specified in RootModule/ModuleToProcess. This may also contain a PSData hashtable with additional module metadata used by PowerShell.
PrivateData = @{

    PSData = @{

        # Tags applied to this module. These help with module discovery in online galleries.
        Tags = 'Microsoft','Office365','Graph','PowerShell','PSModule','PSIncludes_Cmdlet'

        # A URL to the license for this module.
        LicenseUri = 'https://aka.ms/devservicesagreement'

        # A URL to the main website for this project.
        ProjectUri = 'https://github.com/microsoftgraph/msgraph-sdk-powershell'

        # A URL to an icon representing this module.
        IconUri = 'https://raw.githubusercontent.com/microsoftgraph/msgraph-sdk-powershell/dev/docs/images/graph_color256.png'

        # ReleaseNotes of this module
        ReleaseNotes = 'See https://aka.ms/GraphPowerShell-Release.'

        # Prerelease string of this module
        # Prerelease = ''

        # Flag to indicate whether the module requires explicit user acceptance for install/update/save
        # RequireLicenseAcceptance = $false

        # External dependent modules of this module
        # ExternalModuleDependencies = @()

    } # End of PSData hashtable

 } # End of PrivateData hashtable

# HelpInfo URI of this module
# HelpInfoURI = ''

# Default prefix for commands exported from this module. Override the default prefix using Import-Module -Prefix.
# DefaultCommandPrefix = ''

}


# SIG # Begin signature block
# MIIoKgYJKoZIhvcNAQcCoIIoGzCCKBcCAQExDzANBglghkgBZQMEAgEFADB5Bgor
# BgEEAYI3AgEEoGswaTA0BgorBgEEAYI3AgEeMCYCAwEAAAQQH8w7YFlLCE63JNLG
# KX7zUQIBAAIBAAIBAAIBAAIBADAxMA0GCWCGSAFlAwQCAQUABCBVuWbVltrBNoXH
# OB0kr93Dts3W++3AhDGJbEOPOYjdYaCCDXYwggX0MIID3KADAgECAhMzAAAEBGx0
# Bv9XKydyAAAAAAQEMA0GCSqGSIb3DQEBCwUAMH4xCzAJBgNVBAYTAlVTMRMwEQYD
# VQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNy
# b3NvZnQgQ29ycG9yYXRpb24xKDAmBgNVBAMTH01pY3Jvc29mdCBDb2RlIFNpZ25p
# bmcgUENBIDIwMTEwHhcNMjQwOTEyMjAxMTE0WhcNMjUwOTExMjAxMTE0WjB0MQsw
# CQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9u
# ZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMR4wHAYDVQQDExVNaWNy
# b3NvZnQgQ29ycG9yYXRpb24wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIB
# AQC0KDfaY50MDqsEGdlIzDHBd6CqIMRQWW9Af1LHDDTuFjfDsvna0nEuDSYJmNyz
# NB10jpbg0lhvkT1AzfX2TLITSXwS8D+mBzGCWMM/wTpciWBV/pbjSazbzoKvRrNo
# DV/u9omOM2Eawyo5JJJdNkM2d8qzkQ0bRuRd4HarmGunSouyb9NY7egWN5E5lUc3
# a2AROzAdHdYpObpCOdeAY2P5XqtJkk79aROpzw16wCjdSn8qMzCBzR7rvH2WVkvF
# HLIxZQET1yhPb6lRmpgBQNnzidHV2Ocxjc8wNiIDzgbDkmlx54QPfw7RwQi8p1fy
# 4byhBrTjv568x8NGv3gwb0RbAgMBAAGjggFzMIIBbzAfBgNVHSUEGDAWBgorBgEE
# AYI3TAgBBggrBgEFBQcDAzAdBgNVHQ4EFgQU8huhNbETDU+ZWllL4DNMPCijEU4w
# RQYDVR0RBD4wPKQ6MDgxHjAcBgNVBAsTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEW
# MBQGA1UEBRMNMjMwMDEyKzUwMjkyMzAfBgNVHSMEGDAWgBRIbmTlUAXTgqoXNzci
# tW2oynUClTBUBgNVHR8ETTBLMEmgR6BFhkNodHRwOi8vd3d3Lm1pY3Jvc29mdC5j
# b20vcGtpb3BzL2NybC9NaWNDb2RTaWdQQ0EyMDExXzIwMTEtMDctMDguY3JsMGEG
# CCsGAQUFBwEBBFUwUzBRBggrBgEFBQcwAoZFaHR0cDovL3d3dy5taWNyb3NvZnQu
# Y29tL3BraW9wcy9jZXJ0cy9NaWNDb2RTaWdQQ0EyMDExXzIwMTEtMDctMDguY3J0
# MAwGA1UdEwEB/wQCMAAwDQYJKoZIhvcNAQELBQADggIBAIjmD9IpQVvfB1QehvpC
# Ge7QeTQkKQ7j3bmDMjwSqFL4ri6ae9IFTdpywn5smmtSIyKYDn3/nHtaEn0X1NBj
# L5oP0BjAy1sqxD+uy35B+V8wv5GrxhMDJP8l2QjLtH/UglSTIhLqyt8bUAqVfyfp
# h4COMRvwwjTvChtCnUXXACuCXYHWalOoc0OU2oGN+mPJIJJxaNQc1sjBsMbGIWv3
# cmgSHkCEmrMv7yaidpePt6V+yPMik+eXw3IfZ5eNOiNgL1rZzgSJfTnvUqiaEQ0X
# dG1HbkDv9fv6CTq6m4Ty3IzLiwGSXYxRIXTxT4TYs5VxHy2uFjFXWVSL0J2ARTYL
# E4Oyl1wXDF1PX4bxg1yDMfKPHcE1Ijic5lx1KdK1SkaEJdto4hd++05J9Bf9TAmi
# u6EK6C9Oe5vRadroJCK26uCUI4zIjL/qG7mswW+qT0CW0gnR9JHkXCWNbo8ccMk1
# sJatmRoSAifbgzaYbUz8+lv+IXy5GFuAmLnNbGjacB3IMGpa+lbFgih57/fIhamq
# 5VhxgaEmn/UjWyr+cPiAFWuTVIpfsOjbEAww75wURNM1Imp9NJKye1O24EspEHmb
# DmqCUcq7NqkOKIG4PVm3hDDED/WQpzJDkvu4FrIbvyTGVU01vKsg4UfcdiZ0fQ+/
# V0hf8yrtq9CkB8iIuk5bBxuPMIIHejCCBWKgAwIBAgIKYQ6Q0gAAAAAAAzANBgkq
# hkiG9w0BAQsFADCBiDELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldhc2hpbmd0b24x
# EDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlv
# bjEyMDAGA1UEAxMpTWljcm9zb2Z0IFJvb3QgQ2VydGlmaWNhdGUgQXV0aG9yaXR5
# IDIwMTEwHhcNMTEwNzA4MjA1OTA5WhcNMjYwNzA4MjEwOTA5WjB+MQswCQYDVQQG
# EwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwG
# A1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSgwJgYDVQQDEx9NaWNyb3NvZnQg
# Q29kZSBTaWduaW5nIFBDQSAyMDExMIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIIC
# CgKCAgEAq/D6chAcLq3YbqqCEE00uvK2WCGfQhsqa+laUKq4BjgaBEm6f8MMHt03
# a8YS2AvwOMKZBrDIOdUBFDFC04kNeWSHfpRgJGyvnkmc6Whe0t+bU7IKLMOv2akr
# rnoJr9eWWcpgGgXpZnboMlImEi/nqwhQz7NEt13YxC4Ddato88tt8zpcoRb0Rrrg
# OGSsbmQ1eKagYw8t00CT+OPeBw3VXHmlSSnnDb6gE3e+lD3v++MrWhAfTVYoonpy
# 4BI6t0le2O3tQ5GD2Xuye4Yb2T6xjF3oiU+EGvKhL1nkkDstrjNYxbc+/jLTswM9
# sbKvkjh+0p2ALPVOVpEhNSXDOW5kf1O6nA+tGSOEy/S6A4aN91/w0FK/jJSHvMAh
# dCVfGCi2zCcoOCWYOUo2z3yxkq4cI6epZuxhH2rhKEmdX4jiJV3TIUs+UsS1Vz8k
# A/DRelsv1SPjcF0PUUZ3s/gA4bysAoJf28AVs70b1FVL5zmhD+kjSbwYuER8ReTB
# w3J64HLnJN+/RpnF78IcV9uDjexNSTCnq47f7Fufr/zdsGbiwZeBe+3W7UvnSSmn
# Eyimp31ngOaKYnhfsi+E11ecXL93KCjx7W3DKI8sj0A3T8HhhUSJxAlMxdSlQy90
# lfdu+HggWCwTXWCVmj5PM4TasIgX3p5O9JawvEagbJjS4NaIjAsCAwEAAaOCAe0w
# ggHpMBAGCSsGAQQBgjcVAQQDAgEAMB0GA1UdDgQWBBRIbmTlUAXTgqoXNzcitW2o
# ynUClTAZBgkrBgEEAYI3FAIEDB4KAFMAdQBiAEMAQTALBgNVHQ8EBAMCAYYwDwYD
# VR0TAQH/BAUwAwEB/zAfBgNVHSMEGDAWgBRyLToCMZBDuRQFTuHqp8cx0SOJNDBa
# BgNVHR8EUzBRME+gTaBLhklodHRwOi8vY3JsLm1pY3Jvc29mdC5jb20vcGtpL2Ny
# bC9wcm9kdWN0cy9NaWNSb29DZXJBdXQyMDExXzIwMTFfMDNfMjIuY3JsMF4GCCsG
# AQUFBwEBBFIwUDBOBggrBgEFBQcwAoZCaHR0cDovL3d3dy5taWNyb3NvZnQuY29t
# L3BraS9jZXJ0cy9NaWNSb29DZXJBdXQyMDExXzIwMTFfMDNfMjIuY3J0MIGfBgNV
# HSAEgZcwgZQwgZEGCSsGAQQBgjcuAzCBgzA/BggrBgEFBQcCARYzaHR0cDovL3d3
# dy5taWNyb3NvZnQuY29tL3BraW9wcy9kb2NzL3ByaW1hcnljcHMuaHRtMEAGCCsG
# AQUFBwICMDQeMiAdAEwAZQBnAGEAbABfAHAAbwBsAGkAYwB5AF8AcwB0AGEAdABl
# AG0AZQBuAHQALiAdMA0GCSqGSIb3DQEBCwUAA4ICAQBn8oalmOBUeRou09h0ZyKb
# C5YR4WOSmUKWfdJ5DJDBZV8uLD74w3LRbYP+vj/oCso7v0epo/Np22O/IjWll11l
# hJB9i0ZQVdgMknzSGksc8zxCi1LQsP1r4z4HLimb5j0bpdS1HXeUOeLpZMlEPXh6
# I/MTfaaQdION9MsmAkYqwooQu6SpBQyb7Wj6aC6VoCo/KmtYSWMfCWluWpiW5IP0
# wI/zRive/DvQvTXvbiWu5a8n7dDd8w6vmSiXmE0OPQvyCInWH8MyGOLwxS3OW560
# STkKxgrCxq2u5bLZ2xWIUUVYODJxJxp/sfQn+N4sOiBpmLJZiWhub6e3dMNABQam
# ASooPoI/E01mC8CzTfXhj38cbxV9Rad25UAqZaPDXVJihsMdYzaXht/a8/jyFqGa
# J+HNpZfQ7l1jQeNbB5yHPgZ3BtEGsXUfFL5hYbXw3MYbBL7fQccOKO7eZS/sl/ah
# XJbYANahRr1Z85elCUtIEJmAH9AAKcWxm6U/RXceNcbSoqKfenoi+kiVH6v7RyOA
# 9Z74v2u3S5fi63V4GuzqN5l5GEv/1rMjaHXmr/r8i+sLgOppO6/8MO0ETI7f33Vt
# Y5E90Z1WTk+/gFcioXgRMiF670EKsT/7qMykXcGhiJtXcVZOSEXAQsmbdlsKgEhr
# /Xmfwb1tbWrJUnMTDXpQzTGCGgowghoGAgEBMIGVMH4xCzAJBgNVBAYTAlVTMRMw
# EQYDVQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVN
# aWNyb3NvZnQgQ29ycG9yYXRpb24xKDAmBgNVBAMTH01pY3Jvc29mdCBDb2RlIFNp
# Z25pbmcgUENBIDIwMTECEzMAAAQEbHQG/1crJ3IAAAAABAQwDQYJYIZIAWUDBAIB
# BQCgga4wGQYJKoZIhvcNAQkDMQwGCisGAQQBgjcCAQQwHAYKKwYBBAGCNwIBCzEO
# MAwGCisGAQQBgjcCARUwLwYJKoZIhvcNAQkEMSIEIBNwaL+kXFTVrFPHfVFDsY93
# asL0fenzQYsr0RXy7rhOMEIGCisGAQQBgjcCAQwxNDAyoBSAEgBNAGkAYwByAG8A
# cwBvAGYAdKEagBhodHRwOi8vd3d3Lm1pY3Jvc29mdC5jb20wDQYJKoZIhvcNAQEB
# BQAEggEAUekC4soeUiykZuXL5D+/xxMGSjb+5Ypz+yCEVcR/ENYbiEICMMdSR1tE
# o6YiogvihmYlSKTYMCluPRCYEl9Kfh54XZw/iUywplZAC4t/rt8j73JiIACKq9tt
# 5/i0FB5t+OYA6orqCE+P+//Ur2VSNgkKY0yGr3+Uw+ItOypooELVu55De3YgFofX
# pHyMDcehJA18DTkzgVo//XQV1xsJxL6RBtswbwUfU2YORcye4JvCZDNEiJH1pLKr
# utSkH+EfPPHYkcZZbV9ZhWhsO7nD1PY+KhLJWAAtLJEQ9FBIa1D0+vp3SdTlGN8+
# 3MdFNGIr5BjiB2YgCLCp22mOWFhQKqGCF5QwgheQBgorBgEEAYI3AwMBMYIXgDCC
# F3wGCSqGSIb3DQEHAqCCF20wghdpAgEDMQ8wDQYJYIZIAWUDBAIBBQAwggFSBgsq
# hkiG9w0BCRABBKCCAUEEggE9MIIBOQIBAQYKKwYBBAGEWQoDATAxMA0GCWCGSAFl
# AwQCAQUABCD5msgHjoivdKuU/9HNDDkrTJWwjLHi38ZqXTq5fMY/mQIGaEseFyp5
# GBMyMDI1MDcwOTExMDg0Ni45NzRaMASAAgH0oIHRpIHOMIHLMQswCQYDVQQGEwJV
# UzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwGA1UE
# ChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSUwIwYDVQQLExxNaWNyb3NvZnQgQW1l
# cmljYSBPcGVyYXRpb25zMScwJQYDVQQLEx5uU2hpZWxkIFRTUyBFU046ODkwMC0w
# NUUwLUQ5NDcxJTAjBgNVBAMTHE1pY3Jvc29mdCBUaW1lLVN0YW1wIFNlcnZpY2Wg
# ghHqMIIHIDCCBQigAwIBAgITMwAAAg4syyh9lSB1YwABAAACDjANBgkqhkiG9w0B
# AQsFADB8MQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UE
# BxMHUmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSYwJAYD
# VQQDEx1NaWNyb3NvZnQgVGltZS1TdGFtcCBQQ0EgMjAxMDAeFw0yNTAxMzAxOTQz
# MDNaFw0yNjA0MjIxOTQzMDNaMIHLMQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2Fz
# aGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENv
# cnBvcmF0aW9uMSUwIwYDVQQLExxNaWNyb3NvZnQgQW1lcmljYSBPcGVyYXRpb25z
# MScwJQYDVQQLEx5uU2hpZWxkIFRTUyBFU046ODkwMC0wNUUwLUQ5NDcxJTAjBgNV
# BAMTHE1pY3Jvc29mdCBUaW1lLVN0YW1wIFNlcnZpY2UwggIiMA0GCSqGSIb3DQEB
# AQUAA4ICDwAwggIKAoICAQCs5t7iRtXt0hbeo9ME78ZYjIo3saQuWMBFQ7X4s9vo
# oYRABTOf2poTHatx+EwnBUGB1V2t/E6MwsQNmY5XpM/75aCrZdxAnrV9o4Tu5sBe
# pbbfehsrOWRBIGoJE6PtWod1CrFehm1diz3jY3H8iFrh7nqefniZ1SnbcWPMyNIx
# uGFzpQiDA+E5YS33meMqaXwhdb01Cluymh/3EKvknj4dIpQZEWOPM3jxbRVAYN5J
# 2tOrYkJcdDx0l02V/NYd1qkvUBgPxrKviq5kz7E6AbOifCDSMBgcn/X7RQw630Qk
# zqhp0kDU2qei/ao9IHmuuReXEjnjpgTsr4Ab33ICAKMYxOQe+n5wqEVcE9OTyhmW
# ZJS5AnWUTniok4mgwONBWQ1DLOGFkZwXT334IPCqd4/3/Ld/ItizistyUZYsml/C
# 4ZhdALbvfYwzv31Oxf8NTmV5IGxWdHnk2Hhh4bnzTKosEaDrJvQMiQ+loojM7f5b
# gdyBBnYQBm5+/iJsxw8k227zF2jbNI+Ows8HLeZGt8t6uJ2eVjND1B0YtgsBP0cs
# BlnnI+4+dvLYRt0cAqw6PiYSz5FSZcbpi0xdAH/jd3dzyGArbyLuo69HugfGEEb/
# sM07rcoP1o3cZ8eWMb4+MIB8euOb5DVPDnEcFi4NDukYM91g1Dt/qIek+rtE88VS
# 8QIDAQABo4IBSTCCAUUwHQYDVR0OBBYEFIVxRGlSEZE+1ESK6UGI7YNcEIjbMB8G
# A1UdIwQYMBaAFJ+nFV0AXmJdg/Tl0mWnG1M1GelyMF8GA1UdHwRYMFYwVKBSoFCG
# Tmh0dHA6Ly93d3cubWljcm9zb2Z0LmNvbS9wa2lvcHMvY3JsL01pY3Jvc29mdCUy
# MFRpbWUtU3RhbXAlMjBQQ0ElMjAyMDEwKDEpLmNybDBsBggrBgEFBQcBAQRgMF4w
# XAYIKwYBBQUHMAKGUGh0dHA6Ly93d3cubWljcm9zb2Z0LmNvbS9wa2lvcHMvY2Vy
# dHMvTWljcm9zb2Z0JTIwVGltZS1TdGFtcCUyMFBDQSUyMDIwMTAoMSkuY3J0MAwG
# A1UdEwEB/wQCMAAwFgYDVR0lAQH/BAwwCgYIKwYBBQUHAwgwDgYDVR0PAQH/BAQD
# AgeAMA0GCSqGSIb3DQEBCwUAA4ICAQB14L2TL+L8OXLxnGSal2h30mZ7FsBFooiY
# kUVOY05F9pnwPTVufEDGWEpNNy2OfaUHWIOoQ/9/rjwO0hS2SpB0BzMAk2gyz92N
# GWOpWbpBdMvrrRDpiWZi/uLS4ZGdRn3P2DccYmlkNP+vaRAXvnv+mp27KgI79mJ9
# hGyCQbvtMIjkbYoLqK7sF7Wahn9rLjX1y5QJL4lvEy3QmA9KRBj56cEv/lAvzDq7
# eSiqRq/pCyqyc8uzmQ8SeKWyWu6DjUA9vi84QsmLjqPGCnH4cPyg+t95RpW+73sn
# hew1iCV+wXu2RxMnWg7EsD5eLkJHLszUIPd+XClD+FTvV03GfrDDfk+45flH/eKR
# Zc3MUZtnhLJjPwv3KoKDScW4iV6SbCRycYPkqoWBrHf7SvDA7GrH2UOtz1Wa1k27
# sdZgpG6/c9CqKI8CX5vgaa+A7oYHb4ZBj7S8u8sgxwWK7HgWDRByOH3CiJu4LJ8h
# 3TiRkRArmHRp0lbNf1iAKuL886IKE912v0yq55t8jMxjBU7uoLsrYVIoKkzh+sAk
# gkpGOoZL14+dlxVM91Bavza4kODTUlwzb+SpXsSqVx8nuB6qhUy7pqpgww1q4SNh
# AxFnFxsxiTlaoL75GNxPR605lJ2WXehtEi7/+YfJqvH+vnqcpqCjyQ9hNaVzuOEH
# X4MyuqcjwjCCB3EwggVZoAMCAQICEzMAAAAVxedrngKbSZkAAAAAABUwDQYJKoZI
# hvcNAQELBQAwgYgxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNoaW5ndG9uMRAw
# DgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNyb3NvZnQgQ29ycG9yYXRpb24x
# MjAwBgNVBAMTKU1pY3Jvc29mdCBSb290IENlcnRpZmljYXRlIEF1dGhvcml0eSAy
# MDEwMB4XDTIxMDkzMDE4MjIyNVoXDTMwMDkzMDE4MzIyNVowfDELMAkGA1UEBhMC
# VVMxEzARBgNVBAgTCldhc2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNV
# BAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEmMCQGA1UEAxMdTWljcm9zb2Z0IFRp
# bWUtU3RhbXAgUENBIDIwMTAwggIiMA0GCSqGSIb3DQEBAQUAA4ICDwAwggIKAoIC
# AQDk4aZM57RyIQt5osvXJHm9DtWC0/3unAcH0qlsTnXIyjVX9gF/bErg4r25Phdg
# M/9cT8dm95VTcVrifkpa/rg2Z4VGIwy1jRPPdzLAEBjoYH1qUoNEt6aORmsHFPPF
# dvWGUNzBRMhxXFExN6AKOG6N7dcP2CZTfDlhAnrEqv1yaa8dq6z2Nr41JmTamDu6
# GnszrYBbfowQHJ1S/rboYiXcag/PXfT+jlPP1uyFVk3v3byNpOORj7I5LFGc6XBp
# Dco2LXCOMcg1KL3jtIckw+DJj361VI/c+gVVmG1oO5pGve2krnopN6zL64NF50Zu
# yjLVwIYwXE8s4mKyzbnijYjklqwBSru+cakXW2dg3viSkR4dPf0gz3N9QZpGdc3E
# XzTdEonW/aUgfX782Z5F37ZyL9t9X4C626p+Nuw2TPYrbqgSUei/BQOj0XOmTTd0
# lBw0gg/wEPK3Rxjtp+iZfD9M269ewvPV2HM9Q07BMzlMjgK8QmguEOqEUUbi0b1q
# GFphAXPKZ6Je1yh2AuIzGHLXpyDwwvoSCtdjbwzJNmSLW6CmgyFdXzB0kZSU2LlQ
# +QuJYfM2BjUYhEfb3BvR/bLUHMVr9lxSUV0S2yW6r1AFemzFER1y7435UsSFF5PA
# PBXbGjfHCBUYP3irRbb1Hode2o+eFnJpxq57t7c+auIurQIDAQABo4IB3TCCAdkw
# EgYJKwYBBAGCNxUBBAUCAwEAATAjBgkrBgEEAYI3FQIEFgQUKqdS/mTEmr6CkTxG
# NSnPEP8vBO4wHQYDVR0OBBYEFJ+nFV0AXmJdg/Tl0mWnG1M1GelyMFwGA1UdIARV
# MFMwUQYMKwYBBAGCN0yDfQEBMEEwPwYIKwYBBQUHAgEWM2h0dHA6Ly93d3cubWlj
# cm9zb2Z0LmNvbS9wa2lvcHMvRG9jcy9SZXBvc2l0b3J5Lmh0bTATBgNVHSUEDDAK
# BggrBgEFBQcDCDAZBgkrBgEEAYI3FAIEDB4KAFMAdQBiAEMAQTALBgNVHQ8EBAMC
# AYYwDwYDVR0TAQH/BAUwAwEB/zAfBgNVHSMEGDAWgBTV9lbLj+iiXGJo0T2UkFvX
# zpoYxDBWBgNVHR8ETzBNMEugSaBHhkVodHRwOi8vY3JsLm1pY3Jvc29mdC5jb20v
# cGtpL2NybC9wcm9kdWN0cy9NaWNSb29DZXJBdXRfMjAxMC0wNi0yMy5jcmwwWgYI
# KwYBBQUHAQEETjBMMEoGCCsGAQUFBzAChj5odHRwOi8vd3d3Lm1pY3Jvc29mdC5j
# b20vcGtpL2NlcnRzL01pY1Jvb0NlckF1dF8yMDEwLTA2LTIzLmNydDANBgkqhkiG
# 9w0BAQsFAAOCAgEAnVV9/Cqt4SwfZwExJFvhnnJL/Klv6lwUtj5OR2R4sQaTlz0x
# M7U518JxNj/aZGx80HU5bbsPMeTCj/ts0aGUGCLu6WZnOlNN3Zi6th542DYunKmC
# VgADsAW+iehp4LoJ7nvfam++Kctu2D9IdQHZGN5tggz1bSNU5HhTdSRXud2f8449
# xvNo32X2pFaq95W2KFUn0CS9QKC/GbYSEhFdPSfgQJY4rPf5KYnDvBewVIVCs/wM
# nosZiefwC2qBwoEZQhlSdYo2wh3DYXMuLGt7bj8sCXgU6ZGyqVvfSaN0DLzskYDS
# PeZKPmY7T7uG+jIa2Zb0j/aRAfbOxnT99kxybxCrdTDFNLB62FD+CljdQDzHVG2d
# Y3RILLFORy3BFARxv2T5JL5zbcqOCb2zAVdJVGTZc9d/HltEAY5aGZFrDZ+kKNxn
# GSgkujhLmm77IVRrakURR6nxt67I6IleT53S0Ex2tVdUCbFpAUR+fKFhbHP+Crvs
# QWY9af3LwUFJfn6Tvsv4O+S3Fb+0zj6lMVGEvL8CwYKiexcdFYmNcP7ntdAoGokL
# jzbaukz5m/8K6TT4JDVnK+ANuOaMmdbhIurwJ0I9JZTmdHRbatGePu1+oDEzfbzL
# 6Xu/OHBE0ZDxyKs6ijoIYn/ZcGNTTY3ugm2lBRDBcQZqELQdVTNYs6FwZvKhggNN
# MIICNQIBATCB+aGB0aSBzjCByzELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldhc2hp
# bmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBDb3Jw
# b3JhdGlvbjElMCMGA1UECxMcTWljcm9zb2Z0IEFtZXJpY2EgT3BlcmF0aW9uczEn
# MCUGA1UECxMeblNoaWVsZCBUU1MgRVNOOjg5MDAtMDVFMC1EOTQ3MSUwIwYDVQQD
# ExxNaWNyb3NvZnQgVGltZS1TdGFtcCBTZXJ2aWNloiMKAQEwBwYFKw4DAhoDFQBK
# 6HY/ZWLnOcMEQsjkDAoB/JZWCKCBgzCBgKR+MHwxCzAJBgNVBAYTAlVTMRMwEQYD
# VQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNy
# b3NvZnQgQ29ycG9yYXRpb24xJjAkBgNVBAMTHU1pY3Jvc29mdCBUaW1lLVN0YW1w
# IFBDQSAyMDEwMA0GCSqGSIb3DQEBCwUAAgUA7BiKEDAiGA8yMDI1MDcwOTA2MjYy
# NFoYDzIwMjUwNzEwMDYyNjI0WjB0MDoGCisGAQQBhFkKBAExLDAqMAoCBQDsGIoQ
# AgEAMAcCAQACAgd2MAcCAQACAhKYMAoCBQDsGduQAgEAMDYGCisGAQQBhFkKBAIx
# KDAmMAwGCisGAQQBhFkKAwKgCjAIAgEAAgMHoSChCjAIAgEAAgMBhqAwDQYJKoZI
# hvcNAQELBQADggEBAJfv0EYW+0tTGY8DDKUmLlAe7HpHa9PqyEUb0KlF116LPwoH
# 3K0GF0NFauoAeZnhkhANWP08dkeLPEnmp2pxqXitIeRu7TdLMc58+8BGXyckqME0
# 35PFmLSXMmcQlQVpmHfYzh3ZrnVlzxKMvvYfHa3HEaTIyHJuB7oa5DIjpctApKul
# 4Kwa+P0lRrRstbCSkuPoySp+Nv+PMyYvOS0GuoA9IT6OhZyCtd1tqkmoEuG/Fkh2
# 4pmv4TpGT/pNWq7XynPoefnCvC4RqH3rTMhaxpD0H5zX98f2Mc9WK7qJYhAA9sJN
# p09CP7NtzHLtCnaKKYjJv7fIyMOuqWsWy1ttTkAxggQNMIIECQIBATCBkzB8MQsw
# CQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9u
# ZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSYwJAYDVQQDEx1NaWNy
# b3NvZnQgVGltZS1TdGFtcCBQQ0EgMjAxMAITMwAAAg4syyh9lSB1YwABAAACDjAN
# BglghkgBZQMEAgEFAKCCAUowGgYJKoZIhvcNAQkDMQ0GCyqGSIb3DQEJEAEEMC8G
# CSqGSIb3DQEJBDEiBCCFjkfNSn56oTSyA2qNbVFFqnUGd1jXaLzTtOeU/7pVbzCB
# +gYLKoZIhvcNAQkQAi8xgeowgecwgeQwgb0EIAF0HXMl8OmBkK267mxobKSihwOd
# P0eUNXQMypPzTxKGMIGYMIGApH4wfDELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldh
# c2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBD
# b3Jwb3JhdGlvbjEmMCQGA1UEAxMdTWljcm9zb2Z0IFRpbWUtU3RhbXAgUENBIDIw
# MTACEzMAAAIOLMsofZUgdWMAAQAAAg4wIgQgym6+npRzvsZNJcCy+PAQhjspKnOp
# tyujEzAKM58DDdAwDQYJKoZIhvcNAQELBQAEggIAcw9n6V/NzArKJTR1sXQ3jNIa
# lVFwSkOmOaKipL4Hld7DlqPEnAjzqHfeG5ZMmjekEN+gywcO6RsGiZoJIrYFE07y
# QahoMvzdRVyR5eaJCv5L+N0v3whuRR8PowlFKES8qbKRSTOggL6MX6WWVx0gaNgG
# gaVrNKJhxizd1uput2D7j/QGx7wVUuz/koD9w/O2XoL1dJIAkwmgN5QNB9YlSAhW
# E5rTcFuvKQFIvWihhsUykMLY8YeV/s+jyBhexWaJtIIw5U5v++tyUopKckqjtvBy
# x/Aydhp/Jz6ySPQkljcOoAutszvcGKhODw8tJGpExZUI2MdaK/2kNVsemzmDb8yz
# /lV66MLCjEO4l4YG93mYzT0Uuet/cauB97mcShC38u1Gx4if96L5xB0yCy6/Eox+
# zZVSP5fpvl9ysx74Hd7lvTp6fwAtF/R18KbUrJIAoNeU1GgnZazfjbfrd52EeK3C
# 3deHPUNLAHow516xkCdybFFRPvzLj/kNH3mHl2VXGca9LGtIGi/jusysmg6Hzb+d
# jt/zKsS5BePegyUwz2k1TXMOEvnTBh37oCYzliz0mB7EDEYX90gTD+K5U/F/5SZe
# zFrh09sthisrqtYQut8xqWgVWSmkZDW3HGgyisTZREqo8GQTthJxIeKdqdFACQsR
# EoW/CVImOid5SLWn3nU=
# SIG # End signature block
