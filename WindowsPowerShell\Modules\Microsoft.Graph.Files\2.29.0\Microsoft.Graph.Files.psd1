#
# Module manifest for module 'Microsoft.Graph.Files'
#
# Generated by: Microsoft Corporation
#
# Generated on: 7/9/2025
#

@{

# Script module or binary module file associated with this manifest.
RootModule = './Microsoft.Graph.Files.psm1'

# Version number of this module.
ModuleVersion = '2.29.0'

# Supported PSEditions
CompatiblePSEditions = 'Core', 'Desktop'

# ID used to uniquely identify this module
GUID = '45ddab16-496a-4ef0-ac17-dbf0f93494d3'

# Author of this module
Author = 'Microsoft Corporation'

# Company or vendor of this module
CompanyName = 'Microsoft Corporation'

# Copyright statement for this module
Copyright = 'Microsoft Corporation. All rights reserved.'

# Description of the functionality provided by this module
Description = 'Microsoft Graph PowerShell Cmdlets'

# Minimum version of the PowerShell engine required by this module
PowerShellVersion = '5.1'

# Name of the PowerShell host required by this module
# PowerShellHostName = ''

# Minimum version of the PowerShell host required by this module
# PowerShellHostVersion = ''

# Minimum version of Microsoft .NET Framework required by this module. This prerequisite is valid for the PowerShell Desktop edition only.
DotNetFrameworkVersion = '4.7.2'

# Minimum version of the common language runtime (CLR) required by this module. This prerequisite is valid for the PowerShell Desktop edition only.
# ClrVersion = ''

# Processor architecture (None, X86, Amd64) required by this module
# ProcessorArchitecture = ''

# Modules that must be imported into the global environment prior to importing this module
RequiredModules = @(@{ModuleName = 'Microsoft.Graph.Authentication'; RequiredVersion = '2.29.0'; })

# Assemblies that must be loaded prior to importing this module
RequiredAssemblies = './bin/Microsoft.Graph.Files.private.dll'

# Script files (.ps1) that are run in the caller's environment prior to importing this module.
# ScriptsToProcess = @()

# Type files (.ps1xml) to be loaded when importing this module
# TypesToProcess = @()

# Format files (.ps1xml) to be loaded when importing this module
FormatsToProcess = './Microsoft.Graph.Files.format.ps1xml'

# Modules to import as nested modules of the module specified in RootModule/ModuleToProcess
# NestedModules = @()

# Functions to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no functions to export.
FunctionsToExport = 'Add-MgDriveListContentTypeCopy', 
               'Add-MgDriveListContentTypeCopyFromContentTypeHub', 
               'Add-MgGroupDriveListContentTypeCopy', 
               'Add-MgGroupDriveListContentTypeCopyFromContentTypeHub', 
               'Add-MgShareListContentTypeCopy', 
               'Add-MgShareListContentTypeCopyFromContentTypeHub', 
               'Add-MgUserDriveListContentTypeCopy', 
               'Add-MgUserDriveListContentTypeCopyFromContentTypeHub', 
               'Copy-MgDriveItem', 
               'Copy-MgDriveListContentTypeToDefaultContentLocation', 
               'Copy-MgDriveRoot', 'Copy-MgGroupDriveItem', 
               'Copy-MgGroupDriveListContentTypeToDefaultContentLocation', 
               'Copy-MgGroupDriveRoot', 
               'Copy-MgShareListContentTypeToDefaultContentLocation', 
               'Copy-MgUserDriveItem', 
               'Copy-MgUserDriveListContentTypeToDefaultContentLocation', 
               'Copy-MgUserDriveRoot', 'Get-MgDrive', 'Get-MgDriveBundle', 
               'Get-MgDriveBundleContent', 'Get-MgDriveBundleCount', 
               'Get-MgDriveContentTypeBase', 'Get-MgDriveContentTypeBaseType', 
               'Get-MgDriveContentTypeBaseTypeCount', 'Get-MgDriveCreatedByUser', 
               'Get-MgDriveCreatedByUserMailboxSetting', 
               'Get-MgDriveCreatedByUserServiceProvisioningError', 
               'Get-MgDriveCreatedByUserServiceProvisioningErrorCount', 
               'Get-MgDriveFollowing', 'Get-MgDriveFollowingContent', 
               'Get-MgDriveFollowingCount', 'Get-MgDriveItem', 
               'Get-MgDriveItemActivityByInterval', 'Get-MgDriveItemAnalytic', 
               'Get-MgDriveItemAnalyticItemActivityStat', 
               'Get-MgDriveItemAnalyticItemActivityStatActivity', 
               'Get-MgDriveItemAnalyticItemActivityStatCount', 
               'Get-MgDriveItemAnalyticLastSevenDay', 
               'Get-MgDriveItemAnalyticTime', 'Get-MgDriveItemChild', 
               'Get-MgDriveItemChildContent', 'Get-MgDriveItemChildCount', 
               'Get-MgDriveItemContent', 'Get-MgDriveItemCount', 
               'Get-MgDriveItemCreatedByUser', 
               'Get-MgDriveItemCreatedByUserMailboxSetting', 
               'Get-MgDriveItemCreatedByUserServiceProvisioningError', 
               'Get-MgDriveItemCreatedByUserServiceProvisioningErrorCount', 
               'Get-MgDriveItemDelta', 'Get-MgDriveItemItemLastModifiedByUser', 
               'Get-MgDriveItemItemLastModifiedByUserMailboxSetting', 
               'Get-MgDriveItemItemLastModifiedByUserServiceProvisioningError', 
               'Get-MgDriveItemItemLastModifiedByUserServiceProvisioningErrorCount', 
               'Get-MgDriveItemLastModifiedByUser', 
               'Get-MgDriveItemLastModifiedByUserMailboxSetting', 
               'Get-MgDriveItemLastModifiedByUserServiceProvisioningError', 
               'Get-MgDriveItemLastModifiedByUserServiceProvisioningErrorCount', 
               'Get-MgDriveItemListItem', 
               'Get-MgDriveItemListItemActivityByInterval', 
               'Get-MgDriveItemListItemAnalytic', 
               'Get-MgDriveItemListItemCreatedByUser', 
               'Get-MgDriveItemListItemCreatedByUserMailboxSetting', 
               'Get-MgDriveItemListItemCreatedByUserServiceProvisioningError', 
               'Get-MgDriveItemListItemCreatedByUserServiceProvisioningErrorCount', 
               'Get-MgDriveItemListItemDocumentSetVersion', 
               'Get-MgDriveItemListItemDocumentSetVersionCount', 
               'Get-MgDriveItemListItemDocumentSetVersionField', 
               'Get-MgDriveItemListItemDriveItem', 
               'Get-MgDriveItemListItemDriveItemContent', 
               'Get-MgDriveItemListItemField', 'Get-MgDriveItemListItemVersion', 
               'Get-MgDriveItemListItemVersionCount', 
               'Get-MgDriveItemListItemVersionField', 'Get-MgDriveItemPermission', 
               'Get-MgDriveItemPermissionCount', 'Get-MgDriveItemRetentionLabel', 
               'Get-MgDriveItemSubscription', 'Get-MgDriveItemSubscriptionCount', 
               'Get-MgDriveItemThumbnail', 'Get-MgDriveItemThumbnailCount', 
               'Get-MgDriveItemVersion', 'Get-MgDriveItemVersionContent', 
               'Get-MgDriveItemVersionCount', 'Get-MgDriveLastModifiedByUser', 
               'Get-MgDriveLastModifiedByUserMailboxSetting', 
               'Get-MgDriveLastModifiedByUserServiceProvisioningError', 
               'Get-MgDriveLastModifiedByUserServiceProvisioningErrorCount', 
               'Get-MgDriveList', 'Get-MgDriveListColumn', 
               'Get-MgDriveListColumnCount', 'Get-MgDriveListColumnSourceColumn', 
               'Get-MgDriveListContentType', 'Get-MgDriveListContentTypeColumn', 
               'Get-MgDriveListContentTypeColumnCount', 
               'Get-MgDriveListContentTypeColumnLink', 
               'Get-MgDriveListContentTypeColumnLinkCount', 
               'Get-MgDriveListContentTypeColumnPosition', 
               'Get-MgDriveListContentTypeColumnPositionCount', 
               'Get-MgDriveListContentTypeColumnSourceColumn', 
               'Get-MgDriveListContentTypeCompatibleHubContentType', 
               'Get-MgDriveListContentTypeCount', 'Get-MgDriveListCreatedByUser', 
               'Get-MgDriveListCreatedByUserMailboxSetting', 
               'Get-MgDriveListCreatedByUserServiceProvisioningError', 
               'Get-MgDriveListCreatedByUserServiceProvisioningErrorCount', 
               'Get-MgDriveListDrive', 'Get-MgDriveListItem', 
               'Get-MgDriveListItemActivityByInterval', 
               'Get-MgDriveListItemAnalytic', 'Get-MgDriveListItemCount', 
               'Get-MgDriveListItemCreatedByUser', 
               'Get-MgDriveListItemCreatedByUserMailboxSetting', 
               'Get-MgDriveListItemCreatedByUserServiceProvisioningError', 
               'Get-MgDriveListItemCreatedByUserServiceProvisioningErrorCount', 
               'Get-MgDriveListItemDelta', 'Get-MgDriveListItemDocumentSetVersion', 
               'Get-MgDriveListItemDocumentSetVersionCount', 
               'Get-MgDriveListItemDocumentSetVersionField', 
               'Get-MgDriveListItemDriveItem', 
               'Get-MgDriveListItemDriveItemContent', 'Get-MgDriveListItemField', 
               'Get-MgDriveListItemVersion', 'Get-MgDriveListItemVersionCount', 
               'Get-MgDriveListItemVersionField', 'Get-MgDriveListOperation', 
               'Get-MgDriveListOperationCount', 'Get-MgDriveListSubscription', 
               'Get-MgDriveListSubscriptionCount', 'Get-MgDriveRoot', 
               'Get-MgDriveRootActivityByInterval', 'Get-MgDriveRootAnalytic', 
               'Get-MgDriveRootAnalyticItemActivityStat', 
               'Get-MgDriveRootAnalyticItemActivityStatActivity', 
               'Get-MgDriveRootAnalyticItemActivityStatCount', 
               'Get-MgDriveRootAnalyticLastSevenDay', 
               'Get-MgDriveRootAnalyticTime', 'Get-MgDriveRootChild', 
               'Get-MgDriveRootChildContent', 'Get-MgDriveRootChildCount', 
               'Get-MgDriveRootContent', 'Get-MgDriveRootCreatedByUser', 
               'Get-MgDriveRootCreatedByUserMailboxSetting', 
               'Get-MgDriveRootCreatedByUserServiceProvisioningError', 
               'Get-MgDriveRootCreatedByUserServiceProvisioningErrorCount', 
               'Get-MgDriveRootDelta', 'Get-MgDriveRootItemLastModifiedByUser', 
               'Get-MgDriveRootItemLastModifiedByUserMailboxSetting', 
               'Get-MgDriveRootItemLastModifiedByUserServiceProvisioningError', 
               'Get-MgDriveRootItemLastModifiedByUserServiceProvisioningErrorCount', 
               'Get-MgDriveRootLastModifiedByUser', 
               'Get-MgDriveRootLastModifiedByUserMailboxSetting', 
               'Get-MgDriveRootLastModifiedByUserServiceProvisioningError', 
               'Get-MgDriveRootLastModifiedByUserServiceProvisioningErrorCount', 
               'Get-MgDriveRootListItem', 
               'Get-MgDriveRootListItemActivityByInterval', 
               'Get-MgDriveRootListItemAnalytic', 
               'Get-MgDriveRootListItemCreatedByUser', 
               'Get-MgDriveRootListItemCreatedByUserMailboxSetting', 
               'Get-MgDriveRootListItemCreatedByUserServiceProvisioningError', 
               'Get-MgDriveRootListItemCreatedByUserServiceProvisioningErrorCount', 
               'Get-MgDriveRootListItemDocumentSetVersion', 
               'Get-MgDriveRootListItemDocumentSetVersionCount', 
               'Get-MgDriveRootListItemDocumentSetVersionField', 
               'Get-MgDriveRootListItemDriveItem', 
               'Get-MgDriveRootListItemDriveItemContent', 
               'Get-MgDriveRootListItemField', 'Get-MgDriveRootListItemVersion', 
               'Get-MgDriveRootListItemVersionCount', 
               'Get-MgDriveRootListItemVersionField', 'Get-MgDriveRootPermission', 
               'Get-MgDriveRootPermissionCount', 'Get-MgDriveRootRetentionLabel', 
               'Get-MgDriveRootSubscription', 'Get-MgDriveRootSubscriptionCount', 
               'Get-MgDriveRootThumbnail', 'Get-MgDriveRootThumbnailCount', 
               'Get-MgDriveRootVersion', 'Get-MgDriveRootVersionContent', 
               'Get-MgDriveRootVersionCount', 'Get-MgDriveSpecial', 
               'Get-MgDriveSpecialContent', 'Get-MgDriveSpecialCount', 
               'Get-MgGroupDefaultDrive', 'Get-MgGroupDrive', 
               'Get-MgGroupDriveBundle', 'Get-MgGroupDriveBundleContent', 
               'Get-MgGroupDriveBundleCount', 'Get-MgGroupDriveContentTypeBase', 
               'Get-MgGroupDriveContentTypeBaseType', 
               'Get-MgGroupDriveContentTypeBaseTypeCount', 'Get-MgGroupDriveCount', 
               'Get-MgGroupDriveCreatedByUser', 
               'Get-MgGroupDriveCreatedByUserMailboxSetting', 
               'Get-MgGroupDriveCreatedByUserServiceProvisioningError', 
               'Get-MgGroupDriveCreatedByUserServiceProvisioningErrorCount', 
               'Get-MgGroupDriveFollowing', 'Get-MgGroupDriveFollowingContent', 
               'Get-MgGroupDriveFollowingCount', 'Get-MgGroupDriveItem', 
               'Get-MgGroupDriveItemActivityByInterval', 
               'Get-MgGroupDriveItemAnalytic', 
               'Get-MgGroupDriveItemAnalyticItemActivityStat', 
               'Get-MgGroupDriveItemAnalyticItemActivityStatActivity', 
               'Get-MgGroupDriveItemAnalyticItemActivityStatCount', 
               'Get-MgGroupDriveItemAnalyticLastSevenDay', 
               'Get-MgGroupDriveItemAnalyticTime', 'Get-MgGroupDriveItemChild', 
               'Get-MgGroupDriveItemChildContent', 
               'Get-MgGroupDriveItemChildCount', 'Get-MgGroupDriveItemContent', 
               'Get-MgGroupDriveItemCount', 'Get-MgGroupDriveItemCreatedByUser', 
               'Get-MgGroupDriveItemCreatedByUserMailboxSetting', 
               'Get-MgGroupDriveItemCreatedByUserServiceProvisioningError', 
               'Get-MgGroupDriveItemCreatedByUserServiceProvisioningErrorCount', 
               'Get-MgGroupDriveItemDelta', 
               'Get-MgGroupDriveItemItemLastModifiedByUser', 
               'Get-MgGroupDriveItemItemLastModifiedByUserMailboxSetting', 
               'Get-MgGroupDriveItemItemLastModifiedByUserServiceProvisioningError', 
               'Get-MgGroupDriveItemItemLastModifiedByUserServiceProvisioningErrorCount', 
               'Get-MgGroupDriveItemLastModifiedByUser', 
               'Get-MgGroupDriveItemLastModifiedByUserMailboxSetting', 
               'Get-MgGroupDriveItemLastModifiedByUserServiceProvisioningError', 
               'Get-MgGroupDriveItemLastModifiedByUserServiceProvisioningErrorCount', 
               'Get-MgGroupDriveItemListItem', 
               'Get-MgGroupDriveItemListItemActivityByInterval', 
               'Get-MgGroupDriveItemListItemAnalytic', 
               'Get-MgGroupDriveItemListItemCreatedByUser', 
               'Get-MgGroupDriveItemListItemCreatedByUserMailboxSetting', 
               'Get-MgGroupDriveItemListItemCreatedByUserServiceProvisioningError', 
               'Get-MgGroupDriveItemListItemCreatedByUserServiceProvisioningErrorCount', 
               'Get-MgGroupDriveItemListItemDocumentSetVersion', 
               'Get-MgGroupDriveItemListItemDocumentSetVersionCount', 
               'Get-MgGroupDriveItemListItemDocumentSetVersionField', 
               'Get-MgGroupDriveItemListItemDriveItem', 
               'Get-MgGroupDriveItemListItemDriveItemContent', 
               'Get-MgGroupDriveItemListItemField', 
               'Get-MgGroupDriveItemListItemVersion', 
               'Get-MgGroupDriveItemListItemVersionCount', 
               'Get-MgGroupDriveItemListItemVersionField', 
               'Get-MgGroupDriveItemPermission', 
               'Get-MgGroupDriveItemPermissionCount', 
               'Get-MgGroupDriveItemRetentionLabel', 
               'Get-MgGroupDriveItemSubscription', 
               'Get-MgGroupDriveItemSubscriptionCount', 
               'Get-MgGroupDriveItemThumbnail', 
               'Get-MgGroupDriveItemThumbnailCount', 'Get-MgGroupDriveItemVersion', 
               'Get-MgGroupDriveItemVersionContent', 
               'Get-MgGroupDriveItemVersionCount', 
               'Get-MgGroupDriveLastModifiedByUser', 
               'Get-MgGroupDriveLastModifiedByUserMailboxSetting', 
               'Get-MgGroupDriveLastModifiedByUserServiceProvisioningError', 
               'Get-MgGroupDriveLastModifiedByUserServiceProvisioningErrorCount', 
               'Get-MgGroupDriveList', 'Get-MgGroupDriveListColumn', 
               'Get-MgGroupDriveListColumnCount', 
               'Get-MgGroupDriveListColumnSourceColumn', 
               'Get-MgGroupDriveListContentType', 
               'Get-MgGroupDriveListContentTypeColumn', 
               'Get-MgGroupDriveListContentTypeColumnCount', 
               'Get-MgGroupDriveListContentTypeColumnLink', 
               'Get-MgGroupDriveListContentTypeColumnLinkCount', 
               'Get-MgGroupDriveListContentTypeColumnPosition', 
               'Get-MgGroupDriveListContentTypeColumnPositionCount', 
               'Get-MgGroupDriveListContentTypeColumnSourceColumn', 
               'Get-MgGroupDriveListContentTypeCompatibleHubContentType', 
               'Get-MgGroupDriveListContentTypeCount', 
               'Get-MgGroupDriveListCreatedByUser', 
               'Get-MgGroupDriveListCreatedByUserMailboxSetting', 
               'Get-MgGroupDriveListCreatedByUserServiceProvisioningError', 
               'Get-MgGroupDriveListCreatedByUserServiceProvisioningErrorCount', 
               'Get-MgGroupDriveListDrive', 'Get-MgGroupDriveListItem', 
               'Get-MgGroupDriveListItemActivityByInterval', 
               'Get-MgGroupDriveListItemAnalytic', 
               'Get-MgGroupDriveListItemCreatedByUser', 
               'Get-MgGroupDriveListItemCreatedByUserMailboxSetting', 
               'Get-MgGroupDriveListItemCreatedByUserServiceProvisioningError', 
               'Get-MgGroupDriveListItemCreatedByUserServiceProvisioningErrorCount', 
               'Get-MgGroupDriveListItemDelta', 
               'Get-MgGroupDriveListItemDocumentSetVersion', 
               'Get-MgGroupDriveListItemDocumentSetVersionCount', 
               'Get-MgGroupDriveListItemDocumentSetVersionField', 
               'Get-MgGroupDriveListItemDriveItem', 
               'Get-MgGroupDriveListItemDriveItemContent', 
               'Get-MgGroupDriveListItemField', 'Get-MgGroupDriveListItemVersion', 
               'Get-MgGroupDriveListItemVersionCount', 
               'Get-MgGroupDriveListItemVersionField', 
               'Get-MgGroupDriveListOperation', 
               'Get-MgGroupDriveListOperationCount', 
               'Get-MgGroupDriveListSubscription', 
               'Get-MgGroupDriveListSubscriptionCount', 'Get-MgGroupDriveRoot', 
               'Get-MgGroupDriveRootActivityByInterval', 
               'Get-MgGroupDriveRootAnalytic', 
               'Get-MgGroupDriveRootAnalyticItemActivityStat', 
               'Get-MgGroupDriveRootAnalyticItemActivityStatActivity', 
               'Get-MgGroupDriveRootAnalyticItemActivityStatCount', 
               'Get-MgGroupDriveRootAnalyticLastSevenDay', 
               'Get-MgGroupDriveRootAnalyticTime', 'Get-MgGroupDriveRootChild', 
               'Get-MgGroupDriveRootChildContent', 
               'Get-MgGroupDriveRootChildCount', 'Get-MgGroupDriveRootContent', 
               'Get-MgGroupDriveRootCreatedByUser', 
               'Get-MgGroupDriveRootCreatedByUserMailboxSetting', 
               'Get-MgGroupDriveRootCreatedByUserServiceProvisioningError', 
               'Get-MgGroupDriveRootCreatedByUserServiceProvisioningErrorCount', 
               'Get-MgGroupDriveRootDelta', 
               'Get-MgGroupDriveRootItemLastModifiedByUser', 
               'Get-MgGroupDriveRootItemLastModifiedByUserMailboxSetting', 
               'Get-MgGroupDriveRootItemLastModifiedByUserServiceProvisioningError', 
               'Get-MgGroupDriveRootItemLastModifiedByUserServiceProvisioningErrorCount', 
               'Get-MgGroupDriveRootLastModifiedByUser', 
               'Get-MgGroupDriveRootLastModifiedByUserMailboxSetting', 
               'Get-MgGroupDriveRootLastModifiedByUserServiceProvisioningError', 
               'Get-MgGroupDriveRootLastModifiedByUserServiceProvisioningErrorCount', 
               'Get-MgGroupDriveRootListItem', 
               'Get-MgGroupDriveRootListItemActivityByInterval', 
               'Get-MgGroupDriveRootListItemAnalytic', 
               'Get-MgGroupDriveRootListItemCreatedByUser', 
               'Get-MgGroupDriveRootListItemCreatedByUserMailboxSetting', 
               'Get-MgGroupDriveRootListItemCreatedByUserServiceProvisioningError', 
               'Get-MgGroupDriveRootListItemCreatedByUserServiceProvisioningErrorCount', 
               'Get-MgGroupDriveRootListItemDocumentSetVersion', 
               'Get-MgGroupDriveRootListItemDocumentSetVersionCount', 
               'Get-MgGroupDriveRootListItemDocumentSetVersionField', 
               'Get-MgGroupDriveRootListItemDriveItem', 
               'Get-MgGroupDriveRootListItemDriveItemContent', 
               'Get-MgGroupDriveRootListItemField', 
               'Get-MgGroupDriveRootListItemVersion', 
               'Get-MgGroupDriveRootListItemVersionCount', 
               'Get-MgGroupDriveRootListItemVersionField', 
               'Get-MgGroupDriveRootPermission', 
               'Get-MgGroupDriveRootPermissionCount', 
               'Get-MgGroupDriveRootRetentionLabel', 
               'Get-MgGroupDriveRootSubscription', 
               'Get-MgGroupDriveRootSubscriptionCount', 
               'Get-MgGroupDriveRootThumbnail', 
               'Get-MgGroupDriveRootThumbnailCount', 'Get-MgGroupDriveRootVersion', 
               'Get-MgGroupDriveRootVersionContent', 
               'Get-MgGroupDriveRootVersionCount', 'Get-MgGroupDriveSpecial', 
               'Get-MgGroupDriveSpecialContent', 'Get-MgGroupDriveSpecialCount', 
               'Get-MgShareContentTypeBase', 'Get-MgShareContentTypeBaseType', 
               'Get-MgShareContentTypeBaseTypeCount', 'Get-MgShareCount', 
               'Get-MgShareCreatedByUser', 
               'Get-MgShareCreatedByUserMailboxSetting', 
               'Get-MgShareCreatedByUserServiceProvisioningError', 
               'Get-MgShareCreatedByUserServiceProvisioningErrorCount', 
               'Get-MgShareDriveItem', 'Get-MgShareDriveItemContent', 
               'Get-MgShareItem', 'Get-MgShareItemContent', 'Get-MgShareItemCount', 
               'Get-MgShareItemLastModifiedByUser', 
               'Get-MgShareItemLastModifiedByUserMailboxSetting', 
               'Get-MgShareItemLastModifiedByUserServiceProvisioningError', 
               'Get-MgShareItemLastModifiedByUserServiceProvisioningErrorCount', 
               'Get-MgShareLastModifiedByUser', 
               'Get-MgShareLastModifiedByUserMailboxSetting', 
               'Get-MgShareLastModifiedByUserServiceProvisioningError', 
               'Get-MgShareLastModifiedByUserServiceProvisioningErrorCount', 
               'Get-MgShareList', 'Get-MgShareListColumn', 
               'Get-MgShareListColumnCount', 'Get-MgShareListColumnSourceColumn', 
               'Get-MgShareListContentType', 'Get-MgShareListContentTypeColumn', 
               'Get-MgShareListContentTypeColumnCount', 
               'Get-MgShareListContentTypeColumnLink', 
               'Get-MgShareListContentTypeColumnLinkCount', 
               'Get-MgShareListContentTypeColumnPosition', 
               'Get-MgShareListContentTypeColumnPositionCount', 
               'Get-MgShareListContentTypeColumnSourceColumn', 
               'Get-MgShareListContentTypeCompatibleHubContentType', 
               'Get-MgShareListContentTypeCount', 'Get-MgShareListCreatedByUser', 
               'Get-MgShareListCreatedByUserMailboxSetting', 
               'Get-MgShareListCreatedByUserServiceProvisioningError', 
               'Get-MgShareListCreatedByUserServiceProvisioningErrorCount', 
               'Get-MgShareListDrive', 'Get-MgShareListItem', 
               'Get-MgShareListItemActivityByInterval', 
               'Get-MgShareListItemAnalytic', 'Get-MgShareListItemCreatedByUser', 
               'Get-MgShareListItemCreatedByUserMailboxSetting', 
               'Get-MgShareListItemCreatedByUserServiceProvisioningError', 
               'Get-MgShareListItemCreatedByUserServiceProvisioningErrorCount', 
               'Get-MgShareListItemDelta', 'Get-MgShareListItemDocumentSetVersion', 
               'Get-MgShareListItemDocumentSetVersionCount', 
               'Get-MgShareListItemDocumentSetVersionField', 
               'Get-MgShareListItemDriveItem', 
               'Get-MgShareListItemDriveItemContent', 'Get-MgShareListItemField', 
               'Get-MgShareListItemVersion', 'Get-MgShareListItemVersionCount', 
               'Get-MgShareListItemVersionField', 'Get-MgShareListOperation', 
               'Get-MgShareListOperationCount', 'Get-MgShareListSubscription', 
               'Get-MgShareListSubscriptionCount', 'Get-MgSharePermission', 
               'Get-MgShareRoot', 'Get-MgShareRootContent', 
               'Get-MgShareSharedDriveItemSharedDriveItem', 'Get-MgShareSite', 
               'Get-MgUserDefaultDrive', 'Get-MgUserDrive', 'Get-MgUserDriveBundle', 
               'Get-MgUserDriveBundleContent', 'Get-MgUserDriveBundleCount', 
               'Get-MgUserDriveContentTypeBase', 
               'Get-MgUserDriveContentTypeBaseType', 
               'Get-MgUserDriveContentTypeBaseTypeCount', 'Get-MgUserDriveCount', 
               'Get-MgUserDriveCreatedByUser', 
               'Get-MgUserDriveCreatedByUserMailboxSetting', 
               'Get-MgUserDriveCreatedByUserServiceProvisioningError', 
               'Get-MgUserDriveCreatedByUserServiceProvisioningErrorCount', 
               'Get-MgUserDriveFollowing', 'Get-MgUserDriveFollowingContent', 
               'Get-MgUserDriveFollowingCount', 'Get-MgUserDriveItem', 
               'Get-MgUserDriveItemActivityByInterval', 
               'Get-MgUserDriveItemAnalytic', 
               'Get-MgUserDriveItemAnalyticItemActivityStat', 
               'Get-MgUserDriveItemAnalyticItemActivityStatActivity', 
               'Get-MgUserDriveItemAnalyticItemActivityStatCount', 
               'Get-MgUserDriveItemAnalyticLastSevenDay', 
               'Get-MgUserDriveItemAnalyticTime', 'Get-MgUserDriveItemChild', 
               'Get-MgUserDriveItemChildContent', 'Get-MgUserDriveItemChildCount', 
               'Get-MgUserDriveItemContent', 'Get-MgUserDriveItemCount', 
               'Get-MgUserDriveItemCreatedByUser', 
               'Get-MgUserDriveItemCreatedByUserMailboxSetting', 
               'Get-MgUserDriveItemCreatedByUserServiceProvisioningError', 
               'Get-MgUserDriveItemCreatedByUserServiceProvisioningErrorCount', 
               'Get-MgUserDriveItemDelta', 
               'Get-MgUserDriveItemItemLastModifiedByUser', 
               'Get-MgUserDriveItemItemLastModifiedByUserMailboxSetting', 
               'Get-MgUserDriveItemItemLastModifiedByUserServiceProvisioningError', 
               'Get-MgUserDriveItemItemLastModifiedByUserServiceProvisioningErrorCount', 
               'Get-MgUserDriveItemLastModifiedByUser', 
               'Get-MgUserDriveItemLastModifiedByUserMailboxSetting', 
               'Get-MgUserDriveItemLastModifiedByUserServiceProvisioningError', 
               'Get-MgUserDriveItemLastModifiedByUserServiceProvisioningErrorCount', 
               'Get-MgUserDriveItemListItem', 
               'Get-MgUserDriveItemListItemActivityByInterval', 
               'Get-MgUserDriveItemListItemAnalytic', 
               'Get-MgUserDriveItemListItemCreatedByUser', 
               'Get-MgUserDriveItemListItemCreatedByUserMailboxSetting', 
               'Get-MgUserDriveItemListItemCreatedByUserServiceProvisioningError', 
               'Get-MgUserDriveItemListItemCreatedByUserServiceProvisioningErrorCount', 
               'Get-MgUserDriveItemListItemDocumentSetVersion', 
               'Get-MgUserDriveItemListItemDocumentSetVersionCount', 
               'Get-MgUserDriveItemListItemDocumentSetVersionField', 
               'Get-MgUserDriveItemListItemDriveItem', 
               'Get-MgUserDriveItemListItemDriveItemContent', 
               'Get-MgUserDriveItemListItemField', 
               'Get-MgUserDriveItemListItemVersion', 
               'Get-MgUserDriveItemListItemVersionCount', 
               'Get-MgUserDriveItemListItemVersionField', 
               'Get-MgUserDriveItemPermission', 
               'Get-MgUserDriveItemPermissionCount', 
               'Get-MgUserDriveItemRetentionLabel', 
               'Get-MgUserDriveItemSubscription', 
               'Get-MgUserDriveItemSubscriptionCount', 
               'Get-MgUserDriveItemThumbnail', 'Get-MgUserDriveItemThumbnailCount', 
               'Get-MgUserDriveItemVersion', 'Get-MgUserDriveItemVersionContent', 
               'Get-MgUserDriveItemVersionCount', 
               'Get-MgUserDriveLastModifiedByUser', 
               'Get-MgUserDriveLastModifiedByUserMailboxSetting', 
               'Get-MgUserDriveLastModifiedByUserServiceProvisioningError', 
               'Get-MgUserDriveLastModifiedByUserServiceProvisioningErrorCount', 
               'Get-MgUserDriveList', 'Get-MgUserDriveListColumn', 
               'Get-MgUserDriveListColumnCount', 
               'Get-MgUserDriveListColumnSourceColumn', 
               'Get-MgUserDriveListContentType', 
               'Get-MgUserDriveListContentTypeColumn', 
               'Get-MgUserDriveListContentTypeColumnCount', 
               'Get-MgUserDriveListContentTypeColumnLink', 
               'Get-MgUserDriveListContentTypeColumnLinkCount', 
               'Get-MgUserDriveListContentTypeColumnPosition', 
               'Get-MgUserDriveListContentTypeColumnPositionCount', 
               'Get-MgUserDriveListContentTypeColumnSourceColumn', 
               'Get-MgUserDriveListContentTypeCompatibleHubContentType', 
               'Get-MgUserDriveListContentTypeCount', 
               'Get-MgUserDriveListCreatedByUser', 
               'Get-MgUserDriveListCreatedByUserMailboxSetting', 
               'Get-MgUserDriveListCreatedByUserServiceProvisioningError', 
               'Get-MgUserDriveListCreatedByUserServiceProvisioningErrorCount', 
               'Get-MgUserDriveListDrive', 'Get-MgUserDriveListItem', 
               'Get-MgUserDriveListItemActivityByInterval', 
               'Get-MgUserDriveListItemAnalytic', 
               'Get-MgUserDriveListItemCreatedByUser', 
               'Get-MgUserDriveListItemCreatedByUserMailboxSetting', 
               'Get-MgUserDriveListItemCreatedByUserServiceProvisioningError', 
               'Get-MgUserDriveListItemCreatedByUserServiceProvisioningErrorCount', 
               'Get-MgUserDriveListItemDelta', 
               'Get-MgUserDriveListItemDocumentSetVersion', 
               'Get-MgUserDriveListItemDocumentSetVersionCount', 
               'Get-MgUserDriveListItemDocumentSetVersionField', 
               'Get-MgUserDriveListItemDriveItem', 
               'Get-MgUserDriveListItemDriveItemContent', 
               'Get-MgUserDriveListItemField', 'Get-MgUserDriveListItemVersion', 
               'Get-MgUserDriveListItemVersionCount', 
               'Get-MgUserDriveListItemVersionField', 
               'Get-MgUserDriveListOperation', 'Get-MgUserDriveListOperationCount', 
               'Get-MgUserDriveListSubscription', 
               'Get-MgUserDriveListSubscriptionCount', 'Get-MgUserDriveRoot', 
               'Get-MgUserDriveRootActivityByInterval', 
               'Get-MgUserDriveRootAnalytic', 
               'Get-MgUserDriveRootAnalyticItemActivityStat', 
               'Get-MgUserDriveRootAnalyticItemActivityStatActivity', 
               'Get-MgUserDriveRootAnalyticItemActivityStatCount', 
               'Get-MgUserDriveRootAnalyticLastSevenDay', 
               'Get-MgUserDriveRootAnalyticTime', 'Get-MgUserDriveRootChild', 
               'Get-MgUserDriveRootChildContent', 'Get-MgUserDriveRootChildCount', 
               'Get-MgUserDriveRootContent', 'Get-MgUserDriveRootCreatedByUser', 
               'Get-MgUserDriveRootCreatedByUserMailboxSetting', 
               'Get-MgUserDriveRootCreatedByUserServiceProvisioningError', 
               'Get-MgUserDriveRootCreatedByUserServiceProvisioningErrorCount', 
               'Get-MgUserDriveRootDelta', 
               'Get-MgUserDriveRootItemLastModifiedByUser', 
               'Get-MgUserDriveRootItemLastModifiedByUserMailboxSetting', 
               'Get-MgUserDriveRootItemLastModifiedByUserServiceProvisioningError', 
               'Get-MgUserDriveRootItemLastModifiedByUserServiceProvisioningErrorCount', 
               'Get-MgUserDriveRootLastModifiedByUser', 
               'Get-MgUserDriveRootLastModifiedByUserMailboxSetting', 
               'Get-MgUserDriveRootLastModifiedByUserServiceProvisioningError', 
               'Get-MgUserDriveRootLastModifiedByUserServiceProvisioningErrorCount', 
               'Get-MgUserDriveRootListItem', 
               'Get-MgUserDriveRootListItemActivityByInterval', 
               'Get-MgUserDriveRootListItemAnalytic', 
               'Get-MgUserDriveRootListItemCreatedByUser', 
               'Get-MgUserDriveRootListItemCreatedByUserMailboxSetting', 
               'Get-MgUserDriveRootListItemCreatedByUserServiceProvisioningError', 
               'Get-MgUserDriveRootListItemCreatedByUserServiceProvisioningErrorCount', 
               'Get-MgUserDriveRootListItemDocumentSetVersion', 
               'Get-MgUserDriveRootListItemDocumentSetVersionCount', 
               'Get-MgUserDriveRootListItemDocumentSetVersionField', 
               'Get-MgUserDriveRootListItemDriveItem', 
               'Get-MgUserDriveRootListItemDriveItemContent', 
               'Get-MgUserDriveRootListItemField', 
               'Get-MgUserDriveRootListItemVersion', 
               'Get-MgUserDriveRootListItemVersionCount', 
               'Get-MgUserDriveRootListItemVersionField', 
               'Get-MgUserDriveRootPermission', 
               'Get-MgUserDriveRootPermissionCount', 
               'Get-MgUserDriveRootRetentionLabel', 
               'Get-MgUserDriveRootSubscription', 
               'Get-MgUserDriveRootSubscriptionCount', 
               'Get-MgUserDriveRootThumbnail', 'Get-MgUserDriveRootThumbnailCount', 
               'Get-MgUserDriveRootVersion', 'Get-MgUserDriveRootVersionContent', 
               'Get-MgUserDriveRootVersionCount', 'Get-MgUserDriveSpecial', 
               'Get-MgUserDriveSpecialContent', 'Get-MgUserDriveSpecialCount', 
               'Grant-MgDriveItemPermission', 'Grant-MgDriveRootPermission', 
               'Grant-MgGroupDriveItemPermission', 
               'Grant-MgGroupDriveRootPermission', 'Grant-MgSharePermission', 
               'Grant-MgUserDriveItemPermission', 
               'Grant-MgUserDriveRootPermission', 'Invoke-MgCheckinDriveItem', 
               'Invoke-MgCheckinDriveRoot', 'Invoke-MgCheckinGroupDriveItem', 
               'Invoke-MgCheckinGroupDriveRoot', 'Invoke-MgCheckinUserDriveItem', 
               'Invoke-MgCheckinUserDriveRoot', 'Invoke-MgCheckoutDriveItem', 
               'Invoke-MgCheckoutDriveRoot', 'Invoke-MgCheckoutGroupDriveItem', 
               'Invoke-MgCheckoutGroupDriveRoot', 'Invoke-MgCheckoutUserDriveItem', 
               'Invoke-MgCheckoutUserDriveRoot', 
               'Invoke-MgExtractDriveItemSensitivityLabel', 
               'Invoke-MgExtractDriveRootSensitivityLabel', 
               'Invoke-MgExtractGroupDriveItemSensitivityLabel', 
               'Invoke-MgExtractGroupDriveRootSensitivityLabel', 
               'Invoke-MgExtractUserDriveItemSensitivityLabel', 
               'Invoke-MgExtractUserDriveRootSensitivityLabel', 
               'Invoke-MgFollowDriveItem', 'Invoke-MgFollowDriveRoot', 
               'Invoke-MgFollowGroupDriveItem', 'Invoke-MgFollowGroupDriveRoot', 
               'Invoke-MgFollowUserDriveItem', 'Invoke-MgFollowUserDriveRoot', 
               'Invoke-MgGraphDrive', 'Invoke-MgGraphGroupDrive', 
               'Invoke-MgGraphUserDrive', 'Invoke-MgInviteDriveItem', 
               'Invoke-MgInviteDriveRoot', 'Invoke-MgInviteGroupDriveItem', 
               'Invoke-MgInviteGroupDriveRoot', 'Invoke-MgInviteUserDriveItem', 
               'Invoke-MgInviteUserDriveRoot', 'Invoke-MgPreviewDriveItem', 
               'Invoke-MgPreviewDriveRoot', 'Invoke-MgPreviewGroupDriveItem', 
               'Invoke-MgPreviewGroupDriveRoot', 'Invoke-MgPreviewUserDriveItem', 
               'Invoke-MgPreviewUserDriveRoot', 
               'Invoke-MgReauthorizeDriveItemSubscription', 
               'Invoke-MgReauthorizeDriveListSubscription', 
               'Invoke-MgReauthorizeDriveRootSubscription', 
               'Invoke-MgReauthorizeGroupDriveItemSubscription', 
               'Invoke-MgReauthorizeGroupDriveListSubscription', 
               'Invoke-MgReauthorizeGroupDriveRootSubscription', 
               'Invoke-MgReauthorizeShareListSubscription', 
               'Invoke-MgReauthorizeUserDriveItemSubscription', 
               'Invoke-MgReauthorizeUserDriveListSubscription', 
               'Invoke-MgReauthorizeUserDriveRootSubscription', 
               'Invoke-MgRecentDrive', 'Invoke-MgRecentGroupDrive', 
               'Invoke-MgRecentUserDrive', 'Invoke-MgUnfollowDriveItem', 
               'Invoke-MgUnfollowDriveRoot', 'Invoke-MgUnfollowGroupDriveItem', 
               'Invoke-MgUnfollowGroupDriveRoot', 'Invoke-MgUnfollowUserDriveItem', 
               'Invoke-MgUnfollowUserDriveRoot', 
               'Join-MgDriveListContentTypeWithHubSite', 
               'Join-MgGroupDriveListContentTypeWithHubSite', 
               'Join-MgShareListContentTypeWithHubSite', 
               'Join-MgUserDriveListContentTypeWithHubSite', 'New-MgDrive', 
               'New-MgDriveBundle', 'New-MgDriveItem', 
               'New-MgDriveItemAnalyticItemActivityStat', 'New-MgDriveItemChild', 
               'New-MgDriveItemLink', 'New-MgDriveItemListItemDocumentSetVersion', 
               'New-MgDriveItemListItemLink', 'New-MgDriveItemListItemVersion', 
               'New-MgDriveItemPermission', 'New-MgDriveItemSubscription', 
               'New-MgDriveItemThumbnail', 'New-MgDriveItemUploadSession', 
               'New-MgDriveItemVersion', 'New-MgDriveListColumn', 
               'New-MgDriveListContentType', 'New-MgDriveListContentTypeColumn', 
               'New-MgDriveListContentTypeColumnLink', 'New-MgDriveListItem', 
               'New-MgDriveListItemDocumentSetVersion', 'New-MgDriveListItemLink', 
               'New-MgDriveListItemVersion', 'New-MgDriveListOperation', 
               'New-MgDriveListSubscription', 
               'New-MgDriveRootAnalyticItemActivityStat', 'New-MgDriveRootChild', 
               'New-MgDriveRootLink', 'New-MgDriveRootListItemDocumentSetVersion', 
               'New-MgDriveRootListItemLink', 'New-MgDriveRootListItemVersion', 
               'New-MgDriveRootPermission', 'New-MgDriveRootSubscription', 
               'New-MgDriveRootThumbnail', 'New-MgDriveRootUploadSession', 
               'New-MgDriveRootVersion', 'New-MgGroupDrive', 
               'New-MgGroupDriveBundle', 'New-MgGroupDriveItem', 
               'New-MgGroupDriveItemAnalyticItemActivityStat', 
               'New-MgGroupDriveItemChild', 'New-MgGroupDriveItemLink', 
               'New-MgGroupDriveItemListItemDocumentSetVersion', 
               'New-MgGroupDriveItemListItemLink', 
               'New-MgGroupDriveItemListItemVersion', 
               'New-MgGroupDriveItemPermission', 
               'New-MgGroupDriveItemSubscription', 'New-MgGroupDriveItemThumbnail', 
               'New-MgGroupDriveItemUploadSession', 'New-MgGroupDriveItemVersion', 
               'New-MgGroupDriveListColumn', 'New-MgGroupDriveListContentType', 
               'New-MgGroupDriveListContentTypeColumn', 
               'New-MgGroupDriveListContentTypeColumnLink', 
               'New-MgGroupDriveListItem', 
               'New-MgGroupDriveListItemDocumentSetVersion', 
               'New-MgGroupDriveListItemLink', 'New-MgGroupDriveListItemVersion', 
               'New-MgGroupDriveListOperation', 'New-MgGroupDriveListSubscription', 
               'New-MgGroupDriveRootAnalyticItemActivityStat', 
               'New-MgGroupDriveRootChild', 'New-MgGroupDriveRootLink', 
               'New-MgGroupDriveRootListItemDocumentSetVersion', 
               'New-MgGroupDriveRootListItemLink', 
               'New-MgGroupDriveRootListItemVersion', 
               'New-MgGroupDriveRootPermission', 
               'New-MgGroupDriveRootSubscription', 'New-MgGroupDriveRootThumbnail', 
               'New-MgGroupDriveRootUploadSession', 'New-MgGroupDriveRootVersion', 
               'New-MgShareListColumn', 'New-MgShareListContentType', 
               'New-MgShareListContentTypeColumn', 
               'New-MgShareListContentTypeColumnLink', 'New-MgShareListItem', 
               'New-MgShareListItemDocumentSetVersion', 
               'New-MgShareListItemVersion', 'New-MgShareListOperation', 
               'New-MgShareListSubscription', 
               'New-MgShareSharedDriveItemSharedDriveItem', 'New-MgUserDrive', 
               'New-MgUserDriveBundle', 'New-MgUserDriveItem', 
               'New-MgUserDriveItemAnalyticItemActivityStat', 
               'New-MgUserDriveItemChild', 'New-MgUserDriveItemLink', 
               'New-MgUserDriveItemListItemDocumentSetVersion', 
               'New-MgUserDriveItemListItemLink', 
               'New-MgUserDriveItemListItemVersion', 
               'New-MgUserDriveItemPermission', 'New-MgUserDriveItemSubscription', 
               'New-MgUserDriveItemThumbnail', 'New-MgUserDriveItemUploadSession', 
               'New-MgUserDriveItemVersion', 'New-MgUserDriveListColumn', 
               'New-MgUserDriveListContentType', 
               'New-MgUserDriveListContentTypeColumn', 
               'New-MgUserDriveListContentTypeColumnLink', 
               'New-MgUserDriveListItem', 
               'New-MgUserDriveListItemDocumentSetVersion', 
               'New-MgUserDriveListItemLink', 'New-MgUserDriveListItemVersion', 
               'New-MgUserDriveListOperation', 'New-MgUserDriveListSubscription', 
               'New-MgUserDriveRootAnalyticItemActivityStat', 
               'New-MgUserDriveRootChild', 'New-MgUserDriveRootLink', 
               'New-MgUserDriveRootListItemDocumentSetVersion', 
               'New-MgUserDriveRootListItemLink', 
               'New-MgUserDriveRootListItemVersion', 
               'New-MgUserDriveRootPermission', 'New-MgUserDriveRootSubscription', 
               'New-MgUserDriveRootThumbnail', 'New-MgUserDriveRootUploadSession', 
               'New-MgUserDriveRootVersion', 'Publish-MgDriveListContentType', 
               'Publish-MgGroupDriveListContentType', 
               'Publish-MgShareListContentType', 
               'Publish-MgUserDriveListContentType', 'Remove-MgDrive', 
               'Remove-MgDriveBundleContent', 'Remove-MgDriveFollowingContent', 
               'Remove-MgDriveItem', 'Remove-MgDriveItemAnalytic', 
               'Remove-MgDriveItemAnalyticItemActivityStat', 
               'Remove-MgDriveItemCheckout', 'Remove-MgDriveItemChildContent', 
               'Remove-MgDriveItemContent', 'Remove-MgDriveItemListItem', 
               'Remove-MgDriveItemListItemDocumentSetVersion', 
               'Remove-MgDriveItemListItemDocumentSetVersionField', 
               'Remove-MgDriveItemListItemDriveItemContent', 
               'Remove-MgDriveItemListItemField', 
               'Remove-MgDriveItemListItemVersion', 
               'Remove-MgDriveItemListItemVersionField', 
               'Remove-MgDriveItemPermanent', 'Remove-MgDriveItemPermission', 
               'Remove-MgDriveItemRetentionLabel', 
               'Remove-MgDriveItemSubscription', 'Remove-MgDriveItemThumbnail', 
               'Remove-MgDriveItemVersion', 'Remove-MgDriveItemVersionContent', 
               'Remove-MgDriveList', 'Remove-MgDriveListColumn', 
               'Remove-MgDriveListContentType', 
               'Remove-MgDriveListContentTypeColumn', 
               'Remove-MgDriveListContentTypeColumnLink', 'Remove-MgDriveListItem', 
               'Remove-MgDriveListItemDocumentSetVersion', 
               'Remove-MgDriveListItemDocumentSetVersionField', 
               'Remove-MgDriveListItemDriveItemContent', 
               'Remove-MgDriveListItemField', 'Remove-MgDriveListItemVersion', 
               'Remove-MgDriveListItemVersionField', 'Remove-MgDriveListOperation', 
               'Remove-MgDriveListSubscription', 'Remove-MgDriveRoot', 
               'Remove-MgDriveRootAnalytic', 
               'Remove-MgDriveRootAnalyticItemActivityStat', 
               'Remove-MgDriveRootCheckout', 'Remove-MgDriveRootChildContent', 
               'Remove-MgDriveRootContent', 'Remove-MgDriveRootListItem', 
               'Remove-MgDriveRootListItemDocumentSetVersion', 
               'Remove-MgDriveRootListItemDocumentSetVersionField', 
               'Remove-MgDriveRootListItemDriveItemContent', 
               'Remove-MgDriveRootListItemField', 
               'Remove-MgDriveRootListItemVersion', 
               'Remove-MgDriveRootListItemVersionField', 
               'Remove-MgDriveRootPermanent', 'Remove-MgDriveRootPermission', 
               'Remove-MgDriveRootRetentionLabel', 
               'Remove-MgDriveRootSubscription', 'Remove-MgDriveRootThumbnail', 
               'Remove-MgDriveRootVersion', 'Remove-MgDriveRootVersionContent', 
               'Remove-MgDriveSpecialContent', 'Remove-MgGroupDrive', 
               'Remove-MgGroupDriveBundleContent', 
               'Remove-MgGroupDriveFollowingContent', 'Remove-MgGroupDriveItem', 
               'Remove-MgGroupDriveItemAnalytic', 
               'Remove-MgGroupDriveItemAnalyticItemActivityStat', 
               'Remove-MgGroupDriveItemCheckout', 
               'Remove-MgGroupDriveItemChildContent', 
               'Remove-MgGroupDriveItemContent', 'Remove-MgGroupDriveItemListItem', 
               'Remove-MgGroupDriveItemListItemDocumentSetVersion', 
               'Remove-MgGroupDriveItemListItemDocumentSetVersionField', 
               'Remove-MgGroupDriveItemListItemDriveItemContent', 
               'Remove-MgGroupDriveItemListItemField', 
               'Remove-MgGroupDriveItemListItemVersion', 
               'Remove-MgGroupDriveItemListItemVersionField', 
               'Remove-MgGroupDriveItemPermanent', 
               'Remove-MgGroupDriveItemPermission', 
               'Remove-MgGroupDriveItemRetentionLabel', 
               'Remove-MgGroupDriveItemSubscription', 
               'Remove-MgGroupDriveItemThumbnail', 
               'Remove-MgGroupDriveItemVersion', 
               'Remove-MgGroupDriveItemVersionContent', 'Remove-MgGroupDriveList', 
               'Remove-MgGroupDriveListColumn', 
               'Remove-MgGroupDriveListContentType', 
               'Remove-MgGroupDriveListContentTypeColumn', 
               'Remove-MgGroupDriveListContentTypeColumnLink', 
               'Remove-MgGroupDriveListItem', 
               'Remove-MgGroupDriveListItemDocumentSetVersion', 
               'Remove-MgGroupDriveListItemDocumentSetVersionField', 
               'Remove-MgGroupDriveListItemDriveItemContent', 
               'Remove-MgGroupDriveListItemField', 
               'Remove-MgGroupDriveListItemVersion', 
               'Remove-MgGroupDriveListItemVersionField', 
               'Remove-MgGroupDriveListOperation', 
               'Remove-MgGroupDriveListSubscription', 'Remove-MgGroupDriveRoot', 
               'Remove-MgGroupDriveRootAnalytic', 
               'Remove-MgGroupDriveRootAnalyticItemActivityStat', 
               'Remove-MgGroupDriveRootCheckout', 
               'Remove-MgGroupDriveRootChildContent', 
               'Remove-MgGroupDriveRootContent', 'Remove-MgGroupDriveRootListItem', 
               'Remove-MgGroupDriveRootListItemDocumentSetVersion', 
               'Remove-MgGroupDriveRootListItemDocumentSetVersionField', 
               'Remove-MgGroupDriveRootListItemDriveItemContent', 
               'Remove-MgGroupDriveRootListItemField', 
               'Remove-MgGroupDriveRootListItemVersion', 
               'Remove-MgGroupDriveRootListItemVersionField', 
               'Remove-MgGroupDriveRootPermanent', 
               'Remove-MgGroupDriveRootPermission', 
               'Remove-MgGroupDriveRootRetentionLabel', 
               'Remove-MgGroupDriveRootSubscription', 
               'Remove-MgGroupDriveRootThumbnail', 
               'Remove-MgGroupDriveRootVersion', 
               'Remove-MgGroupDriveRootVersionContent', 
               'Remove-MgGroupDriveSpecialContent', 
               'Remove-MgShareDriveItemContent', 'Remove-MgShareItemContent', 
               'Remove-MgShareList', 'Remove-MgShareListColumn', 
               'Remove-MgShareListContentType', 
               'Remove-MgShareListContentTypeColumn', 
               'Remove-MgShareListContentTypeColumnLink', 'Remove-MgShareListItem', 
               'Remove-MgShareListItemDocumentSetVersion', 
               'Remove-MgShareListItemDocumentSetVersionField', 
               'Remove-MgShareListItemDriveItemContent', 
               'Remove-MgShareListItemField', 'Remove-MgShareListItemVersion', 
               'Remove-MgShareListItemVersionField', 'Remove-MgShareListOperation', 
               'Remove-MgShareListSubscription', 'Remove-MgSharePermission', 
               'Remove-MgShareRootContent', 
               'Remove-MgShareSharedDriveItemSharedDriveItem', 
               'Remove-MgUserDrive', 'Remove-MgUserDriveBundleContent', 
               'Remove-MgUserDriveFollowingContent', 'Remove-MgUserDriveItem', 
               'Remove-MgUserDriveItemAnalytic', 
               'Remove-MgUserDriveItemAnalyticItemActivityStat', 
               'Remove-MgUserDriveItemCheckout', 
               'Remove-MgUserDriveItemChildContent', 
               'Remove-MgUserDriveItemContent', 'Remove-MgUserDriveItemListItem', 
               'Remove-MgUserDriveItemListItemDocumentSetVersion', 
               'Remove-MgUserDriveItemListItemDocumentSetVersionField', 
               'Remove-MgUserDriveItemListItemDriveItemContent', 
               'Remove-MgUserDriveItemListItemField', 
               'Remove-MgUserDriveItemListItemVersion', 
               'Remove-MgUserDriveItemListItemVersionField', 
               'Remove-MgUserDriveItemPermanent', 
               'Remove-MgUserDriveItemPermission', 
               'Remove-MgUserDriveItemRetentionLabel', 
               'Remove-MgUserDriveItemSubscription', 
               'Remove-MgUserDriveItemThumbnail', 'Remove-MgUserDriveItemVersion', 
               'Remove-MgUserDriveItemVersionContent', 'Remove-MgUserDriveList', 
               'Remove-MgUserDriveListColumn', 'Remove-MgUserDriveListContentType', 
               'Remove-MgUserDriveListContentTypeColumn', 
               'Remove-MgUserDriveListContentTypeColumnLink', 
               'Remove-MgUserDriveListItem', 
               'Remove-MgUserDriveListItemDocumentSetVersion', 
               'Remove-MgUserDriveListItemDocumentSetVersionField', 
               'Remove-MgUserDriveListItemDriveItemContent', 
               'Remove-MgUserDriveListItemField', 
               'Remove-MgUserDriveListItemVersion', 
               'Remove-MgUserDriveListItemVersionField', 
               'Remove-MgUserDriveListOperation', 
               'Remove-MgUserDriveListSubscription', 'Remove-MgUserDriveRoot', 
               'Remove-MgUserDriveRootAnalytic', 
               'Remove-MgUserDriveRootAnalyticItemActivityStat', 
               'Remove-MgUserDriveRootCheckout', 
               'Remove-MgUserDriveRootChildContent', 
               'Remove-MgUserDriveRootContent', 'Remove-MgUserDriveRootListItem', 
               'Remove-MgUserDriveRootListItemDocumentSetVersion', 
               'Remove-MgUserDriveRootListItemDocumentSetVersionField', 
               'Remove-MgUserDriveRootListItemDriveItemContent', 
               'Remove-MgUserDriveRootListItemField', 
               'Remove-MgUserDriveRootListItemVersion', 
               'Remove-MgUserDriveRootListItemVersionField', 
               'Remove-MgUserDriveRootPermanent', 
               'Remove-MgUserDriveRootPermission', 
               'Remove-MgUserDriveRootRetentionLabel', 
               'Remove-MgUserDriveRootSubscription', 
               'Remove-MgUserDriveRootThumbnail', 'Remove-MgUserDriveRootVersion', 
               'Remove-MgUserDriveRootVersionContent', 
               'Remove-MgUserDriveSpecialContent', 'Restore-MgDriveItem', 
               'Restore-MgDriveItemListItemDocumentSetVersion', 
               'Restore-MgDriveItemListItemVersion', 'Restore-MgDriveItemVersion', 
               'Restore-MgDriveListItemDocumentSetVersion', 
               'Restore-MgDriveListItemVersion', 'Restore-MgDriveRoot', 
               'Restore-MgDriveRootListItemDocumentSetVersion', 
               'Restore-MgDriveRootListItemVersion', 'Restore-MgDriveRootVersion', 
               'Restore-MgGroupDriveItem', 
               'Restore-MgGroupDriveItemListItemDocumentSetVersion', 
               'Restore-MgGroupDriveItemListItemVersion', 
               'Restore-MgGroupDriveItemVersion', 
               'Restore-MgGroupDriveListItemDocumentSetVersion', 
               'Restore-MgGroupDriveListItemVersion', 'Restore-MgGroupDriveRoot', 
               'Restore-MgGroupDriveRootListItemDocumentSetVersion', 
               'Restore-MgGroupDriveRootListItemVersion', 
               'Restore-MgGroupDriveRootVersion', 
               'Restore-MgShareListItemDocumentSetVersion', 
               'Restore-MgShareListItemVersion', 'Restore-MgUserDriveItem', 
               'Restore-MgUserDriveItemListItemDocumentSetVersion', 
               'Restore-MgUserDriveItemListItemVersion', 
               'Restore-MgUserDriveItemVersion', 
               'Restore-MgUserDriveListItemDocumentSetVersion', 
               'Restore-MgUserDriveListItemVersion', 'Restore-MgUserDriveRoot', 
               'Restore-MgUserDriveRootListItemDocumentSetVersion', 
               'Restore-MgUserDriveRootListItemVersion', 
               'Restore-MgUserDriveRootVersion', 'Search-MgDrive', 
               'Search-MgDriveItem', 'Search-MgDriveRoot', 'Search-MgGroupDrive', 
               'Search-MgGroupDriveItem', 'Search-MgGroupDriveRoot', 
               'Search-MgUserDrive', 'Search-MgUserDriveItem', 
               'Search-MgUserDriveRoot', 'Set-MgDriveBundleContent', 
               'Set-MgDriveFollowingContent', 'Set-MgDriveItemChildContent', 
               'Set-MgDriveItemContent', 'Set-MgDriveItemListItemDriveItemContent', 
               'Set-MgDriveItemSensitivityLabel', 'Set-MgDriveItemVersionContent', 
               'Set-MgDriveListItemDriveItemContent', 
               'Set-MgDriveRootChildContent', 'Set-MgDriveRootContent', 
               'Set-MgDriveRootListItemDriveItemContent', 
               'Set-MgDriveRootSensitivityLabel', 'Set-MgDriveRootVersionContent', 
               'Set-MgDriveSpecialContent', 'Set-MgGroupDriveBundleContent', 
               'Set-MgGroupDriveFollowingContent', 
               'Set-MgGroupDriveItemChildContent', 'Set-MgGroupDriveItemContent', 
               'Set-MgGroupDriveItemListItemDriveItemContent', 
               'Set-MgGroupDriveItemSensitivityLabel', 
               'Set-MgGroupDriveItemVersionContent', 
               'Set-MgGroupDriveListItemDriveItemContent', 
               'Set-MgGroupDriveRootChildContent', 'Set-MgGroupDriveRootContent', 
               'Set-MgGroupDriveRootListItemDriveItemContent', 
               'Set-MgGroupDriveRootSensitivityLabel', 
               'Set-MgGroupDriveRootVersionContent', 
               'Set-MgGroupDriveSpecialContent', 'Set-MgShareDriveItemContent', 
               'Set-MgShareItemContent', 'Set-MgShareListItemDriveItemContent', 
               'Set-MgShareRootContent', 'Set-MgUserDriveBundleContent', 
               'Set-MgUserDriveFollowingContent', 
               'Set-MgUserDriveItemChildContent', 'Set-MgUserDriveItemContent', 
               'Set-MgUserDriveItemListItemDriveItemContent', 
               'Set-MgUserDriveItemSensitivityLabel', 
               'Set-MgUserDriveItemVersionContent', 
               'Set-MgUserDriveListItemDriveItemContent', 
               'Set-MgUserDriveRootChildContent', 'Set-MgUserDriveRootContent', 
               'Set-MgUserDriveRootListItemDriveItemContent', 
               'Set-MgUserDriveRootSensitivityLabel', 
               'Set-MgUserDriveRootVersionContent', 
               'Set-MgUserDriveSpecialContent', 'Test-MgDriveItemPermission', 
               'Test-MgDriveListContentTypePublished', 
               'Test-MgDriveRootPermission', 'Test-MgGroupDriveItemPermission', 
               'Test-MgGroupDriveListContentTypePublished', 
               'Test-MgGroupDriveRootPermission', 
               'Test-MgShareListContentTypePublished', 
               'Test-MgUserDriveItemPermission', 
               'Test-MgUserDriveListContentTypePublished', 
               'Test-MgUserDriveRootPermission', 
               'Unpublish-MgDriveListContentType', 
               'Unpublish-MgGroupDriveListContentType', 
               'Unpublish-MgShareListContentType', 
               'Unpublish-MgUserDriveListContentType', 'Update-MgDrive', 
               'Update-MgDriveCreatedByUserMailboxSetting', 'Update-MgDriveItem', 
               'Update-MgDriveItemAnalytic', 
               'Update-MgDriveItemAnalyticItemActivityStat', 
               'Update-MgDriveItemCreatedByUserMailboxSetting', 
               'Update-MgDriveItemLastModifiedByUserMailboxSetting', 
               'Update-MgDriveItemListItem', 
               'Update-MgDriveItemListItemCreatedByUserMailboxSetting', 
               'Update-MgDriveItemListItemDocumentSetVersion', 
               'Update-MgDriveItemListItemDocumentSetVersionField', 
               'Update-MgDriveItemListItemField', 
               'Update-MgDriveItemListItemLastModifiedByUserMailboxSetting', 
               'Update-MgDriveItemListItemVersion', 
               'Update-MgDriveItemListItemVersionField', 
               'Update-MgDriveItemPermission', 'Update-MgDriveItemRetentionLabel', 
               'Update-MgDriveItemSubscription', 'Update-MgDriveItemThumbnail', 
               'Update-MgDriveItemVersion', 
               'Update-MgDriveLastModifiedByUserMailboxSetting', 
               'Update-MgDriveList', 'Update-MgDriveListColumn', 
               'Update-MgDriveListContentType', 
               'Update-MgDriveListContentTypeColumn', 
               'Update-MgDriveListContentTypeColumnLink', 
               'Update-MgDriveListCreatedByUserMailboxSetting', 
               'Update-MgDriveListItem', 
               'Update-MgDriveListItemCreatedByUserMailboxSetting', 
               'Update-MgDriveListItemDocumentSetVersion', 
               'Update-MgDriveListItemDocumentSetVersionField', 
               'Update-MgDriveListItemField', 
               'Update-MgDriveListItemLastModifiedByUserMailboxSetting', 
               'Update-MgDriveListItemVersion', 
               'Update-MgDriveListItemVersionField', 
               'Update-MgDriveListLastModifiedByUserMailboxSetting', 
               'Update-MgDriveListOperation', 'Update-MgDriveListSubscription', 
               'Update-MgDriveRoot', 'Update-MgDriveRootAnalytic', 
               'Update-MgDriveRootAnalyticItemActivityStat', 
               'Update-MgDriveRootCreatedByUserMailboxSetting', 
               'Update-MgDriveRootLastModifiedByUserMailboxSetting', 
               'Update-MgDriveRootListItem', 
               'Update-MgDriveRootListItemCreatedByUserMailboxSetting', 
               'Update-MgDriveRootListItemDocumentSetVersion', 
               'Update-MgDriveRootListItemDocumentSetVersionField', 
               'Update-MgDriveRootListItemField', 
               'Update-MgDriveRootListItemLastModifiedByUserMailboxSetting', 
               'Update-MgDriveRootListItemVersion', 
               'Update-MgDriveRootListItemVersionField', 
               'Update-MgDriveRootPermission', 'Update-MgDriveRootRetentionLabel', 
               'Update-MgDriveRootSubscription', 'Update-MgDriveRootThumbnail', 
               'Update-MgDriveRootVersion', 'Update-MgGroupDrive', 
               'Update-MgGroupDriveCreatedByUserMailboxSetting', 
               'Update-MgGroupDriveItem', 'Update-MgGroupDriveItemAnalytic', 
               'Update-MgGroupDriveItemAnalyticItemActivityStat', 
               'Update-MgGroupDriveItemCreatedByUserMailboxSetting', 
               'Update-MgGroupDriveItemLastModifiedByUserMailboxSetting', 
               'Update-MgGroupDriveItemListItem', 
               'Update-MgGroupDriveItemListItemCreatedByUserMailboxSetting', 
               'Update-MgGroupDriveItemListItemDocumentSetVersion', 
               'Update-MgGroupDriveItemListItemDocumentSetVersionField', 
               'Update-MgGroupDriveItemListItemField', 
               'Update-MgGroupDriveItemListItemLastModifiedByUserMailboxSetting', 
               'Update-MgGroupDriveItemListItemVersion', 
               'Update-MgGroupDriveItemListItemVersionField', 
               'Update-MgGroupDriveItemPermission', 
               'Update-MgGroupDriveItemRetentionLabel', 
               'Update-MgGroupDriveItemSubscription', 
               'Update-MgGroupDriveItemThumbnail', 
               'Update-MgGroupDriveItemVersion', 
               'Update-MgGroupDriveLastModifiedByUserMailboxSetting', 
               'Update-MgGroupDriveList', 'Update-MgGroupDriveListColumn', 
               'Update-MgGroupDriveListContentType', 
               'Update-MgGroupDriveListContentTypeColumn', 
               'Update-MgGroupDriveListContentTypeColumnLink', 
               'Update-MgGroupDriveListCreatedByUserMailboxSetting', 
               'Update-MgGroupDriveListItem', 
               'Update-MgGroupDriveListItemCreatedByUserMailboxSetting', 
               'Update-MgGroupDriveListItemDocumentSetVersion', 
               'Update-MgGroupDriveListItemDocumentSetVersionField', 
               'Update-MgGroupDriveListItemField', 
               'Update-MgGroupDriveListItemLastModifiedByUserMailboxSetting', 
               'Update-MgGroupDriveListItemVersion', 
               'Update-MgGroupDriveListItemVersionField', 
               'Update-MgGroupDriveListLastModifiedByUserMailboxSetting', 
               'Update-MgGroupDriveListOperation', 
               'Update-MgGroupDriveListSubscription', 'Update-MgGroupDriveRoot', 
               'Update-MgGroupDriveRootAnalytic', 
               'Update-MgGroupDriveRootAnalyticItemActivityStat', 
               'Update-MgGroupDriveRootCreatedByUserMailboxSetting', 
               'Update-MgGroupDriveRootLastModifiedByUserMailboxSetting', 
               'Update-MgGroupDriveRootListItem', 
               'Update-MgGroupDriveRootListItemCreatedByUserMailboxSetting', 
               'Update-MgGroupDriveRootListItemDocumentSetVersion', 
               'Update-MgGroupDriveRootListItemDocumentSetVersionField', 
               'Update-MgGroupDriveRootListItemField', 
               'Update-MgGroupDriveRootListItemLastModifiedByUserMailboxSetting', 
               'Update-MgGroupDriveRootListItemVersion', 
               'Update-MgGroupDriveRootListItemVersionField', 
               'Update-MgGroupDriveRootPermission', 
               'Update-MgGroupDriveRootRetentionLabel', 
               'Update-MgGroupDriveRootSubscription', 
               'Update-MgGroupDriveRootThumbnail', 
               'Update-MgGroupDriveRootVersion', 
               'Update-MgShareCreatedByUserMailboxSetting', 
               'Update-MgShareLastModifiedByUserMailboxSetting', 
               'Update-MgShareList', 'Update-MgShareListColumn', 
               'Update-MgShareListContentType', 
               'Update-MgShareListContentTypeColumn', 
               'Update-MgShareListContentTypeColumnLink', 
               'Update-MgShareListCreatedByUserMailboxSetting', 
               'Update-MgShareListItem', 
               'Update-MgShareListItemCreatedByUserMailboxSetting', 
               'Update-MgShareListItemDocumentSetVersion', 
               'Update-MgShareListItemDocumentSetVersionField', 
               'Update-MgShareListItemField', 
               'Update-MgShareListItemLastModifiedByUserMailboxSetting', 
               'Update-MgShareListItemVersion', 
               'Update-MgShareListItemVersionField', 
               'Update-MgShareListLastModifiedByUserMailboxSetting', 
               'Update-MgShareListOperation', 'Update-MgShareListSubscription', 
               'Update-MgSharePermission', 
               'Update-MgShareSharedDriveItemSharedDriveItem', 
               'Update-MgUserDrive', 
               'Update-MgUserDriveCreatedByUserMailboxSetting', 
               'Update-MgUserDriveItem', 'Update-MgUserDriveItemAnalytic', 
               'Update-MgUserDriveItemAnalyticItemActivityStat', 
               'Update-MgUserDriveItemCreatedByUserMailboxSetting', 
               'Update-MgUserDriveItemLastModifiedByUserMailboxSetting', 
               'Update-MgUserDriveItemListItem', 
               'Update-MgUserDriveItemListItemCreatedByUserMailboxSetting', 
               'Update-MgUserDriveItemListItemDocumentSetVersion', 
               'Update-MgUserDriveItemListItemDocumentSetVersionField', 
               'Update-MgUserDriveItemListItemField', 
               'Update-MgUserDriveItemListItemLastModifiedByUserMailboxSetting', 
               'Update-MgUserDriveItemListItemVersion', 
               'Update-MgUserDriveItemListItemVersionField', 
               'Update-MgUserDriveItemPermission', 
               'Update-MgUserDriveItemRetentionLabel', 
               'Update-MgUserDriveItemSubscription', 
               'Update-MgUserDriveItemThumbnail', 'Update-MgUserDriveItemVersion', 
               'Update-MgUserDriveLastModifiedByUserMailboxSetting', 
               'Update-MgUserDriveList', 'Update-MgUserDriveListColumn', 
               'Update-MgUserDriveListContentType', 
               'Update-MgUserDriveListContentTypeColumn', 
               'Update-MgUserDriveListContentTypeColumnLink', 
               'Update-MgUserDriveListCreatedByUserMailboxSetting', 
               'Update-MgUserDriveListItem', 
               'Update-MgUserDriveListItemCreatedByUserMailboxSetting', 
               'Update-MgUserDriveListItemDocumentSetVersion', 
               'Update-MgUserDriveListItemDocumentSetVersionField', 
               'Update-MgUserDriveListItemField', 
               'Update-MgUserDriveListItemLastModifiedByUserMailboxSetting', 
               'Update-MgUserDriveListItemVersion', 
               'Update-MgUserDriveListItemVersionField', 
               'Update-MgUserDriveListLastModifiedByUserMailboxSetting', 
               'Update-MgUserDriveListOperation', 
               'Update-MgUserDriveListSubscription', 'Update-MgUserDriveRoot', 
               'Update-MgUserDriveRootAnalytic', 
               'Update-MgUserDriveRootAnalyticItemActivityStat', 
               'Update-MgUserDriveRootCreatedByUserMailboxSetting', 
               'Update-MgUserDriveRootLastModifiedByUserMailboxSetting', 
               'Update-MgUserDriveRootListItem', 
               'Update-MgUserDriveRootListItemCreatedByUserMailboxSetting', 
               'Update-MgUserDriveRootListItemDocumentSetVersion', 
               'Update-MgUserDriveRootListItemDocumentSetVersionField', 
               'Update-MgUserDriveRootListItemField', 
               'Update-MgUserDriveRootListItemLastModifiedByUserMailboxSetting', 
               'Update-MgUserDriveRootListItemVersion', 
               'Update-MgUserDriveRootListItemVersionField', 
               'Update-MgUserDriveRootPermission', 
               'Update-MgUserDriveRootRetentionLabel', 
               'Update-MgUserDriveRootSubscription', 
               'Update-MgUserDriveRootThumbnail', 'Update-MgUserDriveRootVersion'

# Cmdlets to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no cmdlets to export.
CmdletsToExport = @()

# Variables to export from this module
# VariablesToExport = @()

# Aliases to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no aliases to export.
AliasesToExport = '*'

# DSC resources to export from this module
# DscResourcesToExport = @()

# List of all modules packaged with this module
# ModuleList = @()

# List of all files packaged with this module
# FileList = @()

# Private data to pass to the module specified in RootModule/ModuleToProcess. This may also contain a PSData hashtable with additional module metadata used by PowerShell.
PrivateData = @{

    PSData = @{

        # Tags applied to this module. These help with module discovery in online galleries.
        Tags = 'Microsoft','Office365','Graph','PowerShell','PSModule','PSIncludes_Cmdlet'

        # A URL to the license for this module.
        LicenseUri = 'https://aka.ms/devservicesagreement'

        # A URL to the main website for this project.
        ProjectUri = 'https://github.com/microsoftgraph/msgraph-sdk-powershell'

        # A URL to an icon representing this module.
        IconUri = 'https://raw.githubusercontent.com/microsoftgraph/msgraph-sdk-powershell/dev/docs/images/graph_color256.png'

        # ReleaseNotes of this module
        ReleaseNotes = 'See https://aka.ms/GraphPowerShell-Release.'

        # Prerelease string of this module
        # Prerelease = ''

        # Flag to indicate whether the module requires explicit user acceptance for install/update/save
        # RequireLicenseAcceptance = $false

        # External dependent modules of this module
        # ExternalModuleDependencies = @()

    } # End of PSData hashtable

 } # End of PrivateData hashtable

# HelpInfo URI of this module
# HelpInfoURI = ''

# Default prefix for commands exported from this module. Override the default prefix using Import-Module -Prefix.
# DefaultCommandPrefix = ''

}


# SIG # Begin signature block
# MIIoKgYJKoZIhvcNAQcCoIIoGzCCKBcCAQExDzANBglghkgBZQMEAgEFADB5Bgor
# BgEEAYI3AgEEoGswaTA0BgorBgEEAYI3AgEeMCYCAwEAAAQQH8w7YFlLCE63JNLG
# KX7zUQIBAAIBAAIBAAIBAAIBADAxMA0GCWCGSAFlAwQCAQUABCBqPBTaBahSbvxW
# 3kJG81MvtwUDagSW+TgAY5NIWFm92qCCDXYwggX0MIID3KADAgECAhMzAAAEBGx0
# Bv9XKydyAAAAAAQEMA0GCSqGSIb3DQEBCwUAMH4xCzAJBgNVBAYTAlVTMRMwEQYD
# VQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNy
# b3NvZnQgQ29ycG9yYXRpb24xKDAmBgNVBAMTH01pY3Jvc29mdCBDb2RlIFNpZ25p
# bmcgUENBIDIwMTEwHhcNMjQwOTEyMjAxMTE0WhcNMjUwOTExMjAxMTE0WjB0MQsw
# CQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9u
# ZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMR4wHAYDVQQDExVNaWNy
# b3NvZnQgQ29ycG9yYXRpb24wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIB
# AQC0KDfaY50MDqsEGdlIzDHBd6CqIMRQWW9Af1LHDDTuFjfDsvna0nEuDSYJmNyz
# NB10jpbg0lhvkT1AzfX2TLITSXwS8D+mBzGCWMM/wTpciWBV/pbjSazbzoKvRrNo
# DV/u9omOM2Eawyo5JJJdNkM2d8qzkQ0bRuRd4HarmGunSouyb9NY7egWN5E5lUc3
# a2AROzAdHdYpObpCOdeAY2P5XqtJkk79aROpzw16wCjdSn8qMzCBzR7rvH2WVkvF
# HLIxZQET1yhPb6lRmpgBQNnzidHV2Ocxjc8wNiIDzgbDkmlx54QPfw7RwQi8p1fy
# 4byhBrTjv568x8NGv3gwb0RbAgMBAAGjggFzMIIBbzAfBgNVHSUEGDAWBgorBgEE
# AYI3TAgBBggrBgEFBQcDAzAdBgNVHQ4EFgQU8huhNbETDU+ZWllL4DNMPCijEU4w
# RQYDVR0RBD4wPKQ6MDgxHjAcBgNVBAsTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEW
# MBQGA1UEBRMNMjMwMDEyKzUwMjkyMzAfBgNVHSMEGDAWgBRIbmTlUAXTgqoXNzci
# tW2oynUClTBUBgNVHR8ETTBLMEmgR6BFhkNodHRwOi8vd3d3Lm1pY3Jvc29mdC5j
# b20vcGtpb3BzL2NybC9NaWNDb2RTaWdQQ0EyMDExXzIwMTEtMDctMDguY3JsMGEG
# CCsGAQUFBwEBBFUwUzBRBggrBgEFBQcwAoZFaHR0cDovL3d3dy5taWNyb3NvZnQu
# Y29tL3BraW9wcy9jZXJ0cy9NaWNDb2RTaWdQQ0EyMDExXzIwMTEtMDctMDguY3J0
# MAwGA1UdEwEB/wQCMAAwDQYJKoZIhvcNAQELBQADggIBAIjmD9IpQVvfB1QehvpC
# Ge7QeTQkKQ7j3bmDMjwSqFL4ri6ae9IFTdpywn5smmtSIyKYDn3/nHtaEn0X1NBj
# L5oP0BjAy1sqxD+uy35B+V8wv5GrxhMDJP8l2QjLtH/UglSTIhLqyt8bUAqVfyfp
# h4COMRvwwjTvChtCnUXXACuCXYHWalOoc0OU2oGN+mPJIJJxaNQc1sjBsMbGIWv3
# cmgSHkCEmrMv7yaidpePt6V+yPMik+eXw3IfZ5eNOiNgL1rZzgSJfTnvUqiaEQ0X
# dG1HbkDv9fv6CTq6m4Ty3IzLiwGSXYxRIXTxT4TYs5VxHy2uFjFXWVSL0J2ARTYL
# E4Oyl1wXDF1PX4bxg1yDMfKPHcE1Ijic5lx1KdK1SkaEJdto4hd++05J9Bf9TAmi
# u6EK6C9Oe5vRadroJCK26uCUI4zIjL/qG7mswW+qT0CW0gnR9JHkXCWNbo8ccMk1
# sJatmRoSAifbgzaYbUz8+lv+IXy5GFuAmLnNbGjacB3IMGpa+lbFgih57/fIhamq
# 5VhxgaEmn/UjWyr+cPiAFWuTVIpfsOjbEAww75wURNM1Imp9NJKye1O24EspEHmb
# DmqCUcq7NqkOKIG4PVm3hDDED/WQpzJDkvu4FrIbvyTGVU01vKsg4UfcdiZ0fQ+/
# V0hf8yrtq9CkB8iIuk5bBxuPMIIHejCCBWKgAwIBAgIKYQ6Q0gAAAAAAAzANBgkq
# hkiG9w0BAQsFADCBiDELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldhc2hpbmd0b24x
# EDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlv
# bjEyMDAGA1UEAxMpTWljcm9zb2Z0IFJvb3QgQ2VydGlmaWNhdGUgQXV0aG9yaXR5
# IDIwMTEwHhcNMTEwNzA4MjA1OTA5WhcNMjYwNzA4MjEwOTA5WjB+MQswCQYDVQQG
# EwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwG
# A1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSgwJgYDVQQDEx9NaWNyb3NvZnQg
# Q29kZSBTaWduaW5nIFBDQSAyMDExMIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIIC
# CgKCAgEAq/D6chAcLq3YbqqCEE00uvK2WCGfQhsqa+laUKq4BjgaBEm6f8MMHt03
# a8YS2AvwOMKZBrDIOdUBFDFC04kNeWSHfpRgJGyvnkmc6Whe0t+bU7IKLMOv2akr
# rnoJr9eWWcpgGgXpZnboMlImEi/nqwhQz7NEt13YxC4Ddato88tt8zpcoRb0Rrrg
# OGSsbmQ1eKagYw8t00CT+OPeBw3VXHmlSSnnDb6gE3e+lD3v++MrWhAfTVYoonpy
# 4BI6t0le2O3tQ5GD2Xuye4Yb2T6xjF3oiU+EGvKhL1nkkDstrjNYxbc+/jLTswM9
# sbKvkjh+0p2ALPVOVpEhNSXDOW5kf1O6nA+tGSOEy/S6A4aN91/w0FK/jJSHvMAh
# dCVfGCi2zCcoOCWYOUo2z3yxkq4cI6epZuxhH2rhKEmdX4jiJV3TIUs+UsS1Vz8k
# A/DRelsv1SPjcF0PUUZ3s/gA4bysAoJf28AVs70b1FVL5zmhD+kjSbwYuER8ReTB
# w3J64HLnJN+/RpnF78IcV9uDjexNSTCnq47f7Fufr/zdsGbiwZeBe+3W7UvnSSmn
# Eyimp31ngOaKYnhfsi+E11ecXL93KCjx7W3DKI8sj0A3T8HhhUSJxAlMxdSlQy90
# lfdu+HggWCwTXWCVmj5PM4TasIgX3p5O9JawvEagbJjS4NaIjAsCAwEAAaOCAe0w
# ggHpMBAGCSsGAQQBgjcVAQQDAgEAMB0GA1UdDgQWBBRIbmTlUAXTgqoXNzcitW2o
# ynUClTAZBgkrBgEEAYI3FAIEDB4KAFMAdQBiAEMAQTALBgNVHQ8EBAMCAYYwDwYD
# VR0TAQH/BAUwAwEB/zAfBgNVHSMEGDAWgBRyLToCMZBDuRQFTuHqp8cx0SOJNDBa
# BgNVHR8EUzBRME+gTaBLhklodHRwOi8vY3JsLm1pY3Jvc29mdC5jb20vcGtpL2Ny
# bC9wcm9kdWN0cy9NaWNSb29DZXJBdXQyMDExXzIwMTFfMDNfMjIuY3JsMF4GCCsG
# AQUFBwEBBFIwUDBOBggrBgEFBQcwAoZCaHR0cDovL3d3dy5taWNyb3NvZnQuY29t
# L3BraS9jZXJ0cy9NaWNSb29DZXJBdXQyMDExXzIwMTFfMDNfMjIuY3J0MIGfBgNV
# HSAEgZcwgZQwgZEGCSsGAQQBgjcuAzCBgzA/BggrBgEFBQcCARYzaHR0cDovL3d3
# dy5taWNyb3NvZnQuY29tL3BraW9wcy9kb2NzL3ByaW1hcnljcHMuaHRtMEAGCCsG
# AQUFBwICMDQeMiAdAEwAZQBnAGEAbABfAHAAbwBsAGkAYwB5AF8AcwB0AGEAdABl
# AG0AZQBuAHQALiAdMA0GCSqGSIb3DQEBCwUAA4ICAQBn8oalmOBUeRou09h0ZyKb
# C5YR4WOSmUKWfdJ5DJDBZV8uLD74w3LRbYP+vj/oCso7v0epo/Np22O/IjWll11l
# hJB9i0ZQVdgMknzSGksc8zxCi1LQsP1r4z4HLimb5j0bpdS1HXeUOeLpZMlEPXh6
# I/MTfaaQdION9MsmAkYqwooQu6SpBQyb7Wj6aC6VoCo/KmtYSWMfCWluWpiW5IP0
# wI/zRive/DvQvTXvbiWu5a8n7dDd8w6vmSiXmE0OPQvyCInWH8MyGOLwxS3OW560
# STkKxgrCxq2u5bLZ2xWIUUVYODJxJxp/sfQn+N4sOiBpmLJZiWhub6e3dMNABQam
# ASooPoI/E01mC8CzTfXhj38cbxV9Rad25UAqZaPDXVJihsMdYzaXht/a8/jyFqGa
# J+HNpZfQ7l1jQeNbB5yHPgZ3BtEGsXUfFL5hYbXw3MYbBL7fQccOKO7eZS/sl/ah
# XJbYANahRr1Z85elCUtIEJmAH9AAKcWxm6U/RXceNcbSoqKfenoi+kiVH6v7RyOA
# 9Z74v2u3S5fi63V4GuzqN5l5GEv/1rMjaHXmr/r8i+sLgOppO6/8MO0ETI7f33Vt
# Y5E90Z1WTk+/gFcioXgRMiF670EKsT/7qMykXcGhiJtXcVZOSEXAQsmbdlsKgEhr
# /Xmfwb1tbWrJUnMTDXpQzTGCGgowghoGAgEBMIGVMH4xCzAJBgNVBAYTAlVTMRMw
# EQYDVQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVN
# aWNyb3NvZnQgQ29ycG9yYXRpb24xKDAmBgNVBAMTH01pY3Jvc29mdCBDb2RlIFNp
# Z25pbmcgUENBIDIwMTECEzMAAAQEbHQG/1crJ3IAAAAABAQwDQYJYIZIAWUDBAIB
# BQCgga4wGQYJKoZIhvcNAQkDMQwGCisGAQQBgjcCAQQwHAYKKwYBBAGCNwIBCzEO
# MAwGCisGAQQBgjcCARUwLwYJKoZIhvcNAQkEMSIEINc1mO6EOdzR91WrCZTSkXAt
# weGPpa7jEgOOOWpglHNqMEIGCisGAQQBgjcCAQwxNDAyoBSAEgBNAGkAYwByAG8A
# cwBvAGYAdKEagBhodHRwOi8vd3d3Lm1pY3Jvc29mdC5jb20wDQYJKoZIhvcNAQEB
# BQAEggEAk1b0jBx59v6ZZhJTV9GM6d/HzR649K5T9ThpPestJSFepyszSTkHhNkG
# Xt5YStTnx2eoY6/CTB5dgWmE9rboHUbrObIbCIZyOcSxijv17yF7yAqigJ3UFevQ
# VAW7GayeQuHevrfPlXUcA4mhL2+fzazUsadDj9lH20RUO0hP8QajglcPdm/l5g/D
# 3Cz3zdcTcNPMdoUoO508hidyVFurdrMYmxuoWgn/Uw520PmrT/EErMBU07MB5EV0
# Ayxgg9CgpDPZw/4hrojUIZFYa2M02hD4Sg1VcHecQl0XL0lmepCbHb4goqAJBD1X
# GklKLTjSth60fxNJlxf7180hKbSIw6GCF5QwgheQBgorBgEEAYI3AwMBMYIXgDCC
# F3wGCSqGSIb3DQEHAqCCF20wghdpAgEDMQ8wDQYJYIZIAWUDBAIBBQAwggFSBgsq
# hkiG9w0BCRABBKCCAUEEggE9MIIBOQIBAQYKKwYBBAGEWQoDATAxMA0GCWCGSAFl
# AwQCAQUABCA8MaH5XHaAqE3CVf7bM6bD1UEdmwI3woS3tuY533aV4AIGaErf2xDY
# GBMyMDI1MDcwOTExMDcyMS4wNjZaMASAAgH0oIHRpIHOMIHLMQswCQYDVQQGEwJV
# UzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwGA1UE
# ChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSUwIwYDVQQLExxNaWNyb3NvZnQgQW1l
# cmljYSBPcGVyYXRpb25zMScwJQYDVQQLEx5uU2hpZWxkIFRTUyBFU046QTAwMC0w
# NUUwLUQ5NDcxJTAjBgNVBAMTHE1pY3Jvc29mdCBUaW1lLVN0YW1wIFNlcnZpY2Wg
# ghHqMIIHIDCCBQigAwIBAgITMwAAAgh4nVhdksfZUgABAAACCDANBgkqhkiG9w0B
# AQsFADB8MQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UE
# BxMHUmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSYwJAYD
# VQQDEx1NaWNyb3NvZnQgVGltZS1TdGFtcCBQQ0EgMjAxMDAeFw0yNTAxMzAxOTQy
# NTNaFw0yNjA0MjIxOTQyNTNaMIHLMQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2Fz
# aGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENv
# cnBvcmF0aW9uMSUwIwYDVQQLExxNaWNyb3NvZnQgQW1lcmljYSBPcGVyYXRpb25z
# MScwJQYDVQQLEx5uU2hpZWxkIFRTUyBFU046QTAwMC0wNUUwLUQ5NDcxJTAjBgNV
# BAMTHE1pY3Jvc29mdCBUaW1lLVN0YW1wIFNlcnZpY2UwggIiMA0GCSqGSIb3DQEB
# AQUAA4ICDwAwggIKAoICAQC1y3AI5lIz3Ip1nK5BMUUbGRsjSnCz/VGs33zvY0Ne
# shsPgfld3/Z3/3dS8WKBLlDlosmXJOZlFSiNXUd6DTJxA9ik/ZbCdWJ78LKjbN3t
# FkX2c6RRpRMpA8sq/oBbRryP3c8Q/gxpJAKHHz8cuSn7ewfCLznNmxqliTk3Q5LH
# qz2PjeYKD/dbKMBT2TAAWAvum4z/HXIJ6tFdGoNV4WURZswCSt6ROwaqQ1oAYGvE
# ndH+DXZq1+bHsgvcPNCdTSIpWobQiJS/UKLiR02KNCqB4I9yajFTSlnMIEMz/Ni5
# 38oGI64phcvNpUe2+qaKWHZ8d4T1KghvRmSSF4YF5DNEJbxaCUwsy7nULmsFnTaO
# jVOoTFWWfWXvBuOKkBcQKWGKvrki976j4x+5ezAP36fq3u6dHRJTLZAu4dEuOooU
# 3+kMZr+RBYWjTHQCKV+yZ1ST0eGkbHXoA2lyyRDlNjBQcoeZIxWCZts/d3+nf1ji
# SLN6f6wdHaUz0ADwOTQ/aEo1IC85eFePvyIKaxFJkGU2Mqa6Xzq3qCq5tokIHtjh
# ogsrEgfDKTeFXTtdhl1IPtLcCfMcWOGGAXosVUU7G948F6W96424f2VHD8L3FoyA
# I9+r4zyIQUmqiESzuQWeWpTTjFYwCmgXaGOuSDV8cNOVQB6IPzPneZhVTjwxbAZl
# aQIDAQABo4IBSTCCAUUwHQYDVR0OBBYEFKMx4vfOqcUTgYOVB9f18/mhegFNMB8G
# A1UdIwQYMBaAFJ+nFV0AXmJdg/Tl0mWnG1M1GelyMF8GA1UdHwRYMFYwVKBSoFCG
# Tmh0dHA6Ly93d3cubWljcm9zb2Z0LmNvbS9wa2lvcHMvY3JsL01pY3Jvc29mdCUy
# MFRpbWUtU3RhbXAlMjBQQ0ElMjAyMDEwKDEpLmNybDBsBggrBgEFBQcBAQRgMF4w
# XAYIKwYBBQUHMAKGUGh0dHA6Ly93d3cubWljcm9zb2Z0LmNvbS9wa2lvcHMvY2Vy
# dHMvTWljcm9zb2Z0JTIwVGltZS1TdGFtcCUyMFBDQSUyMDIwMTAoMSkuY3J0MAwG
# A1UdEwEB/wQCMAAwFgYDVR0lAQH/BAwwCgYIKwYBBQUHAwgwDgYDVR0PAQH/BAQD
# AgeAMA0GCSqGSIb3DQEBCwUAA4ICAQBRszKJKwAfswqdaQPFiaYB/ZNAYWDa040X
# TcQsCaCua5nsG1IslYaSpH7miTLr6eQEqXczZoqeOa/xvDnMGifGNda0CHbQwtpn
# IhsutrKO2jhjEaGwlJgOMql21r7Ik6XnBza0e3hBOu4UBkMl/LEX+AURt7i7+RTN
# sGN0cXPwPSbTFE+9z7WagGbY9pwUo/NxkGJseqGCQ/9K2VMU74bw5e7+8IGUhM2x
# spJPqnSeHPhYmcB0WclOxcVIfj/ZuQvworPbTEEYDVCzSN37c0yChPMY7FJ+HGFB
# NJxwd5lKIr7GYfq8a0gOiC2ljGYlc4rt4cCed1XKg83f0l9aUVimWBYXtfNebhpf
# r6Lc3jD8NgsrDhzt0WgnIdnTZCi7jxjsIBilH99pY5/h6bQcLKK/E6KCP9E1YN78
# fLaOXkXMyO6xLrvQZ+uCSi1hdTufFC7oSB/CU5RbfIVHXG0j1o2n1tne4eCbNfKq
# UPTE31tNbWBR23Yiy0r3kQmHeYE1GLbL4pwknqaip1BRn6WIUMJtgncawEN33f8A
# YGZ4a3NnHopzGVV6neffGVag4Tduy+oy1YF+shChoXdMqfhPWFpHe3uJGT4GJEiN
# s4+28a/wHUuF+aRaR0cN5P7XlOwU1360iUCJtQdvKQaNAwGI29KOwS3QGriR9F2j
# OGPUAlpeEzCCB3EwggVZoAMCAQICEzMAAAAVxedrngKbSZkAAAAAABUwDQYJKoZI
# hvcNAQELBQAwgYgxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNoaW5ndG9uMRAw
# DgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNyb3NvZnQgQ29ycG9yYXRpb24x
# MjAwBgNVBAMTKU1pY3Jvc29mdCBSb290IENlcnRpZmljYXRlIEF1dGhvcml0eSAy
# MDEwMB4XDTIxMDkzMDE4MjIyNVoXDTMwMDkzMDE4MzIyNVowfDELMAkGA1UEBhMC
# VVMxEzARBgNVBAgTCldhc2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNV
# BAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEmMCQGA1UEAxMdTWljcm9zb2Z0IFRp
# bWUtU3RhbXAgUENBIDIwMTAwggIiMA0GCSqGSIb3DQEBAQUAA4ICDwAwggIKAoIC
# AQDk4aZM57RyIQt5osvXJHm9DtWC0/3unAcH0qlsTnXIyjVX9gF/bErg4r25Phdg
# M/9cT8dm95VTcVrifkpa/rg2Z4VGIwy1jRPPdzLAEBjoYH1qUoNEt6aORmsHFPPF
# dvWGUNzBRMhxXFExN6AKOG6N7dcP2CZTfDlhAnrEqv1yaa8dq6z2Nr41JmTamDu6
# GnszrYBbfowQHJ1S/rboYiXcag/PXfT+jlPP1uyFVk3v3byNpOORj7I5LFGc6XBp
# Dco2LXCOMcg1KL3jtIckw+DJj361VI/c+gVVmG1oO5pGve2krnopN6zL64NF50Zu
# yjLVwIYwXE8s4mKyzbnijYjklqwBSru+cakXW2dg3viSkR4dPf0gz3N9QZpGdc3E
# XzTdEonW/aUgfX782Z5F37ZyL9t9X4C626p+Nuw2TPYrbqgSUei/BQOj0XOmTTd0
# lBw0gg/wEPK3Rxjtp+iZfD9M269ewvPV2HM9Q07BMzlMjgK8QmguEOqEUUbi0b1q
# GFphAXPKZ6Je1yh2AuIzGHLXpyDwwvoSCtdjbwzJNmSLW6CmgyFdXzB0kZSU2LlQ
# +QuJYfM2BjUYhEfb3BvR/bLUHMVr9lxSUV0S2yW6r1AFemzFER1y7435UsSFF5PA
# PBXbGjfHCBUYP3irRbb1Hode2o+eFnJpxq57t7c+auIurQIDAQABo4IB3TCCAdkw
# EgYJKwYBBAGCNxUBBAUCAwEAATAjBgkrBgEEAYI3FQIEFgQUKqdS/mTEmr6CkTxG
# NSnPEP8vBO4wHQYDVR0OBBYEFJ+nFV0AXmJdg/Tl0mWnG1M1GelyMFwGA1UdIARV
# MFMwUQYMKwYBBAGCN0yDfQEBMEEwPwYIKwYBBQUHAgEWM2h0dHA6Ly93d3cubWlj
# cm9zb2Z0LmNvbS9wa2lvcHMvRG9jcy9SZXBvc2l0b3J5Lmh0bTATBgNVHSUEDDAK
# BggrBgEFBQcDCDAZBgkrBgEEAYI3FAIEDB4KAFMAdQBiAEMAQTALBgNVHQ8EBAMC
# AYYwDwYDVR0TAQH/BAUwAwEB/zAfBgNVHSMEGDAWgBTV9lbLj+iiXGJo0T2UkFvX
# zpoYxDBWBgNVHR8ETzBNMEugSaBHhkVodHRwOi8vY3JsLm1pY3Jvc29mdC5jb20v
# cGtpL2NybC9wcm9kdWN0cy9NaWNSb29DZXJBdXRfMjAxMC0wNi0yMy5jcmwwWgYI
# KwYBBQUHAQEETjBMMEoGCCsGAQUFBzAChj5odHRwOi8vd3d3Lm1pY3Jvc29mdC5j
# b20vcGtpL2NlcnRzL01pY1Jvb0NlckF1dF8yMDEwLTA2LTIzLmNydDANBgkqhkiG
# 9w0BAQsFAAOCAgEAnVV9/Cqt4SwfZwExJFvhnnJL/Klv6lwUtj5OR2R4sQaTlz0x
# M7U518JxNj/aZGx80HU5bbsPMeTCj/ts0aGUGCLu6WZnOlNN3Zi6th542DYunKmC
# VgADsAW+iehp4LoJ7nvfam++Kctu2D9IdQHZGN5tggz1bSNU5HhTdSRXud2f8449
# xvNo32X2pFaq95W2KFUn0CS9QKC/GbYSEhFdPSfgQJY4rPf5KYnDvBewVIVCs/wM
# nosZiefwC2qBwoEZQhlSdYo2wh3DYXMuLGt7bj8sCXgU6ZGyqVvfSaN0DLzskYDS
# PeZKPmY7T7uG+jIa2Zb0j/aRAfbOxnT99kxybxCrdTDFNLB62FD+CljdQDzHVG2d
# Y3RILLFORy3BFARxv2T5JL5zbcqOCb2zAVdJVGTZc9d/HltEAY5aGZFrDZ+kKNxn
# GSgkujhLmm77IVRrakURR6nxt67I6IleT53S0Ex2tVdUCbFpAUR+fKFhbHP+Crvs
# QWY9af3LwUFJfn6Tvsv4O+S3Fb+0zj6lMVGEvL8CwYKiexcdFYmNcP7ntdAoGokL
# jzbaukz5m/8K6TT4JDVnK+ANuOaMmdbhIurwJ0I9JZTmdHRbatGePu1+oDEzfbzL
# 6Xu/OHBE0ZDxyKs6ijoIYn/ZcGNTTY3ugm2lBRDBcQZqELQdVTNYs6FwZvKhggNN
# MIICNQIBATCB+aGB0aSBzjCByzELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldhc2hp
# bmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBDb3Jw
# b3JhdGlvbjElMCMGA1UECxMcTWljcm9zb2Z0IEFtZXJpY2EgT3BlcmF0aW9uczEn
# MCUGA1UECxMeblNoaWVsZCBUU1MgRVNOOkEwMDAtMDVFMC1EOTQ3MSUwIwYDVQQD
# ExxNaWNyb3NvZnQgVGltZS1TdGFtcCBTZXJ2aWNloiMKAQEwBwYFKw4DAhoDFQCN
# kvu0NKcSjdYKyrhJZcsyXOUTNKCBgzCBgKR+MHwxCzAJBgNVBAYTAlVTMRMwEQYD
# VQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNy
# b3NvZnQgQ29ycG9yYXRpb24xJjAkBgNVBAMTHU1pY3Jvc29mdCBUaW1lLVN0YW1w
# IFBDQSAyMDEwMA0GCSqGSIb3DQEBCwUAAgUA7BhLyDAiGA8yMDI1MDcwOTAyMDA0
# MFoYDzIwMjUwNzEwMDIwMDQwWjB0MDoGCisGAQQBhFkKBAExLDAqMAoCBQDsGEvI
# AgEAMAcCAQACAicsMAcCAQACAhIvMAoCBQDsGZ1IAgEAMDYGCisGAQQBhFkKBAIx
# KDAmMAwGCisGAQQBhFkKAwKgCjAIAgEAAgMHoSChCjAIAgEAAgMBhqAwDQYJKoZI
# hvcNAQELBQADggEBADnXGmdDdtuw5MGwOaf96lcG8iq3aoRiQjZhOpVbGtu9gArj
# t+uv2sUqBZjSzFlwiX1PTSAE3S8UCYzWWLeDPWmFxjIV04oimy5NgmVOGDAv2lvR
# iCOG3Vzm317mUdC82WJTGwIYgkrC7ZU5hkXuBODWKwGCuiZ1m+lrCk7NPUg4WXLv
# HEn6PU82GrpnaKTOqd+mRx3x8LUGua3J5JD6EoCtSKPWRMjfd6pHuD/FfCHx1m6i
# M43oGVpeeCfG443+Rz9RZmfKX+f7N1pIFqBid4bYrv14PcsfJe0t9GZ5NyA5n8R+
# bO0E0hNfYg2EuhrMUU82bexalXDjwJeg/kbOVFsxggQNMIIECQIBATCBkzB8MQsw
# CQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9u
# ZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSYwJAYDVQQDEx1NaWNy
# b3NvZnQgVGltZS1TdGFtcCBQQ0EgMjAxMAITMwAAAgh4nVhdksfZUgABAAACCDAN
# BglghkgBZQMEAgEFAKCCAUowGgYJKoZIhvcNAQkDMQ0GCyqGSIb3DQEJEAEEMC8G
# CSqGSIb3DQEJBDEiBCDI6xrOgADxUedNzEQnupHu+CowcP486OVd2R+7vHzHjzCB
# +gYLKoZIhvcNAQkQAi8xgeowgecwgeQwgb0EII//jm8JHa2W1O9778t9+Ft2Z5Nm
# KqttPk6Q+9RRpmepMIGYMIGApH4wfDELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldh
# c2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBD
# b3Jwb3JhdGlvbjEmMCQGA1UEAxMdTWljcm9zb2Z0IFRpbWUtU3RhbXAgUENBIDIw
# MTACEzMAAAIIeJ1YXZLH2VIAAQAAAggwIgQgPL73O+knfnt2bAvd1AuD2lC7DQdu
# 5l6OGM83s86H4zEwDQYJKoZIhvcNAQELBQAEggIAlGuTWSSrGnoBe/GaYCzF0OSh
# lGO696V0NXRmhxo7cqaZx1OgPLRXQee+2iEbtNYFEzH946A8dAvnjjahUgvG0zBb
# YMni8m+0dwy9Qn83pUmAcmsigorcBNyzLq9vSSwhSd+d5uJfFIbWWvnByap+hpQz
# Pfm3Axc4wHHk5Em7hmIwSmdvxIV6vWzKW3ZWDixZIHsZH+N7JM7O6ak44kW1SMuq
# IEDOKqEuJQ/6evkcM8Yt2amhS/1mD+9iqsJzJbWfoK85pRix3zxYPzQL3EvdOMWm
# l/HQuTqrL8GWxJSBmt/2L91oqVpCnovlEcjYchYExEdQY88wIcnbqTW/7uXrWC0o
# KX/SlkABXtTDn9YMaUqWVe2CQU01oarU/kqPfPcYhPdiaJs+jKu6Gk6P/E6rWl4S
# 6IM9nEIIAoUExgUxw3FadRzdcdBH1oRPK8KVZps6t6F2vYh+9WGMyR+DLZfEPZuO
# kV+P6mil3C8PbG3dfFaUGn+pGkoYD/tbNCIeujiUGTVV1NP9STWz48pNFytNKrmj
# QXQN+1zAwQF2ysLGlHzpT8IKpfBFqPTMDyQO0hEml1zJfqdna2fcRzgFmPmze0Kg
# /OWk5vmFboGlA7wGNEb5FKTUW2CH0vmbTHw78oT1frB9WWNboREbq3ynjOWAx9gx
# HlV8ft/3pLe7TDNoR68=
# SIG # End signature block
