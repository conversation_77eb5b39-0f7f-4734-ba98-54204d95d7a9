#
# Module manifest for module 'Microsoft.Graph.Devices.CorporateManagement'
#
# Generated by: Microsoft Corporation
#
# Generated on: 7/9/2025
#

@{

# Script module or binary module file associated with this manifest.
RootModule = './Microsoft.Graph.Devices.CorporateManagement.psm1'

# Version number of this module.
ModuleVersion = '2.29.0'

# Supported PSEditions
CompatiblePSEditions = 'Core', 'Desktop'

# ID used to uniquely identify this module
GUID = '39dbb3bc-1a84-424a-9efe-683be70a1810'

# Author of this module
Author = 'Microsoft Corporation'

# Company or vendor of this module
CompanyName = 'Microsoft Corporation'

# Copyright statement for this module
Copyright = 'Microsoft Corporation. All rights reserved.'

# Description of the functionality provided by this module
Description = 'Microsoft Graph PowerShell Cmdlets'

# Minimum version of the PowerShell engine required by this module
PowerShellVersion = '5.1'

# Name of the PowerShell host required by this module
# PowerShellHostName = ''

# Minimum version of the PowerShell host required by this module
# PowerShellHostVersion = ''

# Minimum version of Microsoft .NET Framework required by this module. This prerequisite is valid for the PowerShell Desktop edition only.
DotNetFrameworkVersion = '4.7.2'

# Minimum version of the common language runtime (CLR) required by this module. This prerequisite is valid for the PowerShell Desktop edition only.
# ClrVersion = ''

# Processor architecture (None, X86, Amd64) required by this module
# ProcessorArchitecture = ''

# Modules that must be imported into the global environment prior to importing this module
RequiredModules = @(@{ModuleName = 'Microsoft.Graph.Authentication'; RequiredVersion = '2.29.0'; })

# Assemblies that must be loaded prior to importing this module
RequiredAssemblies = './bin/Microsoft.Graph.Devices.CorporateManagement.private.dll'

# Script files (.ps1) that are run in the caller's environment prior to importing this module.
# ScriptsToProcess = @()

# Type files (.ps1xml) to be loaded when importing this module
# TypesToProcess = @()

# Format files (.ps1xml) to be loaded when importing this module
FormatsToProcess = './Microsoft.Graph.Devices.CorporateManagement.format.ps1xml'

# Modules to import as nested modules of the module specified in RootModule/ModuleToProcess
# NestedModules = @()

# Functions to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no functions to export.
FunctionsToExport = 'Clear-MgUserManagedDevice', 'Disable-MgUserManagedDeviceLostMode', 
               'Find-MgUserManagedDevice', 'Get-MgDeviceAppManagement', 
               'Get-MgDeviceAppManagementAndroidManagedAppProtection', 
               'Get-MgDeviceAppManagementAndroidManagedAppProtectionApp', 
               'Get-MgDeviceAppManagementAndroidManagedAppProtectionAppCount', 
               'Get-MgDeviceAppManagementAndroidManagedAppProtectionAssignment', 
               'Get-MgDeviceAppManagementAndroidManagedAppProtectionAssignmentCount', 
               'Get-MgDeviceAppManagementAndroidManagedAppProtectionCount', 
               'Get-MgDeviceAppManagementAndroidManagedAppProtectionDeploymentSummary', 
               'Get-MgDeviceAppManagementDefaultManagedAppProtection', 
               'Get-MgDeviceAppManagementDefaultManagedAppProtectionApp', 
               'Get-MgDeviceAppManagementDefaultManagedAppProtectionAppCount', 
               'Get-MgDeviceAppManagementDefaultManagedAppProtectionCount', 
               'Get-MgDeviceAppManagementDefaultManagedAppProtectionDeploymentSummary', 
               'Get-MgDeviceAppManagementiOSManagedAppProtection', 
               'Get-MgDeviceAppManagementiOSManagedAppProtectionApp', 
               'Get-MgDeviceAppManagementiOSManagedAppProtectionAppCount', 
               'Get-MgDeviceAppManagementiOSManagedAppProtectionAssignment', 
               'Get-MgDeviceAppManagementiOSManagedAppProtectionAssignmentCount', 
               'Get-MgDeviceAppManagementiOSManagedAppProtectionCount', 
               'Get-MgDeviceAppManagementiOSManagedAppProtectionDeploymentSummary', 
               'Get-MgDeviceAppManagementManagedAppPolicy', 
               'Get-MgDeviceAppManagementManagedAppPolicyCount', 
               'Get-MgDeviceAppManagementManagedAppRegistration', 
               'Get-MgDeviceAppManagementManagedAppRegistrationAppliedPolicy', 
               'Get-MgDeviceAppManagementManagedAppRegistrationAppliedPolicyCount', 
               'Get-MgDeviceAppManagementManagedAppRegistrationCount', 
               'Get-MgDeviceAppManagementManagedAppRegistrationIntendedPolicy', 
               'Get-MgDeviceAppManagementManagedAppRegistrationIntendedPolicyCount', 
               'Get-MgDeviceAppManagementManagedAppRegistrationOperation', 
               'Get-MgDeviceAppManagementManagedAppRegistrationOperationCount', 
               'Get-MgDeviceAppManagementManagedAppRegistrationUserIdWithFlaggedAppRegistration', 
               'Get-MgDeviceAppManagementManagedAppStatus', 
               'Get-MgDeviceAppManagementManagedAppStatusCount', 
               'Get-MgDeviceAppManagementManagedEBook', 
               'Get-MgDeviceAppManagementManagedEBookAssignment', 
               'Get-MgDeviceAppManagementManagedEBookAssignmentCount', 
               'Get-MgDeviceAppManagementManagedEBookCount', 
               'Get-MgDeviceAppManagementManagedEBookDeviceState', 
               'Get-MgDeviceAppManagementManagedEBookDeviceStateCount', 
               'Get-MgDeviceAppManagementManagedEBookInstallSummary', 
               'Get-MgDeviceAppManagementManagedEBookUserStateSummary', 
               'Get-MgDeviceAppManagementManagedEBookUserStateSummaryCount', 
               'Get-MgDeviceAppManagementManagedEBookUserStateSummaryDeviceState', 
               'Get-MgDeviceAppManagementManagedEBookUserStateSummaryDeviceStateCount', 
               'Get-MgDeviceAppManagementMdmWindowsInformationProtectionPolicy', 
               'Get-MgDeviceAppManagementMdmWindowsInformationProtectionPolicyAssignment', 
               'Get-MgDeviceAppManagementMdmWindowsInformationProtectionPolicyAssignmentCount', 
               'Get-MgDeviceAppManagementMdmWindowsInformationProtectionPolicyCount', 
               'Get-MgDeviceAppManagementMdmWindowsInformationProtectionPolicyExemptAppLockerFile', 
               'Get-MgDeviceAppManagementMdmWindowsInformationProtectionPolicyExemptAppLockerFileCount', 
               'Get-MgDeviceAppManagementMdmWindowsInformationProtectionPolicyProtectedAppLockerFile', 
               'Get-MgDeviceAppManagementMdmWindowsInformationProtectionPolicyProtectedAppLockerFileCount', 
               'Get-MgDeviceAppManagementMobileApp', 
               'Get-MgDeviceAppManagementMobileAppAsAndroidLobApp', 
               'Get-MgDeviceAppManagementMobileAppAsAndroidLobAppAssignment', 
               'Get-MgDeviceAppManagementMobileAppAsAndroidLobAppAssignmentCount', 
               'Get-MgDeviceAppManagementMobileAppAsAndroidLobAppCategory', 
               'Get-MgDeviceAppManagementMobileAppAsAndroidLobAppCategoryCount', 
               'Get-MgDeviceAppManagementMobileAppAsAndroidLobAppContentVersion', 
               'Get-MgDeviceAppManagementMobileAppAsAndroidLobAppContentVersionContainedApp', 
               'Get-MgDeviceAppManagementMobileAppAsAndroidLobAppContentVersionContainedAppCount', 
               'Get-MgDeviceAppManagementMobileAppAsAndroidLobAppContentVersionCount', 
               'Get-MgDeviceAppManagementMobileAppAsAndroidLobAppContentVersionFile', 
               'Get-MgDeviceAppManagementMobileAppAsAndroidLobAppContentVersionFileCount', 
               'Get-MgDeviceAppManagementMobileAppAsAndroidStoreApp', 
               'Get-MgDeviceAppManagementMobileAppAsAndroidStoreAppAssignment', 
               'Get-MgDeviceAppManagementMobileAppAsAndroidStoreAppAssignmentCount', 
               'Get-MgDeviceAppManagementMobileAppAsAndroidStoreAppCategory', 
               'Get-MgDeviceAppManagementMobileAppAsAndroidStoreAppCategoryCount', 
               'Get-MgDeviceAppManagementMobileAppAsiOSLobApp', 
               'Get-MgDeviceAppManagementMobileAppAsiOSLobAppAssignment', 
               'Get-MgDeviceAppManagementMobileAppAsiOSLobAppAssignmentCount', 
               'Get-MgDeviceAppManagementMobileAppAsiOSLobAppCategory', 
               'Get-MgDeviceAppManagementMobileAppAsiOSLobAppCategoryCount', 
               'Get-MgDeviceAppManagementMobileAppAsiOSLobAppContentVersion', 
               'Get-MgDeviceAppManagementMobileAppAsiOSLobAppContentVersionContainedApp', 
               'Get-MgDeviceAppManagementMobileAppAsiOSLobAppContentVersionContainedAppCount', 
               'Get-MgDeviceAppManagementMobileAppAsiOSLobAppContentVersionCount', 
               'Get-MgDeviceAppManagementMobileAppAsiOSLobAppContentVersionFile', 
               'Get-MgDeviceAppManagementMobileAppAsiOSLobAppContentVersionFileCount', 
               'Get-MgDeviceAppManagementMobileAppAsIoStoreApp', 
               'Get-MgDeviceAppManagementMobileAppAsIoStoreAppAssignment', 
               'Get-MgDeviceAppManagementMobileAppAsIoStoreAppAssignmentCount', 
               'Get-MgDeviceAppManagementMobileAppAsIoStoreAppCategory', 
               'Get-MgDeviceAppManagementMobileAppAsIoStoreAppCategoryCount', 
               'Get-MgDeviceAppManagementMobileAppAsIoVppApp', 
               'Get-MgDeviceAppManagementMobileAppAsIoVppAppAssignment', 
               'Get-MgDeviceAppManagementMobileAppAsIoVppAppAssignmentCount', 
               'Get-MgDeviceAppManagementMobileAppAsIoVppAppCategory', 
               'Get-MgDeviceAppManagementMobileAppAsIoVppAppCategoryCount', 
               'Get-MgDeviceAppManagementMobileAppAsMacOSDmgApp', 
               'Get-MgDeviceAppManagementMobileAppAsMacOSDmgAppAssignment', 
               'Get-MgDeviceAppManagementMobileAppAsMacOSDmgAppAssignmentCount', 
               'Get-MgDeviceAppManagementMobileAppAsMacOSDmgAppCategory', 
               'Get-MgDeviceAppManagementMobileAppAsMacOSDmgAppCategoryCount', 
               'Get-MgDeviceAppManagementMobileAppAsMacOSDmgAppContentVersion', 
               'Get-MgDeviceAppManagementMobileAppAsMacOSDmgAppContentVersionContainedApp', 
               'Get-MgDeviceAppManagementMobileAppAsMacOSDmgAppContentVersionContainedAppCount', 
               'Get-MgDeviceAppManagementMobileAppAsMacOSDmgAppContentVersionCount', 
               'Get-MgDeviceAppManagementMobileAppAsMacOSDmgAppContentVersionFile', 
               'Get-MgDeviceAppManagementMobileAppAsMacOSDmgAppContentVersionFileCount', 
               'Get-MgDeviceAppManagementMobileAppAsMacOSLobApp', 
               'Get-MgDeviceAppManagementMobileAppAsMacOSLobAppAssignment', 
               'Get-MgDeviceAppManagementMobileAppAsMacOSLobAppAssignmentCount', 
               'Get-MgDeviceAppManagementMobileAppAsMacOSLobAppCategory', 
               'Get-MgDeviceAppManagementMobileAppAsMacOSLobAppCategoryCount', 
               'Get-MgDeviceAppManagementMobileAppAsMacOSLobAppContentVersion', 
               'Get-MgDeviceAppManagementMobileAppAsMacOSLobAppContentVersionContainedApp', 
               'Get-MgDeviceAppManagementMobileAppAsMacOSLobAppContentVersionContainedAppCount', 
               'Get-MgDeviceAppManagementMobileAppAsMacOSLobAppContentVersionCount', 
               'Get-MgDeviceAppManagementMobileAppAsMacOSLobAppContentVersionFile', 
               'Get-MgDeviceAppManagementMobileAppAsMacOSLobAppContentVersionFileCount', 
               'Get-MgDeviceAppManagementMobileAppAsManagedAndroidLobApp', 
               'Get-MgDeviceAppManagementMobileAppAsManagedAndroidLobAppAssignment', 
               'Get-MgDeviceAppManagementMobileAppAsManagedAndroidLobAppAssignmentCount', 
               'Get-MgDeviceAppManagementMobileAppAsManagedAndroidLobAppCategory', 
               'Get-MgDeviceAppManagementMobileAppAsManagedAndroidLobAppCategoryCount', 
               'Get-MgDeviceAppManagementMobileAppAsManagedAndroidLobAppContentVersion', 
               'Get-MgDeviceAppManagementMobileAppAsManagedAndroidLobAppContentVersionContainedApp', 
               'Get-MgDeviceAppManagementMobileAppAsManagedAndroidLobAppContentVersionContainedAppCount', 
               'Get-MgDeviceAppManagementMobileAppAsManagedAndroidLobAppContentVersionCount', 
               'Get-MgDeviceAppManagementMobileAppAsManagedAndroidLobAppContentVersionFile', 
               'Get-MgDeviceAppManagementMobileAppAsManagedAndroidLobAppContentVersionFileCount', 
               'Get-MgDeviceAppManagementMobileAppAsManagediOSLobApp', 
               'Get-MgDeviceAppManagementMobileAppAsManagediOSLobAppAssignment', 
               'Get-MgDeviceAppManagementMobileAppAsManagediOSLobAppAssignmentCount', 
               'Get-MgDeviceAppManagementMobileAppAsManagediOSLobAppCategory', 
               'Get-MgDeviceAppManagementMobileAppAsManagediOSLobAppCategoryCount', 
               'Get-MgDeviceAppManagementMobileAppAsManagediOSLobAppContentVersion', 
               'Get-MgDeviceAppManagementMobileAppAsManagediOSLobAppContentVersionContainedApp', 
               'Get-MgDeviceAppManagementMobileAppAsManagediOSLobAppContentVersionContainedAppCount', 
               'Get-MgDeviceAppManagementMobileAppAsManagediOSLobAppContentVersionCount', 
               'Get-MgDeviceAppManagementMobileAppAsManagediOSLobAppContentVersionFile', 
               'Get-MgDeviceAppManagementMobileAppAsManagediOSLobAppContentVersionFileCount', 
               'Get-MgDeviceAppManagementMobileAppAsManagedMobileLobApp', 
               'Get-MgDeviceAppManagementMobileAppAsManagedMobileLobAppAssignment', 
               'Get-MgDeviceAppManagementMobileAppAsManagedMobileLobAppAssignmentCount', 
               'Get-MgDeviceAppManagementMobileAppAsManagedMobileLobAppCategory', 
               'Get-MgDeviceAppManagementMobileAppAsManagedMobileLobAppCategoryCount', 
               'Get-MgDeviceAppManagementMobileAppAsManagedMobileLobAppContentVersion', 
               'Get-MgDeviceAppManagementMobileAppAsManagedMobileLobAppContentVersionContainedApp', 
               'Get-MgDeviceAppManagementMobileAppAsManagedMobileLobAppContentVersionContainedAppCount', 
               'Get-MgDeviceAppManagementMobileAppAsManagedMobileLobAppContentVersionCount', 
               'Get-MgDeviceAppManagementMobileAppAsManagedMobileLobAppContentVersionFile', 
               'Get-MgDeviceAppManagementMobileAppAsManagedMobileLobAppContentVersionFileCount', 
               'Get-MgDeviceAppManagementMobileAppAsMicrosoftStoreForBusinessApp', 
               'Get-MgDeviceAppManagementMobileAppAsMicrosoftStoreForBusinessAppAssignment', 
               'Get-MgDeviceAppManagementMobileAppAsMicrosoftStoreForBusinessAppAssignmentCount', 
               'Get-MgDeviceAppManagementMobileAppAsMicrosoftStoreForBusinessAppCategory', 
               'Get-MgDeviceAppManagementMobileAppAsMicrosoftStoreForBusinessAppCategoryCount', 
               'Get-MgDeviceAppManagementMobileAppAssignment', 
               'Get-MgDeviceAppManagementMobileAppAssignmentCount', 
               'Get-MgDeviceAppManagementMobileAppAsWin32LobApp', 
               'Get-MgDeviceAppManagementMobileAppAsWin32LobAppAssignment', 
               'Get-MgDeviceAppManagementMobileAppAsWin32LobAppAssignmentCount', 
               'Get-MgDeviceAppManagementMobileAppAsWin32LobAppCategory', 
               'Get-MgDeviceAppManagementMobileAppAsWin32LobAppCategoryCount', 
               'Get-MgDeviceAppManagementMobileAppAsWin32LobAppContentVersion', 
               'Get-MgDeviceAppManagementMobileAppAsWin32LobAppContentVersionContainedApp', 
               'Get-MgDeviceAppManagementMobileAppAsWin32LobAppContentVersionContainedAppCount', 
               'Get-MgDeviceAppManagementMobileAppAsWin32LobAppContentVersionCount', 
               'Get-MgDeviceAppManagementMobileAppAsWin32LobAppContentVersionFile', 
               'Get-MgDeviceAppManagementMobileAppAsWin32LobAppContentVersionFileCount', 
               'Get-MgDeviceAppManagementMobileAppAsWindowsAppX', 
               'Get-MgDeviceAppManagementMobileAppAsWindowsAppXAssignment', 
               'Get-MgDeviceAppManagementMobileAppAsWindowsAppXAssignmentCount', 
               'Get-MgDeviceAppManagementMobileAppAsWindowsAppXCategory', 
               'Get-MgDeviceAppManagementMobileAppAsWindowsAppXCategoryCount', 
               'Get-MgDeviceAppManagementMobileAppAsWindowsAppXContentVersion', 
               'Get-MgDeviceAppManagementMobileAppAsWindowsAppXContentVersionContainedApp', 
               'Get-MgDeviceAppManagementMobileAppAsWindowsAppXContentVersionContainedAppCount', 
               'Get-MgDeviceAppManagementMobileAppAsWindowsAppXContentVersionCount', 
               'Get-MgDeviceAppManagementMobileAppAsWindowsAppXContentVersionFile', 
               'Get-MgDeviceAppManagementMobileAppAsWindowsAppXContentVersionFileCount', 
               'Get-MgDeviceAppManagementMobileAppAsWindowsMobileMsi', 
               'Get-MgDeviceAppManagementMobileAppAsWindowsMobileMsiAssignment', 
               'Get-MgDeviceAppManagementMobileAppAsWindowsMobileMsiAssignmentCount', 
               'Get-MgDeviceAppManagementMobileAppAsWindowsMobileMsiCategory', 
               'Get-MgDeviceAppManagementMobileAppAsWindowsMobileMsiCategoryCount', 
               'Get-MgDeviceAppManagementMobileAppAsWindowsMobileMsiContentVersion', 
               'Get-MgDeviceAppManagementMobileAppAsWindowsMobileMsiContentVersionContainedApp', 
               'Get-MgDeviceAppManagementMobileAppAsWindowsMobileMsiContentVersionContainedAppCount', 
               'Get-MgDeviceAppManagementMobileAppAsWindowsMobileMsiContentVersionCount', 
               'Get-MgDeviceAppManagementMobileAppAsWindowsMobileMsiContentVersionFile', 
               'Get-MgDeviceAppManagementMobileAppAsWindowsMobileMsiContentVersionFileCount', 
               'Get-MgDeviceAppManagementMobileAppAsWindowsUniversalAppX', 
               'Get-MgDeviceAppManagementMobileAppAsWindowsUniversalAppXAssignment', 
               'Get-MgDeviceAppManagementMobileAppAsWindowsUniversalAppXAssignmentCount', 
               'Get-MgDeviceAppManagementMobileAppAsWindowsUniversalAppXCategory', 
               'Get-MgDeviceAppManagementMobileAppAsWindowsUniversalAppXCategoryCount', 
               'Get-MgDeviceAppManagementMobileAppAsWindowsUniversalAppXCommittedContainedApp', 
               'Get-MgDeviceAppManagementMobileAppAsWindowsUniversalAppXCommittedContainedAppCount', 
               'Get-MgDeviceAppManagementMobileAppAsWindowsUniversalAppXContentVersion', 
               'Get-MgDeviceAppManagementMobileAppAsWindowsUniversalAppXContentVersionContainedApp', 
               'Get-MgDeviceAppManagementMobileAppAsWindowsUniversalAppXContentVersionContainedAppCount', 
               'Get-MgDeviceAppManagementMobileAppAsWindowsUniversalAppXContentVersionCount', 
               'Get-MgDeviceAppManagementMobileAppAsWindowsUniversalAppXContentVersionFile', 
               'Get-MgDeviceAppManagementMobileAppAsWindowsUniversalAppXContentVersionFileCount', 
               'Get-MgDeviceAppManagementMobileAppAsWindowsWebApp', 
               'Get-MgDeviceAppManagementMobileAppAsWindowsWebAppAssignment', 
               'Get-MgDeviceAppManagementMobileAppAsWindowsWebAppAssignmentCount', 
               'Get-MgDeviceAppManagementMobileAppAsWindowsWebAppCategory', 
               'Get-MgDeviceAppManagementMobileAppAsWindowsWebAppCategoryCount', 
               'Get-MgDeviceAppManagementMobileAppCategory', 
               'Get-MgDeviceAppManagementMobileAppCategoryCount', 
               'Get-MgDeviceAppManagementMobileAppConfiguration', 
               'Get-MgDeviceAppManagementMobileAppConfigurationAssignment', 
               'Get-MgDeviceAppManagementMobileAppConfigurationAssignmentCount', 
               'Get-MgDeviceAppManagementMobileAppConfigurationCount', 
               'Get-MgDeviceAppManagementMobileAppConfigurationDeviceStatus', 
               'Get-MgDeviceAppManagementMobileAppConfigurationDeviceStatusCount', 
               'Get-MgDeviceAppManagementMobileAppConfigurationDeviceStatusSummary', 
               'Get-MgDeviceAppManagementMobileAppConfigurationUserStatus', 
               'Get-MgDeviceAppManagementMobileAppConfigurationUserStatusCount', 
               'Get-MgDeviceAppManagementMobileAppConfigurationUserStatusSummary', 
               'Get-MgDeviceAppManagementMobileAppCount', 
               'Get-MgDeviceAppManagementMobileAppCountAsAndroidLobApp', 
               'Get-MgDeviceAppManagementMobileAppCountAsAndroidStoreApp', 
               'Get-MgDeviceAppManagementMobileAppCountAsiOSLobApp', 
               'Get-MgDeviceAppManagementMobileAppCountAsIoStoreApp', 
               'Get-MgDeviceAppManagementMobileAppCountAsIoVppApp', 
               'Get-MgDeviceAppManagementMobileAppCountAsMacOSDmgApp', 
               'Get-MgDeviceAppManagementMobileAppCountAsMacOSLobApp', 
               'Get-MgDeviceAppManagementMobileAppCountAsManagedAndroidLobApp', 
               'Get-MgDeviceAppManagementMobileAppCountAsManagediOSLobApp', 
               'Get-MgDeviceAppManagementMobileAppCountAsManagedMobileLobApp', 
               'Get-MgDeviceAppManagementMobileAppCountAsMicrosoftStoreForBusinessApp', 
               'Get-MgDeviceAppManagementMobileAppCountAsWin32LobApp', 
               'Get-MgDeviceAppManagementMobileAppCountAsWindowsAppX', 
               'Get-MgDeviceAppManagementMobileAppCountAsWindowsMobileMsi', 
               'Get-MgDeviceAppManagementMobileAppCountAsWindowsUniversalAppX', 
               'Get-MgDeviceAppManagementMobileAppCountAsWindowsWebApp', 
               'Get-MgDeviceAppManagementTargetedManagedAppConfiguration', 
               'Get-MgDeviceAppManagementTargetedManagedAppConfigurationApp', 
               'Get-MgDeviceAppManagementTargetedManagedAppConfigurationAppCount', 
               'Get-MgDeviceAppManagementTargetedManagedAppConfigurationAssignment', 
               'Get-MgDeviceAppManagementTargetedManagedAppConfigurationAssignmentCount', 
               'Get-MgDeviceAppManagementTargetedManagedAppConfigurationCount', 
               'Get-MgDeviceAppManagementTargetedManagedAppConfigurationDeploymentSummary', 
               'Get-MgDeviceAppManagementVppToken', 
               'Get-MgDeviceAppManagementVppTokenCount', 
               'Get-MgDeviceAppManagementWindowsInformationProtectionPolicy', 
               'Get-MgDeviceAppManagementWindowsInformationProtectionPolicyAssignment', 
               'Get-MgDeviceAppManagementWindowsInformationProtectionPolicyAssignmentCount', 
               'Get-MgDeviceAppManagementWindowsInformationProtectionPolicyCount', 
               'Get-MgDeviceAppManagementWindowsInformationProtectionPolicyExemptAppLockerFile', 
               'Get-MgDeviceAppManagementWindowsInformationProtectionPolicyExemptAppLockerFileCount', 
               'Get-MgDeviceAppManagementWindowsInformationProtectionPolicyProtectedAppLockerFile', 
               'Get-MgDeviceAppManagementWindowsInformationProtectionPolicyProtectedAppLockerFileCount', 
               'Get-MgUserDeviceManagementTroubleshootingEvent', 
               'Get-MgUserDeviceManagementTroubleshootingEventCount', 
               'Get-MgUserManagedAppRegistration', 
               'Get-MgUserManagedAppRegistrationCount', 'Get-MgUserManagedDevice', 
               'Get-MgUserManagedDeviceCategory', 
               'Get-MgUserManagedDeviceCategoryByRef', 
               'Get-MgUserManagedDeviceCompliancePolicyState', 
               'Get-MgUserManagedDeviceCompliancePolicyStateCount', 
               'Get-MgUserManagedDeviceConfigurationState', 
               'Get-MgUserManagedDeviceConfigurationStateCount', 
               'Get-MgUserManagedDeviceCount', 
               'Get-MgUserManagedDeviceLogCollectionRequestCount', 
               'Get-MgUserManagedDeviceLogCollectionResponse', 
               'Get-MgUserManagedDeviceUser', 
               'Get-MgUserManagedDeviceWindowsProtectionState', 
               'Get-MgUserManagedDeviceWindowsProtectionStateDetectedMalwareState', 
               'Get-MgUserManagedDeviceWindowsProtectionStateDetectedMalwareStateCount', 
               'Invoke-MgCleanUserManagedDeviceWindowsDevice', 
               'Invoke-MgCommitDeviceAppManagementMobileAppMicrosoftGraphAndroidLobAppContentVersionFile', 
               'Invoke-MgCommitDeviceAppManagementMobileAppMicrosoftGraphiOSLobAppContentVersionFile', 
               'Invoke-MgCommitDeviceAppManagementMobileAppMicrosoftGraphMacOSDmgAppContentVersionFile', 
               'Invoke-MgCommitDeviceAppManagementMobileAppMicrosoftGraphMacOSLobAppContentVersionFile', 
               'Invoke-MgCommitDeviceAppManagementMobileAppMicrosoftGraphManagedAndroidLobAppContentVersionFile', 
               'Invoke-MgCommitDeviceAppManagementMobileAppMicrosoftGraphManagediOSLobAppContentVersionFile', 
               'Invoke-MgCommitDeviceAppManagementMobileAppMicrosoftGraphManagedMobileLobAppContentVersionFile', 
               'Invoke-MgCommitDeviceAppManagementMobileAppMicrosoftGraphWin32LobAppContentVersionFile', 
               'Invoke-MgCommitDeviceAppManagementMobileAppMicrosoftGraphWindowsAppXContentVersionFile', 
               'Invoke-MgCommitDeviceAppManagementMobileAppMicrosoftGraphWindowsMobileMsiContentVersionFile', 
               'Invoke-MgCommitDeviceAppManagementMobileAppMicrosoftGraphWindowsUniversalAppXContentVersionFile', 
               'Invoke-MgDownUserManagedDeviceShut', 
               'Invoke-MgLogoutUserManagedDeviceSharedAppleDeviceActiveUser', 
               'Invoke-MgRenewDeviceAppManagementMobileAppMicrosoftGraphAndroidLobAppContentVersionFileUpload', 
               'Invoke-MgRenewDeviceAppManagementMobileAppMicrosoftGraphiOSLobAppContentVersionFileUpload', 
               'Invoke-MgRenewDeviceAppManagementMobileAppMicrosoftGraphMacOSDmgAppContentVersionFileUpload', 
               'Invoke-MgRenewDeviceAppManagementMobileAppMicrosoftGraphMacOSLobAppContentVersionFileUpload', 
               'Invoke-MgRenewDeviceAppManagementMobileAppMicrosoftGraphManagedAndroidLobAppContentVersionFileUpload', 
               'Invoke-MgRenewDeviceAppManagementMobileAppMicrosoftGraphManagediOSLobAppContentVersionFileUpload', 
               'Invoke-MgRenewDeviceAppManagementMobileAppMicrosoftGraphManagedMobileLobAppContentVersionFileUpload', 
               'Invoke-MgRenewDeviceAppManagementMobileAppMicrosoftGraphWin32LobAppContentVersionFileUpload', 
               'Invoke-MgRenewDeviceAppManagementMobileAppMicrosoftGraphWindowsAppXContentVersionFileUpload', 
               'Invoke-MgRenewDeviceAppManagementMobileAppMicrosoftGraphWindowsMobileMsiContentVersionFileUpload', 
               'Invoke-MgRenewDeviceAppManagementMobileAppMicrosoftGraphWindowsUniversalAppXContentVersionFileUpload', 
               'Invoke-MgRetireUserManagedDevice', 
               'Invoke-MgScanUserManagedDeviceWindowsDefender', 
               'Invoke-MgTargetDeviceAppManagementManagedAppPolicyApp', 
               'Invoke-MgTargetDeviceAppManagementManagedAppRegistrationAppliedPolicyApp', 
               'Invoke-MgTargetDeviceAppManagementManagedAppRegistrationIntendedPolicyApp', 
               'Invoke-MgTargetDeviceAppManagementTargetedManagedAppConfigurationApp', 
               'Lock-MgUserManagedDeviceRemote', 
               'New-MgDeviceAppManagementAndroidManagedAppProtection', 
               'New-MgDeviceAppManagementAndroidManagedAppProtectionApp', 
               'New-MgDeviceAppManagementAndroidManagedAppProtectionAssignment', 
               'New-MgDeviceAppManagementDefaultManagedAppProtection', 
               'New-MgDeviceAppManagementDefaultManagedAppProtectionApp', 
               'New-MgDeviceAppManagementiOSManagedAppProtection', 
               'New-MgDeviceAppManagementiOSManagedAppProtectionApp', 
               'New-MgDeviceAppManagementiOSManagedAppProtectionAssignment', 
               'New-MgDeviceAppManagementManagedAppPolicy', 
               'New-MgDeviceAppManagementManagedAppRegistration', 
               'New-MgDeviceAppManagementManagedAppRegistrationAppliedPolicy', 
               'New-MgDeviceAppManagementManagedAppRegistrationIntendedPolicy', 
               'New-MgDeviceAppManagementManagedAppRegistrationOperation', 
               'New-MgDeviceAppManagementManagedAppStatus', 
               'New-MgDeviceAppManagementManagedEBook', 
               'New-MgDeviceAppManagementManagedEBookAssignment', 
               'New-MgDeviceAppManagementManagedEBookDeviceState', 
               'New-MgDeviceAppManagementManagedEBookUserStateSummary', 
               'New-MgDeviceAppManagementManagedEBookUserStateSummaryDeviceState', 
               'New-MgDeviceAppManagementMdmWindowsInformationProtectionPolicy', 
               'New-MgDeviceAppManagementMdmWindowsInformationProtectionPolicyAssignment', 
               'New-MgDeviceAppManagementMdmWindowsInformationProtectionPolicyExemptAppLockerFile', 
               'New-MgDeviceAppManagementMdmWindowsInformationProtectionPolicyProtectedAppLockerFile', 
               'New-MgDeviceAppManagementMobileApp', 
               'New-MgDeviceAppManagementMobileAppAsAndroidLobAppAssignment', 
               'New-MgDeviceAppManagementMobileAppAsAndroidLobAppContentVersion', 
               'New-MgDeviceAppManagementMobileAppAsAndroidLobAppContentVersionContainedApp', 
               'New-MgDeviceAppManagementMobileAppAsAndroidLobAppContentVersionFile', 
               'New-MgDeviceAppManagementMobileAppAsAndroidStoreAppAssignment', 
               'New-MgDeviceAppManagementMobileAppAsiOSLobAppAssignment', 
               'New-MgDeviceAppManagementMobileAppAsiOSLobAppContentVersion', 
               'New-MgDeviceAppManagementMobileAppAsiOSLobAppContentVersionContainedApp', 
               'New-MgDeviceAppManagementMobileAppAsiOSLobAppContentVersionFile', 
               'New-MgDeviceAppManagementMobileAppAsIoStoreAppAssignment', 
               'New-MgDeviceAppManagementMobileAppAsIoVppAppAssignment', 
               'New-MgDeviceAppManagementMobileAppAsMacOSDmgAppAssignment', 
               'New-MgDeviceAppManagementMobileAppAsMacOSDmgAppContentVersion', 
               'New-MgDeviceAppManagementMobileAppAsMacOSDmgAppContentVersionContainedApp', 
               'New-MgDeviceAppManagementMobileAppAsMacOSDmgAppContentVersionFile', 
               'New-MgDeviceAppManagementMobileAppAsMacOSLobAppAssignment', 
               'New-MgDeviceAppManagementMobileAppAsMacOSLobAppContentVersion', 
               'New-MgDeviceAppManagementMobileAppAsMacOSLobAppContentVersionContainedApp', 
               'New-MgDeviceAppManagementMobileAppAsMacOSLobAppContentVersionFile', 
               'New-MgDeviceAppManagementMobileAppAsManagedAndroidLobAppAssignment', 
               'New-MgDeviceAppManagementMobileAppAsManagedAndroidLobAppContentVersion', 
               'New-MgDeviceAppManagementMobileAppAsManagedAndroidLobAppContentVersionContainedApp', 
               'New-MgDeviceAppManagementMobileAppAsManagedAndroidLobAppContentVersionFile', 
               'New-MgDeviceAppManagementMobileAppAsManagediOSLobAppAssignment', 
               'New-MgDeviceAppManagementMobileAppAsManagediOSLobAppContentVersion', 
               'New-MgDeviceAppManagementMobileAppAsManagediOSLobAppContentVersionContainedApp', 
               'New-MgDeviceAppManagementMobileAppAsManagediOSLobAppContentVersionFile', 
               'New-MgDeviceAppManagementMobileAppAsManagedMobileLobAppAssignment', 
               'New-MgDeviceAppManagementMobileAppAsManagedMobileLobAppContentVersion', 
               'New-MgDeviceAppManagementMobileAppAsManagedMobileLobAppContentVersionContainedApp', 
               'New-MgDeviceAppManagementMobileAppAsManagedMobileLobAppContentVersionFile', 
               'New-MgDeviceAppManagementMobileAppAsMicrosoftStoreForBusinessAppAssignment', 
               'New-MgDeviceAppManagementMobileAppAssignment', 
               'New-MgDeviceAppManagementMobileAppAsWin32LobAppAssignment', 
               'New-MgDeviceAppManagementMobileAppAsWin32LobAppContentVersion', 
               'New-MgDeviceAppManagementMobileAppAsWin32LobAppContentVersionContainedApp', 
               'New-MgDeviceAppManagementMobileAppAsWin32LobAppContentVersionFile', 
               'New-MgDeviceAppManagementMobileAppAsWindowsAppXAssignment', 
               'New-MgDeviceAppManagementMobileAppAsWindowsAppXContentVersion', 
               'New-MgDeviceAppManagementMobileAppAsWindowsAppXContentVersionContainedApp', 
               'New-MgDeviceAppManagementMobileAppAsWindowsAppXContentVersionFile', 
               'New-MgDeviceAppManagementMobileAppAsWindowsMobileMsiAssignment', 
               'New-MgDeviceAppManagementMobileAppAsWindowsMobileMsiContentVersion', 
               'New-MgDeviceAppManagementMobileAppAsWindowsMobileMsiContentVersionContainedApp', 
               'New-MgDeviceAppManagementMobileAppAsWindowsMobileMsiContentVersionFile', 
               'New-MgDeviceAppManagementMobileAppAsWindowsUniversalAppXAssignment', 
               'New-MgDeviceAppManagementMobileAppAsWindowsUniversalAppXCommittedContainedApp', 
               'New-MgDeviceAppManagementMobileAppAsWindowsUniversalAppXContentVersion', 
               'New-MgDeviceAppManagementMobileAppAsWindowsUniversalAppXContentVersionContainedApp', 
               'New-MgDeviceAppManagementMobileAppAsWindowsUniversalAppXContentVersionFile', 
               'New-MgDeviceAppManagementMobileAppAsWindowsWebAppAssignment', 
               'New-MgDeviceAppManagementMobileAppCategory', 
               'New-MgDeviceAppManagementMobileAppConfiguration', 
               'New-MgDeviceAppManagementMobileAppConfigurationAssignment', 
               'New-MgDeviceAppManagementMobileAppConfigurationDeviceStatus', 
               'New-MgDeviceAppManagementMobileAppConfigurationUserStatus', 
               'New-MgDeviceAppManagementTargetedManagedAppConfiguration', 
               'New-MgDeviceAppManagementTargetedManagedAppConfigurationApp', 
               'New-MgDeviceAppManagementTargetedManagedAppConfigurationAssignment', 
               'New-MgDeviceAppManagementVppToken', 
               'New-MgDeviceAppManagementWindowsInformationProtectionPolicy', 
               'New-MgDeviceAppManagementWindowsInformationProtectionPolicyAssignment', 
               'New-MgDeviceAppManagementWindowsInformationProtectionPolicyExemptAppLockerFile', 
               'New-MgDeviceAppManagementWindowsInformationProtectionPolicyProtectedAppLockerFile', 
               'New-MgUserDeviceManagementTroubleshootingEvent', 
               'New-MgUserManagedDevice', 
               'New-MgUserManagedDeviceCompliancePolicyState', 
               'New-MgUserManagedDeviceConfigurationState', 
               'New-MgUserManagedDeviceLogCollectionRequestDownloadUrl', 
               'New-MgUserManagedDeviceLogCollectionResponse', 
               'New-MgUserManagedDeviceWindowsProtectionStateDetectedMalwareState', 
               'Remove-MgDeviceAppManagementAndroidManagedAppProtection', 
               'Remove-MgDeviceAppManagementAndroidManagedAppProtectionApp', 
               'Remove-MgDeviceAppManagementAndroidManagedAppProtectionAssignment', 
               'Remove-MgDeviceAppManagementAndroidManagedAppProtectionDeploymentSummary', 
               'Remove-MgDeviceAppManagementDefaultManagedAppProtection', 
               'Remove-MgDeviceAppManagementDefaultManagedAppProtectionApp', 
               'Remove-MgDeviceAppManagementDefaultManagedAppProtectionDeploymentSummary', 
               'Remove-MgDeviceAppManagementiOSManagedAppProtection', 
               'Remove-MgDeviceAppManagementiOSManagedAppProtectionApp', 
               'Remove-MgDeviceAppManagementiOSManagedAppProtectionAssignment', 
               'Remove-MgDeviceAppManagementiOSManagedAppProtectionDeploymentSummary', 
               'Remove-MgDeviceAppManagementManagedAppPolicy', 
               'Remove-MgDeviceAppManagementManagedAppRegistration', 
               'Remove-MgDeviceAppManagementManagedAppRegistrationAppliedPolicy', 
               'Remove-MgDeviceAppManagementManagedAppRegistrationIntendedPolicy', 
               'Remove-MgDeviceAppManagementManagedAppRegistrationOperation', 
               'Remove-MgDeviceAppManagementManagedAppStatus', 
               'Remove-MgDeviceAppManagementManagedEBook', 
               'Remove-MgDeviceAppManagementManagedEBookAssignment', 
               'Remove-MgDeviceAppManagementManagedEBookDeviceState', 
               'Remove-MgDeviceAppManagementManagedEBookInstallSummary', 
               'Remove-MgDeviceAppManagementManagedEBookUserStateSummary', 
               'Remove-MgDeviceAppManagementManagedEBookUserStateSummaryDeviceState', 
               'Remove-MgDeviceAppManagementMdmWindowsInformationProtectionPolicy', 
               'Remove-MgDeviceAppManagementMdmWindowsInformationProtectionPolicyAssignment', 
               'Remove-MgDeviceAppManagementMdmWindowsInformationProtectionPolicyExemptAppLockerFile', 
               'Remove-MgDeviceAppManagementMdmWindowsInformationProtectionPolicyProtectedAppLockerFile', 
               'Remove-MgDeviceAppManagementMobileApp', 
               'Remove-MgDeviceAppManagementMobileAppAsAndroidLobAppAssignment', 
               'Remove-MgDeviceAppManagementMobileAppAsAndroidLobAppContentVersion', 
               'Remove-MgDeviceAppManagementMobileAppAsAndroidLobAppContentVersionContainedApp', 
               'Remove-MgDeviceAppManagementMobileAppAsAndroidLobAppContentVersionFile', 
               'Remove-MgDeviceAppManagementMobileAppAsAndroidStoreAppAssignment', 
               'Remove-MgDeviceAppManagementMobileAppAsiOSLobAppAssignment', 
               'Remove-MgDeviceAppManagementMobileAppAsiOSLobAppContentVersion', 
               'Remove-MgDeviceAppManagementMobileAppAsiOSLobAppContentVersionContainedApp', 
               'Remove-MgDeviceAppManagementMobileAppAsiOSLobAppContentVersionFile', 
               'Remove-MgDeviceAppManagementMobileAppAsIoStoreAppAssignment', 
               'Remove-MgDeviceAppManagementMobileAppAsIoVppAppAssignment', 
               'Remove-MgDeviceAppManagementMobileAppAsMacOSDmgAppAssignment', 
               'Remove-MgDeviceAppManagementMobileAppAsMacOSDmgAppContentVersion', 
               'Remove-MgDeviceAppManagementMobileAppAsMacOSDmgAppContentVersionContainedApp', 
               'Remove-MgDeviceAppManagementMobileAppAsMacOSDmgAppContentVersionFile', 
               'Remove-MgDeviceAppManagementMobileAppAsMacOSLobAppAssignment', 
               'Remove-MgDeviceAppManagementMobileAppAsMacOSLobAppContentVersion', 
               'Remove-MgDeviceAppManagementMobileAppAsMacOSLobAppContentVersionContainedApp', 
               'Remove-MgDeviceAppManagementMobileAppAsMacOSLobAppContentVersionFile', 
               'Remove-MgDeviceAppManagementMobileAppAsManagedAndroidLobAppAssignment', 
               'Remove-MgDeviceAppManagementMobileAppAsManagedAndroidLobAppContentVersion', 
               'Remove-MgDeviceAppManagementMobileAppAsManagedAndroidLobAppContentVersionContainedApp', 
               'Remove-MgDeviceAppManagementMobileAppAsManagedAndroidLobAppContentVersionFile', 
               'Remove-MgDeviceAppManagementMobileAppAsManagediOSLobAppAssignment', 
               'Remove-MgDeviceAppManagementMobileAppAsManagediOSLobAppContentVersion', 
               'Remove-MgDeviceAppManagementMobileAppAsManagediOSLobAppContentVersionContainedApp', 
               'Remove-MgDeviceAppManagementMobileAppAsManagediOSLobAppContentVersionFile', 
               'Remove-MgDeviceAppManagementMobileAppAsManagedMobileLobAppAssignment', 
               'Remove-MgDeviceAppManagementMobileAppAsManagedMobileLobAppContentVersion', 
               'Remove-MgDeviceAppManagementMobileAppAsManagedMobileLobAppContentVersionContainedApp', 
               'Remove-MgDeviceAppManagementMobileAppAsManagedMobileLobAppContentVersionFile', 
               'Remove-MgDeviceAppManagementMobileAppAsMicrosoftStoreForBusinessAppAssignment', 
               'Remove-MgDeviceAppManagementMobileAppAssignment', 
               'Remove-MgDeviceAppManagementMobileAppAsWin32LobAppAssignment', 
               'Remove-MgDeviceAppManagementMobileAppAsWin32LobAppContentVersion', 
               'Remove-MgDeviceAppManagementMobileAppAsWin32LobAppContentVersionContainedApp', 
               'Remove-MgDeviceAppManagementMobileAppAsWin32LobAppContentVersionFile', 
               'Remove-MgDeviceAppManagementMobileAppAsWindowsAppXAssignment', 
               'Remove-MgDeviceAppManagementMobileAppAsWindowsAppXContentVersion', 
               'Remove-MgDeviceAppManagementMobileAppAsWindowsAppXContentVersionContainedApp', 
               'Remove-MgDeviceAppManagementMobileAppAsWindowsAppXContentVersionFile', 
               'Remove-MgDeviceAppManagementMobileAppAsWindowsMobileMsiAssignment', 
               'Remove-MgDeviceAppManagementMobileAppAsWindowsMobileMsiContentVersion', 
               'Remove-MgDeviceAppManagementMobileAppAsWindowsMobileMsiContentVersionContainedApp', 
               'Remove-MgDeviceAppManagementMobileAppAsWindowsMobileMsiContentVersionFile', 
               'Remove-MgDeviceAppManagementMobileAppAsWindowsUniversalAppXAssignment', 
               'Remove-MgDeviceAppManagementMobileAppAsWindowsUniversalAppXCommittedContainedApp', 
               'Remove-MgDeviceAppManagementMobileAppAsWindowsUniversalAppXContentVersion', 
               'Remove-MgDeviceAppManagementMobileAppAsWindowsUniversalAppXContentVersionContainedApp', 
               'Remove-MgDeviceAppManagementMobileAppAsWindowsUniversalAppXContentVersionFile', 
               'Remove-MgDeviceAppManagementMobileAppAsWindowsWebAppAssignment', 
               'Remove-MgDeviceAppManagementMobileAppCategory', 
               'Remove-MgDeviceAppManagementMobileAppConfiguration', 
               'Remove-MgDeviceAppManagementMobileAppConfigurationAssignment', 
               'Remove-MgDeviceAppManagementMobileAppConfigurationDeviceStatus', 
               'Remove-MgDeviceAppManagementMobileAppConfigurationDeviceStatusSummary', 
               'Remove-MgDeviceAppManagementMobileAppConfigurationUserStatus', 
               'Remove-MgDeviceAppManagementMobileAppConfigurationUserStatusSummary', 
               'Remove-MgDeviceAppManagementTargetedManagedAppConfiguration', 
               'Remove-MgDeviceAppManagementTargetedManagedAppConfigurationApp', 
               'Remove-MgDeviceAppManagementTargetedManagedAppConfigurationAssignment', 
               'Remove-MgDeviceAppManagementTargetedManagedAppConfigurationDeploymentSummary', 
               'Remove-MgDeviceAppManagementVppToken', 
               'Remove-MgDeviceAppManagementWindowsInformationProtectionPolicy', 
               'Remove-MgDeviceAppManagementWindowsInformationProtectionPolicyAssignment', 
               'Remove-MgDeviceAppManagementWindowsInformationProtectionPolicyExemptAppLockerFile', 
               'Remove-MgDeviceAppManagementWindowsInformationProtectionPolicyProtectedAppLockerFile', 
               'Remove-MgUserDeviceManagementTroubleshootingEvent', 
               'Remove-MgUserManagedDevice', 'Remove-MgUserManagedDeviceCategory', 
               'Remove-MgUserManagedDeviceCategoryByRef', 
               'Remove-MgUserManagedDeviceCompliancePolicyState', 
               'Remove-MgUserManagedDeviceConfigurationState', 
               'Remove-MgUserManagedDeviceLogCollectionResponse', 
               'Remove-MgUserManagedDeviceUserFromSharedAppleDevice', 
               'Remove-MgUserManagedDeviceWindowsProtectionState', 
               'Remove-MgUserManagedDeviceWindowsProtectionStateDetectedMalwareState', 
               'Request-MgUserManagedDeviceRemoteAssistance', 
               'Reset-MgUserManagedDevicePasscode', 
               'Restart-MgUserManagedDeviceNow', 
               'Restore-MgUserManagedDevicePasscode', 
               'Set-MgDeviceAppManagementManagedEBook', 
               'Set-MgDeviceAppManagementMobileApp', 
               'Set-MgDeviceAppManagementMobileAppConfiguration', 
               'Set-MgDeviceAppManagementTargetedManagedAppConfiguration', 
               'Set-MgUserManagedDeviceCategoryByRef', 
               'Skip-MgUserManagedDeviceActivationLock', 
               'Sync-MgDeviceAppManagementMicrosoftStoreForBusinessApp', 
               'Sync-MgDeviceAppManagementVppTokenLicense', 
               'Sync-MgUserManagedDevice', 'Update-MgDeviceAppManagement', 
               'Update-MgDeviceAppManagementAndroidManagedAppProtection', 
               'Update-MgDeviceAppManagementAndroidManagedAppProtectionApp', 
               'Update-MgDeviceAppManagementAndroidManagedAppProtectionAssignment', 
               'Update-MgDeviceAppManagementAndroidManagedAppProtectionDeploymentSummary', 
               'Update-MgDeviceAppManagementDefaultManagedAppProtection', 
               'Update-MgDeviceAppManagementDefaultManagedAppProtectionApp', 
               'Update-MgDeviceAppManagementDefaultManagedAppProtectionDeploymentSummary', 
               'Update-MgDeviceAppManagementiOSManagedAppProtection', 
               'Update-MgDeviceAppManagementiOSManagedAppProtectionApp', 
               'Update-MgDeviceAppManagementiOSManagedAppProtectionAssignment', 
               'Update-MgDeviceAppManagementiOSManagedAppProtectionDeploymentSummary', 
               'Update-MgDeviceAppManagementManagedAppPolicy', 
               'Update-MgDeviceAppManagementManagedAppRegistration', 
               'Update-MgDeviceAppManagementManagedAppRegistrationAppliedPolicy', 
               'Update-MgDeviceAppManagementManagedAppRegistrationIntendedPolicy', 
               'Update-MgDeviceAppManagementManagedAppRegistrationOperation', 
               'Update-MgDeviceAppManagementManagedAppStatus', 
               'Update-MgDeviceAppManagementManagedEBook', 
               'Update-MgDeviceAppManagementManagedEBookAssignment', 
               'Update-MgDeviceAppManagementManagedEBookDeviceState', 
               'Update-MgDeviceAppManagementManagedEBookInstallSummary', 
               'Update-MgDeviceAppManagementManagedEBookUserStateSummary', 
               'Update-MgDeviceAppManagementManagedEBookUserStateSummaryDeviceState', 
               'Update-MgDeviceAppManagementMdmWindowsInformationProtectionPolicy', 
               'Update-MgDeviceAppManagementMdmWindowsInformationProtectionPolicyAssignment', 
               'Update-MgDeviceAppManagementMdmWindowsInformationProtectionPolicyExemptAppLockerFile', 
               'Update-MgDeviceAppManagementMdmWindowsInformationProtectionPolicyProtectedAppLockerFile', 
               'Update-MgDeviceAppManagementMobileApp', 
               'Update-MgDeviceAppManagementMobileAppAsAndroidLobAppAssignment', 
               'Update-MgDeviceAppManagementMobileAppAsAndroidLobAppContentVersion', 
               'Update-MgDeviceAppManagementMobileAppAsAndroidLobAppContentVersionContainedApp', 
               'Update-MgDeviceAppManagementMobileAppAsAndroidLobAppContentVersionFile', 
               'Update-MgDeviceAppManagementMobileAppAsAndroidStoreAppAssignment', 
               'Update-MgDeviceAppManagementMobileAppAsiOSLobAppAssignment', 
               'Update-MgDeviceAppManagementMobileAppAsiOSLobAppContentVersion', 
               'Update-MgDeviceAppManagementMobileAppAsiOSLobAppContentVersionContainedApp', 
               'Update-MgDeviceAppManagementMobileAppAsiOSLobAppContentVersionFile', 
               'Update-MgDeviceAppManagementMobileAppAsIoStoreAppAssignment', 
               'Update-MgDeviceAppManagementMobileAppAsIoVppAppAssignment', 
               'Update-MgDeviceAppManagementMobileAppAsMacOSDmgAppAssignment', 
               'Update-MgDeviceAppManagementMobileAppAsMacOSDmgAppContentVersion', 
               'Update-MgDeviceAppManagementMobileAppAsMacOSDmgAppContentVersionContainedApp', 
               'Update-MgDeviceAppManagementMobileAppAsMacOSDmgAppContentVersionFile', 
               'Update-MgDeviceAppManagementMobileAppAsMacOSLobAppAssignment', 
               'Update-MgDeviceAppManagementMobileAppAsMacOSLobAppContentVersion', 
               'Update-MgDeviceAppManagementMobileAppAsMacOSLobAppContentVersionContainedApp', 
               'Update-MgDeviceAppManagementMobileAppAsMacOSLobAppContentVersionFile', 
               'Update-MgDeviceAppManagementMobileAppAsManagedAndroidLobAppAssignment', 
               'Update-MgDeviceAppManagementMobileAppAsManagedAndroidLobAppContentVersion', 
               'Update-MgDeviceAppManagementMobileAppAsManagedAndroidLobAppContentVersionContainedApp', 
               'Update-MgDeviceAppManagementMobileAppAsManagedAndroidLobAppContentVersionFile', 
               'Update-MgDeviceAppManagementMobileAppAsManagediOSLobAppAssignment', 
               'Update-MgDeviceAppManagementMobileAppAsManagediOSLobAppContentVersion', 
               'Update-MgDeviceAppManagementMobileAppAsManagediOSLobAppContentVersionContainedApp', 
               'Update-MgDeviceAppManagementMobileAppAsManagediOSLobAppContentVersionFile', 
               'Update-MgDeviceAppManagementMobileAppAsManagedMobileLobAppAssignment', 
               'Update-MgDeviceAppManagementMobileAppAsManagedMobileLobAppContentVersion', 
               'Update-MgDeviceAppManagementMobileAppAsManagedMobileLobAppContentVersionContainedApp', 
               'Update-MgDeviceAppManagementMobileAppAsManagedMobileLobAppContentVersionFile', 
               'Update-MgDeviceAppManagementMobileAppAsMicrosoftStoreForBusinessAppAssignment', 
               'Update-MgDeviceAppManagementMobileAppAssignment', 
               'Update-MgDeviceAppManagementMobileAppAsWin32LobAppAssignment', 
               'Update-MgDeviceAppManagementMobileAppAsWin32LobAppContentVersion', 
               'Update-MgDeviceAppManagementMobileAppAsWin32LobAppContentVersionContainedApp', 
               'Update-MgDeviceAppManagementMobileAppAsWin32LobAppContentVersionFile', 
               'Update-MgDeviceAppManagementMobileAppAsWindowsAppXAssignment', 
               'Update-MgDeviceAppManagementMobileAppAsWindowsAppXContentVersion', 
               'Update-MgDeviceAppManagementMobileAppAsWindowsAppXContentVersionContainedApp', 
               'Update-MgDeviceAppManagementMobileAppAsWindowsAppXContentVersionFile', 
               'Update-MgDeviceAppManagementMobileAppAsWindowsMobileMsiAssignment', 
               'Update-MgDeviceAppManagementMobileAppAsWindowsMobileMsiContentVersion', 
               'Update-MgDeviceAppManagementMobileAppAsWindowsMobileMsiContentVersionContainedApp', 
               'Update-MgDeviceAppManagementMobileAppAsWindowsMobileMsiContentVersionFile', 
               'Update-MgDeviceAppManagementMobileAppAsWindowsUniversalAppXAssignment', 
               'Update-MgDeviceAppManagementMobileAppAsWindowsUniversalAppXCommittedContainedApp', 
               'Update-MgDeviceAppManagementMobileAppAsWindowsUniversalAppXContentVersion', 
               'Update-MgDeviceAppManagementMobileAppAsWindowsUniversalAppXContentVersionContainedApp', 
               'Update-MgDeviceAppManagementMobileAppAsWindowsUniversalAppXContentVersionFile', 
               'Update-MgDeviceAppManagementMobileAppAsWindowsWebAppAssignment', 
               'Update-MgDeviceAppManagementMobileAppCategory', 
               'Update-MgDeviceAppManagementMobileAppConfiguration', 
               'Update-MgDeviceAppManagementMobileAppConfigurationAssignment', 
               'Update-MgDeviceAppManagementMobileAppConfigurationDeviceStatus', 
               'Update-MgDeviceAppManagementMobileAppConfigurationDeviceStatusSummary', 
               'Update-MgDeviceAppManagementMobileAppConfigurationUserStatus', 
               'Update-MgDeviceAppManagementMobileAppConfigurationUserStatusSummary', 
               'Update-MgDeviceAppManagementTargetedManagedAppConfiguration', 
               'Update-MgDeviceAppManagementTargetedManagedAppConfigurationApp', 
               'Update-MgDeviceAppManagementTargetedManagedAppConfigurationAssignment', 
               'Update-MgDeviceAppManagementTargetedManagedAppConfigurationDeploymentSummary', 
               'Update-MgDeviceAppManagementVppToken', 
               'Update-MgDeviceAppManagementWindowsInformationProtectionPolicy', 
               'Update-MgDeviceAppManagementWindowsInformationProtectionPolicyAssignment', 
               'Update-MgDeviceAppManagementWindowsInformationProtectionPolicyExemptAppLockerFile', 
               'Update-MgDeviceAppManagementWindowsInformationProtectionPolicyProtectedAppLockerFile', 
               'Update-MgUserDeviceManagementTroubleshootingEvent', 
               'Update-MgUserManagedDevice', 'Update-MgUserManagedDeviceCategory', 
               'Update-MgUserManagedDeviceCompliancePolicyState', 
               'Update-MgUserManagedDeviceConfigurationState', 
               'Update-MgUserManagedDeviceLogCollectionResponse', 
               'Update-MgUserManagedDeviceWindowsDeviceAccount', 
               'Update-MgUserManagedDeviceWindowsProtectionState', 
               'Update-MgUserManagedDeviceWindowsProtectionStateDetectedMalwareState'

# Cmdlets to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no cmdlets to export.
CmdletsToExport = @()

# Variables to export from this module
# VariablesToExport = @()

# Aliases to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no aliases to export.
AliasesToExport = 'Get-MgDeviceAppMgt', 
               'Get-MgDeviceAppMgtAndroidManagedAppProtection', 
               'Get-MgDeviceAppMgtAndroidManagedAppProtectionApp', 
               'Get-MgDeviceAppMgtAndroidManagedAppProtectionAppCount', 
               'Get-MgDeviceAppMgtAndroidManagedAppProtectionAssignment', 
               'Get-MgDeviceAppMgtAndroidManagedAppProtectionAssignmentCount', 
               'Get-MgDeviceAppMgtAndroidManagedAppProtectionCount', 
               'Get-MgDeviceAppMgtAndroidManagedAppProtectionDeploymentSummary', 
               'Get-MgDeviceAppMgtDefaultManagedAppProtection', 
               'Get-MgDeviceAppMgtDefaultManagedAppProtectionApp', 
               'Get-MgDeviceAppMgtDefaultManagedAppProtectionAppCount', 
               'Get-MgDeviceAppMgtDefaultManagedAppProtectionCount', 
               'Get-MgDeviceAppMgtDefaultManagedAppProtectionDeploymentSummary', 
               'Get-MgDeviceAppMgtiOSManagedAppProtection', 
               'Get-MgDeviceAppMgtiOSManagedAppProtectionApp', 
               'Get-MgDeviceAppMgtiOSManagedAppProtectionAppCount', 
               'Get-MgDeviceAppMgtiOSManagedAppProtectionAssignment', 
               'Get-MgDeviceAppMgtiOSManagedAppProtectionAssignmentCount', 
               'Get-MgDeviceAppMgtiOSManagedAppProtectionCount', 
               'Get-MgDeviceAppMgtiOSManagedAppProtectionDeploymentSummary', 
               'Get-MgDeviceAppMgtManagedAppPolicy', 
               'Get-MgDeviceAppMgtManagedAppPolicyCount', 
               'Get-MgDeviceAppMgtManagedAppRegistration', 
               'Get-MgDeviceAppMgtManagedAppRegistrationAppliedPolicy', 
               'Get-MgDeviceAppMgtManagedAppRegistrationAppliedPolicyCount', 
               'Get-MgDeviceAppMgtManagedAppRegistrationCount', 
               'Get-MgDeviceAppMgtManagedAppRegistrationIntendedPolicy', 
               'Get-MgDeviceAppMgtManagedAppRegistrationIntendedPolicyCount', 
               'Get-MgDeviceAppMgtManagedAppRegistrationOperation', 
               'Get-MgDeviceAppMgtManagedAppRegistrationOperationCount', 
               'Get-MgDeviceAppMgtManagedAppRegistrationUserIdGraphWPreFlaggedAppRegistration', 
               'Get-MgDeviceAppMgtManagedAppStatus', 
               'Get-MgDeviceAppMgtManagedAppStatusCount', 
               'Get-MgDeviceAppMgtManagedEBook', 
               'Get-MgDeviceAppMgtManagedEBookAssignment', 
               'Get-MgDeviceAppMgtManagedEBookAssignmentCount', 
               'Get-MgDeviceAppMgtManagedEBookCount', 
               'Get-MgDeviceAppMgtManagedEBookDeviceState', 
               'Get-MgDeviceAppMgtManagedEBookDeviceStateCount', 
               'Get-MgDeviceAppMgtManagedEBookInstallSummary', 
               'Get-MgDeviceAppMgtManagedEBookUserStateSummary', 
               'Get-MgDeviceAppMgtManagedEBookUserStateSummaryCount', 
               'Get-MgDeviceAppMgtManagedEBookUserStateSummaryDeviceState', 
               'Get-MgDeviceAppMgtManagedEBookUserStateSummaryDeviceStateCount', 
               'Get-MgDeviceAppMgtMdmWindowInformationProtectionPolicy', 
               'Get-MgDeviceAppMgtMdmWindowInformationProtectionPolicyAssignment', 
               'Get-MgDeviceAppMgtMdmWindowInformationProtectionPolicyAssignmentCount', 
               'Get-MgDeviceAppMgtMdmWindowInformationProtectionPolicyCount', 
               'Get-MgDeviceAppMgtMdmWindowInformationProtectionPolicyExemptAppLockerFile', 
               'Get-MgDeviceAppMgtMdmWindowInformationProtectionPolicyExemptAppLockerFileCount', 
               'Get-MgDeviceAppMgtMdmWindowInformationProtectionPolicyProtectedAppLockerFile', 
               'Get-MgDeviceAppMgtMdmWindowInformationProtectionPolicyProtectedAppLockerFileCount', 
               'Get-MgDeviceAppMgtMobileApp', 
               'Get-MgDeviceAppMgtMobileAppAsAndroidLobApp', 
               'Get-MgDeviceAppMgtMobileAppAsAndroidLobAppAssignment', 
               'Get-MgDeviceAppMgtMobileAppAsAndroidLobAppAssignmentCount', 
               'Get-MgDeviceAppMgtMobileAppAsAndroidLobAppCategory', 
               'Get-MgDeviceAppMgtMobileAppAsAndroidLobAppCategoryCount', 
               'Get-MgDeviceAppMgtMobileAppAsAndroidLobAppContentVersion', 
               'Get-MgDeviceAppMgtMobileAppAsAndroidLobAppContentVersionContainedApp', 
               'Get-MgDeviceAppMgtMobileAppAsAndroidLobAppContentVersionContainedAppCount', 
               'Get-MgDeviceAppMgtMobileAppAsAndroidLobAppContentVersionCount', 
               'Get-MgDeviceAppMgtMobileAppAsAndroidLobAppContentVersionFile', 
               'Get-MgDeviceAppMgtMobileAppAsAndroidLobAppContentVersionFileCount', 
               'Get-MgDeviceAppMgtMobileAppAsAndroidStoreApp', 
               'Get-MgDeviceAppMgtMobileAppAsAndroidStoreAppAssignment', 
               'Get-MgDeviceAppMgtMobileAppAsAndroidStoreAppAssignmentCount', 
               'Get-MgDeviceAppMgtMobileAppAsAndroidStoreAppCategory', 
               'Get-MgDeviceAppMgtMobileAppAsAndroidStoreAppCategoryCount', 
               'Get-MgDeviceAppMgtMobileAppAsiOSLobApp', 
               'Get-MgDeviceAppMgtMobileAppAsiOSLobAppAssignment', 
               'Get-MgDeviceAppMgtMobileAppAsiOSLobAppAssignmentCount', 
               'Get-MgDeviceAppMgtMobileAppAsiOSLobAppCategory', 
               'Get-MgDeviceAppMgtMobileAppAsiOSLobAppCategoryCount', 
               'Get-MgDeviceAppMgtMobileAppAsiOSLobAppContentVersion', 
               'Get-MgDeviceAppMgtMobileAppAsiOSLobAppContentVersionContainedApp', 
               'Get-MgDeviceAppMgtMobileAppAsiOSLobAppContentVersionContainedAppCount', 
               'Get-MgDeviceAppMgtMobileAppAsiOSLobAppContentVersionCount', 
               'Get-MgDeviceAppMgtMobileAppAsiOSLobAppContentVersionFile', 
               'Get-MgDeviceAppMgtMobileAppAsiOSLobAppContentVersionFileCount', 
               'Get-MgDeviceAppMgtMobileAppAsIoStoreApp', 
               'Get-MgDeviceAppMgtMobileAppAsIoStoreAppAssignment', 
               'Get-MgDeviceAppMgtMobileAppAsIoStoreAppAssignmentCount', 
               'Get-MgDeviceAppMgtMobileAppAsIoStoreAppCategory', 
               'Get-MgDeviceAppMgtMobileAppAsIoStoreAppCategoryCount', 
               'Get-MgDeviceAppMgtMobileAppAsIoVppApp', 
               'Get-MgDeviceAppMgtMobileAppAsIoVppAppAssignment', 
               'Get-MgDeviceAppMgtMobileAppAsIoVppAppAssignmentCount', 
               'Get-MgDeviceAppMgtMobileAppAsIoVppAppCategory', 
               'Get-MgDeviceAppMgtMobileAppAsIoVppAppCategoryCount', 
               'Get-MgDeviceAppMgtMobileAppAsMacOSDmgApp', 
               'Get-MgDeviceAppMgtMobileAppAsMacOSDmgAppAssignment', 
               'Get-MgDeviceAppMgtMobileAppAsMacOSDmgAppAssignmentCount', 
               'Get-MgDeviceAppMgtMobileAppAsMacOSDmgAppCategory', 
               'Get-MgDeviceAppMgtMobileAppAsMacOSDmgAppCategoryCount', 
               'Get-MgDeviceAppMgtMobileAppAsMacOSDmgAppContentVersion', 
               'Get-MgDeviceAppMgtMobileAppAsMacOSDmgAppContentVersionContainedApp', 
               'Get-MgDeviceAppMgtMobileAppAsMacOSDmgAppContentVersionContainedAppCount', 
               'Get-MgDeviceAppMgtMobileAppAsMacOSDmgAppContentVersionCount', 
               'Get-MgDeviceAppMgtMobileAppAsMacOSDmgAppContentVersionFile', 
               'Get-MgDeviceAppMgtMobileAppAsMacOSDmgAppContentVersionFileCount', 
               'Get-MgDeviceAppMgtMobileAppAsMacOSLobApp', 
               'Get-MgDeviceAppMgtMobileAppAsMacOSLobAppAssignment', 
               'Get-MgDeviceAppMgtMobileAppAsMacOSLobAppAssignmentCount', 
               'Get-MgDeviceAppMgtMobileAppAsMacOSLobAppCategory', 
               'Get-MgDeviceAppMgtMobileAppAsMacOSLobAppCategoryCount', 
               'Get-MgDeviceAppMgtMobileAppAsMacOSLobAppContentVersion', 
               'Get-MgDeviceAppMgtMobileAppAsMacOSLobAppContentVersionContainedApp', 
               'Get-MgDeviceAppMgtMobileAppAsMacOSLobAppContentVersionContainedAppCount', 
               'Get-MgDeviceAppMgtMobileAppAsMacOSLobAppContentVersionCount', 
               'Get-MgDeviceAppMgtMobileAppAsMacOSLobAppContentVersionFile', 
               'Get-MgDeviceAppMgtMobileAppAsMacOSLobAppContentVersionFileCount', 
               'Get-MgDeviceAppMgtMobileAppAsManagedAndroidLobApp', 
               'Get-MgDeviceAppMgtMobileAppAsManagedAndroidLobAppAssignment', 
               'Get-MgDeviceAppMgtMobileAppAsManagedAndroidLobAppAssignmentCount', 
               'Get-MgDeviceAppMgtMobileAppAsManagedAndroidLobAppCategory', 
               'Get-MgDeviceAppMgtMobileAppAsManagedAndroidLobAppCategoryCount', 
               'Get-MgDeviceAppMgtMobileAppAsManagedAndroidLobAppContentVersion', 
               'Get-MgDeviceAppMgtMobileAppAsManagedAndroidLobAppContentVersionContainedApp', 
               'Get-MgDeviceAppMgtMobileAppAsManagedAndroidLobAppContentVersionContainedAppCount', 
               'Get-MgDeviceAppMgtMobileAppAsManagedAndroidLobAppContentVersionCount', 
               'Get-MgDeviceAppMgtMobileAppAsManagedAndroidLobAppContentVersionFile', 
               'Get-MgDeviceAppMgtMobileAppAsManagedAndroidLobAppContentVersionFileCount', 
               'Get-MgDeviceAppMgtMobileAppAsManagediOSLobApp', 
               'Get-MgDeviceAppMgtMobileAppAsManagediOSLobAppAssignment', 
               'Get-MgDeviceAppMgtMobileAppAsManagediOSLobAppAssignmentCount', 
               'Get-MgDeviceAppMgtMobileAppAsManagediOSLobAppCategory', 
               'Get-MgDeviceAppMgtMobileAppAsManagediOSLobAppCategoryCount', 
               'Get-MgDeviceAppMgtMobileAppAsManagediOSLobAppContentVersion', 
               'Get-MgDeviceAppMgtMobileAppAsManagediOSLobAppContentVersionContainedApp', 
               'Get-MgDeviceAppMgtMobileAppAsManagediOSLobAppContentVersionContainedAppCount', 
               'Get-MgDeviceAppMgtMobileAppAsManagediOSLobAppContentVersionCount', 
               'Get-MgDeviceAppMgtMobileAppAsManagediOSLobAppContentVersionFile', 
               'Get-MgDeviceAppMgtMobileAppAsManagediOSLobAppContentVersionFileCount', 
               'Get-MgDeviceAppMgtMobileAppAsManagedMobileLobApp', 
               'Get-MgDeviceAppMgtMobileAppAsManagedMobileLobAppAssignment', 
               'Get-MgDeviceAppMgtMobileAppAsManagedMobileLobAppAssignmentCount', 
               'Get-MgDeviceAppMgtMobileAppAsManagedMobileLobAppCategory', 
               'Get-MgDeviceAppMgtMobileAppAsManagedMobileLobAppCategoryCount', 
               'Get-MgDeviceAppMgtMobileAppAsManagedMobileLobAppContentVersion', 
               'Get-MgDeviceAppMgtMobileAppAsManagedMobileLobAppContentVersionContainedApp', 
               'Get-MgDeviceAppMgtMobileAppAsManagedMobileLobAppContentVersionContainedAppCount', 
               'Get-MgDeviceAppMgtMobileAppAsManagedMobileLobAppContentVersionCount', 
               'Get-MgDeviceAppMgtMobileAppAsManagedMobileLobAppContentVersionFile', 
               'Get-MgDeviceAppMgtMobileAppAsManagedMobileLobAppContentVersionFileCount', 
               'Get-MgDeviceAppMgtMobileAppAsMicrosoftStoreGraphFPreBusinessApp', 
               'Get-MgDeviceAppMgtMobileAppAsMicrosoftStoreGraphFPreBusinessAppAssignment', 
               'Get-MgDeviceAppMgtMobileAppAsMicrosoftStoreGraphFPreBusinessAppAssignmentCount', 
               'Get-MgDeviceAppMgtMobileAppAsMicrosoftStoreGraphFPreBusinessAppCategory', 
               'Get-MgDeviceAppMgtMobileAppAsMicrosoftStoreGraphFPreBusinessAppCategoryCount', 
               'Get-MgDeviceAppMgtMobileAppAssignment', 
               'Get-MgDeviceAppMgtMobileAppAssignmentCount', 
               'Get-MgDeviceAppMgtMobileAppAsWin32LobApp', 
               'Get-MgDeviceAppMgtMobileAppAsWin32LobAppAssignment', 
               'Get-MgDeviceAppMgtMobileAppAsWin32LobAppAssignmentCount', 
               'Get-MgDeviceAppMgtMobileAppAsWin32LobAppCategory', 
               'Get-MgDeviceAppMgtMobileAppAsWin32LobAppCategoryCount', 
               'Get-MgDeviceAppMgtMobileAppAsWin32LobAppContentVersion', 
               'Get-MgDeviceAppMgtMobileAppAsWin32LobAppContentVersionContainedApp', 
               'Get-MgDeviceAppMgtMobileAppAsWin32LobAppContentVersionContainedAppCount', 
               'Get-MgDeviceAppMgtMobileAppAsWin32LobAppContentVersionCount', 
               'Get-MgDeviceAppMgtMobileAppAsWin32LobAppContentVersionFile', 
               'Get-MgDeviceAppMgtMobileAppAsWin32LobAppContentVersionFileCount', 
               'Get-MgDeviceAppMgtMobileAppAsWindowAppX', 
               'Get-MgDeviceAppMgtMobileAppAsWindowAppXAssignment', 
               'Get-MgDeviceAppMgtMobileAppAsWindowAppXAssignmentCount', 
               'Get-MgDeviceAppMgtMobileAppAsWindowAppXCategory', 
               'Get-MgDeviceAppMgtMobileAppAsWindowAppXCategoryCount', 
               'Get-MgDeviceAppMgtMobileAppAsWindowAppXContentVersion', 
               'Get-MgDeviceAppMgtMobileAppAsWindowAppXContentVersionContainedApp', 
               'Get-MgDeviceAppMgtMobileAppAsWindowAppXContentVersionContainedAppCount', 
               'Get-MgDeviceAppMgtMobileAppAsWindowAppXContentVersionCount', 
               'Get-MgDeviceAppMgtMobileAppAsWindowAppXContentVersionFile', 
               'Get-MgDeviceAppMgtMobileAppAsWindowAppXContentVersionFileCount', 
               'Get-MgDeviceAppMgtMobileAppAsWindowMobileMsi', 
               'Get-MgDeviceAppMgtMobileAppAsWindowMobileMsiAssignment', 
               'Get-MgDeviceAppMgtMobileAppAsWindowMobileMsiAssignmentCount', 
               'Get-MgDeviceAppMgtMobileAppAsWindowMobileMsiCategory', 
               'Get-MgDeviceAppMgtMobileAppAsWindowMobileMsiCategoryCount', 
               'Get-MgDeviceAppMgtMobileAppAsWindowMobileMsiContentVersion', 
               'Get-MgDeviceAppMgtMobileAppAsWindowMobileMsiContentVersionContainedApp', 
               'Get-MgDeviceAppMgtMobileAppAsWindowMobileMsiContentVersionContainedAppCount', 
               'Get-MgDeviceAppMgtMobileAppAsWindowMobileMsiContentVersionCount', 
               'Get-MgDeviceAppMgtMobileAppAsWindowMobileMsiContentVersionFile', 
               'Get-MgDeviceAppMgtMobileAppAsWindowMobileMsiContentVersionFileCount', 
               'Get-MgDeviceAppMgtMobileAppAsWindowUniversalAppX', 
               'Get-MgDeviceAppMgtMobileAppAsWindowUniversalAppXAssignment', 
               'Get-MgDeviceAppMgtMobileAppAsWindowUniversalAppXAssignmentCount', 
               'Get-MgDeviceAppMgtMobileAppAsWindowUniversalAppXCategory', 
               'Get-MgDeviceAppMgtMobileAppAsWindowUniversalAppXCategoryCount', 
               'Get-MgDeviceAppMgtMobileAppAsWindowUniversalAppXCommittedContainedApp', 
               'Get-MgDeviceAppMgtMobileAppAsWindowUniversalAppXCommittedContainedAppCount', 
               'Get-MgDeviceAppMgtMobileAppAsWindowUniversalAppXContentVersion', 
               'Get-MgDeviceAppMgtMobileAppAsWindowUniversalAppXContentVersionContainedApp', 
               'Get-MgDeviceAppMgtMobileAppAsWindowUniversalAppXContentVersionContainedAppCount', 
               'Get-MgDeviceAppMgtMobileAppAsWindowUniversalAppXContentVersionCount', 
               'Get-MgDeviceAppMgtMobileAppAsWindowUniversalAppXContentVersionFile', 
               'Get-MgDeviceAppMgtMobileAppAsWindowUniversalAppXContentVersionFileCount', 
               'Get-MgDeviceAppMgtMobileAppAsWindowWebApp', 
               'Get-MgDeviceAppMgtMobileAppAsWindowWebAppAssignment', 
               'Get-MgDeviceAppMgtMobileAppAsWindowWebAppAssignmentCount', 
               'Get-MgDeviceAppMgtMobileAppAsWindowWebAppCategory', 
               'Get-MgDeviceAppMgtMobileAppAsWindowWebAppCategoryCount', 
               'Get-MgDeviceAppMgtMobileAppCategory', 
               'Get-MgDeviceAppMgtMobileAppCategoryCount', 
               'Get-MgDeviceAppMgtMobileAppConfiguration', 
               'Get-MgDeviceAppMgtMobileAppConfigurationAssignment', 
               'Get-MgDeviceAppMgtMobileAppConfigurationAssignmentCount', 
               'Get-MgDeviceAppMgtMobileAppConfigurationCount', 
               'Get-MgDeviceAppMgtMobileAppConfigurationDeviceStatus', 
               'Get-MgDeviceAppMgtMobileAppConfigurationDeviceStatusCount', 
               'Get-MgDeviceAppMgtMobileAppConfigurationDeviceStatusSummary', 
               'Get-MgDeviceAppMgtMobileAppConfigurationUserStatus', 
               'Get-MgDeviceAppMgtMobileAppConfigurationUserStatusCount', 
               'Get-MgDeviceAppMgtMobileAppConfigurationUserStatusSummary', 
               'Get-MgDeviceAppMgtMobileAppCount', 
               'Get-MgDeviceAppMgtMobileAppCountAsAndroidLobApp', 
               'Get-MgDeviceAppMgtMobileAppCountAsAndroidStoreApp', 
               'Get-MgDeviceAppMgtMobileAppCountAsiOSLobApp', 
               'Get-MgDeviceAppMgtMobileAppCountAsIoStoreApp', 
               'Get-MgDeviceAppMgtMobileAppCountAsIoVppApp', 
               'Get-MgDeviceAppMgtMobileAppCountAsMacOSDmgApp', 
               'Get-MgDeviceAppMgtMobileAppCountAsMacOSLobApp', 
               'Get-MgDeviceAppMgtMobileAppCountAsManagedAndroidLobApp', 
               'Get-MgDeviceAppMgtMobileAppCountAsManagediOSLobApp', 
               'Get-MgDeviceAppMgtMobileAppCountAsManagedMobileLobApp', 
               'Get-MgDeviceAppMgtMobileAppCountAsMicrosoftStoreGraphFPreBusinessApp', 
               'Get-MgDeviceAppMgtMobileAppCountAsWin32LobApp', 
               'Get-MgDeviceAppMgtMobileAppCountAsWindowAppX', 
               'Get-MgDeviceAppMgtMobileAppCountAsWindowMobileMsi', 
               'Get-MgDeviceAppMgtMobileAppCountAsWindowUniversalAppX', 
               'Get-MgDeviceAppMgtMobileAppCountAsWindowWebApp', 
               'Get-MgDeviceAppMgtTargetedManagedAppConfiguration', 
               'Get-MgDeviceAppMgtTargetedManagedAppConfigurationApp', 
               'Get-MgDeviceAppMgtTargetedManagedAppConfigurationAppCount', 
               'Get-MgDeviceAppMgtTargetedManagedAppConfigurationAssignment', 
               'Get-MgDeviceAppMgtTargetedManagedAppConfigurationAssignmentCount', 
               'Get-MgDeviceAppMgtTargetedManagedAppConfigurationCount', 
               'Get-MgDeviceAppMgtTargetedManagedAppConfigurationDeploymentSummary', 
               'Get-MgDeviceAppMgtVppToken', 'Get-MgDeviceAppMgtVppTokenCount', 
               'Get-MgDeviceAppMgtWindowInformationProtectionPolicy', 
               'Get-MgDeviceAppMgtWindowInformationProtectionPolicyAssignment', 
               'Get-MgDeviceAppMgtWindowInformationProtectionPolicyAssignmentCount', 
               'Get-MgDeviceAppMgtWindowInformationProtectionPolicyCount', 
               'Get-MgDeviceAppMgtWindowInformationProtectionPolicyExemptAppLockerFile', 
               'Get-MgDeviceAppMgtWindowInformationProtectionPolicyExemptAppLockerFileCount', 
               'Get-MgDeviceAppMgtWindowInformationProtectionPolicyProtectedAppLockerFile', 
               'Get-MgDeviceAppMgtWindowInformationProtectionPolicyProtectedAppLockerFileCount', 
               'New-MgDeviceAppMgtAndroidManagedAppProtection', 
               'New-MgDeviceAppMgtAndroidManagedAppProtectionApp', 
               'New-MgDeviceAppMgtAndroidManagedAppProtectionAssignment', 
               'New-MgDeviceAppMgtDefaultManagedAppProtection', 
               'New-MgDeviceAppMgtDefaultManagedAppProtectionApp', 
               'New-MgDeviceAppMgtiOSManagedAppProtection', 
               'New-MgDeviceAppMgtiOSManagedAppProtectionApp', 
               'New-MgDeviceAppMgtiOSManagedAppProtectionAssignment', 
               'New-MgDeviceAppMgtManagedAppPolicy', 
               'New-MgDeviceAppMgtManagedAppRegistration', 
               'New-MgDeviceAppMgtManagedAppRegistrationAppliedPolicy', 
               'New-MgDeviceAppMgtManagedAppRegistrationIntendedPolicy', 
               'New-MgDeviceAppMgtManagedAppRegistrationOperation', 
               'New-MgDeviceAppMgtManagedAppStatus', 
               'New-MgDeviceAppMgtManagedEBook', 
               'New-MgDeviceAppMgtManagedEBookAssignment', 
               'New-MgDeviceAppMgtManagedEBookDeviceState', 
               'New-MgDeviceAppMgtManagedEBookUserStateSummary', 
               'New-MgDeviceAppMgtManagedEBookUserStateSummaryDeviceState', 
               'New-MgDeviceAppMgtMdmWindowInformationProtectionPolicy', 
               'New-MgDeviceAppMgtMdmWindowInformationProtectionPolicyAssignment', 
               'New-MgDeviceAppMgtMdmWindowInformationProtectionPolicyExemptAppLockerFile', 
               'New-MgDeviceAppMgtMdmWindowInformationProtectionPolicyProtectedAppLockerFile', 
               'New-MgDeviceAppMgtMobileApp', 
               'New-MgDeviceAppMgtMobileAppAsAndroidLobAppAssignment', 
               'New-MgDeviceAppMgtMobileAppAsAndroidLobAppContentVersion', 
               'New-MgDeviceAppMgtMobileAppAsAndroidLobAppContentVersionContainedApp', 
               'New-MgDeviceAppMgtMobileAppAsAndroidLobAppContentVersionFile', 
               'New-MgDeviceAppMgtMobileAppAsAndroidStoreAppAssignment', 
               'New-MgDeviceAppMgtMobileAppAsiOSLobAppAssignment', 
               'New-MgDeviceAppMgtMobileAppAsiOSLobAppContentVersion', 
               'New-MgDeviceAppMgtMobileAppAsiOSLobAppContentVersionContainedApp', 
               'New-MgDeviceAppMgtMobileAppAsiOSLobAppContentVersionFile', 
               'New-MgDeviceAppMgtMobileAppAsIoStoreAppAssignment', 
               'New-MgDeviceAppMgtMobileAppAsIoVppAppAssignment', 
               'New-MgDeviceAppMgtMobileAppAsMacOSDmgAppAssignment', 
               'New-MgDeviceAppMgtMobileAppAsMacOSDmgAppContentVersion', 
               'New-MgDeviceAppMgtMobileAppAsMacOSDmgAppContentVersionContainedApp', 
               'New-MgDeviceAppMgtMobileAppAsMacOSDmgAppContentVersionFile', 
               'New-MgDeviceAppMgtMobileAppAsMacOSLobAppAssignment', 
               'New-MgDeviceAppMgtMobileAppAsMacOSLobAppContentVersion', 
               'New-MgDeviceAppMgtMobileAppAsMacOSLobAppContentVersionContainedApp', 
               'New-MgDeviceAppMgtMobileAppAsMacOSLobAppContentVersionFile', 
               'New-MgDeviceAppMgtMobileAppAsManagedAndroidLobAppAssignment', 
               'New-MgDeviceAppMgtMobileAppAsManagedAndroidLobAppContentVersion', 
               'New-MgDeviceAppMgtMobileAppAsManagedAndroidLobAppContentVersionContainedApp', 
               'New-MgDeviceAppMgtMobileAppAsManagedAndroidLobAppContentVersionFile', 
               'New-MgDeviceAppMgtMobileAppAsManagediOSLobAppAssignment', 
               'New-MgDeviceAppMgtMobileAppAsManagediOSLobAppContentVersion', 
               'New-MgDeviceAppMgtMobileAppAsManagediOSLobAppContentVersionContainedApp', 
               'New-MgDeviceAppMgtMobileAppAsManagediOSLobAppContentVersionFile', 
               'New-MgDeviceAppMgtMobileAppAsManagedMobileLobAppAssignment', 
               'New-MgDeviceAppMgtMobileAppAsManagedMobileLobAppContentVersion', 
               'New-MgDeviceAppMgtMobileAppAsManagedMobileLobAppContentVersionContainedApp', 
               'New-MgDeviceAppMgtMobileAppAsManagedMobileLobAppContentVersionFile', 
               'New-MgDeviceAppMgtMobileAppAsMicrosoftStoreGraphFPreBusinessAppAssignment', 
               'New-MgDeviceAppMgtMobileAppAssignment', 
               'New-MgDeviceAppMgtMobileAppAsWin32LobAppAssignment', 
               'New-MgDeviceAppMgtMobileAppAsWin32LobAppContentVersion', 
               'New-MgDeviceAppMgtMobileAppAsWin32LobAppContentVersionContainedApp', 
               'New-MgDeviceAppMgtMobileAppAsWin32LobAppContentVersionFile', 
               'New-MgDeviceAppMgtMobileAppAsWindowAppXAssignment', 
               'New-MgDeviceAppMgtMobileAppAsWindowAppXContentVersion', 
               'New-MgDeviceAppMgtMobileAppAsWindowAppXContentVersionContainedApp', 
               'New-MgDeviceAppMgtMobileAppAsWindowAppXContentVersionFile', 
               'New-MgDeviceAppMgtMobileAppAsWindowMobileMsiAssignment', 
               'New-MgDeviceAppMgtMobileAppAsWindowMobileMsiContentVersion', 
               'New-MgDeviceAppMgtMobileAppAsWindowMobileMsiContentVersionContainedApp', 
               'New-MgDeviceAppMgtMobileAppAsWindowMobileMsiContentVersionFile', 
               'New-MgDeviceAppMgtMobileAppAsWindowUniversalAppXAssignment', 
               'New-MgDeviceAppMgtMobileAppAsWindowUniversalAppXCommittedContainedApp', 
               'New-MgDeviceAppMgtMobileAppAsWindowUniversalAppXContentVersion', 
               'New-MgDeviceAppMgtMobileAppAsWindowUniversalAppXContentVersionContainedApp', 
               'New-MgDeviceAppMgtMobileAppAsWindowUniversalAppXContentVersionFile', 
               'New-MgDeviceAppMgtMobileAppAsWindowWebAppAssignment', 
               'New-MgDeviceAppMgtMobileAppCategory', 
               'New-MgDeviceAppMgtMobileAppConfiguration', 
               'New-MgDeviceAppMgtMobileAppConfigurationAssignment', 
               'New-MgDeviceAppMgtMobileAppConfigurationDeviceStatus', 
               'New-MgDeviceAppMgtMobileAppConfigurationUserStatus', 
               'New-MgDeviceAppMgtTargetedManagedAppConfiguration', 
               'New-MgDeviceAppMgtTargetedManagedAppConfigurationApp', 
               'New-MgDeviceAppMgtTargetedManagedAppConfigurationAssignment', 
               'New-MgDeviceAppMgtVppToken', 
               'New-MgDeviceAppMgtWindowInformationProtectionPolicy', 
               'New-MgDeviceAppMgtWindowInformationProtectionPolicyAssignment', 
               'New-MgDeviceAppMgtWindowInformationProtectionPolicyExemptAppLockerFile', 
               'New-MgDeviceAppMgtWindowInformationProtectionPolicyProtectedAppLockerFile', 
               'Remove-MgDeviceAppMgtAndroidManagedAppProtection', 
               'Remove-MgDeviceAppMgtAndroidManagedAppProtectionApp', 
               'Remove-MgDeviceAppMgtAndroidManagedAppProtectionAssignment', 
               'Remove-MgDeviceAppMgtAndroidManagedAppProtectionDeploymentSummary', 
               'Remove-MgDeviceAppMgtDefaultManagedAppProtection', 
               'Remove-MgDeviceAppMgtDefaultManagedAppProtectionApp', 
               'Remove-MgDeviceAppMgtDefaultManagedAppProtectionDeploymentSummary', 
               'Remove-MgDeviceAppMgtiOSManagedAppProtection', 
               'Remove-MgDeviceAppMgtiOSManagedAppProtectionApp', 
               'Remove-MgDeviceAppMgtiOSManagedAppProtectionAssignment', 
               'Remove-MgDeviceAppMgtiOSManagedAppProtectionDeploymentSummary', 
               'Remove-MgDeviceAppMgtManagedAppPolicy', 
               'Remove-MgDeviceAppMgtManagedAppRegistration', 
               'Remove-MgDeviceAppMgtManagedAppRegistrationAppliedPolicy', 
               'Remove-MgDeviceAppMgtManagedAppRegistrationIntendedPolicy', 
               'Remove-MgDeviceAppMgtManagedAppRegistrationOperation', 
               'Remove-MgDeviceAppMgtManagedAppStatus', 
               'Remove-MgDeviceAppMgtManagedEBook', 
               'Remove-MgDeviceAppMgtManagedEBookAssignment', 
               'Remove-MgDeviceAppMgtManagedEBookDeviceState', 
               'Remove-MgDeviceAppMgtManagedEBookInstallSummary', 
               'Remove-MgDeviceAppMgtManagedEBookUserStateSummary', 
               'Remove-MgDeviceAppMgtManagedEBookUserStateSummaryDeviceState', 
               'Remove-MgDeviceAppMgtMdmWindowInformationProtectionPolicy', 
               'Remove-MgDeviceAppMgtMdmWindowInformationProtectionPolicyAssignment', 
               'Remove-MgDeviceAppMgtMdmWindowInformationProtectionPolicyExemptAppLockerFile', 
               'Remove-MgDeviceAppMgtMdmWindowInformationProtectionPolicyProtectedAppLockerFile', 
               'Remove-MgDeviceAppMgtMobileApp', 
               'Remove-MgDeviceAppMgtMobileAppAsAndroidLobAppAssignment', 
               'Remove-MgDeviceAppMgtMobileAppAsAndroidLobAppContentVersion', 
               'Remove-MgDeviceAppMgtMobileAppAsAndroidLobAppContentVersionContainedApp', 
               'Remove-MgDeviceAppMgtMobileAppAsAndroidLobAppContentVersionFile', 
               'Remove-MgDeviceAppMgtMobileAppAsAndroidStoreAppAssignment', 
               'Remove-MgDeviceAppMgtMobileAppAsiOSLobAppAssignment', 
               'Remove-MgDeviceAppMgtMobileAppAsiOSLobAppContentVersion', 
               'Remove-MgDeviceAppMgtMobileAppAsiOSLobAppContentVersionContainedApp', 
               'Remove-MgDeviceAppMgtMobileAppAsiOSLobAppContentVersionFile', 
               'Remove-MgDeviceAppMgtMobileAppAsIoStoreAppAssignment', 
               'Remove-MgDeviceAppMgtMobileAppAsIoVppAppAssignment', 
               'Remove-MgDeviceAppMgtMobileAppAsMacOSDmgAppAssignment', 
               'Remove-MgDeviceAppMgtMobileAppAsMacOSDmgAppContentVersion', 
               'Remove-MgDeviceAppMgtMobileAppAsMacOSDmgAppContentVersionContainedApp', 
               'Remove-MgDeviceAppMgtMobileAppAsMacOSDmgAppContentVersionFile', 
               'Remove-MgDeviceAppMgtMobileAppAsMacOSLobAppAssignment', 
               'Remove-MgDeviceAppMgtMobileAppAsMacOSLobAppContentVersion', 
               'Remove-MgDeviceAppMgtMobileAppAsMacOSLobAppContentVersionContainedApp', 
               'Remove-MgDeviceAppMgtMobileAppAsMacOSLobAppContentVersionFile', 
               'Remove-MgDeviceAppMgtMobileAppAsManagedAndroidLobAppAssignment', 
               'Remove-MgDeviceAppMgtMobileAppAsManagedAndroidLobAppContentVersion', 
               'Remove-MgDeviceAppMgtMobileAppAsManagedAndroidLobAppContentVersionContainedApp', 
               'Remove-MgDeviceAppMgtMobileAppAsManagedAndroidLobAppContentVersionFile', 
               'Remove-MgDeviceAppMgtMobileAppAsManagediOSLobAppAssignment', 
               'Remove-MgDeviceAppMgtMobileAppAsManagediOSLobAppContentVersion', 
               'Remove-MgDeviceAppMgtMobileAppAsManagediOSLobAppContentVersionContainedApp', 
               'Remove-MgDeviceAppMgtMobileAppAsManagediOSLobAppContentVersionFile', 
               'Remove-MgDeviceAppMgtMobileAppAsManagedMobileLobAppAssignment', 
               'Remove-MgDeviceAppMgtMobileAppAsManagedMobileLobAppContentVersion', 
               'Remove-MgDeviceAppMgtMobileAppAsManagedMobileLobAppContentVersionContainedApp', 
               'Remove-MgDeviceAppMgtMobileAppAsManagedMobileLobAppContentVersionFile', 
               'Remove-MgDeviceAppMgtMobileAppAsMicrosoftStoreGraphFPreBusinessAppAssignment', 
               'Remove-MgDeviceAppMgtMobileAppAssignment', 
               'Remove-MgDeviceAppMgtMobileAppAsWin32LobAppAssignment', 
               'Remove-MgDeviceAppMgtMobileAppAsWin32LobAppContentVersion', 
               'Remove-MgDeviceAppMgtMobileAppAsWin32LobAppContentVersionContainedApp', 
               'Remove-MgDeviceAppMgtMobileAppAsWin32LobAppContentVersionFile', 
               'Remove-MgDeviceAppMgtMobileAppAsWindowAppXAssignment', 
               'Remove-MgDeviceAppMgtMobileAppAsWindowAppXContentVersion', 
               'Remove-MgDeviceAppMgtMobileAppAsWindowAppXContentVersionContainedApp', 
               'Remove-MgDeviceAppMgtMobileAppAsWindowAppXContentVersionFile', 
               'Remove-MgDeviceAppMgtMobileAppAsWindowMobileMsiAssignment', 
               'Remove-MgDeviceAppMgtMobileAppAsWindowMobileMsiContentVersion', 
               'Remove-MgDeviceAppMgtMobileAppAsWindowMobileMsiContentVersionContainedApp', 
               'Remove-MgDeviceAppMgtMobileAppAsWindowMobileMsiContentVersionFile', 
               'Remove-MgDeviceAppMgtMobileAppAsWindowUniversalAppXAssignment', 
               'Remove-MgDeviceAppMgtMobileAppAsWindowUniversalAppXCommittedContainedApp', 
               'Remove-MgDeviceAppMgtMobileAppAsWindowUniversalAppXContentVersion', 
               'Remove-MgDeviceAppMgtMobileAppAsWindowUniversalAppXContentVersionContainedApp', 
               'Remove-MgDeviceAppMgtMobileAppAsWindowUniversalAppXContentVersionFile', 
               'Remove-MgDeviceAppMgtMobileAppAsWindowWebAppAssignment', 
               'Remove-MgDeviceAppMgtMobileAppCategory', 
               'Remove-MgDeviceAppMgtMobileAppConfiguration', 
               'Remove-MgDeviceAppMgtMobileAppConfigurationAssignment', 
               'Remove-MgDeviceAppMgtMobileAppConfigurationDeviceStatus', 
               'Remove-MgDeviceAppMgtMobileAppConfigurationDeviceStatusSummary', 
               'Remove-MgDeviceAppMgtMobileAppConfigurationUserStatus', 
               'Remove-MgDeviceAppMgtMobileAppConfigurationUserStatusSummary', 
               'Remove-MgDeviceAppMgtTargetedManagedAppConfiguration', 
               'Remove-MgDeviceAppMgtTargetedManagedAppConfigurationApp', 
               'Remove-MgDeviceAppMgtTargetedManagedAppConfigurationAssignment', 
               'Remove-MgDeviceAppMgtTargetedManagedAppConfigurationDeploymentSummary', 
               'Remove-MgDeviceAppMgtVppToken', 
               'Remove-MgDeviceAppMgtWindowInformationProtectionPolicy', 
               'Remove-MgDeviceAppMgtWindowInformationProtectionPolicyAssignment', 
               'Remove-MgDeviceAppMgtWindowInformationProtectionPolicyExemptAppLockerFile', 
               'Remove-MgDeviceAppMgtWindowInformationProtectionPolicyProtectedAppLockerFile', 
               'Set-MgDeviceAppMgtManagedEBook', 'Set-MgDeviceAppMgtMobileApp', 
               'Set-MgDeviceAppMgtMobileAppConfiguration', 
               'Set-MgDeviceAppMgtTargetedManagedAppConfiguration', 
               'Sync-MgDeviceAppMgtMicrosoftStoreGraphFPreBusinessApp', 
               'Sync-MgDeviceAppMgtVppTokenLicense', 'Update-MgDeviceAppMgt', 
               'Update-MgDeviceAppMgtAndroidManagedAppProtection', 
               'Update-MgDeviceAppMgtAndroidManagedAppProtectionApp', 
               'Update-MgDeviceAppMgtAndroidManagedAppProtectionAssignment', 
               'Update-MgDeviceAppMgtAndroidManagedAppProtectionDeploymentSummary', 
               'Update-MgDeviceAppMgtDefaultManagedAppProtection', 
               'Update-MgDeviceAppMgtDefaultManagedAppProtectionApp', 
               'Update-MgDeviceAppMgtDefaultManagedAppProtectionDeploymentSummary', 
               'Update-MgDeviceAppMgtiOSManagedAppProtection', 
               'Update-MgDeviceAppMgtiOSManagedAppProtectionApp', 
               'Update-MgDeviceAppMgtiOSManagedAppProtectionAssignment', 
               'Update-MgDeviceAppMgtiOSManagedAppProtectionDeploymentSummary', 
               'Update-MgDeviceAppMgtManagedAppPolicy', 
               'Update-MgDeviceAppMgtManagedAppRegistration', 
               'Update-MgDeviceAppMgtManagedAppRegistrationAppliedPolicy', 
               'Update-MgDeviceAppMgtManagedAppRegistrationIntendedPolicy', 
               'Update-MgDeviceAppMgtManagedAppRegistrationOperation', 
               'Update-MgDeviceAppMgtManagedAppStatus', 
               'Update-MgDeviceAppMgtManagedEBook', 
               'Update-MgDeviceAppMgtManagedEBookAssignment', 
               'Update-MgDeviceAppMgtManagedEBookDeviceState', 
               'Update-MgDeviceAppMgtManagedEBookInstallSummary', 
               'Update-MgDeviceAppMgtManagedEBookUserStateSummary', 
               'Update-MgDeviceAppMgtManagedEBookUserStateSummaryDeviceState', 
               'Update-MgDeviceAppMgtMdmWindowInformationProtectionPolicy', 
               'Update-MgDeviceAppMgtMdmWindowInformationProtectionPolicyAssignment', 
               'Update-MgDeviceAppMgtMdmWindowInformationProtectionPolicyExemptAppLockerFile', 
               'Update-MgDeviceAppMgtMdmWindowInformationProtectionPolicyProtectedAppLockerFile', 
               'Update-MgDeviceAppMgtMobileApp', 
               'Update-MgDeviceAppMgtMobileAppAsAndroidLobAppAssignment', 
               'Update-MgDeviceAppMgtMobileAppAsAndroidLobAppContentVersion', 
               'Update-MgDeviceAppMgtMobileAppAsAndroidLobAppContentVersionContainedApp', 
               'Update-MgDeviceAppMgtMobileAppAsAndroidLobAppContentVersionFile', 
               'Update-MgDeviceAppMgtMobileAppAsAndroidStoreAppAssignment', 
               'Update-MgDeviceAppMgtMobileAppAsiOSLobAppAssignment', 
               'Update-MgDeviceAppMgtMobileAppAsiOSLobAppContentVersion', 
               'Update-MgDeviceAppMgtMobileAppAsiOSLobAppContentVersionContainedApp', 
               'Update-MgDeviceAppMgtMobileAppAsiOSLobAppContentVersionFile', 
               'Update-MgDeviceAppMgtMobileAppAsIoStoreAppAssignment', 
               'Update-MgDeviceAppMgtMobileAppAsIoVppAppAssignment', 
               'Update-MgDeviceAppMgtMobileAppAsMacOSDmgAppAssignment', 
               'Update-MgDeviceAppMgtMobileAppAsMacOSDmgAppContentVersion', 
               'Update-MgDeviceAppMgtMobileAppAsMacOSDmgAppContentVersionContainedApp', 
               'Update-MgDeviceAppMgtMobileAppAsMacOSDmgAppContentVersionFile', 
               'Update-MgDeviceAppMgtMobileAppAsMacOSLobAppAssignment', 
               'Update-MgDeviceAppMgtMobileAppAsMacOSLobAppContentVersion', 
               'Update-MgDeviceAppMgtMobileAppAsMacOSLobAppContentVersionContainedApp', 
               'Update-MgDeviceAppMgtMobileAppAsMacOSLobAppContentVersionFile', 
               'Update-MgDeviceAppMgtMobileAppAsManagedAndroidLobAppAssignment', 
               'Update-MgDeviceAppMgtMobileAppAsManagedAndroidLobAppContentVersion', 
               'Update-MgDeviceAppMgtMobileAppAsManagedAndroidLobAppContentVersionContainedApp', 
               'Update-MgDeviceAppMgtMobileAppAsManagedAndroidLobAppContentVersionFile', 
               'Update-MgDeviceAppMgtMobileAppAsManagediOSLobAppAssignment', 
               'Update-MgDeviceAppMgtMobileAppAsManagediOSLobAppContentVersion', 
               'Update-MgDeviceAppMgtMobileAppAsManagediOSLobAppContentVersionContainedApp', 
               'Update-MgDeviceAppMgtMobileAppAsManagediOSLobAppContentVersionFile', 
               'Update-MgDeviceAppMgtMobileAppAsManagedMobileLobAppAssignment', 
               'Update-MgDeviceAppMgtMobileAppAsManagedMobileLobAppContentVersion', 
               'Update-MgDeviceAppMgtMobileAppAsManagedMobileLobAppContentVersionContainedApp', 
               'Update-MgDeviceAppMgtMobileAppAsManagedMobileLobAppContentVersionFile', 
               'Update-MgDeviceAppMgtMobileAppAsMicrosoftStoreGraphFPreBusinessAppAssignment', 
               'Update-MgDeviceAppMgtMobileAppAssignment', 
               'Update-MgDeviceAppMgtMobileAppAsWin32LobAppAssignment', 
               'Update-MgDeviceAppMgtMobileAppAsWin32LobAppContentVersion', 
               'Update-MgDeviceAppMgtMobileAppAsWin32LobAppContentVersionContainedApp', 
               'Update-MgDeviceAppMgtMobileAppAsWin32LobAppContentVersionFile', 
               'Update-MgDeviceAppMgtMobileAppAsWindowAppXAssignment', 
               'Update-MgDeviceAppMgtMobileAppAsWindowAppXContentVersion', 
               'Update-MgDeviceAppMgtMobileAppAsWindowAppXContentVersionContainedApp', 
               'Update-MgDeviceAppMgtMobileAppAsWindowAppXContentVersionFile', 
               'Update-MgDeviceAppMgtMobileAppAsWindowMobileMsiAssignment', 
               'Update-MgDeviceAppMgtMobileAppAsWindowMobileMsiContentVersion', 
               'Update-MgDeviceAppMgtMobileAppAsWindowMobileMsiContentVersionContainedApp', 
               'Update-MgDeviceAppMgtMobileAppAsWindowMobileMsiContentVersionFile', 
               'Update-MgDeviceAppMgtMobileAppAsWindowUniversalAppXAssignment', 
               'Update-MgDeviceAppMgtMobileAppAsWindowUniversalAppXCommittedContainedApp', 
               'Update-MgDeviceAppMgtMobileAppAsWindowUniversalAppXContentVersion', 
               'Update-MgDeviceAppMgtMobileAppAsWindowUniversalAppXContentVersionContainedApp', 
               'Update-MgDeviceAppMgtMobileAppAsWindowUniversalAppXContentVersionFile', 
               'Update-MgDeviceAppMgtMobileAppAsWindowWebAppAssignment', 
               'Update-MgDeviceAppMgtMobileAppCategory', 
               'Update-MgDeviceAppMgtMobileAppConfiguration', 
               'Update-MgDeviceAppMgtMobileAppConfigurationAssignment', 
               'Update-MgDeviceAppMgtMobileAppConfigurationDeviceStatus', 
               'Update-MgDeviceAppMgtMobileAppConfigurationDeviceStatusSummary', 
               'Update-MgDeviceAppMgtMobileAppConfigurationUserStatus', 
               'Update-MgDeviceAppMgtMobileAppConfigurationUserStatusSummary', 
               'Update-MgDeviceAppMgtTargetedManagedAppConfiguration', 
               'Update-MgDeviceAppMgtTargetedManagedAppConfigurationApp', 
               'Update-MgDeviceAppMgtTargetedManagedAppConfigurationAssignment', 
               'Update-MgDeviceAppMgtTargetedManagedAppConfigurationDeploymentSummary', 
               'Update-MgDeviceAppMgtVppToken', 
               'Update-MgDeviceAppMgtWindowInformationProtectionPolicy', 
               'Update-MgDeviceAppMgtWindowInformationProtectionPolicyAssignment', 
               'Update-MgDeviceAppMgtWindowInformationProtectionPolicyExemptAppLockerFile', 
               'Update-MgDeviceAppMgtWindowInformationProtectionPolicyProtectedAppLockerFile'

# DSC resources to export from this module
# DscResourcesToExport = @()

# List of all modules packaged with this module
# ModuleList = @()

# List of all files packaged with this module
# FileList = @()

# Private data to pass to the module specified in RootModule/ModuleToProcess. This may also contain a PSData hashtable with additional module metadata used by PowerShell.
PrivateData = @{

    PSData = @{

        # Tags applied to this module. These help with module discovery in online galleries.
        Tags = 'Microsoft','Office365','Graph','PowerShell','PSModule','PSIncludes_Cmdlet'

        # A URL to the license for this module.
        LicenseUri = 'https://aka.ms/devservicesagreement'

        # A URL to the main website for this project.
        ProjectUri = 'https://github.com/microsoftgraph/msgraph-sdk-powershell'

        # A URL to an icon representing this module.
        IconUri = 'https://raw.githubusercontent.com/microsoftgraph/msgraph-sdk-powershell/dev/docs/images/graph_color256.png'

        # ReleaseNotes of this module
        ReleaseNotes = 'See https://aka.ms/GraphPowerShell-Release.'

        # Prerelease string of this module
        # Prerelease = ''

        # Flag to indicate whether the module requires explicit user acceptance for install/update/save
        # RequireLicenseAcceptance = $false

        # External dependent modules of this module
        # ExternalModuleDependencies = @()

    } # End of PSData hashtable

 } # End of PrivateData hashtable

# HelpInfo URI of this module
# HelpInfoURI = ''

# Default prefix for commands exported from this module. Override the default prefix using Import-Module -Prefix.
# DefaultCommandPrefix = ''

}


# SIG # Begin signature block
# MIIoKgYJKoZIhvcNAQcCoIIoGzCCKBcCAQExDzANBglghkgBZQMEAgEFADB5Bgor
# BgEEAYI3AgEEoGswaTA0BgorBgEEAYI3AgEeMCYCAwEAAAQQH8w7YFlLCE63JNLG
# KX7zUQIBAAIBAAIBAAIBAAIBADAxMA0GCWCGSAFlAwQCAQUABCCI9iYnVg4wg8cz
# 03qNKwjHal1QtPeb3isN5/cRCARSiKCCDXYwggX0MIID3KADAgECAhMzAAAEBGx0
# Bv9XKydyAAAAAAQEMA0GCSqGSIb3DQEBCwUAMH4xCzAJBgNVBAYTAlVTMRMwEQYD
# VQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNy
# b3NvZnQgQ29ycG9yYXRpb24xKDAmBgNVBAMTH01pY3Jvc29mdCBDb2RlIFNpZ25p
# bmcgUENBIDIwMTEwHhcNMjQwOTEyMjAxMTE0WhcNMjUwOTExMjAxMTE0WjB0MQsw
# CQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9u
# ZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMR4wHAYDVQQDExVNaWNy
# b3NvZnQgQ29ycG9yYXRpb24wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIB
# AQC0KDfaY50MDqsEGdlIzDHBd6CqIMRQWW9Af1LHDDTuFjfDsvna0nEuDSYJmNyz
# NB10jpbg0lhvkT1AzfX2TLITSXwS8D+mBzGCWMM/wTpciWBV/pbjSazbzoKvRrNo
# DV/u9omOM2Eawyo5JJJdNkM2d8qzkQ0bRuRd4HarmGunSouyb9NY7egWN5E5lUc3
# a2AROzAdHdYpObpCOdeAY2P5XqtJkk79aROpzw16wCjdSn8qMzCBzR7rvH2WVkvF
# HLIxZQET1yhPb6lRmpgBQNnzidHV2Ocxjc8wNiIDzgbDkmlx54QPfw7RwQi8p1fy
# 4byhBrTjv568x8NGv3gwb0RbAgMBAAGjggFzMIIBbzAfBgNVHSUEGDAWBgorBgEE
# AYI3TAgBBggrBgEFBQcDAzAdBgNVHQ4EFgQU8huhNbETDU+ZWllL4DNMPCijEU4w
# RQYDVR0RBD4wPKQ6MDgxHjAcBgNVBAsTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEW
# MBQGA1UEBRMNMjMwMDEyKzUwMjkyMzAfBgNVHSMEGDAWgBRIbmTlUAXTgqoXNzci
# tW2oynUClTBUBgNVHR8ETTBLMEmgR6BFhkNodHRwOi8vd3d3Lm1pY3Jvc29mdC5j
# b20vcGtpb3BzL2NybC9NaWNDb2RTaWdQQ0EyMDExXzIwMTEtMDctMDguY3JsMGEG
# CCsGAQUFBwEBBFUwUzBRBggrBgEFBQcwAoZFaHR0cDovL3d3dy5taWNyb3NvZnQu
# Y29tL3BraW9wcy9jZXJ0cy9NaWNDb2RTaWdQQ0EyMDExXzIwMTEtMDctMDguY3J0
# MAwGA1UdEwEB/wQCMAAwDQYJKoZIhvcNAQELBQADggIBAIjmD9IpQVvfB1QehvpC
# Ge7QeTQkKQ7j3bmDMjwSqFL4ri6ae9IFTdpywn5smmtSIyKYDn3/nHtaEn0X1NBj
# L5oP0BjAy1sqxD+uy35B+V8wv5GrxhMDJP8l2QjLtH/UglSTIhLqyt8bUAqVfyfp
# h4COMRvwwjTvChtCnUXXACuCXYHWalOoc0OU2oGN+mPJIJJxaNQc1sjBsMbGIWv3
# cmgSHkCEmrMv7yaidpePt6V+yPMik+eXw3IfZ5eNOiNgL1rZzgSJfTnvUqiaEQ0X
# dG1HbkDv9fv6CTq6m4Ty3IzLiwGSXYxRIXTxT4TYs5VxHy2uFjFXWVSL0J2ARTYL
# E4Oyl1wXDF1PX4bxg1yDMfKPHcE1Ijic5lx1KdK1SkaEJdto4hd++05J9Bf9TAmi
# u6EK6C9Oe5vRadroJCK26uCUI4zIjL/qG7mswW+qT0CW0gnR9JHkXCWNbo8ccMk1
# sJatmRoSAifbgzaYbUz8+lv+IXy5GFuAmLnNbGjacB3IMGpa+lbFgih57/fIhamq
# 5VhxgaEmn/UjWyr+cPiAFWuTVIpfsOjbEAww75wURNM1Imp9NJKye1O24EspEHmb
# DmqCUcq7NqkOKIG4PVm3hDDED/WQpzJDkvu4FrIbvyTGVU01vKsg4UfcdiZ0fQ+/
# V0hf8yrtq9CkB8iIuk5bBxuPMIIHejCCBWKgAwIBAgIKYQ6Q0gAAAAAAAzANBgkq
# hkiG9w0BAQsFADCBiDELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldhc2hpbmd0b24x
# EDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlv
# bjEyMDAGA1UEAxMpTWljcm9zb2Z0IFJvb3QgQ2VydGlmaWNhdGUgQXV0aG9yaXR5
# IDIwMTEwHhcNMTEwNzA4MjA1OTA5WhcNMjYwNzA4MjEwOTA5WjB+MQswCQYDVQQG
# EwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwG
# A1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSgwJgYDVQQDEx9NaWNyb3NvZnQg
# Q29kZSBTaWduaW5nIFBDQSAyMDExMIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIIC
# CgKCAgEAq/D6chAcLq3YbqqCEE00uvK2WCGfQhsqa+laUKq4BjgaBEm6f8MMHt03
# a8YS2AvwOMKZBrDIOdUBFDFC04kNeWSHfpRgJGyvnkmc6Whe0t+bU7IKLMOv2akr
# rnoJr9eWWcpgGgXpZnboMlImEi/nqwhQz7NEt13YxC4Ddato88tt8zpcoRb0Rrrg
# OGSsbmQ1eKagYw8t00CT+OPeBw3VXHmlSSnnDb6gE3e+lD3v++MrWhAfTVYoonpy
# 4BI6t0le2O3tQ5GD2Xuye4Yb2T6xjF3oiU+EGvKhL1nkkDstrjNYxbc+/jLTswM9
# sbKvkjh+0p2ALPVOVpEhNSXDOW5kf1O6nA+tGSOEy/S6A4aN91/w0FK/jJSHvMAh
# dCVfGCi2zCcoOCWYOUo2z3yxkq4cI6epZuxhH2rhKEmdX4jiJV3TIUs+UsS1Vz8k
# A/DRelsv1SPjcF0PUUZ3s/gA4bysAoJf28AVs70b1FVL5zmhD+kjSbwYuER8ReTB
# w3J64HLnJN+/RpnF78IcV9uDjexNSTCnq47f7Fufr/zdsGbiwZeBe+3W7UvnSSmn
# Eyimp31ngOaKYnhfsi+E11ecXL93KCjx7W3DKI8sj0A3T8HhhUSJxAlMxdSlQy90
# lfdu+HggWCwTXWCVmj5PM4TasIgX3p5O9JawvEagbJjS4NaIjAsCAwEAAaOCAe0w
# ggHpMBAGCSsGAQQBgjcVAQQDAgEAMB0GA1UdDgQWBBRIbmTlUAXTgqoXNzcitW2o
# ynUClTAZBgkrBgEEAYI3FAIEDB4KAFMAdQBiAEMAQTALBgNVHQ8EBAMCAYYwDwYD
# VR0TAQH/BAUwAwEB/zAfBgNVHSMEGDAWgBRyLToCMZBDuRQFTuHqp8cx0SOJNDBa
# BgNVHR8EUzBRME+gTaBLhklodHRwOi8vY3JsLm1pY3Jvc29mdC5jb20vcGtpL2Ny
# bC9wcm9kdWN0cy9NaWNSb29DZXJBdXQyMDExXzIwMTFfMDNfMjIuY3JsMF4GCCsG
# AQUFBwEBBFIwUDBOBggrBgEFBQcwAoZCaHR0cDovL3d3dy5taWNyb3NvZnQuY29t
# L3BraS9jZXJ0cy9NaWNSb29DZXJBdXQyMDExXzIwMTFfMDNfMjIuY3J0MIGfBgNV
# HSAEgZcwgZQwgZEGCSsGAQQBgjcuAzCBgzA/BggrBgEFBQcCARYzaHR0cDovL3d3
# dy5taWNyb3NvZnQuY29tL3BraW9wcy9kb2NzL3ByaW1hcnljcHMuaHRtMEAGCCsG
# AQUFBwICMDQeMiAdAEwAZQBnAGEAbABfAHAAbwBsAGkAYwB5AF8AcwB0AGEAdABl
# AG0AZQBuAHQALiAdMA0GCSqGSIb3DQEBCwUAA4ICAQBn8oalmOBUeRou09h0ZyKb
# C5YR4WOSmUKWfdJ5DJDBZV8uLD74w3LRbYP+vj/oCso7v0epo/Np22O/IjWll11l
# hJB9i0ZQVdgMknzSGksc8zxCi1LQsP1r4z4HLimb5j0bpdS1HXeUOeLpZMlEPXh6
# I/MTfaaQdION9MsmAkYqwooQu6SpBQyb7Wj6aC6VoCo/KmtYSWMfCWluWpiW5IP0
# wI/zRive/DvQvTXvbiWu5a8n7dDd8w6vmSiXmE0OPQvyCInWH8MyGOLwxS3OW560
# STkKxgrCxq2u5bLZ2xWIUUVYODJxJxp/sfQn+N4sOiBpmLJZiWhub6e3dMNABQam
# ASooPoI/E01mC8CzTfXhj38cbxV9Rad25UAqZaPDXVJihsMdYzaXht/a8/jyFqGa
# J+HNpZfQ7l1jQeNbB5yHPgZ3BtEGsXUfFL5hYbXw3MYbBL7fQccOKO7eZS/sl/ah
# XJbYANahRr1Z85elCUtIEJmAH9AAKcWxm6U/RXceNcbSoqKfenoi+kiVH6v7RyOA
# 9Z74v2u3S5fi63V4GuzqN5l5GEv/1rMjaHXmr/r8i+sLgOppO6/8MO0ETI7f33Vt
# Y5E90Z1WTk+/gFcioXgRMiF670EKsT/7qMykXcGhiJtXcVZOSEXAQsmbdlsKgEhr
# /Xmfwb1tbWrJUnMTDXpQzTGCGgowghoGAgEBMIGVMH4xCzAJBgNVBAYTAlVTMRMw
# EQYDVQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVN
# aWNyb3NvZnQgQ29ycG9yYXRpb24xKDAmBgNVBAMTH01pY3Jvc29mdCBDb2RlIFNp
# Z25pbmcgUENBIDIwMTECEzMAAAQEbHQG/1crJ3IAAAAABAQwDQYJYIZIAWUDBAIB
# BQCgga4wGQYJKoZIhvcNAQkDMQwGCisGAQQBgjcCAQQwHAYKKwYBBAGCNwIBCzEO
# MAwGCisGAQQBgjcCARUwLwYJKoZIhvcNAQkEMSIEIAj1rLVGfq0sesJ1AcipD4Jj
# oAVWH2mm5Jg/eEHgIQDKMEIGCisGAQQBgjcCAQwxNDAyoBSAEgBNAGkAYwByAG8A
# cwBvAGYAdKEagBhodHRwOi8vd3d3Lm1pY3Jvc29mdC5jb20wDQYJKoZIhvcNAQEB
# BQAEggEAUWPt0D1vQTvMO2Lr2u7fGRUz64sC094SliKc6d5romSIeuc60RxOekA4
# rDIRZGxwpJUKLeeOCM4GVO+9BoNQmnSs1bNWiOUY4lhbigaTH552bgPF/YoTiOFG
# cOREakST9vmPeg1TgYfuWX1BUDPeEvYPukZf1qaGBSKhWNr3RDrDk2Ti1Tq04Qa3
# 8+199Ahr2FRrlC5n0N8zIj9LPU+HVfwtL5G+RnjMApiRIpiK2F+/k8hseLb0Oady
# T7IBGHHN+G6QdXo0kudd44b+uYLhItKPBaw4C1Jsu7ghxEETHI2TmSLQyg/TAnMw
# DhBNei+4A7rFNoRcLQfarniW3MEpwqGCF5QwgheQBgorBgEEAYI3AwMBMYIXgDCC
# F3wGCSqGSIb3DQEHAqCCF20wghdpAgEDMQ8wDQYJYIZIAWUDBAIBBQAwggFSBgsq
# hkiG9w0BCRABBKCCAUEEggE9MIIBOQIBAQYKKwYBBAGEWQoDATAxMA0GCWCGSAFl
# AwQCAQUABCAqfY9/O/HW4IOBaP72mTw53CrZWjjilXLVQJ0Q/7QuPgIGaErf2xFd
# GBMyMDI1MDcwOTExMDcyNS43ODFaMASAAgH0oIHRpIHOMIHLMQswCQYDVQQGEwJV
# UzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwGA1UE
# ChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSUwIwYDVQQLExxNaWNyb3NvZnQgQW1l
# cmljYSBPcGVyYXRpb25zMScwJQYDVQQLEx5uU2hpZWxkIFRTUyBFU046QTAwMC0w
# NUUwLUQ5NDcxJTAjBgNVBAMTHE1pY3Jvc29mdCBUaW1lLVN0YW1wIFNlcnZpY2Wg
# ghHqMIIHIDCCBQigAwIBAgITMwAAAgh4nVhdksfZUgABAAACCDANBgkqhkiG9w0B
# AQsFADB8MQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UE
# BxMHUmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSYwJAYD
# VQQDEx1NaWNyb3NvZnQgVGltZS1TdGFtcCBQQ0EgMjAxMDAeFw0yNTAxMzAxOTQy
# NTNaFw0yNjA0MjIxOTQyNTNaMIHLMQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2Fz
# aGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENv
# cnBvcmF0aW9uMSUwIwYDVQQLExxNaWNyb3NvZnQgQW1lcmljYSBPcGVyYXRpb25z
# MScwJQYDVQQLEx5uU2hpZWxkIFRTUyBFU046QTAwMC0wNUUwLUQ5NDcxJTAjBgNV
# BAMTHE1pY3Jvc29mdCBUaW1lLVN0YW1wIFNlcnZpY2UwggIiMA0GCSqGSIb3DQEB
# AQUAA4ICDwAwggIKAoICAQC1y3AI5lIz3Ip1nK5BMUUbGRsjSnCz/VGs33zvY0Ne
# shsPgfld3/Z3/3dS8WKBLlDlosmXJOZlFSiNXUd6DTJxA9ik/ZbCdWJ78LKjbN3t
# FkX2c6RRpRMpA8sq/oBbRryP3c8Q/gxpJAKHHz8cuSn7ewfCLznNmxqliTk3Q5LH
# qz2PjeYKD/dbKMBT2TAAWAvum4z/HXIJ6tFdGoNV4WURZswCSt6ROwaqQ1oAYGvE
# ndH+DXZq1+bHsgvcPNCdTSIpWobQiJS/UKLiR02KNCqB4I9yajFTSlnMIEMz/Ni5
# 38oGI64phcvNpUe2+qaKWHZ8d4T1KghvRmSSF4YF5DNEJbxaCUwsy7nULmsFnTaO
# jVOoTFWWfWXvBuOKkBcQKWGKvrki976j4x+5ezAP36fq3u6dHRJTLZAu4dEuOooU
# 3+kMZr+RBYWjTHQCKV+yZ1ST0eGkbHXoA2lyyRDlNjBQcoeZIxWCZts/d3+nf1ji
# SLN6f6wdHaUz0ADwOTQ/aEo1IC85eFePvyIKaxFJkGU2Mqa6Xzq3qCq5tokIHtjh
# ogsrEgfDKTeFXTtdhl1IPtLcCfMcWOGGAXosVUU7G948F6W96424f2VHD8L3FoyA
# I9+r4zyIQUmqiESzuQWeWpTTjFYwCmgXaGOuSDV8cNOVQB6IPzPneZhVTjwxbAZl
# aQIDAQABo4IBSTCCAUUwHQYDVR0OBBYEFKMx4vfOqcUTgYOVB9f18/mhegFNMB8G
# A1UdIwQYMBaAFJ+nFV0AXmJdg/Tl0mWnG1M1GelyMF8GA1UdHwRYMFYwVKBSoFCG
# Tmh0dHA6Ly93d3cubWljcm9zb2Z0LmNvbS9wa2lvcHMvY3JsL01pY3Jvc29mdCUy
# MFRpbWUtU3RhbXAlMjBQQ0ElMjAyMDEwKDEpLmNybDBsBggrBgEFBQcBAQRgMF4w
# XAYIKwYBBQUHMAKGUGh0dHA6Ly93d3cubWljcm9zb2Z0LmNvbS9wa2lvcHMvY2Vy
# dHMvTWljcm9zb2Z0JTIwVGltZS1TdGFtcCUyMFBDQSUyMDIwMTAoMSkuY3J0MAwG
# A1UdEwEB/wQCMAAwFgYDVR0lAQH/BAwwCgYIKwYBBQUHAwgwDgYDVR0PAQH/BAQD
# AgeAMA0GCSqGSIb3DQEBCwUAA4ICAQBRszKJKwAfswqdaQPFiaYB/ZNAYWDa040X
# TcQsCaCua5nsG1IslYaSpH7miTLr6eQEqXczZoqeOa/xvDnMGifGNda0CHbQwtpn
# IhsutrKO2jhjEaGwlJgOMql21r7Ik6XnBza0e3hBOu4UBkMl/LEX+AURt7i7+RTN
# sGN0cXPwPSbTFE+9z7WagGbY9pwUo/NxkGJseqGCQ/9K2VMU74bw5e7+8IGUhM2x
# spJPqnSeHPhYmcB0WclOxcVIfj/ZuQvworPbTEEYDVCzSN37c0yChPMY7FJ+HGFB
# NJxwd5lKIr7GYfq8a0gOiC2ljGYlc4rt4cCed1XKg83f0l9aUVimWBYXtfNebhpf
# r6Lc3jD8NgsrDhzt0WgnIdnTZCi7jxjsIBilH99pY5/h6bQcLKK/E6KCP9E1YN78
# fLaOXkXMyO6xLrvQZ+uCSi1hdTufFC7oSB/CU5RbfIVHXG0j1o2n1tne4eCbNfKq
# UPTE31tNbWBR23Yiy0r3kQmHeYE1GLbL4pwknqaip1BRn6WIUMJtgncawEN33f8A
# YGZ4a3NnHopzGVV6neffGVag4Tduy+oy1YF+shChoXdMqfhPWFpHe3uJGT4GJEiN
# s4+28a/wHUuF+aRaR0cN5P7XlOwU1360iUCJtQdvKQaNAwGI29KOwS3QGriR9F2j
# OGPUAlpeEzCCB3EwggVZoAMCAQICEzMAAAAVxedrngKbSZkAAAAAABUwDQYJKoZI
# hvcNAQELBQAwgYgxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNoaW5ndG9uMRAw
# DgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNyb3NvZnQgQ29ycG9yYXRpb24x
# MjAwBgNVBAMTKU1pY3Jvc29mdCBSb290IENlcnRpZmljYXRlIEF1dGhvcml0eSAy
# MDEwMB4XDTIxMDkzMDE4MjIyNVoXDTMwMDkzMDE4MzIyNVowfDELMAkGA1UEBhMC
# VVMxEzARBgNVBAgTCldhc2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNV
# BAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEmMCQGA1UEAxMdTWljcm9zb2Z0IFRp
# bWUtU3RhbXAgUENBIDIwMTAwggIiMA0GCSqGSIb3DQEBAQUAA4ICDwAwggIKAoIC
# AQDk4aZM57RyIQt5osvXJHm9DtWC0/3unAcH0qlsTnXIyjVX9gF/bErg4r25Phdg
# M/9cT8dm95VTcVrifkpa/rg2Z4VGIwy1jRPPdzLAEBjoYH1qUoNEt6aORmsHFPPF
# dvWGUNzBRMhxXFExN6AKOG6N7dcP2CZTfDlhAnrEqv1yaa8dq6z2Nr41JmTamDu6
# GnszrYBbfowQHJ1S/rboYiXcag/PXfT+jlPP1uyFVk3v3byNpOORj7I5LFGc6XBp
# Dco2LXCOMcg1KL3jtIckw+DJj361VI/c+gVVmG1oO5pGve2krnopN6zL64NF50Zu
# yjLVwIYwXE8s4mKyzbnijYjklqwBSru+cakXW2dg3viSkR4dPf0gz3N9QZpGdc3E
# XzTdEonW/aUgfX782Z5F37ZyL9t9X4C626p+Nuw2TPYrbqgSUei/BQOj0XOmTTd0
# lBw0gg/wEPK3Rxjtp+iZfD9M269ewvPV2HM9Q07BMzlMjgK8QmguEOqEUUbi0b1q
# GFphAXPKZ6Je1yh2AuIzGHLXpyDwwvoSCtdjbwzJNmSLW6CmgyFdXzB0kZSU2LlQ
# +QuJYfM2BjUYhEfb3BvR/bLUHMVr9lxSUV0S2yW6r1AFemzFER1y7435UsSFF5PA
# PBXbGjfHCBUYP3irRbb1Hode2o+eFnJpxq57t7c+auIurQIDAQABo4IB3TCCAdkw
# EgYJKwYBBAGCNxUBBAUCAwEAATAjBgkrBgEEAYI3FQIEFgQUKqdS/mTEmr6CkTxG
# NSnPEP8vBO4wHQYDVR0OBBYEFJ+nFV0AXmJdg/Tl0mWnG1M1GelyMFwGA1UdIARV
# MFMwUQYMKwYBBAGCN0yDfQEBMEEwPwYIKwYBBQUHAgEWM2h0dHA6Ly93d3cubWlj
# cm9zb2Z0LmNvbS9wa2lvcHMvRG9jcy9SZXBvc2l0b3J5Lmh0bTATBgNVHSUEDDAK
# BggrBgEFBQcDCDAZBgkrBgEEAYI3FAIEDB4KAFMAdQBiAEMAQTALBgNVHQ8EBAMC
# AYYwDwYDVR0TAQH/BAUwAwEB/zAfBgNVHSMEGDAWgBTV9lbLj+iiXGJo0T2UkFvX
# zpoYxDBWBgNVHR8ETzBNMEugSaBHhkVodHRwOi8vY3JsLm1pY3Jvc29mdC5jb20v
# cGtpL2NybC9wcm9kdWN0cy9NaWNSb29DZXJBdXRfMjAxMC0wNi0yMy5jcmwwWgYI
# KwYBBQUHAQEETjBMMEoGCCsGAQUFBzAChj5odHRwOi8vd3d3Lm1pY3Jvc29mdC5j
# b20vcGtpL2NlcnRzL01pY1Jvb0NlckF1dF8yMDEwLTA2LTIzLmNydDANBgkqhkiG
# 9w0BAQsFAAOCAgEAnVV9/Cqt4SwfZwExJFvhnnJL/Klv6lwUtj5OR2R4sQaTlz0x
# M7U518JxNj/aZGx80HU5bbsPMeTCj/ts0aGUGCLu6WZnOlNN3Zi6th542DYunKmC
# VgADsAW+iehp4LoJ7nvfam++Kctu2D9IdQHZGN5tggz1bSNU5HhTdSRXud2f8449
# xvNo32X2pFaq95W2KFUn0CS9QKC/GbYSEhFdPSfgQJY4rPf5KYnDvBewVIVCs/wM
# nosZiefwC2qBwoEZQhlSdYo2wh3DYXMuLGt7bj8sCXgU6ZGyqVvfSaN0DLzskYDS
# PeZKPmY7T7uG+jIa2Zb0j/aRAfbOxnT99kxybxCrdTDFNLB62FD+CljdQDzHVG2d
# Y3RILLFORy3BFARxv2T5JL5zbcqOCb2zAVdJVGTZc9d/HltEAY5aGZFrDZ+kKNxn
# GSgkujhLmm77IVRrakURR6nxt67I6IleT53S0Ex2tVdUCbFpAUR+fKFhbHP+Crvs
# QWY9af3LwUFJfn6Tvsv4O+S3Fb+0zj6lMVGEvL8CwYKiexcdFYmNcP7ntdAoGokL
# jzbaukz5m/8K6TT4JDVnK+ANuOaMmdbhIurwJ0I9JZTmdHRbatGePu1+oDEzfbzL
# 6Xu/OHBE0ZDxyKs6ijoIYn/ZcGNTTY3ugm2lBRDBcQZqELQdVTNYs6FwZvKhggNN
# MIICNQIBATCB+aGB0aSBzjCByzELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldhc2hp
# bmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBDb3Jw
# b3JhdGlvbjElMCMGA1UECxMcTWljcm9zb2Z0IEFtZXJpY2EgT3BlcmF0aW9uczEn
# MCUGA1UECxMeblNoaWVsZCBUU1MgRVNOOkEwMDAtMDVFMC1EOTQ3MSUwIwYDVQQD
# ExxNaWNyb3NvZnQgVGltZS1TdGFtcCBTZXJ2aWNloiMKAQEwBwYFKw4DAhoDFQCN
# kvu0NKcSjdYKyrhJZcsyXOUTNKCBgzCBgKR+MHwxCzAJBgNVBAYTAlVTMRMwEQYD
# VQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNy
# b3NvZnQgQ29ycG9yYXRpb24xJjAkBgNVBAMTHU1pY3Jvc29mdCBUaW1lLVN0YW1w
# IFBDQSAyMDEwMA0GCSqGSIb3DQEBCwUAAgUA7BhLyDAiGA8yMDI1MDcwOTAyMDA0
# MFoYDzIwMjUwNzEwMDIwMDQwWjB0MDoGCisGAQQBhFkKBAExLDAqMAoCBQDsGEvI
# AgEAMAcCAQACAicsMAcCAQACAhIvMAoCBQDsGZ1IAgEAMDYGCisGAQQBhFkKBAIx
# KDAmMAwGCisGAQQBhFkKAwKgCjAIAgEAAgMHoSChCjAIAgEAAgMBhqAwDQYJKoZI
# hvcNAQELBQADggEBADnXGmdDdtuw5MGwOaf96lcG8iq3aoRiQjZhOpVbGtu9gArj
# t+uv2sUqBZjSzFlwiX1PTSAE3S8UCYzWWLeDPWmFxjIV04oimy5NgmVOGDAv2lvR
# iCOG3Vzm317mUdC82WJTGwIYgkrC7ZU5hkXuBODWKwGCuiZ1m+lrCk7NPUg4WXLv
# HEn6PU82GrpnaKTOqd+mRx3x8LUGua3J5JD6EoCtSKPWRMjfd6pHuD/FfCHx1m6i
# M43oGVpeeCfG443+Rz9RZmfKX+f7N1pIFqBid4bYrv14PcsfJe0t9GZ5NyA5n8R+
# bO0E0hNfYg2EuhrMUU82bexalXDjwJeg/kbOVFsxggQNMIIECQIBATCBkzB8MQsw
# CQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9u
# ZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSYwJAYDVQQDEx1NaWNy
# b3NvZnQgVGltZS1TdGFtcCBQQ0EgMjAxMAITMwAAAgh4nVhdksfZUgABAAACCDAN
# BglghkgBZQMEAgEFAKCCAUowGgYJKoZIhvcNAQkDMQ0GCyqGSIb3DQEJEAEEMC8G
# CSqGSIb3DQEJBDEiBCDrtYwKW9Etur2Ih8LTri6aUUw+oAz0yiH8lOK6Y3SqcTCB
# +gYLKoZIhvcNAQkQAi8xgeowgecwgeQwgb0EII//jm8JHa2W1O9778t9+Ft2Z5Nm
# KqttPk6Q+9RRpmepMIGYMIGApH4wfDELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldh
# c2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBD
# b3Jwb3JhdGlvbjEmMCQGA1UEAxMdTWljcm9zb2Z0IFRpbWUtU3RhbXAgUENBIDIw
# MTACEzMAAAIIeJ1YXZLH2VIAAQAAAggwIgQgPL73O+knfnt2bAvd1AuD2lC7DQdu
# 5l6OGM83s86H4zEwDQYJKoZIhvcNAQELBQAEggIAkOWTCbs5js1fb5QIANhzpiUP
# aFp8jVwv/v+/dvL9FkSpioEQjedxgnmmHfBdza2IvsPdV81XizDVkYcyvBNz9N7u
# Cbfbc+K+GKXc1OhPXW/CP7237B+acfaQdJ9DXcc6RjBHDazTpMj0HM5vZ1uquyeU
# AdsX7+JjlCuwELqewEiVFVTJmV1Rg7bqDnGCE/hUDvgXthli4oGFX/Ja3Bd92Kw0
# XVpbCJLly+747Wz8G7SUNKySHdXTALycqKNJyaNf078QYsEUG6X5Mqi+1QaF/8q2
# ZlizZbIH8KkAiZ/AOM6PosUMjX6erVtKLKflXoXjByDCl+d2jgcmC21145WgsX87
# ztYRoj+I6E/p89oBaZXSofQEa8SFlQpihrCzeZDZnFNtmdhz8VJ2wA2n2Tzocr6i
# POWywR+EDm/nHOvEZxQI3GfjYmZLqBJ3SHTaR+vLVuV+qVCBr54MZxE6ZPciCtWX
# d/PouYD+FDP92brrqeRap5vyg2WvHLere/HsYFWuOWo6c6MnHEG0BOacvYC1kn3m
# Y9x2qd9hm/jCTtUagdy3O0je2/1n5yGHsQQ1wl1GT8RhcqZqrrPaHrSDAyDzZs8D
# fCnQEbfAJacMpTj/lAv2qUXzN8nzajXegHvNIuuU2ELFL2JJ+McZgNgIagGZPh6t
# HV9ao7CTB+a7DyaDCF4=
# SIG # End signature block
