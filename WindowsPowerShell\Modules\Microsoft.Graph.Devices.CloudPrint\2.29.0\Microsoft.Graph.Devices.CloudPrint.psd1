#
# Module manifest for module 'Microsoft.Graph.Devices.CloudPrint'
#
# Generated by: Microsoft Corporation
#
# Generated on: 7/9/2025
#

@{

# Script module or binary module file associated with this manifest.
RootModule = './Microsoft.Graph.Devices.CloudPrint.psm1'

# Version number of this module.
ModuleVersion = '2.29.0'

# Supported PSEditions
CompatiblePSEditions = 'Core', 'Desktop'

# ID used to uniquely identify this module
GUID = '1920dbbe-772a-4def-84b5-f89857768fe5'

# Author of this module
Author = 'Microsoft Corporation'

# Company or vendor of this module
CompanyName = 'Microsoft Corporation'

# Copyright statement for this module
Copyright = 'Microsoft Corporation. All rights reserved.'

# Description of the functionality provided by this module
Description = 'Microsoft Graph PowerShell Cmdlets'

# Minimum version of the PowerShell engine required by this module
PowerShellVersion = '5.1'

# Name of the PowerShell host required by this module
# PowerShellHostName = ''

# Minimum version of the PowerShell host required by this module
# PowerShellHostVersion = ''

# Minimum version of Microsoft .NET Framework required by this module. This prerequisite is valid for the PowerShell Desktop edition only.
DotNetFrameworkVersion = '4.7.2'

# Minimum version of the common language runtime (CLR) required by this module. This prerequisite is valid for the PowerShell Desktop edition only.
# ClrVersion = ''

# Processor architecture (None, X86, Amd64) required by this module
# ProcessorArchitecture = ''

# Modules that must be imported into the global environment prior to importing this module
RequiredModules = @(@{ModuleName = 'Microsoft.Graph.Authentication'; RequiredVersion = '2.29.0'; })

# Assemblies that must be loaded prior to importing this module
RequiredAssemblies = './bin/Microsoft.Graph.Devices.CloudPrint.private.dll'

# Script files (.ps1) that are run in the caller's environment prior to importing this module.
# ScriptsToProcess = @()

# Type files (.ps1xml) to be loaded when importing this module
# TypesToProcess = @()

# Format files (.ps1xml) to be loaded when importing this module
FormatsToProcess = './Microsoft.Graph.Devices.CloudPrint.format.ps1xml'

# Modules to import as nested modules of the module specified in RootModule/ModuleToProcess
# NestedModules = @()

# Functions to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no functions to export.
FunctionsToExport = 'Get-MgPrint', 'Get-MgPrintConnector', 'Get-MgPrintConnectorCount', 
               'Get-MgPrintOperation', 'Get-MgPrintOperationCount', 
               'Get-MgPrintPrinter', 'Get-MgPrintPrinterConnector', 
               'Get-MgPrintPrinterConnectorCount', 'Get-MgPrintPrinterCount', 
               'Get-MgPrintPrinterJob', 'Get-MgPrintPrinterJobCount', 
               'Get-MgPrintPrinterJobDocument', 
               'Get-MgPrintPrinterJobDocumentContent', 
               'Get-MgPrintPrinterJobDocumentCount', 'Get-MgPrintPrinterJobTask', 
               'Get-MgPrintPrinterJobTaskCount', 
               'Get-MgPrintPrinterJobTaskDefinition', 
               'Get-MgPrintPrinterJobTaskTrigger', 'Get-MgPrintPrinterShare', 
               'Get-MgPrintPrinterShareCount', 'Get-MgPrintPrinterTaskTrigger', 
               'Get-MgPrintPrinterTaskTriggerCount', 
               'Get-MgPrintPrinterTaskTriggerDefinition', 'Get-MgPrintService', 
               'Get-MgPrintServiceCount', 'Get-MgPrintServiceEndpoint', 
               'Get-MgPrintServiceEndpointCount', 'Get-MgPrintShare', 
               'Get-MgPrintShareAllowedGroup', 'Get-MgPrintShareAllowedGroupByRef', 
               'Get-MgPrintShareAllowedGroupCount', 
               'Get-MgPrintShareAllowedGroupServiceProvisioningError', 
               'Get-MgPrintShareAllowedGroupServiceProvisioningErrorCount', 
               'Get-MgPrintShareAllowedUser', 'Get-MgPrintShareAllowedUserByRef', 
               'Get-MgPrintShareAllowedUserCount', 
               'Get-MgPrintShareAllowedUserMailboxSetting', 
               'Get-MgPrintShareAllowedUserServiceProvisioningError', 
               'Get-MgPrintShareAllowedUserServiceProvisioningErrorCount', 
               'Get-MgPrintShareCount', 'Get-MgPrintShareJob', 
               'Get-MgPrintShareJobCount', 'Get-MgPrintShareJobDocument', 
               'Get-MgPrintShareJobDocumentContent', 
               'Get-MgPrintShareJobDocumentCount', 'Get-MgPrintShareJobTask', 
               'Get-MgPrintShareJobTaskCount', 'Get-MgPrintShareJobTaskDefinition', 
               'Get-MgPrintShareJobTaskTrigger', 'Get-MgPrintSharePrinter', 
               'Get-MgPrintTaskDefinition', 'Get-MgPrintTaskDefinitionCount', 
               'Get-MgPrintTaskDefinitionTask', 
               'Get-MgPrintTaskDefinitionTaskCount', 
               'Get-MgPrintTaskDefinitionTaskTrigger', 
               'Invoke-MgAbortPrintPrinterJob', 'Invoke-MgAbortPrintShareJob', 
               'Invoke-MgRedirectPrintPrinterJob', 
               'Invoke-MgRedirectPrintShareJob', 'New-MgPrintConnector', 
               'New-MgPrintOperation', 'New-MgPrintPrinter', 'New-MgPrintPrinterJob', 
               'New-MgPrintPrinterJobDocument', 
               'New-MgPrintPrinterJobDocumentUploadSession', 
               'New-MgPrintPrinterJobTask', 'New-MgPrintPrinterTaskTrigger', 
               'New-MgPrintService', 'New-MgPrintServiceEndpoint', 
               'New-MgPrintShare', 'New-MgPrintShareAllowedGroupByRef', 
               'New-MgPrintShareAllowedUserByRef', 'New-MgPrintShareJob', 
               'New-MgPrintShareJobDocument', 
               'New-MgPrintShareJobDocumentUploadSession', 
               'New-MgPrintShareJobTask', 'New-MgPrintTaskDefinition', 
               'New-MgPrintTaskDefinitionTask', 'Remove-MgPrintConnector', 
               'Remove-MgPrintOperation', 'Remove-MgPrintPrinter', 
               'Remove-MgPrintPrinterJob', 'Remove-MgPrintPrinterJobDocument', 
               'Remove-MgPrintPrinterJobDocumentContent', 
               'Remove-MgPrintPrinterJobTask', 'Remove-MgPrintPrinterTaskTrigger', 
               'Remove-MgPrintService', 'Remove-MgPrintServiceEndpoint', 
               'Remove-MgPrintShare', 'Remove-MgPrintShareAllowedGroupByRef', 
               'Remove-MgPrintShareAllowedUserByRef', 'Remove-MgPrintShareJob', 
               'Remove-MgPrintShareJobDocument', 
               'Remove-MgPrintShareJobDocumentContent', 
               'Remove-MgPrintShareJobTask', 'Remove-MgPrintTaskDefinition', 
               'Remove-MgPrintTaskDefinitionTask', 
               'Restore-MgPrintPrinterFactoryDefault', 
               'Set-MgPrintPrinterJobDocumentContent', 
               'Set-MgPrintShareJobDocumentContent', 'Start-MgPrintPrinterJob', 
               'Start-MgPrintShareJob', 'Stop-MgPrintPrinterJob', 
               'Stop-MgPrintShareJob', 'Update-MgPrint', 'Update-MgPrintConnector', 
               'Update-MgPrintOperation', 'Update-MgPrintPrinter', 
               'Update-MgPrintPrinterJob', 'Update-MgPrintPrinterJobDocument', 
               'Update-MgPrintPrinterJobTask', 'Update-MgPrintPrinterTaskTrigger', 
               'Update-MgPrintService', 'Update-MgPrintServiceEndpoint', 
               'Update-MgPrintShare', 
               'Update-MgPrintShareAllowedUserMailboxSetting', 
               'Update-MgPrintShareJob', 'Update-MgPrintShareJobDocument', 
               'Update-MgPrintShareJobTask', 'Update-MgPrintTaskDefinition', 
               'Update-MgPrintTaskDefinitionTask'

# Cmdlets to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no cmdlets to export.
CmdletsToExport = @()

# Variables to export from this module
# VariablesToExport = @()

# Aliases to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no aliases to export.
AliasesToExport = '*'

# DSC resources to export from this module
# DscResourcesToExport = @()

# List of all modules packaged with this module
# ModuleList = @()

# List of all files packaged with this module
# FileList = @()

# Private data to pass to the module specified in RootModule/ModuleToProcess. This may also contain a PSData hashtable with additional module metadata used by PowerShell.
PrivateData = @{

    PSData = @{

        # Tags applied to this module. These help with module discovery in online galleries.
        Tags = 'Microsoft','Office365','Graph','PowerShell','PSModule','PSIncludes_Cmdlet'

        # A URL to the license for this module.
        LicenseUri = 'https://aka.ms/devservicesagreement'

        # A URL to the main website for this project.
        ProjectUri = 'https://github.com/microsoftgraph/msgraph-sdk-powershell'

        # A URL to an icon representing this module.
        IconUri = 'https://raw.githubusercontent.com/microsoftgraph/msgraph-sdk-powershell/dev/docs/images/graph_color256.png'

        # ReleaseNotes of this module
        ReleaseNotes = 'See https://aka.ms/GraphPowerShell-Release.'

        # Prerelease string of this module
        # Prerelease = ''

        # Flag to indicate whether the module requires explicit user acceptance for install/update/save
        # RequireLicenseAcceptance = $false

        # External dependent modules of this module
        # ExternalModuleDependencies = @()

    } # End of PSData hashtable

 } # End of PrivateData hashtable

# HelpInfo URI of this module
# HelpInfoURI = ''

# Default prefix for commands exported from this module. Override the default prefix using Import-Module -Prefix.
# DefaultCommandPrefix = ''

}


# SIG # Begin signature block
# MIIoKQYJKoZIhvcNAQcCoIIoGjCCKBYCAQExDzANBglghkgBZQMEAgEFADB5Bgor
# BgEEAYI3AgEEoGswaTA0BgorBgEEAYI3AgEeMCYCAwEAAAQQH8w7YFlLCE63JNLG
# KX7zUQIBAAIBAAIBAAIBAAIBADAxMA0GCWCGSAFlAwQCAQUABCDoorP3W1ykkTVQ
# Ww7al6l2gYUPscHdnDm4duLiTXc8iKCCDXYwggX0MIID3KADAgECAhMzAAAEBGx0
# Bv9XKydyAAAAAAQEMA0GCSqGSIb3DQEBCwUAMH4xCzAJBgNVBAYTAlVTMRMwEQYD
# VQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNy
# b3NvZnQgQ29ycG9yYXRpb24xKDAmBgNVBAMTH01pY3Jvc29mdCBDb2RlIFNpZ25p
# bmcgUENBIDIwMTEwHhcNMjQwOTEyMjAxMTE0WhcNMjUwOTExMjAxMTE0WjB0MQsw
# CQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9u
# ZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMR4wHAYDVQQDExVNaWNy
# b3NvZnQgQ29ycG9yYXRpb24wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIB
# AQC0KDfaY50MDqsEGdlIzDHBd6CqIMRQWW9Af1LHDDTuFjfDsvna0nEuDSYJmNyz
# NB10jpbg0lhvkT1AzfX2TLITSXwS8D+mBzGCWMM/wTpciWBV/pbjSazbzoKvRrNo
# DV/u9omOM2Eawyo5JJJdNkM2d8qzkQ0bRuRd4HarmGunSouyb9NY7egWN5E5lUc3
# a2AROzAdHdYpObpCOdeAY2P5XqtJkk79aROpzw16wCjdSn8qMzCBzR7rvH2WVkvF
# HLIxZQET1yhPb6lRmpgBQNnzidHV2Ocxjc8wNiIDzgbDkmlx54QPfw7RwQi8p1fy
# 4byhBrTjv568x8NGv3gwb0RbAgMBAAGjggFzMIIBbzAfBgNVHSUEGDAWBgorBgEE
# AYI3TAgBBggrBgEFBQcDAzAdBgNVHQ4EFgQU8huhNbETDU+ZWllL4DNMPCijEU4w
# RQYDVR0RBD4wPKQ6MDgxHjAcBgNVBAsTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEW
# MBQGA1UEBRMNMjMwMDEyKzUwMjkyMzAfBgNVHSMEGDAWgBRIbmTlUAXTgqoXNzci
# tW2oynUClTBUBgNVHR8ETTBLMEmgR6BFhkNodHRwOi8vd3d3Lm1pY3Jvc29mdC5j
# b20vcGtpb3BzL2NybC9NaWNDb2RTaWdQQ0EyMDExXzIwMTEtMDctMDguY3JsMGEG
# CCsGAQUFBwEBBFUwUzBRBggrBgEFBQcwAoZFaHR0cDovL3d3dy5taWNyb3NvZnQu
# Y29tL3BraW9wcy9jZXJ0cy9NaWNDb2RTaWdQQ0EyMDExXzIwMTEtMDctMDguY3J0
# MAwGA1UdEwEB/wQCMAAwDQYJKoZIhvcNAQELBQADggIBAIjmD9IpQVvfB1QehvpC
# Ge7QeTQkKQ7j3bmDMjwSqFL4ri6ae9IFTdpywn5smmtSIyKYDn3/nHtaEn0X1NBj
# L5oP0BjAy1sqxD+uy35B+V8wv5GrxhMDJP8l2QjLtH/UglSTIhLqyt8bUAqVfyfp
# h4COMRvwwjTvChtCnUXXACuCXYHWalOoc0OU2oGN+mPJIJJxaNQc1sjBsMbGIWv3
# cmgSHkCEmrMv7yaidpePt6V+yPMik+eXw3IfZ5eNOiNgL1rZzgSJfTnvUqiaEQ0X
# dG1HbkDv9fv6CTq6m4Ty3IzLiwGSXYxRIXTxT4TYs5VxHy2uFjFXWVSL0J2ARTYL
# E4Oyl1wXDF1PX4bxg1yDMfKPHcE1Ijic5lx1KdK1SkaEJdto4hd++05J9Bf9TAmi
# u6EK6C9Oe5vRadroJCK26uCUI4zIjL/qG7mswW+qT0CW0gnR9JHkXCWNbo8ccMk1
# sJatmRoSAifbgzaYbUz8+lv+IXy5GFuAmLnNbGjacB3IMGpa+lbFgih57/fIhamq
# 5VhxgaEmn/UjWyr+cPiAFWuTVIpfsOjbEAww75wURNM1Imp9NJKye1O24EspEHmb
# DmqCUcq7NqkOKIG4PVm3hDDED/WQpzJDkvu4FrIbvyTGVU01vKsg4UfcdiZ0fQ+/
# V0hf8yrtq9CkB8iIuk5bBxuPMIIHejCCBWKgAwIBAgIKYQ6Q0gAAAAAAAzANBgkq
# hkiG9w0BAQsFADCBiDELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldhc2hpbmd0b24x
# EDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlv
# bjEyMDAGA1UEAxMpTWljcm9zb2Z0IFJvb3QgQ2VydGlmaWNhdGUgQXV0aG9yaXR5
# IDIwMTEwHhcNMTEwNzA4MjA1OTA5WhcNMjYwNzA4MjEwOTA5WjB+MQswCQYDVQQG
# EwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwG
# A1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSgwJgYDVQQDEx9NaWNyb3NvZnQg
# Q29kZSBTaWduaW5nIFBDQSAyMDExMIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIIC
# CgKCAgEAq/D6chAcLq3YbqqCEE00uvK2WCGfQhsqa+laUKq4BjgaBEm6f8MMHt03
# a8YS2AvwOMKZBrDIOdUBFDFC04kNeWSHfpRgJGyvnkmc6Whe0t+bU7IKLMOv2akr
# rnoJr9eWWcpgGgXpZnboMlImEi/nqwhQz7NEt13YxC4Ddato88tt8zpcoRb0Rrrg
# OGSsbmQ1eKagYw8t00CT+OPeBw3VXHmlSSnnDb6gE3e+lD3v++MrWhAfTVYoonpy
# 4BI6t0le2O3tQ5GD2Xuye4Yb2T6xjF3oiU+EGvKhL1nkkDstrjNYxbc+/jLTswM9
# sbKvkjh+0p2ALPVOVpEhNSXDOW5kf1O6nA+tGSOEy/S6A4aN91/w0FK/jJSHvMAh
# dCVfGCi2zCcoOCWYOUo2z3yxkq4cI6epZuxhH2rhKEmdX4jiJV3TIUs+UsS1Vz8k
# A/DRelsv1SPjcF0PUUZ3s/gA4bysAoJf28AVs70b1FVL5zmhD+kjSbwYuER8ReTB
# w3J64HLnJN+/RpnF78IcV9uDjexNSTCnq47f7Fufr/zdsGbiwZeBe+3W7UvnSSmn
# Eyimp31ngOaKYnhfsi+E11ecXL93KCjx7W3DKI8sj0A3T8HhhUSJxAlMxdSlQy90
# lfdu+HggWCwTXWCVmj5PM4TasIgX3p5O9JawvEagbJjS4NaIjAsCAwEAAaOCAe0w
# ggHpMBAGCSsGAQQBgjcVAQQDAgEAMB0GA1UdDgQWBBRIbmTlUAXTgqoXNzcitW2o
# ynUClTAZBgkrBgEEAYI3FAIEDB4KAFMAdQBiAEMAQTALBgNVHQ8EBAMCAYYwDwYD
# VR0TAQH/BAUwAwEB/zAfBgNVHSMEGDAWgBRyLToCMZBDuRQFTuHqp8cx0SOJNDBa
# BgNVHR8EUzBRME+gTaBLhklodHRwOi8vY3JsLm1pY3Jvc29mdC5jb20vcGtpL2Ny
# bC9wcm9kdWN0cy9NaWNSb29DZXJBdXQyMDExXzIwMTFfMDNfMjIuY3JsMF4GCCsG
# AQUFBwEBBFIwUDBOBggrBgEFBQcwAoZCaHR0cDovL3d3dy5taWNyb3NvZnQuY29t
# L3BraS9jZXJ0cy9NaWNSb29DZXJBdXQyMDExXzIwMTFfMDNfMjIuY3J0MIGfBgNV
# HSAEgZcwgZQwgZEGCSsGAQQBgjcuAzCBgzA/BggrBgEFBQcCARYzaHR0cDovL3d3
# dy5taWNyb3NvZnQuY29tL3BraW9wcy9kb2NzL3ByaW1hcnljcHMuaHRtMEAGCCsG
# AQUFBwICMDQeMiAdAEwAZQBnAGEAbABfAHAAbwBsAGkAYwB5AF8AcwB0AGEAdABl
# AG0AZQBuAHQALiAdMA0GCSqGSIb3DQEBCwUAA4ICAQBn8oalmOBUeRou09h0ZyKb
# C5YR4WOSmUKWfdJ5DJDBZV8uLD74w3LRbYP+vj/oCso7v0epo/Np22O/IjWll11l
# hJB9i0ZQVdgMknzSGksc8zxCi1LQsP1r4z4HLimb5j0bpdS1HXeUOeLpZMlEPXh6
# I/MTfaaQdION9MsmAkYqwooQu6SpBQyb7Wj6aC6VoCo/KmtYSWMfCWluWpiW5IP0
# wI/zRive/DvQvTXvbiWu5a8n7dDd8w6vmSiXmE0OPQvyCInWH8MyGOLwxS3OW560
# STkKxgrCxq2u5bLZ2xWIUUVYODJxJxp/sfQn+N4sOiBpmLJZiWhub6e3dMNABQam
# ASooPoI/E01mC8CzTfXhj38cbxV9Rad25UAqZaPDXVJihsMdYzaXht/a8/jyFqGa
# J+HNpZfQ7l1jQeNbB5yHPgZ3BtEGsXUfFL5hYbXw3MYbBL7fQccOKO7eZS/sl/ah
# XJbYANahRr1Z85elCUtIEJmAH9AAKcWxm6U/RXceNcbSoqKfenoi+kiVH6v7RyOA
# 9Z74v2u3S5fi63V4GuzqN5l5GEv/1rMjaHXmr/r8i+sLgOppO6/8MO0ETI7f33Vt
# Y5E90Z1WTk+/gFcioXgRMiF670EKsT/7qMykXcGhiJtXcVZOSEXAQsmbdlsKgEhr
# /Xmfwb1tbWrJUnMTDXpQzTGCGgkwghoFAgEBMIGVMH4xCzAJBgNVBAYTAlVTMRMw
# EQYDVQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVN
# aWNyb3NvZnQgQ29ycG9yYXRpb24xKDAmBgNVBAMTH01pY3Jvc29mdCBDb2RlIFNp
# Z25pbmcgUENBIDIwMTECEzMAAAQEbHQG/1crJ3IAAAAABAQwDQYJYIZIAWUDBAIB
# BQCgga4wGQYJKoZIhvcNAQkDMQwGCisGAQQBgjcCAQQwHAYKKwYBBAGCNwIBCzEO
# MAwGCisGAQQBgjcCARUwLwYJKoZIhvcNAQkEMSIEIB0MHvGBUiYgD9qWHLHi1RAB
# WE+ybTqiCI7+DNqanbEdMEIGCisGAQQBgjcCAQwxNDAyoBSAEgBNAGkAYwByAG8A
# cwBvAGYAdKEagBhodHRwOi8vd3d3Lm1pY3Jvc29mdC5jb20wDQYJKoZIhvcNAQEB
# BQAEggEAglwPIJpSS0r0JpSK3ZRWaZXiGMtMCe1mMEHUQ7iOSs+ml55ZFZZKUevv
# OoOVrpT42VkKoIITu/7JoRkB1+jYcFE+kB5vgLTBqsXnUOFwQyC6AmD2U6TGAMfS
# YPpFkiluETSvY7sAr21o8LTkfvq+XwiZQobnX24JdlVNqTg7R5hKVftnqXus5qer
# 1FwZgtgTMh2X3tccl+sNup1it+heP3s3Fkmi8Y0uitSyhnhU4y6aFiaF+WviPgR0
# Vk3j1pZN/jIKC6c/J0tV2wALzQEAQeQ2sUoRxKAfkSCpXw9+UOEyqISeCC7YPTs7
# SdO+RyNEajuGRZdGKmvI2WxC7JZVN6GCF5MwghePBgorBgEEAYI3AwMBMYIXfzCC
# F3sGCSqGSIb3DQEHAqCCF2wwghdoAgEDMQ8wDQYJYIZIAWUDBAIBBQAwggFRBgsq
# hkiG9w0BCRABBKCCAUAEggE8MIIBOAIBAQYKKwYBBAGEWQoDATAxMA0GCWCGSAFl
# AwQCAQUABCDml923jvHL4WaKDx9I4CTU8/aVUQjGXbZlLlKGak2Y7wIGaErKQWfl
# GBIyMDI1MDcwOTExMDcyNi43N1owBIACAfSggdGkgc4wgcsxCzAJBgNVBAYTAlVT
# MRMwEQYDVQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQK
# ExVNaWNyb3NvZnQgQ29ycG9yYXRpb24xJTAjBgNVBAsTHE1pY3Jvc29mdCBBbWVy
# aWNhIE9wZXJhdGlvbnMxJzAlBgNVBAsTHm5TaGllbGQgVFNTIEVTTjpBOTM1LTAz
# RTAtRDk0NzElMCMGA1UEAxMcTWljcm9zb2Z0IFRpbWUtU3RhbXAgU2VydmljZaCC
# EeowggcgMIIFCKADAgECAhMzAAACDLlk4zWc7PSuAAEAAAIMMA0GCSqGSIb3DQEB
# CwUAMHwxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQH
# EwdSZWRtb25kMR4wHAYDVQQKExVNaWNyb3NvZnQgQ29ycG9yYXRpb24xJjAkBgNV
# BAMTHU1pY3Jvc29mdCBUaW1lLVN0YW1wIFBDQSAyMDEwMB4XDTI1MDEzMDE5NDMw
# MFoXDTI2MDQyMjE5NDMwMFowgcsxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNo
# aW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNyb3NvZnQgQ29y
# cG9yYXRpb24xJTAjBgNVBAsTHE1pY3Jvc29mdCBBbWVyaWNhIE9wZXJhdGlvbnMx
# JzAlBgNVBAsTHm5TaGllbGQgVFNTIEVTTjpBOTM1LTAzRTAtRDk0NzElMCMGA1UE
# AxMcTWljcm9zb2Z0IFRpbWUtU3RhbXAgU2VydmljZTCCAiIwDQYJKoZIhvcNAQEB
# BQADggIPADCCAgoCggIBAMoBViY95G2Br9TqPOrKosPQwCiiXbeBwE3nz5n9eyRj
# A0mxn477BXJBiXx09MrX8ELbECJzWb4m9ySqNVpDfYqZRGwRmi2KtBjg8pVb55fB
# G3evqzOAu6JzqqgeVtejH+XQcm2BRGTMNdYyQqYZIvvPz9yupy+Ziq/y3+yUAXgn
# 6anNv20wVWaPApc41V1HCD1DdZo9kELta+iLs9Eg3aOCNIGcdjIBlKWy0o2ulhvr
# 4a7qhIWRDMalHrn5A0N2Q/i585/g9s6Dd9vi4Y+MjwQ8qWnAzBqLWRDJf5+ByAKh
# X0n6jwxhgJlR63eTOGHBHOqHosx4ONpcs/vTVJdeJdzZkfO4MdtL+xm0nfrbtxWk
# KVcQhS+DbGmvSs+Ui0fC2OjU/AwKldiqdgq9fxonydrBP1bwVS67Jk8bXznb6riO
# RWV4ovvH7t6XwRN6Ft2TB2EBfJeKZoTNZ6001KYb8p8cCn1zPCwvW8qvhGCf6kgi
# Rke6iZ1/l7jzUr7EhaEsI92m5XzsSoY4r+NuE6dkSrB28DQCUxot+yYJ6Zma6l6N
# pi4STTn/pwJTGAXjMKeQl5h0wA/71niRWHu3NEWzD+VlKXYPsSEgDoqePpF98faT
# ti1IZK/zoJKHN+JdrP3LqxO7xIaoXo5sv9678OSK/JWgJ9RdYuOJImytLrcPQQcd
# AgMBAAGjggFJMIIBRTAdBgNVHQ4EFgQUdQ5FIf+wH+tD9t4PSXlXFDvToYgwHwYD
# VR0jBBgwFoAUn6cVXQBeYl2D9OXSZacbUzUZ6XIwXwYDVR0fBFgwVjBUoFKgUIZO
# aHR0cDovL3d3dy5taWNyb3NvZnQuY29tL3BraW9wcy9jcmwvTWljcm9zb2Z0JTIw
# VGltZS1TdGFtcCUyMFBDQSUyMDIwMTAoMSkuY3JsMGwGCCsGAQUFBwEBBGAwXjBc
# BggrBgEFBQcwAoZQaHR0cDovL3d3dy5taWNyb3NvZnQuY29tL3BraW9wcy9jZXJ0
# cy9NaWNyb3NvZnQlMjBUaW1lLVN0YW1wJTIwUENBJTIwMjAxMCgxKS5jcnQwDAYD
# VR0TAQH/BAIwADAWBgNVHSUBAf8EDDAKBggrBgEFBQcDCDAOBgNVHQ8BAf8EBAMC
# B4AwDQYJKoZIhvcNAQELBQADggIBANkw+viBWbDB/gYwHll8dKvfi6G6DrLO7gdR
# P4lYxmrP26EtkGhfkI+N0onPABW9ig24uZLT72UDlLviu8qp3+72+nzxUaTpTuAx
# x5q12qkqVtVF2fZl+sxykjjM5zoG25ivMlXhwSzViZf3m6IDFoQPfjDTYGd+49lc
# DR52wMFt3iLEVTxf/UnQN8hSTVgVg86ubCYjaTXq7pNwo7RilGXBN0Kr287R4QgR
# HVIuZA0HNf2HZxwK+2B6Q5oGghDdlFqLwOzV/7BwoI/MPioNffE2C8sWIqgDplIb
# 1L/I6sZqJIYh4PLk31VC6pM2OvK4DOO9/lbwBCnfWFXUZtQM6RtR137OQlYpfgWb
# N543nYQRvKShZwnlX0zgM8Y3nGkWpfL1o7T51HRRRha6p4uEPJGdV5lxMS7TGCaj
# 6lAdq4VUBKxU5EynxMXx2l6x362qSRDxU28jbSg5+dN8v7tmBQx/uo1XSWXRajme
# WvUIm9rVt+TYdzkFjUz2x3duUGR7PK8k+fiPRt846sJhPBiw2yOJGX9ZbXw06mLC
# pyLAWVQ2q1YJEzML2vzhhpQxDzYHLCTjx3i4GiflkDylddLuPAlOMmPlRJ5GX2+N
# P3w8NnIIU7Z4VI4V0N1/pYGj9ZlQDaEZnSr4nuPXjR9tcJ85QibSPbcdoBXRyQNL
# +eYL+gXWMIIHcTCCBVmgAwIBAgITMwAAABXF52ueAptJmQAAAAAAFTANBgkqhkiG
# 9w0BAQsFADCBiDELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldhc2hpbmd0b24xEDAO
# BgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEy
# MDAGA1UEAxMpTWljcm9zb2Z0IFJvb3QgQ2VydGlmaWNhdGUgQXV0aG9yaXR5IDIw
# MTAwHhcNMjEwOTMwMTgyMjI1WhcNMzAwOTMwMTgzMjI1WjB8MQswCQYDVQQGEwJV
# UzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwGA1UE
# ChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSYwJAYDVQQDEx1NaWNyb3NvZnQgVGlt
# ZS1TdGFtcCBQQ0EgMjAxMDCCAiIwDQYJKoZIhvcNAQEBBQADggIPADCCAgoCggIB
# AOThpkzntHIhC3miy9ckeb0O1YLT/e6cBwfSqWxOdcjKNVf2AX9sSuDivbk+F2Az
# /1xPx2b3lVNxWuJ+Slr+uDZnhUYjDLWNE893MsAQGOhgfWpSg0S3po5GawcU88V2
# 9YZQ3MFEyHFcUTE3oAo4bo3t1w/YJlN8OWECesSq/XJprx2rrPY2vjUmZNqYO7oa
# ezOtgFt+jBAcnVL+tuhiJdxqD89d9P6OU8/W7IVWTe/dvI2k45GPsjksUZzpcGkN
# yjYtcI4xyDUoveO0hyTD4MmPfrVUj9z6BVWYbWg7mka97aSueik3rMvrg0XnRm7K
# MtXAhjBcTyziYrLNueKNiOSWrAFKu75xqRdbZ2De+JKRHh09/SDPc31BmkZ1zcRf
# NN0Sidb9pSB9fvzZnkXftnIv231fgLrbqn427DZM9ituqBJR6L8FA6PRc6ZNN3SU
# HDSCD/AQ8rdHGO2n6Jl8P0zbr17C89XYcz1DTsEzOUyOArxCaC4Q6oRRRuLRvWoY
# WmEBc8pnol7XKHYC4jMYctenIPDC+hIK12NvDMk2ZItboKaDIV1fMHSRlJTYuVD5
# C4lh8zYGNRiER9vcG9H9stQcxWv2XFJRXRLbJbqvUAV6bMURHXLvjflSxIUXk8A8
# FdsaN8cIFRg/eKtFtvUeh17aj54WcmnGrnu3tz5q4i6tAgMBAAGjggHdMIIB2TAS
# BgkrBgEEAYI3FQEEBQIDAQABMCMGCSsGAQQBgjcVAgQWBBQqp1L+ZMSavoKRPEY1
# Kc8Q/y8E7jAdBgNVHQ4EFgQUn6cVXQBeYl2D9OXSZacbUzUZ6XIwXAYDVR0gBFUw
# UzBRBgwrBgEEAYI3TIN9AQEwQTA/BggrBgEFBQcCARYzaHR0cDovL3d3dy5taWNy
# b3NvZnQuY29tL3BraW9wcy9Eb2NzL1JlcG9zaXRvcnkuaHRtMBMGA1UdJQQMMAoG
# CCsGAQUFBwMIMBkGCSsGAQQBgjcUAgQMHgoAUwB1AGIAQwBBMAsGA1UdDwQEAwIB
# hjAPBgNVHRMBAf8EBTADAQH/MB8GA1UdIwQYMBaAFNX2VsuP6KJcYmjRPZSQW9fO
# mhjEMFYGA1UdHwRPME0wS6BJoEeGRWh0dHA6Ly9jcmwubWljcm9zb2Z0LmNvbS9w
# a2kvY3JsL3Byb2R1Y3RzL01pY1Jvb0NlckF1dF8yMDEwLTA2LTIzLmNybDBaBggr
# BgEFBQcBAQROMEwwSgYIKwYBBQUHMAKGPmh0dHA6Ly93d3cubWljcm9zb2Z0LmNv
# bS9wa2kvY2VydHMvTWljUm9vQ2VyQXV0XzIwMTAtMDYtMjMuY3J0MA0GCSqGSIb3
# DQEBCwUAA4ICAQCdVX38Kq3hLB9nATEkW+Geckv8qW/qXBS2Pk5HZHixBpOXPTEz
# tTnXwnE2P9pkbHzQdTltuw8x5MKP+2zRoZQYIu7pZmc6U03dmLq2HnjYNi6cqYJW
# AAOwBb6J6Gngugnue99qb74py27YP0h1AdkY3m2CDPVtI1TkeFN1JFe53Z/zjj3G
# 82jfZfakVqr3lbYoVSfQJL1AoL8ZthISEV09J+BAljis9/kpicO8F7BUhUKz/Aye
# ixmJ5/ALaoHCgRlCGVJ1ijbCHcNhcy4sa3tuPywJeBTpkbKpW99Jo3QMvOyRgNI9
# 5ko+ZjtPu4b6MhrZlvSP9pEB9s7GdP32THJvEKt1MMU0sHrYUP4KWN1APMdUbZ1j
# dEgssU5HLcEUBHG/ZPkkvnNtyo4JvbMBV0lUZNlz138eW0QBjloZkWsNn6Qo3GcZ
# KCS6OEuabvshVGtqRRFHqfG3rsjoiV5PndLQTHa1V1QJsWkBRH58oWFsc/4Ku+xB
# Zj1p/cvBQUl+fpO+y/g75LcVv7TOPqUxUYS8vwLBgqJ7Fx0ViY1w/ue10CgaiQuP
# Ntq6TPmb/wrpNPgkNWcr4A245oyZ1uEi6vAnQj0llOZ0dFtq0Z4+7X6gMTN9vMvp
# e784cETRkPHIqzqKOghif9lwY1NNje6CbaUFEMFxBmoQtB1VM1izoXBm8qGCA00w
# ggI1AgEBMIH5oYHRpIHOMIHLMQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGlu
# Z3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBv
# cmF0aW9uMSUwIwYDVQQLExxNaWNyb3NvZnQgQW1lcmljYSBPcGVyYXRpb25zMScw
# JQYDVQQLEx5uU2hpZWxkIFRTUyBFU046QTkzNS0wM0UwLUQ5NDcxJTAjBgNVBAMT
# HE1pY3Jvc29mdCBUaW1lLVN0YW1wIFNlcnZpY2WiIwoBATAHBgUrDgMCGgMVAO+7
# yGSEQy3lnwt15+WzvPUtVTymoIGDMIGApH4wfDELMAkGA1UEBhMCVVMxEzARBgNV
# BAgTCldhc2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jv
# c29mdCBDb3Jwb3JhdGlvbjEmMCQGA1UEAxMdTWljcm9zb2Z0IFRpbWUtU3RhbXAg
# UENBIDIwMTAwDQYJKoZIhvcNAQELBQACBQDsGDYvMCIYDzIwMjUwNzA5MDAyODMx
# WhgPMjAyNTA3MTAwMDI4MzFaMHQwOgYKKwYBBAGEWQoEATEsMCowCgIFAOwYNi8C
# AQAwBwIBAAICAyYwBwIBAAICEogwCgIFAOwZh68CAQAwNgYKKwYBBAGEWQoEAjEo
# MCYwDAYKKwYBBAGEWQoDAqAKMAgCAQACAwehIKEKMAgCAQACAwGGoDANBgkqhkiG
# 9w0BAQsFAAOCAQEAes/mh1BT9M5GmukQrTdevpwuuwRS7iFust5VZUPh2KD0XaHD
# 0HLARqjEE3ocHlJQOnEh3jQL+/58Y/N7BCCiDquwe41JQFEaqEuTGBGu+Rky26hC
# sflEQKISruGEWQhtqfQDKLS8Jeasra9TlZ0Qvo71xtxQ0UvMpi/TIC7HGHIHrLYR
# qH7VV6WePpkYEZff8zUVN/JfeQtr8BYwfovHYz/kWAwiucO02XTFDvbiyv5f6VXI
# LIY87DX8Sq8aOFlMuBBAfn0WtFbl0PqXJDo4z8qY55uTDhslrATR/EWx8KCosa53
# ROwe+wFZBOd8xz9PIx0lhcJQzwNSMQNc8Q1xKTGCBA0wggQJAgEBMIGTMHwxCzAJ
# BgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25k
# MR4wHAYDVQQKExVNaWNyb3NvZnQgQ29ycG9yYXRpb24xJjAkBgNVBAMTHU1pY3Jv
# c29mdCBUaW1lLVN0YW1wIFBDQSAyMDEwAhMzAAACDLlk4zWc7PSuAAEAAAIMMA0G
# CWCGSAFlAwQCAQUAoIIBSjAaBgkqhkiG9w0BCQMxDQYLKoZIhvcNAQkQAQQwLwYJ
# KoZIhvcNAQkEMSIEIHa1vkMmWBUcp0D2WkSSGttGTQraUWeGAP+HvxTof1SmMIH6
# BgsqhkiG9w0BCRACLzGB6jCB5zCB5DCBvQQg1SjXtwUxk3jowjk18gCD1THlw7nE
# z2Ket7muK45nwi0wgZgwgYCkfjB8MQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2Fz
# aGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENv
# cnBvcmF0aW9uMSYwJAYDVQQDEx1NaWNyb3NvZnQgVGltZS1TdGFtcCBQQ0EgMjAx
# MAITMwAAAgy5ZOM1nOz0rgABAAACDDAiBCA2Ojbtqq2NL8GS+NSwRMmMT8vLp5S3
# 8iJF5skSYzir0zANBgkqhkiG9w0BAQsFAASCAgCa8gh/HNy7PyJpf/jzrvX8xZfn
# UFOvdSIw4GDvvQHNJiLa5OUn6lkPoBU3hA+yr/PdV6q2MF8+5lqk4BvcSXSbaARo
# gegF/5LTWdDR90QFsVhfRJtXWE//3CjJJvQTwN/aERH7bJWLeLz85gPkuZUuRutw
# TLoOoaX0BcZyN6H8g/QlIkc/7BvPlFCyq62QITLv2vRlAXdGbHLydrdSRY48WQWx
# XGT3MuYAnAeZQcqVKAGlD4K19Hr1Yw5vMZyECki6l1++CoLfitgjr6F5kh9nIxdj
# oPJ9YGkj2LG7tzhwoQpLoGw4sVLx7+E1GqY+JPczCv8XjqRmVLjZfW9wn0c3hR9L
# i7qgtfSNzLuv8be+g811yMGQzU+B/bqNcW3cGBUtDZTaDz9BKANoElhXlqzR9PQ6
# YYEY5RmOTnRCJy+JORMJdPV6i+bsYXlZR+n4iT7YdF8a5R2mv00oNEr3rjodAQPF
# gLCo6M0GO15vfjVJ4mm205c8hI/+2QCM+TTjWnTl3FqXwvc7EixtvjA3hk+QdLYq
# rodnnNcPfNBomHFOfAhHXFHmaOJBMJ5+FaTqB4X+tM7c1KGeDoZd6XChMV2Bpd4u
# XVxnd09MxaGefVgrOZpSPn4CU4l9YmwfWqntht+ZpslOUXLwNGOK8aIPt9W++rvG
# WiADD3TYSEev56zStg==
# SIG # End signature block
