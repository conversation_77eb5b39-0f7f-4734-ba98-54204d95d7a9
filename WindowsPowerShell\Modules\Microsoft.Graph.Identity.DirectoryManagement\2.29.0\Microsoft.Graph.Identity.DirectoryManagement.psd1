#
# Module manifest for module 'Microsoft.Graph.Identity.DirectoryManagement'
#
# Generated by: Microsoft Corporation
#
# Generated on: 7/9/2025
#

@{

# Script module or binary module file associated with this manifest.
RootModule = './Microsoft.Graph.Identity.DirectoryManagement.psm1'

# Version number of this module.
ModuleVersion = '2.29.0'

# Supported PSEditions
CompatiblePSEditions = 'Core', 'Desktop'

# ID used to uniquely identify this module
GUID = 'c767240d-585c-42cb-bb2f-6e76e6d639d4'

# Author of this module
Author = 'Microsoft Corporation'

# Company or vendor of this module
CompanyName = 'Microsoft Corporation'

# Copyright statement for this module
Copyright = 'Microsoft Corporation. All rights reserved.'

# Description of the functionality provided by this module
Description = 'Microsoft Graph PowerShell Cmdlets'

# Minimum version of the PowerShell engine required by this module
PowerShellVersion = '5.1'

# Name of the PowerShell host required by this module
# PowerShellHostName = ''

# Minimum version of the PowerShell host required by this module
# PowerShellHostVersion = ''

# Minimum version of Microsoft .NET Framework required by this module. This prerequisite is valid for the PowerShell Desktop edition only.
DotNetFrameworkVersion = '4.7.2'

# Minimum version of the common language runtime (CLR) required by this module. This prerequisite is valid for the PowerShell Desktop edition only.
# ClrVersion = ''

# Processor architecture (None, X86, Amd64) required by this module
# ProcessorArchitecture = ''

# Modules that must be imported into the global environment prior to importing this module
RequiredModules = @(@{ModuleName = 'Microsoft.Graph.Authentication'; RequiredVersion = '2.29.0'; })

# Assemblies that must be loaded prior to importing this module
RequiredAssemblies = './bin/Microsoft.Graph.Identity.DirectoryManagement.private.dll'

# Script files (.ps1) that are run in the caller's environment prior to importing this module.
# ScriptsToProcess = @()

# Type files (.ps1xml) to be loaded when importing this module
# TypesToProcess = @()

# Format files (.ps1xml) to be loaded when importing this module
FormatsToProcess = './Microsoft.Graph.Identity.DirectoryManagement.format.ps1xml'

# Modules to import as nested modules of the module specified in RootModule/ModuleToProcess
# NestedModules = @()

# Functions to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no functions to export.
FunctionsToExport = 'Confirm-MgContactMemberGroup', 'Confirm-MgContactMemberObject', 
               'Confirm-MgContractMemberGroup', 'Confirm-MgContractMemberObject', 
               'Confirm-MgDeviceMemberGroup', 'Confirm-MgDeviceMemberObject', 
               'Confirm-MgDirectoryDeletedItemMemberGroup', 
               'Confirm-MgDirectoryDeletedItemMemberObject', 
               'Confirm-MgDirectoryRoleMemberGroup', 
               'Confirm-MgDirectoryRoleMemberObject', 
               'Confirm-MgDirectoryRoleTemplateMemberGroup', 
               'Confirm-MgDirectoryRoleTemplateMemberObject', 'Confirm-MgDomain', 
               'Confirm-MgOrganizationMemberGroup', 
               'Confirm-MgOrganizationMemberObject', 
               'Find-MgTenantRelationshipTenantInformationByDomainName', 
               'Find-MgTenantRelationshipTenantInformationByTenantId', 
               'Get-MgAdminPeople', 'Get-MgAdminPeopleItemInsight', 
               'Get-MgAdminPeopleProfileCardProperty', 
               'Get-MgAdminPeopleProfileCardPropertyCount', 
               'Get-MgAdminPeoplePronoun', 'Get-MgContact', 'Get-MgContactById', 
               'Get-MgContactCount', 'Get-MgContactDelta', 
               'Get-MgContactDirectReport', 
               'Get-MgContactDirectReportAsOrgContact', 
               'Get-MgContactDirectReportAsUser', 'Get-MgContactDirectReportCount', 
               'Get-MgContactDirectReportCountAsOrgContact', 
               'Get-MgContactDirectReportCountAsUser', 'Get-MgContactManager', 
               'Get-MgContactMemberGroup', 'Get-MgContactMemberObject', 
               'Get-MgContactMemberOf', 
               'Get-MgContactMemberOfAsAdministrativeUnit', 
               'Get-MgContactMemberOfAsGroup', 'Get-MgContactMemberOfCount', 
               'Get-MgContactMemberOfCountAsAdministrativeUnit', 
               'Get-MgContactMemberOfCountAsGroup', 
               'Get-MgContactServiceProvisioningError', 
               'Get-MgContactServiceProvisioningErrorCount', 
               'Get-MgContactTransitiveMemberOf', 
               'Get-MgContactTransitiveMemberOfAsAdministrativeUnit', 
               'Get-MgContactTransitiveMemberOfAsGroup', 
               'Get-MgContactTransitiveMemberOfCount', 
               'Get-MgContactTransitiveMemberOfCountAsAdministrativeUnit', 
               'Get-MgContactTransitiveMemberOfCountAsGroup', 'Get-MgContract', 
               'Get-MgContractById', 'Get-MgContractCount', 'Get-MgContractDelta', 
               'Get-MgContractMemberGroup', 'Get-MgContractMemberObject', 
               'Get-MgDevice', 'Get-MgDeviceByDeviceId', 'Get-MgDeviceById', 
               'Get-MgDeviceCount', 'Get-MgDeviceDelta', 'Get-MgDeviceExtension', 
               'Get-MgDeviceExtensionCount', 'Get-MgDeviceMemberGroup', 
               'Get-MgDeviceMemberObject', 'Get-MgDeviceMemberOf', 
               'Get-MgDeviceMemberOfAsAdministrativeUnit', 
               'Get-MgDeviceMemberOfAsGroup', 'Get-MgDeviceMemberOfCount', 
               'Get-MgDeviceMemberOfCountAsAdministrativeUnit', 
               'Get-MgDeviceMemberOfCountAsGroup', 'Get-MgDeviceRegisteredOwner', 
               'Get-MgDeviceRegisteredOwnerAsAppRoleAssignment', 
               'Get-MgDeviceRegisteredOwnerAsEndpoint', 
               'Get-MgDeviceRegisteredOwnerAsServicePrincipal', 
               'Get-MgDeviceRegisteredOwnerAsUser', 
               'Get-MgDeviceRegisteredOwnerByRef', 
               'Get-MgDeviceRegisteredOwnerCount', 
               'Get-MgDeviceRegisteredOwnerCountAsAppRoleAssignment', 
               'Get-MgDeviceRegisteredOwnerCountAsEndpoint', 
               'Get-MgDeviceRegisteredOwnerCountAsServicePrincipal', 
               'Get-MgDeviceRegisteredOwnerCountAsUser', 
               'Get-MgDeviceRegisteredUser', 
               'Get-MgDeviceRegisteredUserAsAppRoleAssignment', 
               'Get-MgDeviceRegisteredUserAsEndpoint', 
               'Get-MgDeviceRegisteredUserAsServicePrincipal', 
               'Get-MgDeviceRegisteredUserAsUser', 
               'Get-MgDeviceRegisteredUserByRef', 
               'Get-MgDeviceRegisteredUserCount', 
               'Get-MgDeviceRegisteredUserCountAsAppRoleAssignment', 
               'Get-MgDeviceRegisteredUserCountAsEndpoint', 
               'Get-MgDeviceRegisteredUserCountAsServicePrincipal', 
               'Get-MgDeviceRegisteredUserCountAsUser', 
               'Get-MgDeviceTransitiveMemberOf', 
               'Get-MgDeviceTransitiveMemberOfAsAdministrativeUnit', 
               'Get-MgDeviceTransitiveMemberOfAsGroup', 
               'Get-MgDeviceTransitiveMemberOfCount', 
               'Get-MgDeviceTransitiveMemberOfCountAsAdministrativeUnit', 
               'Get-MgDeviceTransitiveMemberOfCountAsGroup', 'Get-MgDirectory', 
               'Get-MgDirectoryAdministrativeUnit', 
               'Get-MgDirectoryAdministrativeUnitCount', 
               'Get-MgDirectoryAdministrativeUnitDelta', 
               'Get-MgDirectoryAdministrativeUnitExtension', 
               'Get-MgDirectoryAdministrativeUnitExtensionCount', 
               'Get-MgDirectoryAdministrativeUnitMember', 
               'Get-MgDirectoryAdministrativeUnitMemberAsApplication', 
               'Get-MgDirectoryAdministrativeUnitMemberAsDevice', 
               'Get-MgDirectoryAdministrativeUnitMemberAsGroup', 
               'Get-MgDirectoryAdministrativeUnitMemberAsOrgContact', 
               'Get-MgDirectoryAdministrativeUnitMemberAsServicePrincipal', 
               'Get-MgDirectoryAdministrativeUnitMemberAsUser', 
               'Get-MgDirectoryAdministrativeUnitMemberByRef', 
               'Get-MgDirectoryAdministrativeUnitMemberCount', 
               'Get-MgDirectoryAdministrativeUnitMemberCountAsApplication', 
               'Get-MgDirectoryAdministrativeUnitMemberCountAsDevice', 
               'Get-MgDirectoryAdministrativeUnitMemberCountAsGroup', 
               'Get-MgDirectoryAdministrativeUnitMemberCountAsOrgContact', 
               'Get-MgDirectoryAdministrativeUnitMemberCountAsServicePrincipal', 
               'Get-MgDirectoryAdministrativeUnitMemberCountAsUser', 
               'Get-MgDirectoryAdministrativeUnitScopedRoleMember', 
               'Get-MgDirectoryAdministrativeUnitScopedRoleMemberCount', 
               'Get-MgDirectoryAttributeSet', 'Get-MgDirectoryAttributeSetCount', 
               'Get-MgDirectoryCustomSecurityAttributeDefinition', 
               'Get-MgDirectoryCustomSecurityAttributeDefinitionAllowedValue', 
               'Get-MgDirectoryCustomSecurityAttributeDefinitionAllowedValueCount', 
               'Get-MgDirectoryCustomSecurityAttributeDefinitionCount', 
               'Get-MgDirectoryDeletedItem', 
               'Get-MgDirectoryDeletedItemAsAdministrativeUnit', 
               'Get-MgDirectoryDeletedItemAsApplication', 
               'Get-MgDirectoryDeletedItemAsDevice', 
               'Get-MgDirectoryDeletedItemAsGroup', 
               'Get-MgDirectoryDeletedItemAsServicePrincipal', 
               'Get-MgDirectoryDeletedItemAsUser', 
               'Get-MgDirectoryDeletedItemById', 
               'Get-MgDirectoryDeletedItemCountAsAdministrativeUnit', 
               'Get-MgDirectoryDeletedItemCountAsApplication', 
               'Get-MgDirectoryDeletedItemCountAsDevice', 
               'Get-MgDirectoryDeletedItemCountAsGroup', 
               'Get-MgDirectoryDeletedItemCountAsServicePrincipal', 
               'Get-MgDirectoryDeletedItemCountAsUser', 
               'Get-MgDirectoryDeletedItemMemberGroup', 
               'Get-MgDirectoryDeletedItemMemberObject', 
               'Get-MgDirectoryDeviceLocalCredential', 
               'Get-MgDirectoryDeviceLocalCredentialCount', 
               'Get-MgDirectoryFederationConfiguration', 
               'Get-MgDirectoryFederationConfigurationCount', 
               'Get-MgDirectoryOnPremiseSynchronization', 
               'Get-MgDirectoryOnPremiseSynchronizationCount', 
               'Get-MgDirectoryPublicKeyInfrastructure', 
               'Get-MgDirectoryPublicKeyInfrastructureCertificateBasedAuthConfiguration', 
               'Get-MgDirectoryPublicKeyInfrastructureCertificateBasedAuthConfigurationCertificateAuthority', 
               'Get-MgDirectoryPublicKeyInfrastructureCertificateBasedAuthConfigurationCertificateAuthorityCount', 
               'Get-MgDirectoryPublicKeyInfrastructureCertificateBasedAuthConfigurationCount', 
               'Get-MgDirectoryRole', 'Get-MgDirectoryRoleById', 
               'Get-MgDirectoryRoleByRoleTemplateId', 'Get-MgDirectoryRoleCount', 
               'Get-MgDirectoryRoleDelta', 'Get-MgDirectoryRoleMember', 
               'Get-MgDirectoryRoleMemberAsApplication', 
               'Get-MgDirectoryRoleMemberAsDevice', 
               'Get-MgDirectoryRoleMemberAsGroup', 
               'Get-MgDirectoryRoleMemberAsOrgContact', 
               'Get-MgDirectoryRoleMemberAsServicePrincipal', 
               'Get-MgDirectoryRoleMemberAsUser', 'Get-MgDirectoryRoleMemberByRef', 
               'Get-MgDirectoryRoleMemberCount', 
               'Get-MgDirectoryRoleMemberCountAsApplication', 
               'Get-MgDirectoryRoleMemberCountAsDevice', 
               'Get-MgDirectoryRoleMemberCountAsGroup', 
               'Get-MgDirectoryRoleMemberCountAsOrgContact', 
               'Get-MgDirectoryRoleMemberCountAsServicePrincipal', 
               'Get-MgDirectoryRoleMemberCountAsUser', 
               'Get-MgDirectoryRoleMemberGroup', 'Get-MgDirectoryRoleMemberObject', 
               'Get-MgDirectoryRoleScopedMember', 
               'Get-MgDirectoryRoleScopedMemberCount', 
               'Get-MgDirectoryRoleTemplate', 'Get-MgDirectoryRoleTemplateById', 
               'Get-MgDirectoryRoleTemplateCount', 
               'Get-MgDirectoryRoleTemplateDelta', 
               'Get-MgDirectoryRoleTemplateMemberGroup', 
               'Get-MgDirectoryRoleTemplateMemberObject', 
               'Get-MgDirectorySubscription', 
               'Get-MgDirectorySubscriptionByCommerceSubscriptionId', 
               'Get-MgDirectorySubscriptionCount', 'Get-MgDomain', 
               'Get-MgDomainCount', 'Get-MgDomainFederationConfiguration', 
               'Get-MgDomainFederationConfigurationCount', 
               'Get-MgDomainNameReference', 'Get-MgDomainNameReferenceCount', 
               'Get-MgDomainRootDomain', 'Get-MgDomainServiceConfigurationRecord', 
               'Get-MgDomainServiceConfigurationRecordCount', 
               'Get-MgDomainVerificationDnsRecord', 
               'Get-MgDomainVerificationDnsRecordCount', 'Get-MgOrganization', 
               'Get-MgOrganizationBranding', 
               'Get-MgOrganizationBrandingBackgroundImage', 
               'Get-MgOrganizationBrandingBannerLogo', 
               'Get-MgOrganizationBrandingCustomCss', 
               'Get-MgOrganizationBrandingFavicon', 
               'Get-MgOrganizationBrandingHeaderLogo', 
               'Get-MgOrganizationBrandingLocalization', 
               'Get-MgOrganizationBrandingLocalizationBackgroundImage', 
               'Get-MgOrganizationBrandingLocalizationBannerLogo', 
               'Get-MgOrganizationBrandingLocalizationCount', 
               'Get-MgOrganizationBrandingLocalizationCustomCss', 
               'Get-MgOrganizationBrandingLocalizationFavicon', 
               'Get-MgOrganizationBrandingLocalizationHeaderLogo', 
               'Get-MgOrganizationBrandingLocalizationSquareLogo', 
               'Get-MgOrganizationBrandingLocalizationSquareLogoDark', 
               'Get-MgOrganizationBrandingSquareLogo', 
               'Get-MgOrganizationBrandingSquareLogoDark', 
               'Get-MgOrganizationById', 'Get-MgOrganizationCount', 
               'Get-MgOrganizationExtension', 'Get-MgOrganizationExtensionCount', 
               'Get-MgOrganizationMemberGroup', 'Get-MgOrganizationMemberObject', 
               'Get-MgSubscribedSku', 'Get-MgUserScopedRoleMemberOf', 
               'Get-MgUserScopedRoleMemberOfCount', 
               'Invoke-MgAvailableDirectoryFederationConfigurationProviderType', 
               'Invoke-MgForceDomainDelete', 'Invoke-MgPromoteDomain', 
               'Invoke-MgRetryContactServiceProvisioning', 
               'Invoke-MgUploadDirectoryPublicKeyInfrastructureCertificateBasedAuthConfiguration', 
               'New-MgAdminPeopleProfileCardProperty', 'New-MgContract', 
               'New-MgDevice', 'New-MgDeviceExtension', 
               'New-MgDeviceRegisteredOwnerByRef', 
               'New-MgDeviceRegisteredUserByRef', 
               'New-MgDirectoryAdministrativeUnit', 
               'New-MgDirectoryAdministrativeUnitExtension', 
               'New-MgDirectoryAdministrativeUnitMember', 
               'New-MgDirectoryAdministrativeUnitMemberByRef', 
               'New-MgDirectoryAdministrativeUnitScopedRoleMember', 
               'New-MgDirectoryAttributeSet', 
               'New-MgDirectoryCustomSecurityAttributeDefinition', 
               'New-MgDirectoryCustomSecurityAttributeDefinitionAllowedValue', 
               'New-MgDirectoryDeviceLocalCredential', 
               'New-MgDirectoryFederationConfiguration', 
               'New-MgDirectoryOnPremiseSynchronization', 
               'New-MgDirectoryPublicKeyInfrastructureCertificateBasedAuthConfiguration', 
               'New-MgDirectoryPublicKeyInfrastructureCertificateBasedAuthConfigurationCertificateAuthority', 
               'New-MgDirectoryRole', 'New-MgDirectoryRoleMemberByRef', 
               'New-MgDirectoryRoleScopedMember', 'New-MgDirectoryRoleTemplate', 
               'New-MgDirectorySubscription', 'New-MgDomain', 
               'New-MgDomainFederationConfiguration', 
               'New-MgDomainServiceConfigurationRecord', 
               'New-MgDomainVerificationDnsRecord', 'New-MgOrganization', 
               'New-MgOrganizationBrandingLocalization', 
               'New-MgOrganizationExtension', 'New-MgSubscribedSku', 
               'New-MgUserScopedRoleMemberOf', 'Remove-MgAdminPeopleItemInsight', 
               'Remove-MgAdminPeopleProfileCardProperty', 'Remove-MgContract', 
               'Remove-MgDevice', 'Remove-MgDeviceByDeviceId', 
               'Remove-MgDeviceExtension', 
               'Remove-MgDeviceRegisteredOwnerDirectoryObjectByRef', 
               'Remove-MgDeviceRegisteredUserDirectoryObjectByRef', 
               'Remove-MgDirectoryAdministrativeUnit', 
               'Remove-MgDirectoryAdministrativeUnitExtension', 
               'Remove-MgDirectoryAdministrativeUnitMemberDirectoryObjectByRef', 
               'Remove-MgDirectoryAdministrativeUnitScopedRoleMember', 
               'Remove-MgDirectoryAttributeSet', 
               'Remove-MgDirectoryCustomSecurityAttributeDefinition', 
               'Remove-MgDirectoryCustomSecurityAttributeDefinitionAllowedValue', 
               'Remove-MgDirectoryDeletedItem', 
               'Remove-MgDirectoryDeviceLocalCredential', 
               'Remove-MgDirectoryFederationConfiguration', 
               'Remove-MgDirectoryOnPremiseSynchronization', 
               'Remove-MgDirectoryPublicKeyInfrastructure', 
               'Remove-MgDirectoryPublicKeyInfrastructureCertificateBasedAuthConfiguration', 
               'Remove-MgDirectoryPublicKeyInfrastructureCertificateBasedAuthConfigurationCertificateAuthority', 
               'Remove-MgDirectoryRole', 'Remove-MgDirectoryRoleByRoleTemplateId', 
               'Remove-MgDirectoryRoleMemberDirectoryObjectByRef', 
               'Remove-MgDirectoryRoleScopedMember', 
               'Remove-MgDirectoryRoleTemplate', 'Remove-MgDirectorySubscription', 
               'Remove-MgDirectorySubscriptionByCommerceSubscriptionId', 
               'Remove-MgDomain', 'Remove-MgDomainFederationConfiguration', 
               'Remove-MgDomainServiceConfigurationRecord', 
               'Remove-MgDomainVerificationDnsRecord', 'Remove-MgOrganization', 
               'Remove-MgOrganizationBranding', 
               'Remove-MgOrganizationBrandingBackgroundImage', 
               'Remove-MgOrganizationBrandingBannerLogo', 
               'Remove-MgOrganizationBrandingCustomCss', 
               'Remove-MgOrganizationBrandingFavicon', 
               'Remove-MgOrganizationBrandingHeaderLogo', 
               'Remove-MgOrganizationBrandingLocalization', 
               'Remove-MgOrganizationBrandingLocalizationBackgroundImage', 
               'Remove-MgOrganizationBrandingLocalizationBannerLogo', 
               'Remove-MgOrganizationBrandingLocalizationCustomCss', 
               'Remove-MgOrganizationBrandingLocalizationFavicon', 
               'Remove-MgOrganizationBrandingLocalizationHeaderLogo', 
               'Remove-MgOrganizationBrandingLocalizationSquareLogo', 
               'Remove-MgOrganizationBrandingLocalizationSquareLogoDark', 
               'Remove-MgOrganizationBrandingSquareLogo', 
               'Remove-MgOrganizationBrandingSquareLogoDark', 
               'Remove-MgOrganizationExtension', 'Remove-MgSubscribedSku', 
               'Remove-MgUserScopedRoleMemberOf', 'Restore-MgDirectoryDeletedItem', 
               'Set-MgOrganizationBrandingBackgroundImage', 
               'Set-MgOrganizationBrandingBannerLogo', 
               'Set-MgOrganizationBrandingCustomCss', 
               'Set-MgOrganizationBrandingFavicon', 
               'Set-MgOrganizationBrandingHeaderLogo', 
               'Set-MgOrganizationBrandingLocalizationBackgroundImage', 
               'Set-MgOrganizationBrandingLocalizationBannerLogo', 
               'Set-MgOrganizationBrandingLocalizationCustomCss', 
               'Set-MgOrganizationBrandingLocalizationFavicon', 
               'Set-MgOrganizationBrandingLocalizationHeaderLogo', 
               'Set-MgOrganizationBrandingLocalizationSquareLogo', 
               'Set-MgOrganizationBrandingLocalizationSquareLogoDark', 
               'Set-MgOrganizationBrandingSquareLogo', 
               'Set-MgOrganizationBrandingSquareLogoDark', 
               'Set-MgOrganizationMobileDeviceManagementAuthority', 
               'Test-MgContactProperty', 'Test-MgContractProperty', 
               'Test-MgDeviceProperty', 'Test-MgDirectoryDeletedItemProperty', 
               'Test-MgDirectoryRoleProperty', 
               'Test-MgDirectoryRoleTemplateProperty', 
               'Test-MgOrganizationProperty', 'Update-MgAdminPeopleItemInsight', 
               'Update-MgAdminPeopleProfileCardProperty', 
               'Update-MgAdminPeoplePronoun', 'Update-MgContract', 'Update-MgDevice', 
               'Update-MgDeviceByDeviceId', 'Update-MgDeviceExtension', 
               'Update-MgDirectory', 'Update-MgDirectoryAdministrativeUnit', 
               'Update-MgDirectoryAdministrativeUnitExtension', 
               'Update-MgDirectoryAdministrativeUnitScopedRoleMember', 
               'Update-MgDirectoryAttributeSet', 
               'Update-MgDirectoryCustomSecurityAttributeDefinition', 
               'Update-MgDirectoryCustomSecurityAttributeDefinitionAllowedValue', 
               'Update-MgDirectoryDeviceLocalCredential', 
               'Update-MgDirectoryFederationConfiguration', 
               'Update-MgDirectoryOnPremiseSynchronization', 
               'Update-MgDirectoryPublicKeyInfrastructure', 
               'Update-MgDirectoryPublicKeyInfrastructureCertificateBasedAuthConfiguration', 
               'Update-MgDirectoryPublicKeyInfrastructureCertificateBasedAuthConfigurationCertificateAuthority', 
               'Update-MgDirectoryRole', 'Update-MgDirectoryRoleByRoleTemplateId', 
               'Update-MgDirectoryRoleScopedMember', 
               'Update-MgDirectoryRoleTemplate', 'Update-MgDirectorySubscription', 
               'Update-MgDirectorySubscriptionByCommerceSubscriptionId', 
               'Update-MgDomain', 'Update-MgDomainFederationConfiguration', 
               'Update-MgDomainServiceConfigurationRecord', 
               'Update-MgDomainVerificationDnsRecord', 'Update-MgOrganization', 
               'Update-MgOrganizationBranding', 
               'Update-MgOrganizationBrandingLocalization', 
               'Update-MgOrganizationExtension', 'Update-MgSubscribedSku', 
               'Update-MgUserScopedRoleMemberOf'

# Cmdlets to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no cmdlets to export.
CmdletsToExport = @()

# Variables to export from this module
# VariablesToExport = @()

# Aliases to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no aliases to export.
AliasesToExport = 'Get-MgDirectoryDeletedAdministrativeUnit', 
               'Get-MgDirectoryDeletedApplication', 'Get-MgDirectoryDeletedDevice', 
               'Get-MgDirectoryDeletedGroup', 
               'Get-MgDirectoryDeletedServicePrincipal', 
               'Get-MgDirectoryDeletedUser', 'Remove-MgDeviceRegisteredOwnerByRef', 
               'Remove-MgDeviceRegisteredUserByRef', 
               'Remove-MgDirectoryAdministrativeUnitMemberByRef', 
               'Remove-MgDirectoryRoleMemberByRef'

# DSC resources to export from this module
# DscResourcesToExport = @()

# List of all modules packaged with this module
# ModuleList = @()

# List of all files packaged with this module
# FileList = @()

# Private data to pass to the module specified in RootModule/ModuleToProcess. This may also contain a PSData hashtable with additional module metadata used by PowerShell.
PrivateData = @{

    PSData = @{

        # Tags applied to this module. These help with module discovery in online galleries.
        Tags = 'Microsoft','Office365','Graph','PowerShell','PSModule','PSIncludes_Cmdlet'

        # A URL to the license for this module.
        LicenseUri = 'https://aka.ms/devservicesagreement'

        # A URL to the main website for this project.
        ProjectUri = 'https://github.com/microsoftgraph/msgraph-sdk-powershell'

        # A URL to an icon representing this module.
        IconUri = 'https://raw.githubusercontent.com/microsoftgraph/msgraph-sdk-powershell/dev/docs/images/graph_color256.png'

        # ReleaseNotes of this module
        ReleaseNotes = 'See https://aka.ms/GraphPowerShell-Release.'

        # Prerelease string of this module
        # Prerelease = ''

        # Flag to indicate whether the module requires explicit user acceptance for install/update/save
        # RequireLicenseAcceptance = $false

        # External dependent modules of this module
        # ExternalModuleDependencies = @()

    } # End of PSData hashtable

 } # End of PrivateData hashtable

# HelpInfo URI of this module
# HelpInfoURI = ''

# Default prefix for commands exported from this module. Override the default prefix using Import-Module -Prefix.
# DefaultCommandPrefix = ''

}


# SIG # Begin signature block
# MIIoLQYJKoZIhvcNAQcCoIIoHjCCKBoCAQExDzANBglghkgBZQMEAgEFADB5Bgor
# BgEEAYI3AgEEoGswaTA0BgorBgEEAYI3AgEeMCYCAwEAAAQQH8w7YFlLCE63JNLG
# KX7zUQIBAAIBAAIBAAIBAAIBADAxMA0GCWCGSAFlAwQCAQUABCAcy/vhGkHmxPLq
# sZ/VWdSaCMj06Tzl/K8PT+NuZBlKLKCCDXYwggX0MIID3KADAgECAhMzAAAEBGx0
# Bv9XKydyAAAAAAQEMA0GCSqGSIb3DQEBCwUAMH4xCzAJBgNVBAYTAlVTMRMwEQYD
# VQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNy
# b3NvZnQgQ29ycG9yYXRpb24xKDAmBgNVBAMTH01pY3Jvc29mdCBDb2RlIFNpZ25p
# bmcgUENBIDIwMTEwHhcNMjQwOTEyMjAxMTE0WhcNMjUwOTExMjAxMTE0WjB0MQsw
# CQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9u
# ZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMR4wHAYDVQQDExVNaWNy
# b3NvZnQgQ29ycG9yYXRpb24wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIB
# AQC0KDfaY50MDqsEGdlIzDHBd6CqIMRQWW9Af1LHDDTuFjfDsvna0nEuDSYJmNyz
# NB10jpbg0lhvkT1AzfX2TLITSXwS8D+mBzGCWMM/wTpciWBV/pbjSazbzoKvRrNo
# DV/u9omOM2Eawyo5JJJdNkM2d8qzkQ0bRuRd4HarmGunSouyb9NY7egWN5E5lUc3
# a2AROzAdHdYpObpCOdeAY2P5XqtJkk79aROpzw16wCjdSn8qMzCBzR7rvH2WVkvF
# HLIxZQET1yhPb6lRmpgBQNnzidHV2Ocxjc8wNiIDzgbDkmlx54QPfw7RwQi8p1fy
# 4byhBrTjv568x8NGv3gwb0RbAgMBAAGjggFzMIIBbzAfBgNVHSUEGDAWBgorBgEE
# AYI3TAgBBggrBgEFBQcDAzAdBgNVHQ4EFgQU8huhNbETDU+ZWllL4DNMPCijEU4w
# RQYDVR0RBD4wPKQ6MDgxHjAcBgNVBAsTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEW
# MBQGA1UEBRMNMjMwMDEyKzUwMjkyMzAfBgNVHSMEGDAWgBRIbmTlUAXTgqoXNzci
# tW2oynUClTBUBgNVHR8ETTBLMEmgR6BFhkNodHRwOi8vd3d3Lm1pY3Jvc29mdC5j
# b20vcGtpb3BzL2NybC9NaWNDb2RTaWdQQ0EyMDExXzIwMTEtMDctMDguY3JsMGEG
# CCsGAQUFBwEBBFUwUzBRBggrBgEFBQcwAoZFaHR0cDovL3d3dy5taWNyb3NvZnQu
# Y29tL3BraW9wcy9jZXJ0cy9NaWNDb2RTaWdQQ0EyMDExXzIwMTEtMDctMDguY3J0
# MAwGA1UdEwEB/wQCMAAwDQYJKoZIhvcNAQELBQADggIBAIjmD9IpQVvfB1QehvpC
# Ge7QeTQkKQ7j3bmDMjwSqFL4ri6ae9IFTdpywn5smmtSIyKYDn3/nHtaEn0X1NBj
# L5oP0BjAy1sqxD+uy35B+V8wv5GrxhMDJP8l2QjLtH/UglSTIhLqyt8bUAqVfyfp
# h4COMRvwwjTvChtCnUXXACuCXYHWalOoc0OU2oGN+mPJIJJxaNQc1sjBsMbGIWv3
# cmgSHkCEmrMv7yaidpePt6V+yPMik+eXw3IfZ5eNOiNgL1rZzgSJfTnvUqiaEQ0X
# dG1HbkDv9fv6CTq6m4Ty3IzLiwGSXYxRIXTxT4TYs5VxHy2uFjFXWVSL0J2ARTYL
# E4Oyl1wXDF1PX4bxg1yDMfKPHcE1Ijic5lx1KdK1SkaEJdto4hd++05J9Bf9TAmi
# u6EK6C9Oe5vRadroJCK26uCUI4zIjL/qG7mswW+qT0CW0gnR9JHkXCWNbo8ccMk1
# sJatmRoSAifbgzaYbUz8+lv+IXy5GFuAmLnNbGjacB3IMGpa+lbFgih57/fIhamq
# 5VhxgaEmn/UjWyr+cPiAFWuTVIpfsOjbEAww75wURNM1Imp9NJKye1O24EspEHmb
# DmqCUcq7NqkOKIG4PVm3hDDED/WQpzJDkvu4FrIbvyTGVU01vKsg4UfcdiZ0fQ+/
# V0hf8yrtq9CkB8iIuk5bBxuPMIIHejCCBWKgAwIBAgIKYQ6Q0gAAAAAAAzANBgkq
# hkiG9w0BAQsFADCBiDELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldhc2hpbmd0b24x
# EDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlv
# bjEyMDAGA1UEAxMpTWljcm9zb2Z0IFJvb3QgQ2VydGlmaWNhdGUgQXV0aG9yaXR5
# IDIwMTEwHhcNMTEwNzA4MjA1OTA5WhcNMjYwNzA4MjEwOTA5WjB+MQswCQYDVQQG
# EwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwG
# A1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSgwJgYDVQQDEx9NaWNyb3NvZnQg
# Q29kZSBTaWduaW5nIFBDQSAyMDExMIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIIC
# CgKCAgEAq/D6chAcLq3YbqqCEE00uvK2WCGfQhsqa+laUKq4BjgaBEm6f8MMHt03
# a8YS2AvwOMKZBrDIOdUBFDFC04kNeWSHfpRgJGyvnkmc6Whe0t+bU7IKLMOv2akr
# rnoJr9eWWcpgGgXpZnboMlImEi/nqwhQz7NEt13YxC4Ddato88tt8zpcoRb0Rrrg
# OGSsbmQ1eKagYw8t00CT+OPeBw3VXHmlSSnnDb6gE3e+lD3v++MrWhAfTVYoonpy
# 4BI6t0le2O3tQ5GD2Xuye4Yb2T6xjF3oiU+EGvKhL1nkkDstrjNYxbc+/jLTswM9
# sbKvkjh+0p2ALPVOVpEhNSXDOW5kf1O6nA+tGSOEy/S6A4aN91/w0FK/jJSHvMAh
# dCVfGCi2zCcoOCWYOUo2z3yxkq4cI6epZuxhH2rhKEmdX4jiJV3TIUs+UsS1Vz8k
# A/DRelsv1SPjcF0PUUZ3s/gA4bysAoJf28AVs70b1FVL5zmhD+kjSbwYuER8ReTB
# w3J64HLnJN+/RpnF78IcV9uDjexNSTCnq47f7Fufr/zdsGbiwZeBe+3W7UvnSSmn
# Eyimp31ngOaKYnhfsi+E11ecXL93KCjx7W3DKI8sj0A3T8HhhUSJxAlMxdSlQy90
# lfdu+HggWCwTXWCVmj5PM4TasIgX3p5O9JawvEagbJjS4NaIjAsCAwEAAaOCAe0w
# ggHpMBAGCSsGAQQBgjcVAQQDAgEAMB0GA1UdDgQWBBRIbmTlUAXTgqoXNzcitW2o
# ynUClTAZBgkrBgEEAYI3FAIEDB4KAFMAdQBiAEMAQTALBgNVHQ8EBAMCAYYwDwYD
# VR0TAQH/BAUwAwEB/zAfBgNVHSMEGDAWgBRyLToCMZBDuRQFTuHqp8cx0SOJNDBa
# BgNVHR8EUzBRME+gTaBLhklodHRwOi8vY3JsLm1pY3Jvc29mdC5jb20vcGtpL2Ny
# bC9wcm9kdWN0cy9NaWNSb29DZXJBdXQyMDExXzIwMTFfMDNfMjIuY3JsMF4GCCsG
# AQUFBwEBBFIwUDBOBggrBgEFBQcwAoZCaHR0cDovL3d3dy5taWNyb3NvZnQuY29t
# L3BraS9jZXJ0cy9NaWNSb29DZXJBdXQyMDExXzIwMTFfMDNfMjIuY3J0MIGfBgNV
# HSAEgZcwgZQwgZEGCSsGAQQBgjcuAzCBgzA/BggrBgEFBQcCARYzaHR0cDovL3d3
# dy5taWNyb3NvZnQuY29tL3BraW9wcy9kb2NzL3ByaW1hcnljcHMuaHRtMEAGCCsG
# AQUFBwICMDQeMiAdAEwAZQBnAGEAbABfAHAAbwBsAGkAYwB5AF8AcwB0AGEAdABl
# AG0AZQBuAHQALiAdMA0GCSqGSIb3DQEBCwUAA4ICAQBn8oalmOBUeRou09h0ZyKb
# C5YR4WOSmUKWfdJ5DJDBZV8uLD74w3LRbYP+vj/oCso7v0epo/Np22O/IjWll11l
# hJB9i0ZQVdgMknzSGksc8zxCi1LQsP1r4z4HLimb5j0bpdS1HXeUOeLpZMlEPXh6
# I/MTfaaQdION9MsmAkYqwooQu6SpBQyb7Wj6aC6VoCo/KmtYSWMfCWluWpiW5IP0
# wI/zRive/DvQvTXvbiWu5a8n7dDd8w6vmSiXmE0OPQvyCInWH8MyGOLwxS3OW560
# STkKxgrCxq2u5bLZ2xWIUUVYODJxJxp/sfQn+N4sOiBpmLJZiWhub6e3dMNABQam
# ASooPoI/E01mC8CzTfXhj38cbxV9Rad25UAqZaPDXVJihsMdYzaXht/a8/jyFqGa
# J+HNpZfQ7l1jQeNbB5yHPgZ3BtEGsXUfFL5hYbXw3MYbBL7fQccOKO7eZS/sl/ah
# XJbYANahRr1Z85elCUtIEJmAH9AAKcWxm6U/RXceNcbSoqKfenoi+kiVH6v7RyOA
# 9Z74v2u3S5fi63V4GuzqN5l5GEv/1rMjaHXmr/r8i+sLgOppO6/8MO0ETI7f33Vt
# Y5E90Z1WTk+/gFcioXgRMiF670EKsT/7qMykXcGhiJtXcVZOSEXAQsmbdlsKgEhr
# /Xmfwb1tbWrJUnMTDXpQzTGCGg0wghoJAgEBMIGVMH4xCzAJBgNVBAYTAlVTMRMw
# EQYDVQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVN
# aWNyb3NvZnQgQ29ycG9yYXRpb24xKDAmBgNVBAMTH01pY3Jvc29mdCBDb2RlIFNp
# Z25pbmcgUENBIDIwMTECEzMAAAQEbHQG/1crJ3IAAAAABAQwDQYJYIZIAWUDBAIB
# BQCgga4wGQYJKoZIhvcNAQkDMQwGCisGAQQBgjcCAQQwHAYKKwYBBAGCNwIBCzEO
# MAwGCisGAQQBgjcCARUwLwYJKoZIhvcNAQkEMSIEIMNchP1Hnt946AJ7SHhlRqwt
# WPmRWklR8RlSlw0C2fyIMEIGCisGAQQBgjcCAQwxNDAyoBSAEgBNAGkAYwByAG8A
# cwBvAGYAdKEagBhodHRwOi8vd3d3Lm1pY3Jvc29mdC5jb20wDQYJKoZIhvcNAQEB
# BQAEggEAZL7e4XxQnQgCezLcqk1aGdTFodAk3BPXxhC3QmvmfhV3cZsT+KaxNNlv
# ZJj4xopTkTGprsZpc9gLss1ap0TNB9MaLuB3PLwhi0Jxp1ABQmS+C02UKhD/MLdl
# Yd3QHE5kCCGGVox/bjfOZSsVCCeMDL0ppr8duyWX5gQ3Bc6wuSLNLLQ0Y2EbvsOm
# 5+kzbW4AsySM/8GgnoSUV3XNCr9scs9L0ccRG3SAu2p61rAOM5Avs16TY9+PAf8h
# GI6x+brGJalF+nOGa+EzBVRotHncW4IP3FKyf87GSE9FiRyKuYR/uk/dR3yrRyRp
# aREL8Jguhhk0HhgKA/0zV/9vH/9Wm6GCF5cwgheTBgorBgEEAYI3AwMBMYIXgzCC
# F38GCSqGSIb3DQEHAqCCF3AwghdsAgEDMQ8wDQYJYIZIAWUDBAIBBQAwggFSBgsq
# hkiG9w0BCRABBKCCAUEEggE9MIIBOQIBAQYKKwYBBAGEWQoDATAxMA0GCWCGSAFl
# AwQCAQUABCCByBnwNBzdUHqfwAkuyLwqnusy7trlYnFgcl5MJfmVewIGaEtBjnKz
# GBMyMDI1MDcwOTExMDcyNC44NDhaMASAAgH0oIHRpIHOMIHLMQswCQYDVQQGEwJV
# UzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwGA1UE
# ChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSUwIwYDVQQLExxNaWNyb3NvZnQgQW1l
# cmljYSBPcGVyYXRpb25zMScwJQYDVQQLEx5uU2hpZWxkIFRTUyBFU046RjAwMi0w
# NUUwLUQ5NDcxJTAjBgNVBAMTHE1pY3Jvc29mdCBUaW1lLVN0YW1wIFNlcnZpY2Wg
# ghHtMIIHIDCCBQigAwIBAgITMwAAAgU8dWyCRIfN/gABAAACBTANBgkqhkiG9w0B
# AQsFADB8MQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UE
# BxMHUmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSYwJAYD
# VQQDEx1NaWNyb3NvZnQgVGltZS1TdGFtcCBQQ0EgMjAxMDAeFw0yNTAxMzAxOTQy
# NDlaFw0yNjA0MjIxOTQyNDlaMIHLMQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2Fz
# aGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENv
# cnBvcmF0aW9uMSUwIwYDVQQLExxNaWNyb3NvZnQgQW1lcmljYSBPcGVyYXRpb25z
# MScwJQYDVQQLEx5uU2hpZWxkIFRTUyBFU046RjAwMi0wNUUwLUQ5NDcxJTAjBgNV
# BAMTHE1pY3Jvc29mdCBUaW1lLVN0YW1wIFNlcnZpY2UwggIiMA0GCSqGSIb3DQEB
# AQUAA4ICDwAwggIKAoICAQCSkvLfd7gF1r2wGdy85CFYXHUC8ywEyD4LRLv0WYEX
# eeZ0u5YuK7p2cXVzQmZPOHTN8TWqG2SPlUb+7PldzFDDAlR3vU8piOjmhu9rHW43
# M2dbor9jl9gluhzwUd2SciVGa7f9t67tM3KFKRSMXFtHKF3KwBB7aVo+b1qy5p9D
# Wlo2N5FGrBqHMEVlNyzreHYoDLL+m8fSsqMu/iYUqxzK5F4S7IY5NemAB8B+A3Qg
# wVIi64KJIfeKZUeiWKCTf4odUgP3AQilxh48P6z7AT4IA0dMEtKhYLFs4W/KNDMs
# Yr7KpQPKVCcC5E8uDHdKewubyzenkTxy4ff1N3g8yho5Pi9BfjR0VytrkmpDfep8
# JPwcb4BNOIXOo1pfdHZ8EvnR7JFZFQiqpMZFlO5CAuTYH8ujc5PUHlaMAJ8NEa9T
# FJTOSBrB7PRgeh/6NJ2xu9yxPh/kVN9BGss93MC6UjpoxeM4x70bwbwiK8SNHIO8
# D8cql7VSevUYbjN4NogFFwhBClhodE/zeGPq6y6ixD4z65IHY3zwFQbBVX/w+L/V
# HNn/BMGs2PGHnlRjO/Kk8NIpN4shkFQqA1fM08frrDSNEY9VKDtpsUpAF51Y1oQ6
# tJhWM1d3neCXh6b/6N+XeHORCwnY83K+pFMMhg8isXQb6KRl65kg8XYBd4JwkbKo
# VQIDAQABo4IBSTCCAUUwHQYDVR0OBBYEFHR6Wrs27b6+yJ3bEZ9o5NdL1bLwMB8G
# A1UdIwQYMBaAFJ+nFV0AXmJdg/Tl0mWnG1M1GelyMF8GA1UdHwRYMFYwVKBSoFCG
# Tmh0dHA6Ly93d3cubWljcm9zb2Z0LmNvbS9wa2lvcHMvY3JsL01pY3Jvc29mdCUy
# MFRpbWUtU3RhbXAlMjBQQ0ElMjAyMDEwKDEpLmNybDBsBggrBgEFBQcBAQRgMF4w
# XAYIKwYBBQUHMAKGUGh0dHA6Ly93d3cubWljcm9zb2Z0LmNvbS9wa2lvcHMvY2Vy
# dHMvTWljcm9zb2Z0JTIwVGltZS1TdGFtcCUyMFBDQSUyMDIwMTAoMSkuY3J0MAwG
# A1UdEwEB/wQCMAAwFgYDVR0lAQH/BAwwCgYIKwYBBQUHAwgwDgYDVR0PAQH/BAQD
# AgeAMA0GCSqGSIb3DQEBCwUAA4ICAQAOuxk47b1i75V81Tx6xo10xNIr4zZxYVfk
# F5TFq2kndPHgzVyLnssw/HKkEZRCgZVpkKEJ6Y4jvG5tugMi+Wjt7hUMSipk+RpB
# 5gFQvh1xmAEL2flegzTWEsnj0wrESplI5Z3vgf2eGXAr/RcqGjSpouHbD2HY9Y3F
# 0Ol6FRDCV/HEGKRHzn2M5rQpFGSjacT4DkqVYmem/ArOfSvVojnKEIW914UxGtuh
# JSr9jOo5RqTX7GIqbtvN7zhWld+i3XxdhdNcflQz9YhoFqQexBenoIRgAPAtwH68
# xczr9LMC3l9ALEpnsvO0RiKPXF4l22/OfcFffaphnl/TDwkiJfxOyAMfUF3xI9+3
# izT1WX2CFs2RaOAq3dcohyJw+xRG0E8wkCHqkV57BbUBEzLX8L9lGJ1DoxYNpoDX
# 7iQzJ9Qdkypi5fv773E3Ch8A+toxeFp6FifQZyCc8IcIBlHyak6MbT6YTVQNgQ/h
# 8FF+S5OqP7CECFvIH2Kt2P0GlOu9C0BfashnTjodmtZFZsptUvirk/2HOLLjBiMj
# DwJsQAFAzJuz4ZtTyorrvER10Gl/mbmViHqhvNACfTzPiLfjDgyvp9s7/bHu/Cal
# KmeiJULGjh/lwAj5319pggsGJqbhJ4FbFc+oU5zffbm/rKjVZ8kxND3im10Qp41n
# 2t/qpyP6ETCCB3EwggVZoAMCAQICEzMAAAAVxedrngKbSZkAAAAAABUwDQYJKoZI
# hvcNAQELBQAwgYgxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNoaW5ndG9uMRAw
# DgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNyb3NvZnQgQ29ycG9yYXRpb24x
# MjAwBgNVBAMTKU1pY3Jvc29mdCBSb290IENlcnRpZmljYXRlIEF1dGhvcml0eSAy
# MDEwMB4XDTIxMDkzMDE4MjIyNVoXDTMwMDkzMDE4MzIyNVowfDELMAkGA1UEBhMC
# VVMxEzARBgNVBAgTCldhc2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNV
# BAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEmMCQGA1UEAxMdTWljcm9zb2Z0IFRp
# bWUtU3RhbXAgUENBIDIwMTAwggIiMA0GCSqGSIb3DQEBAQUAA4ICDwAwggIKAoIC
# AQDk4aZM57RyIQt5osvXJHm9DtWC0/3unAcH0qlsTnXIyjVX9gF/bErg4r25Phdg
# M/9cT8dm95VTcVrifkpa/rg2Z4VGIwy1jRPPdzLAEBjoYH1qUoNEt6aORmsHFPPF
# dvWGUNzBRMhxXFExN6AKOG6N7dcP2CZTfDlhAnrEqv1yaa8dq6z2Nr41JmTamDu6
# GnszrYBbfowQHJ1S/rboYiXcag/PXfT+jlPP1uyFVk3v3byNpOORj7I5LFGc6XBp
# Dco2LXCOMcg1KL3jtIckw+DJj361VI/c+gVVmG1oO5pGve2krnopN6zL64NF50Zu
# yjLVwIYwXE8s4mKyzbnijYjklqwBSru+cakXW2dg3viSkR4dPf0gz3N9QZpGdc3E
# XzTdEonW/aUgfX782Z5F37ZyL9t9X4C626p+Nuw2TPYrbqgSUei/BQOj0XOmTTd0
# lBw0gg/wEPK3Rxjtp+iZfD9M269ewvPV2HM9Q07BMzlMjgK8QmguEOqEUUbi0b1q
# GFphAXPKZ6Je1yh2AuIzGHLXpyDwwvoSCtdjbwzJNmSLW6CmgyFdXzB0kZSU2LlQ
# +QuJYfM2BjUYhEfb3BvR/bLUHMVr9lxSUV0S2yW6r1AFemzFER1y7435UsSFF5PA
# PBXbGjfHCBUYP3irRbb1Hode2o+eFnJpxq57t7c+auIurQIDAQABo4IB3TCCAdkw
# EgYJKwYBBAGCNxUBBAUCAwEAATAjBgkrBgEEAYI3FQIEFgQUKqdS/mTEmr6CkTxG
# NSnPEP8vBO4wHQYDVR0OBBYEFJ+nFV0AXmJdg/Tl0mWnG1M1GelyMFwGA1UdIARV
# MFMwUQYMKwYBBAGCN0yDfQEBMEEwPwYIKwYBBQUHAgEWM2h0dHA6Ly93d3cubWlj
# cm9zb2Z0LmNvbS9wa2lvcHMvRG9jcy9SZXBvc2l0b3J5Lmh0bTATBgNVHSUEDDAK
# BggrBgEFBQcDCDAZBgkrBgEEAYI3FAIEDB4KAFMAdQBiAEMAQTALBgNVHQ8EBAMC
# AYYwDwYDVR0TAQH/BAUwAwEB/zAfBgNVHSMEGDAWgBTV9lbLj+iiXGJo0T2UkFvX
# zpoYxDBWBgNVHR8ETzBNMEugSaBHhkVodHRwOi8vY3JsLm1pY3Jvc29mdC5jb20v
# cGtpL2NybC9wcm9kdWN0cy9NaWNSb29DZXJBdXRfMjAxMC0wNi0yMy5jcmwwWgYI
# KwYBBQUHAQEETjBMMEoGCCsGAQUFBzAChj5odHRwOi8vd3d3Lm1pY3Jvc29mdC5j
# b20vcGtpL2NlcnRzL01pY1Jvb0NlckF1dF8yMDEwLTA2LTIzLmNydDANBgkqhkiG
# 9w0BAQsFAAOCAgEAnVV9/Cqt4SwfZwExJFvhnnJL/Klv6lwUtj5OR2R4sQaTlz0x
# M7U518JxNj/aZGx80HU5bbsPMeTCj/ts0aGUGCLu6WZnOlNN3Zi6th542DYunKmC
# VgADsAW+iehp4LoJ7nvfam++Kctu2D9IdQHZGN5tggz1bSNU5HhTdSRXud2f8449
# xvNo32X2pFaq95W2KFUn0CS9QKC/GbYSEhFdPSfgQJY4rPf5KYnDvBewVIVCs/wM
# nosZiefwC2qBwoEZQhlSdYo2wh3DYXMuLGt7bj8sCXgU6ZGyqVvfSaN0DLzskYDS
# PeZKPmY7T7uG+jIa2Zb0j/aRAfbOxnT99kxybxCrdTDFNLB62FD+CljdQDzHVG2d
# Y3RILLFORy3BFARxv2T5JL5zbcqOCb2zAVdJVGTZc9d/HltEAY5aGZFrDZ+kKNxn
# GSgkujhLmm77IVRrakURR6nxt67I6IleT53S0Ex2tVdUCbFpAUR+fKFhbHP+Crvs
# QWY9af3LwUFJfn6Tvsv4O+S3Fb+0zj6lMVGEvL8CwYKiexcdFYmNcP7ntdAoGokL
# jzbaukz5m/8K6TT4JDVnK+ANuOaMmdbhIurwJ0I9JZTmdHRbatGePu1+oDEzfbzL
# 6Xu/OHBE0ZDxyKs6ijoIYn/ZcGNTTY3ugm2lBRDBcQZqELQdVTNYs6FwZvKhggNQ
# MIICOAIBATCB+aGB0aSBzjCByzELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldhc2hp
# bmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBDb3Jw
# b3JhdGlvbjElMCMGA1UECxMcTWljcm9zb2Z0IEFtZXJpY2EgT3BlcmF0aW9uczEn
# MCUGA1UECxMeblNoaWVsZCBUU1MgRVNOOkYwMDItMDVFMC1EOTQ3MSUwIwYDVQQD
# ExxNaWNyb3NvZnQgVGltZS1TdGFtcCBTZXJ2aWNloiMKAQEwBwYFKw4DAhoDFQDV
# sH9p1tJn+krwCMvqOhVvXrbetKCBgzCBgKR+MHwxCzAJBgNVBAYTAlVTMRMwEQYD
# VQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNy
# b3NvZnQgQ29ycG9yYXRpb24xJjAkBgNVBAMTHU1pY3Jvc29mdCBUaW1lLVN0YW1w
# IFBDQSAyMDEwMA0GCSqGSIb3DQEBCwUAAgUA7BitjDAiGA8yMDI1MDcwOTA4NTc0
# OFoYDzIwMjUwNzEwMDg1NzQ4WjB3MD0GCisGAQQBhFkKBAExLzAtMAoCBQDsGK2M
# AgEAMAoCAQACAhReAgH/MAcCAQACAhQqMAoCBQDsGf8MAgEAMDYGCisGAQQBhFkK
# BAIxKDAmMAwGCisGAQQBhFkKAwKgCjAIAgEAAgMHoSChCjAIAgEAAgMBhqAwDQYJ
# KoZIhvcNAQELBQADggEBAAnCDz+qPDz/0mPci6H23LSSoj6TxgiCLL+tb1jhrfYT
# TtoJo4pxQ9fhWXHrJvBlFQmBnH0v3Zy6QeBmF5ix4cl1lBFuIyzFoM+rkP20mekP
# o4BlymGRHjNzsBHCnKjSstQPno+B6fs+QYgWgs9wVXOL7icTW2tTTydkC9lr1yBk
# 58xojq6blHspc9d4I3uecXlSCzHu9l7puYyXFt+01YhY5fL/YOA5/YQcnSSCc6H3
# ieW53yrn0dwvt8j+mexUyaMlgOzI/UyjE3fS/bWhYmLh5y6FC7+c5ljqqI0uElRi
# zuYhNmQasygstNj1fMdVWwL8n+HwooUpXKiAtPzjIT4xggQNMIIECQIBATCBkzB8
# MQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVk
# bW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSYwJAYDVQQDEx1N
# aWNyb3NvZnQgVGltZS1TdGFtcCBQQ0EgMjAxMAITMwAAAgU8dWyCRIfN/gABAAAC
# BTANBglghkgBZQMEAgEFAKCCAUowGgYJKoZIhvcNAQkDMQ0GCyqGSIb3DQEJEAEE
# MC8GCSqGSIb3DQEJBDEiBCBWxMJEgIIDRChwKFUGRx6kGYdZGdjHlxtb+gSqBoQ1
# bzCB+gYLKoZIhvcNAQkQAi8xgeowgecwgeQwgb0EIIANAz3ceY0umhdWLR2sJpq0
# OPqtJDTAYRmjHVkwEW9IMIGYMIGApH4wfDELMAkGA1UEBhMCVVMxEzARBgNVBAgT
# Cldhc2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29m
# dCBDb3Jwb3JhdGlvbjEmMCQGA1UEAxMdTWljcm9zb2Z0IFRpbWUtU3RhbXAgUENB
# IDIwMTACEzMAAAIFPHVsgkSHzf4AAQAAAgUwIgQgh/pUJm/ruCVusNEKt9+okLiz
# 20HccxEppmuHA87uybgwDQYJKoZIhvcNAQELBQAEggIACBx4Zexxi162EpXPtFxS
# 8v6uVSe7nwP5X3o6gNiLZWeiIWfc94ruARVyKEjvDizjSuYCa8acWueeadIdEQ2o
# A9/266EWk+hIxcD+Khhq0u1JBYNEKjiPqAynHNfDYrwjMpS77G5LDw+qpelFoodP
# u77+a6q5ZQ2Hka1fwHB0h422G17jWXa60o5HPOJS5MAsYaDpvdAJyGBpH10SsIIX
# oN7I0/So76xtrbSuLARnCDQlK9q+G9ekfkbyroazNIelu4MefuFpshzs99n5Dc4w
# sDr93nYa3G/sgorv6xexAGwRz3dl00JaumncDmPZsVrAHQV+e81f4fI3uU83fzrt
# J0c5qrGIiaQv28VOX9VmR8xoNnINeeJXj2ZmmzgO4T//NuBtmlTMlJ+lGDqjQqsT
# ywBmcS/Nn5gkTQZ5o4nvo3uHJn0ojuzbmHO+f6pZYILA76NVuC7jklZtrmuN50gy
# PBTjNd4NsOSF6Dw4wv2M2jn/vVVOn7g3fqPOw+ppJzTZUhU/3SpW3nc0SrLXaxoY
# Heyy9bhxBlch4x8LJk3DfF+1VvHaRiaHIgUlwkNkxzu/Fd4F9NAJpZGzQZyvksqo
# 8WNTbUoUBZBSWIIKyLn7I4PSdOWaAA0kl+aFggrHy2KzddOhoxDXJsBYMuvP+mDE
# RgVHX7mF6LzjkkychJ6BBPE=
# SIG # End signature block
