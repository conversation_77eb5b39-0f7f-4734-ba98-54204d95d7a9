#
# Module manifest for module 'Microsoft.Graph.Teams'
#
# Generated by: Microsoft Corporation
#
# Generated on: 7/9/2025
#

@{

# Script module or binary module file associated with this manifest.
RootModule = './Microsoft.Graph.Teams.psm1'

# Version number of this module.
ModuleVersion = '2.29.0'

# Supported PSEditions
CompatiblePSEditions = 'Core', 'Desktop'

# ID used to uniquely identify this module
GUID = 'f8619bb2-8640-4d8d-baf5-0829db98fbe2'

# Author of this module
Author = 'Microsoft Corporation'

# Company or vendor of this module
CompanyName = 'Microsoft Corporation'

# Copyright statement for this module
Copyright = 'Microsoft Corporation. All rights reserved.'

# Description of the functionality provided by this module
Description = 'Microsoft Graph PowerShell Cmdlets'

# Minimum version of the PowerShell engine required by this module
PowerShellVersion = '5.1'

# Name of the PowerShell host required by this module
# PowerShellHostName = ''

# Minimum version of the PowerShell host required by this module
# PowerShellHostVersion = ''

# Minimum version of Microsoft .NET Framework required by this module. This prerequisite is valid for the PowerShell Desktop edition only.
DotNetFrameworkVersion = '4.7.2'

# Minimum version of the common language runtime (CLR) required by this module. This prerequisite is valid for the PowerShell Desktop edition only.
# ClrVersion = ''

# Processor architecture (None, X86, Amd64) required by this module
# ProcessorArchitecture = ''

# Modules that must be imported into the global environment prior to importing this module
RequiredModules = @(@{ModuleName = 'Microsoft.Graph.Authentication'; RequiredVersion = '2.29.0'; })

# Assemblies that must be loaded prior to importing this module
RequiredAssemblies = './bin/Microsoft.Graph.Teams.private.dll'

# Script files (.ps1) that are run in the caller's environment prior to importing this module.
# ScriptsToProcess = @()

# Type files (.ps1xml) to be loaded when importing this module
# TypesToProcess = @()

# Format files (.ps1xml) to be loaded when importing this module
FormatsToProcess = './Microsoft.Graph.Teams.format.ps1xml'

# Modules to import as nested modules of the module specified in RootModule/ModuleToProcess
# NestedModules = @()

# Functions to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no functions to export.
FunctionsToExport = 'Add-MgChatMember', 'Add-MgGroupTeamChannelAllMember', 
               'Add-MgGroupTeamChannelMember', 'Add-MgGroupTeamMember', 
               'Add-MgGroupTeamPrimaryChannelAllMember', 
               'Add-MgGroupTeamPrimaryChannelMember', 'Add-MgTeamChannelAllMember', 
               'Add-MgTeamChannelMember', 'Add-MgTeamMember', 
               'Add-MgTeamPrimaryChannelAllMember', 
               'Add-MgTeamPrimaryChannelMember', 
               'Add-MgTeamworkDeletedTeamChannelAllMember', 
               'Add-MgTeamworkDeletedTeamChannelMember', 'Add-MgUserChatMember', 
               'Clear-MgChatMessageReaction', 'Clear-MgChatMessageReplyReaction', 
               'Clear-MgGroupTeamChannelMessageReaction', 
               'Clear-MgGroupTeamChannelMessageReplyReaction', 
               'Clear-MgGroupTeamPrimaryChannelMessageReaction', 
               'Clear-MgGroupTeamPrimaryChannelMessageReplyReaction', 
               'Clear-MgTeamChannelMessageReaction', 
               'Clear-MgTeamChannelMessageReplyReaction', 
               'Clear-MgTeamPrimaryChannelMessageReaction', 
               'Clear-MgTeamPrimaryChannelMessageReplyReaction', 
               'Clear-MgTeamworkDeletedTeamChannelMessageReaction', 
               'Clear-MgTeamworkDeletedTeamChannelMessageReplyReaction', 
               'Clear-MgUserChatMessageReaction', 
               'Clear-MgUserChatMessageReplyReaction', 
               'Complete-MgGroupTeamChannelMigration', 
               'Complete-MgGroupTeamMigration', 
               'Complete-MgGroupTeamPrimaryChannelMigration', 
               'Complete-MgTeamChannelMigration', 'Complete-MgTeamMigration', 
               'Complete-MgTeamPrimaryChannelMigration', 
               'Complete-MgTeamworkDeletedTeamChannelMigration', 
               'Confirm-MgGroupTeamScheduleTimeCard', 
               'Confirm-MgTeamScheduleTimeCard', 'Copy-MgGroupTeam', 'Copy-MgTeam', 
               'Get-MgAllGroupTeamChannel', 'Get-MgAllGroupTeamChannelCount', 
               'Get-MgAllTeamChannel', 'Get-MgAllTeamChannelCount', 
               'Get-MgAllTeamMessage', 'Get-MgAllTeamworkDeletedTeamMessage', 
               'Get-MgAllUserChatMessage', 'Get-MgAppCatalogTeamApp', 
               'Get-MgAppCatalogTeamAppCount', 'Get-MgAppCatalogTeamAppDefinition', 
               'Get-MgAppCatalogTeamAppDefinitionBot', 
               'Get-MgAppCatalogTeamAppDefinitionCount', 'Get-MgChat', 
               'Get-MgChatCount', 'Get-MgChatInstalledApp', 
               'Get-MgChatInstalledAppCount', 'Get-MgChatInstalledAppTeamApp', 
               'Get-MgChatInstalledAppTeamAppDefinition', 
               'Get-MgChatLastMessagePreview', 'Get-MgChatMember', 
               'Get-MgChatMemberCount', 'Get-MgChatMessage', 
               'Get-MgChatMessageCount', 'Get-MgChatMessageDelta', 
               'Get-MgChatMessageHostedContent', 
               'Get-MgChatMessageHostedContentCount', 'Get-MgChatMessageReply', 
               'Get-MgChatMessageReplyCount', 'Get-MgChatMessageReplyDelta', 
               'Get-MgChatMessageReplyHostedContent', 
               'Get-MgChatMessageReplyHostedContentCount', 
               'Get-MgChatPermissionGrant', 'Get-MgChatPermissionGrantCount', 
               'Get-MgChatPinnedMessage', 'Get-MgChatPinnedMessageCount', 
               'Get-MgChatRetainedMessage', 'Get-MgChatTab', 'Get-MgChatTabCount', 
               'Get-MgChatTabTeamApp', 'Get-MgGroupTeam', 'Get-MgGroupTeamChannel', 
               'Get-MgGroupTeamChannelAllMemberCount', 
               'Get-MgGroupTeamChannelCount', 'Get-MgGroupTeamChannelFileFolder', 
               'Get-MgGroupTeamChannelFileFolderContent', 
               'Get-MgGroupTeamChannelMember', 'Get-MgGroupTeamChannelMemberCount', 
               'Get-MgGroupTeamChannelMessage', 
               'Get-MgGroupTeamChannelMessageCount', 
               'Get-MgGroupTeamChannelMessageDelta', 
               'Get-MgGroupTeamChannelMessageHostedContent', 
               'Get-MgGroupTeamChannelMessageHostedContentCount', 
               'Get-MgGroupTeamChannelMessageReply', 
               'Get-MgGroupTeamChannelMessageReplyCount', 
               'Get-MgGroupTeamChannelMessageReplyDelta', 
               'Get-MgGroupTeamChannelMessageReplyHostedContent', 
               'Get-MgGroupTeamChannelMessageReplyHostedContentCount', 
               'Get-MgGroupTeamChannelRetainedMessage', 
               'Get-MgGroupTeamChannelSharedWithTeam', 
               'Get-MgGroupTeamChannelSharedWithTeamAllowedMember', 
               'Get-MgGroupTeamChannelSharedWithTeamAllowedMemberCount', 
               'Get-MgGroupTeamChannelSharedWithTeamCount', 
               'Get-MgGroupTeamChannelTab', 'Get-MgGroupTeamChannelTabCount', 
               'Get-MgGroupTeamChannelTabTeamApp', 'Get-MgGroupTeamGroup', 
               'Get-MgGroupTeamGroupServiceProvisioningError', 
               'Get-MgGroupTeamGroupServiceProvisioningErrorCount', 
               'Get-MgGroupTeamIncomingChannel', 
               'Get-MgGroupTeamIncomingChannelCount', 
               'Get-MgGroupTeamInstalledApp', 'Get-MgGroupTeamInstalledAppCount', 
               'Get-MgGroupTeamInstalledAppTeamApp', 
               'Get-MgGroupTeamInstalledAppTeamAppDefinition', 
               'Get-MgGroupTeamMember', 'Get-MgGroupTeamMemberCount', 
               'Get-MgGroupTeamOperation', 'Get-MgGroupTeamOperationCount', 
               'Get-MgGroupTeamPermissionGrant', 
               'Get-MgGroupTeamPermissionGrantCount', 'Get-MgGroupTeamPhoto', 
               'Get-MgGroupTeamPhotoContent', 'Get-MgGroupTeamPrimaryChannel', 
               'Get-MgGroupTeamPrimaryChannelAllMemberCount', 
               'Get-MgGroupTeamPrimaryChannelFileFolder', 
               'Get-MgGroupTeamPrimaryChannelFileFolderContent', 
               'Get-MgGroupTeamPrimaryChannelMember', 
               'Get-MgGroupTeamPrimaryChannelMemberCount', 
               'Get-MgGroupTeamPrimaryChannelMessage', 
               'Get-MgGroupTeamPrimaryChannelMessageCount', 
               'Get-MgGroupTeamPrimaryChannelMessageDelta', 
               'Get-MgGroupTeamPrimaryChannelMessageHostedContent', 
               'Get-MgGroupTeamPrimaryChannelMessageHostedContentCount', 
               'Get-MgGroupTeamPrimaryChannelMessageReply', 
               'Get-MgGroupTeamPrimaryChannelMessageReplyCount', 
               'Get-MgGroupTeamPrimaryChannelMessageReplyDelta', 
               'Get-MgGroupTeamPrimaryChannelMessageReplyHostedContent', 
               'Get-MgGroupTeamPrimaryChannelMessageReplyHostedContentCount', 
               'Get-MgGroupTeamPrimaryChannelSharedWithTeam', 
               'Get-MgGroupTeamPrimaryChannelSharedWithTeamAllowedMember', 
               'Get-MgGroupTeamPrimaryChannelSharedWithTeamAllowedMemberCount', 
               'Get-MgGroupTeamPrimaryChannelSharedWithTeamCount', 
               'Get-MgGroupTeamPrimaryChannelTab', 
               'Get-MgGroupTeamPrimaryChannelTabCount', 
               'Get-MgGroupTeamPrimaryChannelTabTeamApp', 
               'Get-MgGroupTeamSchedule', 'Get-MgGroupTeamScheduleDayNote', 
               'Get-MgGroupTeamScheduleDayNoteCount', 
               'Get-MgGroupTeamScheduleOfferShiftRequest', 
               'Get-MgGroupTeamScheduleOfferShiftRequestCount', 
               'Get-MgGroupTeamScheduleOpenShift', 
               'Get-MgGroupTeamScheduleOpenShiftChangeRequest', 
               'Get-MgGroupTeamScheduleOpenShiftChangeRequestCount', 
               'Get-MgGroupTeamScheduleOpenShiftCount', 
               'Get-MgGroupTeamScheduleSchedulingGroup', 
               'Get-MgGroupTeamScheduleSchedulingGroupCount', 
               'Get-MgGroupTeamScheduleShift', 'Get-MgGroupTeamScheduleShiftCount', 
               'Get-MgGroupTeamScheduleSwapShiftChangeRequest', 
               'Get-MgGroupTeamScheduleSwapShiftChangeRequestCount', 
               'Get-MgGroupTeamScheduleTimeCard', 
               'Get-MgGroupTeamScheduleTimeCardCount', 
               'Get-MgGroupTeamScheduleTimeOff', 
               'Get-MgGroupTeamScheduleTimeOffCount', 
               'Get-MgGroupTeamScheduleTimeOffReason', 
               'Get-MgGroupTeamScheduleTimeOffReasonCount', 
               'Get-MgGroupTeamScheduleTimeOffRequest', 
               'Get-MgGroupTeamScheduleTimeOffRequestCount', 'Get-MgGroupTeamTag', 
               'Get-MgGroupTeamTagCount', 'Get-MgGroupTeamTagMember', 
               'Get-MgGroupTeamTagMemberCount', 'Get-MgGroupTeamTemplate', 
               'Get-MgTeam', 'Get-MgTeamChannel', 'Get-MgTeamChannelAllMemberCount', 
               'Get-MgTeamChannelCount', 'Get-MgTeamChannelFileFolder', 
               'Get-MgTeamChannelFileFolderContent', 'Get-MgTeamChannelMember', 
               'Get-MgTeamChannelMemberCount', 'Get-MgTeamChannelMessage', 
               'Get-MgTeamChannelMessageCount', 'Get-MgTeamChannelMessageDelta', 
               'Get-MgTeamChannelMessageHostedContent', 
               'Get-MgTeamChannelMessageHostedContentCount', 
               'Get-MgTeamChannelMessageReply', 
               'Get-MgTeamChannelMessageReplyCount', 
               'Get-MgTeamChannelMessageReplyDelta', 
               'Get-MgTeamChannelMessageReplyHostedContent', 
               'Get-MgTeamChannelMessageReplyHostedContentCount', 
               'Get-MgTeamChannelRetainedMessage', 
               'Get-MgTeamChannelSharedWithTeam', 
               'Get-MgTeamChannelSharedWithTeamAllowedMember', 
               'Get-MgTeamChannelSharedWithTeamAllowedMemberCount', 
               'Get-MgTeamChannelSharedWithTeamCount', 'Get-MgTeamChannelTab', 
               'Get-MgTeamChannelTabCount', 'Get-MgTeamChannelTabTeamApp', 
               'Get-MgTeamCount', 'Get-MgTeamGroupServiceProvisioningError', 
               'Get-MgTeamGroupServiceProvisioningErrorCount', 
               'Get-MgTeamIncomingChannel', 'Get-MgTeamIncomingChannelCount', 
               'Get-MgTeamInstalledApp', 'Get-MgTeamInstalledAppCount', 
               'Get-MgTeamInstalledAppTeamApp', 
               'Get-MgTeamInstalledAppTeamAppDefinition', 'Get-MgTeamMember', 
               'Get-MgTeamMemberCount', 'Get-MgTeamOperation', 
               'Get-MgTeamOperationCount', 'Get-MgTeamPermissionGrant', 
               'Get-MgTeamPermissionGrantCount', 'Get-MgTeamPhoto', 
               'Get-MgTeamPhotoContent', 'Get-MgTeamPrimaryChannel', 
               'Get-MgTeamPrimaryChannelAllMemberCount', 
               'Get-MgTeamPrimaryChannelFileFolder', 
               'Get-MgTeamPrimaryChannelFileFolderContent', 
               'Get-MgTeamPrimaryChannelMember', 
               'Get-MgTeamPrimaryChannelMemberCount', 
               'Get-MgTeamPrimaryChannelMessage', 
               'Get-MgTeamPrimaryChannelMessageCount', 
               'Get-MgTeamPrimaryChannelMessageDelta', 
               'Get-MgTeamPrimaryChannelMessageHostedContent', 
               'Get-MgTeamPrimaryChannelMessageHostedContentCount', 
               'Get-MgTeamPrimaryChannelMessageReply', 
               'Get-MgTeamPrimaryChannelMessageReplyCount', 
               'Get-MgTeamPrimaryChannelMessageReplyDelta', 
               'Get-MgTeamPrimaryChannelMessageReplyHostedContent', 
               'Get-MgTeamPrimaryChannelMessageReplyHostedContentCount', 
               'Get-MgTeamPrimaryChannelSharedWithTeam', 
               'Get-MgTeamPrimaryChannelSharedWithTeamAllowedMember', 
               'Get-MgTeamPrimaryChannelSharedWithTeamAllowedMemberCount', 
               'Get-MgTeamPrimaryChannelSharedWithTeamCount', 
               'Get-MgTeamPrimaryChannelTab', 'Get-MgTeamPrimaryChannelTabCount', 
               'Get-MgTeamPrimaryChannelTabTeamApp', 'Get-MgTeamSchedule', 
               'Get-MgTeamScheduleDayNote', 'Get-MgTeamScheduleDayNoteCount', 
               'Get-MgTeamScheduleOfferShiftRequest', 
               'Get-MgTeamScheduleOfferShiftRequestCount', 
               'Get-MgTeamScheduleOpenShift', 
               'Get-MgTeamScheduleOpenShiftChangeRequest', 
               'Get-MgTeamScheduleOpenShiftChangeRequestCount', 
               'Get-MgTeamScheduleOpenShiftCount', 
               'Get-MgTeamScheduleSchedulingGroup', 
               'Get-MgTeamScheduleSchedulingGroupCount', 'Get-MgTeamScheduleShift', 
               'Get-MgTeamScheduleShiftCount', 
               'Get-MgTeamScheduleSwapShiftChangeRequest', 
               'Get-MgTeamScheduleSwapShiftChangeRequestCount', 
               'Get-MgTeamScheduleTimeCard', 'Get-MgTeamScheduleTimeCardCount', 
               'Get-MgTeamScheduleTimeOff', 'Get-MgTeamScheduleTimeOffCount', 
               'Get-MgTeamScheduleTimeOffReason', 
               'Get-MgTeamScheduleTimeOffReasonCount', 
               'Get-MgTeamScheduleTimeOffRequest', 
               'Get-MgTeamScheduleTimeOffRequestCount', 'Get-MgTeamTag', 
               'Get-MgTeamTagCount', 'Get-MgTeamTagMember', 
               'Get-MgTeamTagMemberCount', 'Get-MgTeamTemplate', 'Get-MgTeamwork', 
               'Get-MgTeamworkDeletedChat', 'Get-MgTeamworkDeletedChatCount', 
               'Get-MgTeamworkDeletedTeam', 'Get-MgTeamworkDeletedTeamChannel', 
               'Get-MgTeamworkDeletedTeamChannelAllMemberCount', 
               'Get-MgTeamworkDeletedTeamChannelCount', 
               'Get-MgTeamworkDeletedTeamChannelFileFolder', 
               'Get-MgTeamworkDeletedTeamChannelFileFolderContent', 
               'Get-MgTeamworkDeletedTeamChannelMember', 
               'Get-MgTeamworkDeletedTeamChannelMemberCount', 
               'Get-MgTeamworkDeletedTeamChannelMessage', 
               'Get-MgTeamworkDeletedTeamChannelMessageCount', 
               'Get-MgTeamworkDeletedTeamChannelMessageDelta', 
               'Get-MgTeamworkDeletedTeamChannelMessageHostedContent', 
               'Get-MgTeamworkDeletedTeamChannelMessageHostedContentCount', 
               'Get-MgTeamworkDeletedTeamChannelMessageReply', 
               'Get-MgTeamworkDeletedTeamChannelMessageReplyCount', 
               'Get-MgTeamworkDeletedTeamChannelMessageReplyDelta', 
               'Get-MgTeamworkDeletedTeamChannelMessageReplyHostedContent', 
               'Get-MgTeamworkDeletedTeamChannelMessageReplyHostedContentCount', 
               'Get-MgTeamworkDeletedTeamChannelRetainedMessage', 
               'Get-MgTeamworkDeletedTeamChannelSharedWithTeam', 
               'Get-MgTeamworkDeletedTeamChannelSharedWithTeamAllowedMember', 
               'Get-MgTeamworkDeletedTeamChannelSharedWithTeamAllowedMemberCount', 
               'Get-MgTeamworkDeletedTeamChannelSharedWithTeamCount', 
               'Get-MgTeamworkDeletedTeamChannelTab', 
               'Get-MgTeamworkDeletedTeamChannelTabCount', 
               'Get-MgTeamworkDeletedTeamChannelTabTeamApp', 
               'Get-MgTeamworkDeletedTeamCount', 'Get-MgTeamworkTeamAppSetting', 
               'Get-MgTeamworkWorkforceIntegration', 
               'Get-MgTeamworkWorkforceIntegrationCount', 'Get-MgUserChat', 
               'Get-MgUserChatCount', 'Get-MgUserChatInstalledApp', 
               'Get-MgUserChatInstalledAppCount', 
               'Get-MgUserChatInstalledAppTeamApp', 
               'Get-MgUserChatInstalledAppTeamAppDefinition', 
               'Get-MgUserChatLastMessagePreview', 'Get-MgUserChatMember', 
               'Get-MgUserChatMemberCount', 'Get-MgUserChatMessageCount', 
               'Get-MgUserChatMessageDelta', 'Get-MgUserChatMessageHostedContent', 
               'Get-MgUserChatMessageHostedContentCount', 
               'Get-MgUserChatMessageReply', 'Get-MgUserChatMessageReplyCount', 
               'Get-MgUserChatMessageReplyDelta', 
               'Get-MgUserChatMessageReplyHostedContent', 
               'Get-MgUserChatMessageReplyHostedContentCount', 
               'Get-MgUserChatPermissionGrant', 
               'Get-MgUserChatPermissionGrantCount', 'Get-MgUserChatPinnedMessage', 
               'Get-MgUserChatPinnedMessageCount', 'Get-MgUserChatRetainedMessage', 
               'Get-MgUserChatTab', 'Get-MgUserChatTabCount', 
               'Get-MgUserChatTabTeamApp', 'Get-MgUserJoinedTeam', 
               'Get-MgUserTeamwork', 'Get-MgUserTeamworkAssociatedTeam', 
               'Get-MgUserTeamworkAssociatedTeamCount', 
               'Get-MgUserTeamworkInstalledApp', 
               'Get-MgUserTeamworkInstalledAppChat', 
               'Get-MgUserTeamworkInstalledAppCount', 
               'Get-MgUserTeamworkInstalledAppTeamApp', 
               'Get-MgUserTeamworkInstalledAppTeamAppDefinition', 
               'Hide-MgChatForUser', 'Hide-MgUserChatForUser', 
               'Invoke-MgArchiveGroupTeam', 'Invoke-MgArchiveGroupTeamChannel', 
               'Invoke-MgArchiveGroupTeamPrimaryChannel', 'Invoke-MgArchiveTeam', 
               'Invoke-MgArchiveTeamChannel', 'Invoke-MgArchiveTeamPrimaryChannel', 
               'Invoke-MgArchiveTeamworkDeletedTeamChannel', 
               'Invoke-MgClockGroupTeamScheduleTimeCardIn', 
               'Invoke-MgClockGroupTeamScheduleTimeCardOut', 
               'Invoke-MgClockTeamScheduleTimeCardIn', 
               'Invoke-MgClockTeamScheduleTimeCardOut', 'Invoke-MgGraphChat', 
               'Invoke-MgGraphUserChat', 'Invoke-MgHaveGroupTeamChannel', 
               'Invoke-MgHaveGroupTeamPrimaryChannel', 'Invoke-MgHaveTeamChannel', 
               'Invoke-MgHaveTeamPrimaryChannel', 
               'Invoke-MgHaveTeamworkDeletedTeamChannel', 
               'Invoke-MgMarkChatReadForUser', 'Invoke-MgMarkChatUnreadForUser', 
               'Invoke-MgMarkUserChatReadForUser', 
               'Invoke-MgMarkUserChatUnreadForUser', 
               'Invoke-MgShareGroupTeamSchedule', 'Invoke-MgShareTeamSchedule', 
               'Invoke-MgSoftChatMessageDelete', 
               'Invoke-MgSoftChatMessageReplyDelete', 
               'Invoke-MgSoftGroupTeamChannelMessageDelete', 
               'Invoke-MgSoftGroupTeamChannelMessageReplyDelete', 
               'Invoke-MgSoftGroupTeamPrimaryChannelMessageDelete', 
               'Invoke-MgSoftGroupTeamPrimaryChannelMessageReplyDelete', 
               'Invoke-MgSoftTeamChannelMessageDelete', 
               'Invoke-MgSoftTeamChannelMessageReplyDelete', 
               'Invoke-MgSoftTeamPrimaryChannelMessageDelete', 
               'Invoke-MgSoftTeamPrimaryChannelMessageReplyDelete', 
               'Invoke-MgSoftTeamworkDeletedTeamChannelMessageDelete', 
               'Invoke-MgSoftTeamworkDeletedTeamChannelMessageReplyDelete', 
               'Invoke-MgSoftUserChatMessageDelete', 
               'Invoke-MgSoftUserChatMessageReplyDelete', 
               'Invoke-MgUnarchiveGroupTeam', 'Invoke-MgUnarchiveGroupTeamChannel', 
               'Invoke-MgUnarchiveGroupTeamPrimaryChannel', 
               'Invoke-MgUnarchiveTeam', 'Invoke-MgUnarchiveTeamChannel', 
               'Invoke-MgUnarchiveTeamPrimaryChannel', 
               'Invoke-MgUnarchiveTeamworkDeletedTeamChannel', 
               'New-MgAppCatalogTeamApp', 'New-MgAppCatalogTeamAppDefinition', 
               'New-MgChat', 'New-MgChatInstalledApp', 'New-MgChatMember', 
               'New-MgChatMessage', 'New-MgChatMessageHostedContent', 
               'New-MgChatMessageReply', 'New-MgChatMessageReplyHostedContent', 
               'New-MgChatPermissionGrant', 'New-MgChatPinnedMessage', 
               'New-MgChatTab', 'New-MgGroupTeamChannel', 
               'New-MgGroupTeamChannelEmail', 'New-MgGroupTeamChannelMember', 
               'New-MgGroupTeamChannelMessage', 
               'New-MgGroupTeamChannelMessageHostedContent', 
               'New-MgGroupTeamChannelMessageReply', 
               'New-MgGroupTeamChannelMessageReplyHostedContent', 
               'New-MgGroupTeamChannelSharedWithTeam', 'New-MgGroupTeamChannelTab', 
               'New-MgGroupTeamInstalledApp', 'New-MgGroupTeamMember', 
               'New-MgGroupTeamOperation', 'New-MgGroupTeamPermissionGrant', 
               'New-MgGroupTeamPrimaryChannelEmail', 
               'New-MgGroupTeamPrimaryChannelMember', 
               'New-MgGroupTeamPrimaryChannelMessage', 
               'New-MgGroupTeamPrimaryChannelMessageHostedContent', 
               'New-MgGroupTeamPrimaryChannelMessageReply', 
               'New-MgGroupTeamPrimaryChannelMessageReplyHostedContent', 
               'New-MgGroupTeamPrimaryChannelSharedWithTeam', 
               'New-MgGroupTeamPrimaryChannelTab', 
               'New-MgGroupTeamScheduleDayNote', 
               'New-MgGroupTeamScheduleOfferShiftRequest', 
               'New-MgGroupTeamScheduleOpenShift', 
               'New-MgGroupTeamScheduleOpenShiftChangeRequest', 
               'New-MgGroupTeamScheduleSchedulingGroup', 
               'New-MgGroupTeamScheduleShift', 
               'New-MgGroupTeamScheduleSwapShiftChangeRequest', 
               'New-MgGroupTeamScheduleTimeCard', 'New-MgGroupTeamScheduleTimeOff', 
               'New-MgGroupTeamScheduleTimeOffReason', 
               'New-MgGroupTeamScheduleTimeOffRequest', 'New-MgGroupTeamTag', 
               'New-MgGroupTeamTagMember', 'New-MgTeam', 'New-MgTeamChannel', 
               'New-MgTeamChannelEmail', 'New-MgTeamChannelMember', 
               'New-MgTeamChannelMessage', 'New-MgTeamChannelMessageHostedContent', 
               'New-MgTeamChannelMessageReply', 
               'New-MgTeamChannelMessageReplyHostedContent', 
               'New-MgTeamChannelSharedWithTeam', 'New-MgTeamChannelTab', 
               'New-MgTeamInstalledApp', 'New-MgTeamMember', 'New-MgTeamOperation', 
               'New-MgTeamPermissionGrant', 'New-MgTeamPrimaryChannelEmail', 
               'New-MgTeamPrimaryChannelMember', 'New-MgTeamPrimaryChannelMessage', 
               'New-MgTeamPrimaryChannelMessageHostedContent', 
               'New-MgTeamPrimaryChannelMessageReply', 
               'New-MgTeamPrimaryChannelMessageReplyHostedContent', 
               'New-MgTeamPrimaryChannelSharedWithTeam', 
               'New-MgTeamPrimaryChannelTab', 'New-MgTeamScheduleDayNote', 
               'New-MgTeamScheduleOfferShiftRequest', 
               'New-MgTeamScheduleOpenShift', 
               'New-MgTeamScheduleOpenShiftChangeRequest', 
               'New-MgTeamScheduleSchedulingGroup', 'New-MgTeamScheduleShift', 
               'New-MgTeamScheduleSwapShiftChangeRequest', 
               'New-MgTeamScheduleTimeCard', 'New-MgTeamScheduleTimeOff', 
               'New-MgTeamScheduleTimeOffReason', 
               'New-MgTeamScheduleTimeOffRequest', 'New-MgTeamTag', 
               'New-MgTeamTagMember', 'New-MgTeamworkDeletedChat', 
               'New-MgTeamworkDeletedTeam', 'New-MgTeamworkDeletedTeamChannel', 
               'New-MgTeamworkDeletedTeamChannelEmail', 
               'New-MgTeamworkDeletedTeamChannelMember', 
               'New-MgTeamworkDeletedTeamChannelMessage', 
               'New-MgTeamworkDeletedTeamChannelMessageHostedContent', 
               'New-MgTeamworkDeletedTeamChannelMessageReply', 
               'New-MgTeamworkDeletedTeamChannelMessageReplyHostedContent', 
               'New-MgTeamworkDeletedTeamChannelSharedWithTeam', 
               'New-MgTeamworkDeletedTeamChannelTab', 
               'New-MgTeamworkWorkforceIntegration', 'New-MgUserChat', 
               'New-MgUserChatInstalledApp', 'New-MgUserChatMember', 
               'New-MgUserChatMessage', 'New-MgUserChatMessageHostedContent', 
               'New-MgUserChatMessageReply', 
               'New-MgUserChatMessageReplyHostedContent', 
               'New-MgUserChatPermissionGrant', 'New-MgUserChatPinnedMessage', 
               'New-MgUserChatTab', 'New-MgUserTeamworkAssociatedTeam', 
               'New-MgUserTeamworkInstalledApp', 'Remove-MgAppCatalogTeamApp', 
               'Remove-MgAppCatalogTeamAppDefinition', 
               'Remove-MgAppCatalogTeamAppDefinitionBot', 'Remove-MgChat', 
               'Remove-MgChatInstalledApp', 'Remove-MgChatLastMessagePreview', 
               'Remove-MgChatMember', 'Remove-MgChatMessageReplyHostedContent', 
               'Remove-MgChatPermissionGrant', 'Remove-MgChatPinnedMessage', 
               'Remove-MgChatTab', 'Remove-MgGroupTeam', 'Remove-MgGroupTeamChannel', 
               'Remove-MgGroupTeamChannelAllMember', 
               'Remove-MgGroupTeamChannelEmail', 
               'Remove-MgGroupTeamChannelFileFolderContent', 
               'Remove-MgGroupTeamChannelMember', 
               'Remove-MgGroupTeamChannelMessage', 
               'Remove-MgGroupTeamChannelMessageHostedContent', 
               'Remove-MgGroupTeamChannelMessageReply', 
               'Remove-MgGroupTeamChannelMessageReplyHostedContent', 
               'Remove-MgGroupTeamChannelSharedWithTeam', 
               'Remove-MgGroupTeamChannelTab', 'Remove-MgGroupTeamInstalledApp', 
               'Remove-MgGroupTeamMember', 'Remove-MgGroupTeamOperation', 
               'Remove-MgGroupTeamPermissionGrant', 
               'Remove-MgGroupTeamPhotoContent', 
               'Remove-MgGroupTeamPrimaryChannel', 
               'Remove-MgGroupTeamPrimaryChannelAllMember', 
               'Remove-MgGroupTeamPrimaryChannelEmail', 
               'Remove-MgGroupTeamPrimaryChannelFileFolderContent', 
               'Remove-MgGroupTeamPrimaryChannelMember', 
               'Remove-MgGroupTeamPrimaryChannelMessage', 
               'Remove-MgGroupTeamPrimaryChannelMessageHostedContent', 
               'Remove-MgGroupTeamPrimaryChannelMessageReply', 
               'Remove-MgGroupTeamPrimaryChannelMessageReplyHostedContent', 
               'Remove-MgGroupTeamPrimaryChannelSharedWithTeam', 
               'Remove-MgGroupTeamPrimaryChannelTab', 'Remove-MgGroupTeamSchedule', 
               'Remove-MgGroupTeamScheduleDayNote', 
               'Remove-MgGroupTeamScheduleOfferShiftRequest', 
               'Remove-MgGroupTeamScheduleOpenShift', 
               'Remove-MgGroupTeamScheduleOpenShiftChangeRequest', 
               'Remove-MgGroupTeamScheduleSchedulingGroup', 
               'Remove-MgGroupTeamScheduleShift', 
               'Remove-MgGroupTeamScheduleSwapShiftChangeRequest', 
               'Remove-MgGroupTeamScheduleTimeCard', 
               'Remove-MgGroupTeamScheduleTimeOff', 
               'Remove-MgGroupTeamScheduleTimeOffReason', 
               'Remove-MgGroupTeamScheduleTimeOffRequest', 'Remove-MgGroupTeamTag', 
               'Remove-MgGroupTeamTagMember', 'Remove-MgTeam', 
               'Remove-MgTeamChannel', 'Remove-MgTeamChannelAllMember', 
               'Remove-MgTeamChannelEmail', 
               'Remove-MgTeamChannelFileFolderContent', 
               'Remove-MgTeamChannelMember', 
               'Remove-MgTeamChannelMessageReplyHostedContent', 
               'Remove-MgTeamChannelSharedWithTeam', 'Remove-MgTeamChannelTab', 
               'Remove-MgTeamInstalledApp', 'Remove-MgTeamMember', 
               'Remove-MgTeamOperation', 'Remove-MgTeamPermissionGrant', 
               'Remove-MgTeamPhotoContent', 'Remove-MgTeamPrimaryChannel', 
               'Remove-MgTeamPrimaryChannelAllMember', 
               'Remove-MgTeamPrimaryChannelEmail', 
               'Remove-MgTeamPrimaryChannelFileFolderContent', 
               'Remove-MgTeamPrimaryChannelMember', 
               'Remove-MgTeamPrimaryChannelMessageReplyHostedContent', 
               'Remove-MgTeamPrimaryChannelSharedWithTeam', 
               'Remove-MgTeamPrimaryChannelTab', 'Remove-MgTeamSchedule', 
               'Remove-MgTeamScheduleDayNote', 
               'Remove-MgTeamScheduleOfferShiftRequest', 
               'Remove-MgTeamScheduleOpenShift', 
               'Remove-MgTeamScheduleOpenShiftChangeRequest', 
               'Remove-MgTeamScheduleSchedulingGroup', 
               'Remove-MgTeamScheduleShift', 
               'Remove-MgTeamScheduleSwapShiftChangeRequest', 
               'Remove-MgTeamScheduleTimeCard', 'Remove-MgTeamScheduleTimeOff', 
               'Remove-MgTeamScheduleTimeOffReason', 
               'Remove-MgTeamScheduleTimeOffRequest', 'Remove-MgTeamTag', 
               'Remove-MgTeamTagMember', 'Remove-MgTeamworkDeletedChat', 
               'Remove-MgTeamworkDeletedTeam', 
               'Remove-MgTeamworkDeletedTeamChannel', 
               'Remove-MgTeamworkDeletedTeamChannelAllMember', 
               'Remove-MgTeamworkDeletedTeamChannelEmail', 
               'Remove-MgTeamworkDeletedTeamChannelFileFolderContent', 
               'Remove-MgTeamworkDeletedTeamChannelMember', 
               'Remove-MgTeamworkDeletedTeamChannelMessage', 
               'Remove-MgTeamworkDeletedTeamChannelMessageHostedContent', 
               'Remove-MgTeamworkDeletedTeamChannelMessageReply', 
               'Remove-MgTeamworkDeletedTeamChannelMessageReplyHostedContent', 
               'Remove-MgTeamworkDeletedTeamChannelSharedWithTeam', 
               'Remove-MgTeamworkDeletedTeamChannelTab', 
               'Remove-MgTeamworkTeamAppSetting', 
               'Remove-MgTeamworkWorkforceIntegration', 'Remove-MgUserChat', 
               'Remove-MgUserChatInstalledApp', 
               'Remove-MgUserChatLastMessagePreview', 'Remove-MgUserChatMember', 
               'Remove-MgUserChatMessage', 'Remove-MgUserChatMessageHostedContent', 
               'Remove-MgUserChatMessageReply', 
               'Remove-MgUserChatMessageReplyHostedContent', 
               'Remove-MgUserChatPermissionGrant', 
               'Remove-MgUserChatPinnedMessage', 'Remove-MgUserChatTab', 
               'Remove-MgUserTeamwork', 'Remove-MgUserTeamworkAssociatedTeam', 
               'Remove-MgUserTeamworkInstalledApp', 
               'Send-MgChatActivityNotification', 
               'Send-MgGroupTeamActivityNotification', 
               'Send-MgTeamActivityNotification', 
               'Send-MgTeamworkActivityNotificationToRecipient', 
               'Send-MgUserChatActivityNotification', 
               'Send-MgUserTeamworkActivityNotification', 
               'Set-MgChatMessageHostedContent', 'Set-MgChatMessageReaction', 
               'Set-MgChatMessageReplyHostedContent', 
               'Set-MgChatMessageReplyReaction', 'Set-MgGroupTeam', 
               'Set-MgGroupTeamChannelFileFolderContent', 
               'Set-MgGroupTeamChannelMessageHostedContent', 
               'Set-MgGroupTeamChannelMessageReaction', 
               'Set-MgGroupTeamChannelMessageReplyHostedContent', 
               'Set-MgGroupTeamChannelMessageReplyReaction', 
               'Set-MgGroupTeamPhotoContent', 
               'Set-MgGroupTeamPrimaryChannelFileFolderContent', 
               'Set-MgGroupTeamPrimaryChannelMessageHostedContent', 
               'Set-MgGroupTeamPrimaryChannelMessageReaction', 
               'Set-MgGroupTeamPrimaryChannelMessageReplyHostedContent', 
               'Set-MgGroupTeamPrimaryChannelMessageReplyReaction', 
               'Set-MgGroupTeamSchedule', 'Set-MgTeamChannelFileFolderContent', 
               'Set-MgTeamChannelMessageHostedContent', 
               'Set-MgTeamChannelMessageReaction', 
               'Set-MgTeamChannelMessageReplyHostedContent', 
               'Set-MgTeamChannelMessageReplyReaction', 'Set-MgTeamPhotoContent', 
               'Set-MgTeamPrimaryChannelFileFolderContent', 
               'Set-MgTeamPrimaryChannelMessageHostedContent', 
               'Set-MgTeamPrimaryChannelMessageReaction', 
               'Set-MgTeamPrimaryChannelMessageReplyHostedContent', 
               'Set-MgTeamPrimaryChannelMessageReplyReaction', 
               'Set-MgTeamSchedule', 
               'Set-MgTeamworkDeletedTeamChannelFileFolderContent', 
               'Set-MgTeamworkDeletedTeamChannelMessageHostedContent', 
               'Set-MgTeamworkDeletedTeamChannelMessageReaction', 
               'Set-MgTeamworkDeletedTeamChannelMessageReplyHostedContent', 
               'Set-MgTeamworkDeletedTeamChannelMessageReplyReaction', 
               'Set-MgUserChatMessageHostedContent', 
               'Set-MgUserChatMessageReaction', 
               'Set-MgUserChatMessageReplyHostedContent', 
               'Set-MgUserChatMessageReplyReaction', 
               'Start-MgGroupTeamScheduleTimeCardBreak', 
               'Start-MgTeamScheduleTimeCardBreak', 
               'Stop-MgGroupTeamScheduleTimeCardBreak', 
               'Stop-MgTeamScheduleTimeCardBreak', 
               'Undo-MgChatMessageReplySoftDelete', 'Undo-MgChatMessageSoftDelete', 
               'Undo-MgGroupTeamChannelMessageReplySoftDelete', 
               'Undo-MgGroupTeamChannelMessageSoftDelete', 
               'Undo-MgGroupTeamPrimaryChannelMessageReplySoftDelete', 
               'Undo-MgGroupTeamPrimaryChannelMessageSoftDelete', 
               'Undo-MgTeamChannelMessageReplySoftDelete', 
               'Undo-MgTeamChannelMessageSoftDelete', 
               'Undo-MgTeamPrimaryChannelMessageReplySoftDelete', 
               'Undo-MgTeamPrimaryChannelMessageSoftDelete', 
               'Undo-MgTeamworkDeletedChatDelete', 
               'Undo-MgTeamworkDeletedTeamChannelMessageReplySoftDelete', 
               'Undo-MgTeamworkDeletedTeamChannelMessageSoftDelete', 
               'Undo-MgUserChatMessageReplySoftDelete', 
               'Undo-MgUserChatMessageSoftDelete', 'Update-MgAppCatalogTeamApp', 
               'Update-MgAppCatalogTeamAppDefinition', 
               'Update-MgAppCatalogTeamAppDefinitionBot', 'Update-MgChat', 
               'Update-MgChatInstalledApp', 'Update-MgChatLastMessagePreview', 
               'Update-MgChatMember', 'Update-MgChatMessage', 
               'Update-MgChatMessageReply', 
               'Update-MgChatMessageReplyHostedContent', 
               'Update-MgChatPermissionGrant', 'Update-MgChatPinnedMessage', 
               'Update-MgChatTab', 'Update-MgGroupTeamChannel', 
               'Update-MgGroupTeamChannelMember', 
               'Update-MgGroupTeamChannelMessage', 
               'Update-MgGroupTeamChannelMessageHostedContent', 
               'Update-MgGroupTeamChannelMessageReply', 
               'Update-MgGroupTeamChannelMessageReplyHostedContent', 
               'Update-MgGroupTeamChannelSharedWithTeam', 
               'Update-MgGroupTeamChannelTab', 'Update-MgGroupTeamInstalledApp', 
               'Update-MgGroupTeamMember', 'Update-MgGroupTeamOperation', 
               'Update-MgGroupTeamPermissionGrant', 'Update-MgGroupTeamPhoto', 
               'Update-MgGroupTeamPrimaryChannel', 
               'Update-MgGroupTeamPrimaryChannelMember', 
               'Update-MgGroupTeamPrimaryChannelMessage', 
               'Update-MgGroupTeamPrimaryChannelMessageHostedContent', 
               'Update-MgGroupTeamPrimaryChannelMessageReply', 
               'Update-MgGroupTeamPrimaryChannelMessageReplyHostedContent', 
               'Update-MgGroupTeamPrimaryChannelSharedWithTeam', 
               'Update-MgGroupTeamPrimaryChannelTab', 
               'Update-MgGroupTeamScheduleDayNote', 
               'Update-MgGroupTeamScheduleOfferShiftRequest', 
               'Update-MgGroupTeamScheduleOpenShift', 
               'Update-MgGroupTeamScheduleOpenShiftChangeRequest', 
               'Update-MgGroupTeamScheduleSchedulingGroup', 
               'Update-MgGroupTeamScheduleShift', 
               'Update-MgGroupTeamScheduleSwapShiftChangeRequest', 
               'Update-MgGroupTeamScheduleTimeCard', 
               'Update-MgGroupTeamScheduleTimeOff', 
               'Update-MgGroupTeamScheduleTimeOffReason', 
               'Update-MgGroupTeamScheduleTimeOffRequest', 'Update-MgGroupTeamTag', 
               'Update-MgGroupTeamTagMember', 'Update-MgTeam', 
               'Update-MgTeamChannel', 'Update-MgTeamChannelMember', 
               'Update-MgTeamChannelMessage', 'Update-MgTeamChannelMessageReply', 
               'Update-MgTeamChannelMessageReplyHostedContent', 
               'Update-MgTeamChannelSharedWithTeam', 'Update-MgTeamChannelTab', 
               'Update-MgTeamInstalledApp', 'Update-MgTeamMember', 
               'Update-MgTeamOperation', 'Update-MgTeamPermissionGrant', 
               'Update-MgTeamPhoto', 'Update-MgTeamPrimaryChannel', 
               'Update-MgTeamPrimaryChannelMember', 
               'Update-MgTeamPrimaryChannelMessage', 
               'Update-MgTeamPrimaryChannelMessageReply', 
               'Update-MgTeamPrimaryChannelMessageReplyHostedContent', 
               'Update-MgTeamPrimaryChannelSharedWithTeam', 
               'Update-MgTeamPrimaryChannelTab', 'Update-MgTeamScheduleDayNote', 
               'Update-MgTeamScheduleOfferShiftRequest', 
               'Update-MgTeamScheduleOpenShift', 
               'Update-MgTeamScheduleOpenShiftChangeRequest', 
               'Update-MgTeamScheduleSchedulingGroup', 
               'Update-MgTeamScheduleShift', 
               'Update-MgTeamScheduleSwapShiftChangeRequest', 
               'Update-MgTeamScheduleTimeCard', 'Update-MgTeamScheduleTimeOff', 
               'Update-MgTeamScheduleTimeOffReason', 
               'Update-MgTeamScheduleTimeOffRequest', 'Update-MgTeamTag', 
               'Update-MgTeamTagMember', 'Update-MgTeamwork', 
               'Update-MgTeamworkDeletedChat', 'Update-MgTeamworkDeletedTeam', 
               'Update-MgTeamworkDeletedTeamChannel', 
               'Update-MgTeamworkDeletedTeamChannelMember', 
               'Update-MgTeamworkDeletedTeamChannelMessage', 
               'Update-MgTeamworkDeletedTeamChannelMessageHostedContent', 
               'Update-MgTeamworkDeletedTeamChannelMessageReply', 
               'Update-MgTeamworkDeletedTeamChannelMessageReplyHostedContent', 
               'Update-MgTeamworkDeletedTeamChannelSharedWithTeam', 
               'Update-MgTeamworkDeletedTeamChannelTab', 
               'Update-MgTeamworkTeamAppSetting', 
               'Update-MgTeamworkWorkforceIntegration', 'Update-MgUserChat', 
               'Update-MgUserChatInstalledApp', 
               'Update-MgUserChatLastMessagePreview', 'Update-MgUserChatMember', 
               'Update-MgUserChatMessage', 'Update-MgUserChatMessageHostedContent', 
               'Update-MgUserChatMessageReply', 
               'Update-MgUserChatMessageReplyHostedContent', 
               'Update-MgUserChatPermissionGrant', 
               'Update-MgUserChatPinnedMessage', 'Update-MgUserChatTab', 
               'Update-MgUserTeamwork', 'Update-MgUserTeamworkAssociatedTeam'

# Cmdlets to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no cmdlets to export.
CmdletsToExport = @()

# Variables to export from this module
# VariablesToExport = @()

# Aliases to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no aliases to export.
AliasesToExport = '*'

# DSC resources to export from this module
# DscResourcesToExport = @()

# List of all modules packaged with this module
# ModuleList = @()

# List of all files packaged with this module
# FileList = @()

# Private data to pass to the module specified in RootModule/ModuleToProcess. This may also contain a PSData hashtable with additional module metadata used by PowerShell.
PrivateData = @{

    PSData = @{

        # Tags applied to this module. These help with module discovery in online galleries.
        Tags = 'Microsoft','Office365','Graph','PowerShell','PSModule','PSIncludes_Cmdlet'

        # A URL to the license for this module.
        LicenseUri = 'https://aka.ms/devservicesagreement'

        # A URL to the main website for this project.
        ProjectUri = 'https://github.com/microsoftgraph/msgraph-sdk-powershell'

        # A URL to an icon representing this module.
        IconUri = 'https://raw.githubusercontent.com/microsoftgraph/msgraph-sdk-powershell/dev/docs/images/graph_color256.png'

        # ReleaseNotes of this module
        ReleaseNotes = 'See https://aka.ms/GraphPowerShell-Release.'

        # Prerelease string of this module
        # Prerelease = ''

        # Flag to indicate whether the module requires explicit user acceptance for install/update/save
        # RequireLicenseAcceptance = $false

        # External dependent modules of this module
        # ExternalModuleDependencies = @()

    } # End of PSData hashtable

 } # End of PrivateData hashtable

# HelpInfo URI of this module
# HelpInfoURI = ''

# Default prefix for commands exported from this module. Override the default prefix using Import-Module -Prefix.
# DefaultCommandPrefix = ''

}


# SIG # Begin signature block
# MIIoVQYJKoZIhvcNAQcCoIIoRjCCKEICAQExDzANBglghkgBZQMEAgEFADB5Bgor
# BgEEAYI3AgEEoGswaTA0BgorBgEEAYI3AgEeMCYCAwEAAAQQH8w7YFlLCE63JNLG
# KX7zUQIBAAIBAAIBAAIBAAIBADAxMA0GCWCGSAFlAwQCAQUABCBRtbHDYdJe8CPW
# rf7QrxH7Swb0ACQXNEkJUbsfWEGg/6CCDYUwggYDMIID66ADAgECAhMzAAAEA73V
# lV0POxitAAAAAAQDMA0GCSqGSIb3DQEBCwUAMH4xCzAJBgNVBAYTAlVTMRMwEQYD
# VQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNy
# b3NvZnQgQ29ycG9yYXRpb24xKDAmBgNVBAMTH01pY3Jvc29mdCBDb2RlIFNpZ25p
# bmcgUENBIDIwMTEwHhcNMjQwOTEyMjAxMTEzWhcNMjUwOTExMjAxMTEzWjB0MQsw
# CQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9u
# ZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMR4wHAYDVQQDExVNaWNy
# b3NvZnQgQ29ycG9yYXRpb24wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIB
# AQCfdGddwIOnbRYUyg03O3iz19XXZPmuhEmW/5uyEN+8mgxl+HJGeLGBR8YButGV
# LVK38RxcVcPYyFGQXcKcxgih4w4y4zJi3GvawLYHlsNExQwz+v0jgY/aejBS2EJY
# oUhLVE+UzRihV8ooxoftsmKLb2xb7BoFS6UAo3Zz4afnOdqI7FGoi7g4vx/0MIdi
# kwTn5N56TdIv3mwfkZCFmrsKpN0zR8HD8WYsvH3xKkG7u/xdqmhPPqMmnI2jOFw/
# /n2aL8W7i1Pasja8PnRXH/QaVH0M1nanL+LI9TsMb/enWfXOW65Gne5cqMN9Uofv
# ENtdwwEmJ3bZrcI9u4LZAkujAgMBAAGjggGCMIIBfjAfBgNVHSUEGDAWBgorBgEE
# AYI3TAgBBggrBgEFBQcDAzAdBgNVHQ4EFgQU6m4qAkpz4641iK2irF8eWsSBcBkw
# VAYDVR0RBE0wS6RJMEcxLTArBgNVBAsTJE1pY3Jvc29mdCBJcmVsYW5kIE9wZXJh
# dGlvbnMgTGltaXRlZDEWMBQGA1UEBRMNMjMwMDEyKzUwMjkyNjAfBgNVHSMEGDAW
# gBRIbmTlUAXTgqoXNzcitW2oynUClTBUBgNVHR8ETTBLMEmgR6BFhkNodHRwOi8v
# d3d3Lm1pY3Jvc29mdC5jb20vcGtpb3BzL2NybC9NaWNDb2RTaWdQQ0EyMDExXzIw
# MTEtMDctMDguY3JsMGEGCCsGAQUFBwEBBFUwUzBRBggrBgEFBQcwAoZFaHR0cDov
# L3d3dy5taWNyb3NvZnQuY29tL3BraW9wcy9jZXJ0cy9NaWNDb2RTaWdQQ0EyMDEx
# XzIwMTEtMDctMDguY3J0MAwGA1UdEwEB/wQCMAAwDQYJKoZIhvcNAQELBQADggIB
# AFFo/6E4LX51IqFuoKvUsi80QytGI5ASQ9zsPpBa0z78hutiJd6w154JkcIx/f7r
# EBK4NhD4DIFNfRiVdI7EacEs7OAS6QHF7Nt+eFRNOTtgHb9PExRy4EI/jnMwzQJV
# NokTxu2WgHr/fBsWs6G9AcIgvHjWNN3qRSrhsgEdqHc0bRDUf8UILAdEZOMBvKLC
# rmf+kJPEvPldgK7hFO/L9kmcVe67BnKejDKO73Sa56AJOhM7CkeATrJFxO9GLXos
# oKvrwBvynxAg18W+pagTAkJefzneuWSmniTurPCUE2JnvW7DalvONDOtG01sIVAB
# +ahO2wcUPa2Zm9AiDVBWTMz9XUoKMcvngi2oqbsDLhbK+pYrRUgRpNt0y1sxZsXO
# raGRF8lM2cWvtEkV5UL+TQM1ppv5unDHkW8JS+QnfPbB8dZVRyRmMQ4aY/tx5x5+
# sX6semJ//FbiclSMxSI+zINu1jYerdUwuCi+P6p7SmQmClhDM+6Q+btE2FtpsU0W
# +r6RdYFf/P+nK6j2otl9Nvr3tWLu+WXmz8MGM+18ynJ+lYbSmFWcAj7SYziAfT0s
# IwlQRFkyC71tsIZUhBHtxPliGUu362lIO0Lpe0DOrg8lspnEWOkHnCT5JEnWCbzu
# iVt8RX1IV07uIveNZuOBWLVCzWJjEGa+HhaEtavjy6i7MIIHejCCBWKgAwIBAgIK
# YQ6Q0gAAAAAAAzANBgkqhkiG9w0BAQsFADCBiDELMAkGA1UEBhMCVVMxEzARBgNV
# BAgTCldhc2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jv
# c29mdCBDb3Jwb3JhdGlvbjEyMDAGA1UEAxMpTWljcm9zb2Z0IFJvb3QgQ2VydGlm
# aWNhdGUgQXV0aG9yaXR5IDIwMTEwHhcNMTEwNzA4MjA1OTA5WhcNMjYwNzA4MjEw
# OTA5WjB+MQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UE
# BxMHUmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSgwJgYD
# VQQDEx9NaWNyb3NvZnQgQ29kZSBTaWduaW5nIFBDQSAyMDExMIICIjANBgkqhkiG
# 9w0BAQEFAAOCAg8AMIICCgKCAgEAq/D6chAcLq3YbqqCEE00uvK2WCGfQhsqa+la
# UKq4BjgaBEm6f8MMHt03a8YS2AvwOMKZBrDIOdUBFDFC04kNeWSHfpRgJGyvnkmc
# 6Whe0t+bU7IKLMOv2akrrnoJr9eWWcpgGgXpZnboMlImEi/nqwhQz7NEt13YxC4D
# dato88tt8zpcoRb0RrrgOGSsbmQ1eKagYw8t00CT+OPeBw3VXHmlSSnnDb6gE3e+
# lD3v++MrWhAfTVYoonpy4BI6t0le2O3tQ5GD2Xuye4Yb2T6xjF3oiU+EGvKhL1nk
# kDstrjNYxbc+/jLTswM9sbKvkjh+0p2ALPVOVpEhNSXDOW5kf1O6nA+tGSOEy/S6
# A4aN91/w0FK/jJSHvMAhdCVfGCi2zCcoOCWYOUo2z3yxkq4cI6epZuxhH2rhKEmd
# X4jiJV3TIUs+UsS1Vz8kA/DRelsv1SPjcF0PUUZ3s/gA4bysAoJf28AVs70b1FVL
# 5zmhD+kjSbwYuER8ReTBw3J64HLnJN+/RpnF78IcV9uDjexNSTCnq47f7Fufr/zd
# sGbiwZeBe+3W7UvnSSmnEyimp31ngOaKYnhfsi+E11ecXL93KCjx7W3DKI8sj0A3
# T8HhhUSJxAlMxdSlQy90lfdu+HggWCwTXWCVmj5PM4TasIgX3p5O9JawvEagbJjS
# 4NaIjAsCAwEAAaOCAe0wggHpMBAGCSsGAQQBgjcVAQQDAgEAMB0GA1UdDgQWBBRI
# bmTlUAXTgqoXNzcitW2oynUClTAZBgkrBgEEAYI3FAIEDB4KAFMAdQBiAEMAQTAL
# BgNVHQ8EBAMCAYYwDwYDVR0TAQH/BAUwAwEB/zAfBgNVHSMEGDAWgBRyLToCMZBD
# uRQFTuHqp8cx0SOJNDBaBgNVHR8EUzBRME+gTaBLhklodHRwOi8vY3JsLm1pY3Jv
# c29mdC5jb20vcGtpL2NybC9wcm9kdWN0cy9NaWNSb29DZXJBdXQyMDExXzIwMTFf
# MDNfMjIuY3JsMF4GCCsGAQUFBwEBBFIwUDBOBggrBgEFBQcwAoZCaHR0cDovL3d3
# dy5taWNyb3NvZnQuY29tL3BraS9jZXJ0cy9NaWNSb29DZXJBdXQyMDExXzIwMTFf
# MDNfMjIuY3J0MIGfBgNVHSAEgZcwgZQwgZEGCSsGAQQBgjcuAzCBgzA/BggrBgEF
# BQcCARYzaHR0cDovL3d3dy5taWNyb3NvZnQuY29tL3BraW9wcy9kb2NzL3ByaW1h
# cnljcHMuaHRtMEAGCCsGAQUFBwICMDQeMiAdAEwAZQBnAGEAbABfAHAAbwBsAGkA
# YwB5AF8AcwB0AGEAdABlAG0AZQBuAHQALiAdMA0GCSqGSIb3DQEBCwUAA4ICAQBn
# 8oalmOBUeRou09h0ZyKbC5YR4WOSmUKWfdJ5DJDBZV8uLD74w3LRbYP+vj/oCso7
# v0epo/Np22O/IjWll11lhJB9i0ZQVdgMknzSGksc8zxCi1LQsP1r4z4HLimb5j0b
# pdS1HXeUOeLpZMlEPXh6I/MTfaaQdION9MsmAkYqwooQu6SpBQyb7Wj6aC6VoCo/
# KmtYSWMfCWluWpiW5IP0wI/zRive/DvQvTXvbiWu5a8n7dDd8w6vmSiXmE0OPQvy
# CInWH8MyGOLwxS3OW560STkKxgrCxq2u5bLZ2xWIUUVYODJxJxp/sfQn+N4sOiBp
# mLJZiWhub6e3dMNABQamASooPoI/E01mC8CzTfXhj38cbxV9Rad25UAqZaPDXVJi
# hsMdYzaXht/a8/jyFqGaJ+HNpZfQ7l1jQeNbB5yHPgZ3BtEGsXUfFL5hYbXw3MYb
# BL7fQccOKO7eZS/sl/ahXJbYANahRr1Z85elCUtIEJmAH9AAKcWxm6U/RXceNcbS
# oqKfenoi+kiVH6v7RyOA9Z74v2u3S5fi63V4GuzqN5l5GEv/1rMjaHXmr/r8i+sL
# gOppO6/8MO0ETI7f33VtY5E90Z1WTk+/gFcioXgRMiF670EKsT/7qMykXcGhiJtX
# cVZOSEXAQsmbdlsKgEhr/Xmfwb1tbWrJUnMTDXpQzTGCGiYwghoiAgEBMIGVMH4x
# CzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRt
# b25kMR4wHAYDVQQKExVNaWNyb3NvZnQgQ29ycG9yYXRpb24xKDAmBgNVBAMTH01p
# Y3Jvc29mdCBDb2RlIFNpZ25pbmcgUENBIDIwMTECEzMAAAQDvdWVXQ87GK0AAAAA
# BAMwDQYJYIZIAWUDBAIBBQCgga4wGQYJKoZIhvcNAQkDMQwGCisGAQQBgjcCAQQw
# HAYKKwYBBAGCNwIBCzEOMAwGCisGAQQBgjcCARUwLwYJKoZIhvcNAQkEMSIEIPY9
# Sf26W73doP/wo3iTUcBI3w1XorzD+JCkBas/HFTlMEIGCisGAQQBgjcCAQwxNDAy
# oBSAEgBNAGkAYwByAG8AcwBvAGYAdKEagBhodHRwOi8vd3d3Lm1pY3Jvc29mdC5j
# b20wDQYJKoZIhvcNAQEBBQAEggEAcMr3ebZfTlXbS4eAVgRF32hlMYLC01QO8qWw
# lbVhr/cFAbkyJxJrTDqASjXYKORUm8n/Qeka0A2DA5wOh/pgUsry6g16E76FEjeq
# GJiTehQD43EDQWQ+pdjre9DoOcPur8UJiiU6W1g6+c4p+/W32mhfO2w3o/gP88Db
# G875VRnQyVCwkSV9XmkASlp2RnTdjBZ1kX3Q4/GugWbLJgPenR2EsH1rkSu9jvsG
# yAoQB1UIiP0RS3Gr/jL9UsOdP51av4ak5KqU6UY9yiP3CVKSYBW0uFawKC9EPt5N
# yQtMj58hCw5L53KeI2TXbCkvjb4LnmD5lI+WhJhytAgoW4HeIKGCF7AwghesBgor
# BgEEAYI3AwMBMYIXnDCCF5gGCSqGSIb3DQEHAqCCF4kwgheFAgEDMQ8wDQYJYIZI
# AWUDBAIBBQAwggFaBgsqhkiG9w0BCRABBKCCAUkEggFFMIIBQQIBAQYKKwYBBAGE
# WQoDATAxMA0GCWCGSAFlAwQCAQUABCD/DFpFVtQFuw3nNYgCHWsAj89HtN7OwhPo
# aHe1cXwDRQIGaFMyquK6GBMyMDI1MDcwOTExMDcyNC43MjdaMASAAgH0oIHZpIHW
# MIHTMQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMH
# UmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMS0wKwYDVQQL
# EyRNaWNyb3NvZnQgSXJlbGFuZCBPcGVyYXRpb25zIExpbWl0ZWQxJzAlBgNVBAsT
# Hm5TaGllbGQgVFNTIEVTTjo2RjFBLTA1RTAtRDk0NzElMCMGA1UEAxMcTWljcm9z
# b2Z0IFRpbWUtU3RhbXAgU2VydmljZaCCEf4wggcoMIIFEKADAgECAhMzAAAB/Big
# r8xpWoc6AAEAAAH8MA0GCSqGSIb3DQEBCwUAMHwxCzAJBgNVBAYTAlVTMRMwEQYD
# VQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNy
# b3NvZnQgQ29ycG9yYXRpb24xJjAkBgNVBAMTHU1pY3Jvc29mdCBUaW1lLVN0YW1w
# IFBDQSAyMDEwMB4XDTI0MDcyNTE4MzExNFoXDTI1MTAyMjE4MzExNFowgdMxCzAJ
# BgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25k
# MR4wHAYDVQQKExVNaWNyb3NvZnQgQ29ycG9yYXRpb24xLTArBgNVBAsTJE1pY3Jv
# c29mdCBJcmVsYW5kIE9wZXJhdGlvbnMgTGltaXRlZDEnMCUGA1UECxMeblNoaWVs
# ZCBUU1MgRVNOOjZGMUEtMDVFMC1EOTQ3MSUwIwYDVQQDExxNaWNyb3NvZnQgVGlt
# ZS1TdGFtcCBTZXJ2aWNlMIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEA
# p1DAKLxpbQcPVYPHlJHyW7W5lBZjJWWDjMfl5WyhuAylP/LDm2hb4ymUmSymV0EF
# RQcmM8BypwjhWP8F7x4iO88d+9GZ9MQmNh3jSDohhXXgf8rONEAyfCPVmJzM7yts
# urZ9xocbuEL7+P7EkIwoOuMFlTF2G/zuqx1E+wANslpPqPpb8PC56BQxgJCI1LOF
# 5lk3AePJ78OL3aw/NdlkvdVl3VgBSPX4Nawt3UgUofuPn/cp9vwKKBwuIWQEFZ83
# 7GXXITshd2Mfs6oYfxXEtmj2SBGEhxVs7xERuWGb0cK6afy7naKkbZI2v1UqsxuZ
# t94rn/ey2ynvunlx0R6/b6nNkC1rOTAfWlpsAj/QlzyM6uYTSxYZC2YWzLbbRl0l
# RtSz+4TdpUU/oAZSB+Y+s12Rqmgzi7RVxNcI2lm//sCEm6A63nCJCgYtM+LLe9pT
# shl/Wf8OOuPQRiA+stTsg89BOG9tblaz2kfeOkYf5hdH8phAbuOuDQfr6s5Ya6W+
# vZz6E0Zsenzi0OtMf5RCa2hADYVgUxD+grC8EptfWeVAWgYCaQFheNN/ZGNQMkk7
# 8V63yoPBffJEAu+B5xlTPYoijUdo9NXovJmoGXj6R8Tgso+QPaAGHKxCbHa1QL9A
# SMF3Os1jrogCHGiykfp1dKGnmA5wJT6Nx7BedlSDsAkCAwEAAaOCAUkwggFFMB0G
# A1UdDgQWBBSY8aUrsUazhxByH79dhiQCL/7QdjAfBgNVHSMEGDAWgBSfpxVdAF5i
# XYP05dJlpxtTNRnpcjBfBgNVHR8EWDBWMFSgUqBQhk5odHRwOi8vd3d3Lm1pY3Jv
# c29mdC5jb20vcGtpb3BzL2NybC9NaWNyb3NvZnQlMjBUaW1lLVN0YW1wJTIwUENB
# JTIwMjAxMCgxKS5jcmwwbAYIKwYBBQUHAQEEYDBeMFwGCCsGAQUFBzAChlBodHRw
# Oi8vd3d3Lm1pY3Jvc29mdC5jb20vcGtpb3BzL2NlcnRzL01pY3Jvc29mdCUyMFRp
# bWUtU3RhbXAlMjBQQ0ElMjAyMDEwKDEpLmNydDAMBgNVHRMBAf8EAjAAMBYGA1Ud
# JQEB/wQMMAoGCCsGAQUFBwMIMA4GA1UdDwEB/wQEAwIHgDANBgkqhkiG9w0BAQsF
# AAOCAgEAT7ss/ZAZ0bTaFsrsiJYd//LQ6ImKb9JZSKiRw9xs8hwk5Y/7zign9gGt
# weRChC2lJ8GVRHgrFkBxACjuuPprSz/UYX7n522JKcudnWuIeE1p30BZrqPTOnsc
# D98DZi6WNTAymnaS7it5qAgNInreAJbTU2cAosJoeXAHr50YgSGlmJM+cN6mYLAL
# 6TTFMtFYJrpK9TM5Ryh5eZmm6UTJnGg0jt1pF/2u8PSdz3dDy7DF7KDJad2qHxZO
# RvM3k9V8Yn3JI5YLPuLso2J5s3fpXyCVgR/hq86g5zjd9bRRyyiC8iLIm/N95q6H
# WVsCeySetrqfsDyYWStwL96hy7DIyLL5ih8YFMd0AdmvTRoylmADuKwE2TQCTvPn
# jnLk7ypJW29t17Yya4V+Jlz54sBnPU7kIeYZsvUT+YKgykP1QB+p+uUdRH6e79Va
# iz+iewWrIJZ4tXkDMmL21nh0j+58E1ecAYDvT6B4yFIeonxA/6Gl9Xs7JLciPCIC
# 6hGdliiEBpyYeUF0ohZFn7NKQu80IZ0jd511WA2bq6x9aUq/zFyf8Egw+dunUj1K
# tNoWpq7VuJqapckYsmvmmYHZXCjK1Eus7V1I+aXjrBYuqyM9QpeFZU4U01YG15uW
# wUCaj0uZlah/RGSYMd84y9DCqOpfeKE6PLMk7hLnhvcOQrnxP6kwggdxMIIFWaAD
# AgECAhMzAAAAFcXna54Cm0mZAAAAAAAVMA0GCSqGSIb3DQEBCwUAMIGIMQswCQYD
# VQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEe
# MBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMTIwMAYDVQQDEylNaWNyb3Nv
# ZnQgUm9vdCBDZXJ0aWZpY2F0ZSBBdXRob3JpdHkgMjAxMDAeFw0yMTA5MzAxODIy
# MjVaFw0zMDA5MzAxODMyMjVaMHwxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNo
# aW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNyb3NvZnQgQ29y
# cG9yYXRpb24xJjAkBgNVBAMTHU1pY3Jvc29mdCBUaW1lLVN0YW1wIFBDQSAyMDEw
# MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEA5OGmTOe0ciELeaLL1yR5
# vQ7VgtP97pwHB9KpbE51yMo1V/YBf2xK4OK9uT4XYDP/XE/HZveVU3Fa4n5KWv64
# NmeFRiMMtY0Tz3cywBAY6GB9alKDRLemjkZrBxTzxXb1hlDcwUTIcVxRMTegCjhu
# je3XD9gmU3w5YQJ6xKr9cmmvHaus9ja+NSZk2pg7uhp7M62AW36MEBydUv626GIl
# 3GoPz130/o5Tz9bshVZN7928jaTjkY+yOSxRnOlwaQ3KNi1wjjHINSi947SHJMPg
# yY9+tVSP3PoFVZhtaDuaRr3tpK56KTesy+uDRedGbsoy1cCGMFxPLOJiss254o2I
# 5JasAUq7vnGpF1tnYN74kpEeHT39IM9zfUGaRnXNxF803RKJ1v2lIH1+/NmeRd+2
# ci/bfV+AutuqfjbsNkz2K26oElHovwUDo9Fzpk03dJQcNIIP8BDyt0cY7afomXw/
# TNuvXsLz1dhzPUNOwTM5TI4CvEJoLhDqhFFG4tG9ahhaYQFzymeiXtcodgLiMxhy
# 16cg8ML6EgrXY28MyTZki1ugpoMhXV8wdJGUlNi5UPkLiWHzNgY1GIRH29wb0f2y
# 1BzFa/ZcUlFdEtsluq9QBXpsxREdcu+N+VLEhReTwDwV2xo3xwgVGD94q0W29R6H
# XtqPnhZyacaue7e3PmriLq0CAwEAAaOCAd0wggHZMBIGCSsGAQQBgjcVAQQFAgMB
# AAEwIwYJKwYBBAGCNxUCBBYEFCqnUv5kxJq+gpE8RjUpzxD/LwTuMB0GA1UdDgQW
# BBSfpxVdAF5iXYP05dJlpxtTNRnpcjBcBgNVHSAEVTBTMFEGDCsGAQQBgjdMg30B
# ATBBMD8GCCsGAQUFBwIBFjNodHRwOi8vd3d3Lm1pY3Jvc29mdC5jb20vcGtpb3Bz
# L0RvY3MvUmVwb3NpdG9yeS5odG0wEwYDVR0lBAwwCgYIKwYBBQUHAwgwGQYJKwYB
# BAGCNxQCBAweCgBTAHUAYgBDAEEwCwYDVR0PBAQDAgGGMA8GA1UdEwEB/wQFMAMB
# Af8wHwYDVR0jBBgwFoAU1fZWy4/oolxiaNE9lJBb186aGMQwVgYDVR0fBE8wTTBL
# oEmgR4ZFaHR0cDovL2NybC5taWNyb3NvZnQuY29tL3BraS9jcmwvcHJvZHVjdHMv
# TWljUm9vQ2VyQXV0XzIwMTAtMDYtMjMuY3JsMFoGCCsGAQUFBwEBBE4wTDBKBggr
# BgEFBQcwAoY+aHR0cDovL3d3dy5taWNyb3NvZnQuY29tL3BraS9jZXJ0cy9NaWNS
# b29DZXJBdXRfMjAxMC0wNi0yMy5jcnQwDQYJKoZIhvcNAQELBQADggIBAJ1Vffwq
# reEsH2cBMSRb4Z5yS/ypb+pcFLY+TkdkeLEGk5c9MTO1OdfCcTY/2mRsfNB1OW27
# DzHkwo/7bNGhlBgi7ulmZzpTTd2YurYeeNg2LpypglYAA7AFvonoaeC6Ce5732pv
# vinLbtg/SHUB2RjebYIM9W0jVOR4U3UkV7ndn/OOPcbzaN9l9qRWqveVtihVJ9Ak
# vUCgvxm2EhIRXT0n4ECWOKz3+SmJw7wXsFSFQrP8DJ6LGYnn8AtqgcKBGUIZUnWK
# NsIdw2FzLixre24/LAl4FOmRsqlb30mjdAy87JGA0j3mSj5mO0+7hvoyGtmW9I/2
# kQH2zsZ0/fZMcm8Qq3UwxTSwethQ/gpY3UA8x1RtnWN0SCyxTkctwRQEcb9k+SS+
# c23Kjgm9swFXSVRk2XPXfx5bRAGOWhmRaw2fpCjcZxkoJLo4S5pu+yFUa2pFEUep
# 8beuyOiJXk+d0tBMdrVXVAmxaQFEfnyhYWxz/gq77EFmPWn9y8FBSX5+k77L+Dvk
# txW/tM4+pTFRhLy/AsGConsXHRWJjXD+57XQKBqJC4822rpM+Zv/Cuk0+CQ1Zyvg
# DbjmjJnW4SLq8CdCPSWU5nR0W2rRnj7tfqAxM328y+l7vzhwRNGQ8cirOoo6CGJ/
# 2XBjU02N7oJtpQUQwXEGahC0HVUzWLOhcGbyoYIDWTCCAkECAQEwggEBoYHZpIHW
# MIHTMQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMH
# UmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMS0wKwYDVQQL
# EyRNaWNyb3NvZnQgSXJlbGFuZCBPcGVyYXRpb25zIExpbWl0ZWQxJzAlBgNVBAsT
# Hm5TaGllbGQgVFNTIEVTTjo2RjFBLTA1RTAtRDk0NzElMCMGA1UEAxMcTWljcm9z
# b2Z0IFRpbWUtU3RhbXAgU2VydmljZaIjCgEBMAcGBSsOAwIaAxUATkEpJXOaqI2w
# fqBsw4NLVwqYqqqggYMwgYCkfjB8MQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2Fz
# aGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENv
# cnBvcmF0aW9uMSYwJAYDVQQDEx1NaWNyb3NvZnQgVGltZS1TdGFtcCBQQ0EgMjAx
# MDANBgkqhkiG9w0BAQsFAAIFAOwYtoAwIhgPMjAyNTA3MDkwOTM2MDBaGA8yMDI1
# MDcxMDA5MzYwMFowdzA9BgorBgEEAYRZCgQBMS8wLTAKAgUA7Bi2gAIBADAKAgEA
# AgITQAIB/zAHAgEAAgITYTAKAgUA7BoIAAIBADA2BgorBgEEAYRZCgQCMSgwJjAM
# BgorBgEEAYRZCgMCoAowCAIBAAIDB6EgoQowCAIBAAIDAYagMA0GCSqGSIb3DQEB
# CwUAA4IBAQBgZZmnPe/t/zyWITc1Ge0vvtIb2ppc7xbZzG2BBywlfH7Q+gfaDyDs
# 0KpgFf/IECRZ/lYg5gZF/Ehor80aLxCPOYyKwCLZNadycGqiIwrrdIiVbN9rqZMk
# WAs4KMTYnXcPsr+bMK+pAWqEZbD53bBuz/cPV9tATNSdGSUh/NvKZ7OkgJeSMxbZ
# FsFu5JCsQDBwbEzAGBzM0sN2SyDhOVbHOf+GVihN1F3TwWP5IJHaVLOxxmIBeYek
# n3XhyUFpWySj4omNbMhdvFdTIjwjSNyVEcG4wgg1cmX+6u3LGcoce0DaYPqX2CwK
# X5n3KL5iHGzrJJclyto3nGNOYVBxuYCeMYIEDTCCBAkCAQEwgZMwfDELMAkGA1UE
# BhMCVVMxEzARBgNVBAgTCldhc2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAc
# BgNVBAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEmMCQGA1UEAxMdTWljcm9zb2Z0
# IFRpbWUtU3RhbXAgUENBIDIwMTACEzMAAAH8GKCvzGlahzoAAQAAAfwwDQYJYIZI
# AWUDBAIBBQCgggFKMBoGCSqGSIb3DQEJAzENBgsqhkiG9w0BCRABBDAvBgkqhkiG
# 9w0BCQQxIgQghQHmrXvgEGE6WJVM1xBhfmOBvHgQjk0aBaR6Wz0ljFQwgfoGCyqG
# SIb3DQEJEAIvMYHqMIHnMIHkMIG9BCCVQq+Qu+/h/BOVP4wweUwbHuCUhh+T7hq3
# d5MCaNEtYjCBmDCBgKR+MHwxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNoaW5n
# dG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNyb3NvZnQgQ29ycG9y
# YXRpb24xJjAkBgNVBAMTHU1pY3Jvc29mdCBUaW1lLVN0YW1wIFBDQSAyMDEwAhMz
# AAAB/Bigr8xpWoc6AAEAAAH8MCIEIM5T+pGqjHKdncAUdE8IRKJC6je47T3y4R7y
# OXX3UYu0MA0GCSqGSIb3DQEBCwUABIICAA4XoV9vUAZy0PFv77p3AaT0K5qMqUm7
# kJgGQjg6VlyMHqq7hCX/qcsXKmre25luGSM1pGZ8fsE+GrwzGEoV/iHESI+Xog3d
# n95uXYy/uquiU497fm1PpxozjKvsicyyvFBTp3tTzTQNQOdQoEslt8xWNht47oMW
# xYPm+c/PIzjia8tsc7YzDSVoFBrzysKb78kzJrFw4oc2+lJ2Y/hYiDk1DnNNOtYx
# 2fevngt3WzpW/eHUGFtkOKEq84v5t4d1Z5iKiknuGLIMgiq70t6Wj6dBSj42YryQ
# s/F1dsvCpNOznusceg9UxanO1NpILXcfktwo34zpsY+1k7Q9EraSIJM9HmV08VXe
# GkL7OoxBBwiGdU7eL/CxV1nd/TSakHXD/I6b6uJwVj92jRrJb86gtOtPAiHbIMHk
# DpUc8LeZwAauBwxx026VVw0nC2D22LyHYSoidDtKo/Wm6bSnOTMB4iT+DddUMuMU
# sAz0C6QuW4I9d0ytT9VZLqDlcw3Z3a9fM6zBCVhTNhrFRvVI4jSQkV5vOq+GcyDt
# hpOpI6rtFlRJVpcFnGqLLRWZ4IyGiCM7EYFvV+hthctXsBT1xapP8y8LQk776aiL
# EeYDqiL9ahq6H7FQ9sd2/HgyLfceCOhkRxw+4PdL66JMQeAhBvNg5tZuThUMQ/ok
# ZGZZZVpYx/Xg
# SIG # End signature block
