﻿<?xml version="1.0" encoding="utf-8"?>
<helpItems schema="maml" xmlns="http://msh">
	<command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
		<command:details>
			<command:name>Get-ConnectionInformation</command:name>
			<command:verb>Get</command:verb>
			<command:noun>ConnectionInformation</command:noun>
			<maml:description>
				<maml:para>This cmdlet is available in the Exchange Online PowerShell module v3.0.0 or later. For more information, see About the Exchange Online PowerShell module (https://aka.ms/exov3-module).</maml:para>
				<maml:para>Use the Get-ConnectionInformation cmdlet to get information about all REST-based connections in the current PowerShell instance with Exchange Online.</maml:para>
				<maml:para>For information about the parameter sets in the Syntax section below, see Exchange cmdlet syntax (https://learn.microsoft.com/powershell/exchange/exchange-cmdlet-syntax).</maml:para>
			</maml:description>
		</command:details>
		<maml:description>
			<maml:para>The Get-ConnectionInformation cmdlet returns the information about all active REST-based connections with Exchange Online in the current PowerShell instance. This cmdlet is equivalent to the Get-PSSession cmdlet that's used with remote PowerShell sessions.</maml:para>
			<maml:para>The output of the cmdlet contains the following properties:</maml:para>
			<maml:para>- ConnectionId: A unique GUID value for the connection. For example, 8b632b3a-a2e2-8ff3-adcd-6d119d07694b.</maml:para>
			<maml:para>- State: For example, Connected.</maml:para>
			<maml:para>- Id: An integer that identifies the session in the PowerShell window. The first connection is 1, the second is 2, etc.</maml:para>
			<maml:para>- Name: A unique name that's based on the PowerShell environment and Id value. For example, ExchangeOnline_1 for Exchange Online PowerShell or ExchangeOnlineProtection_1 for Security &amp; Compliance PowerShell.</maml:para>
			<maml:para>- UserPrincipalName: The account that was used to connect. For example, `<EMAIL>`.</maml:para>
			<maml:para>- ConnectionUri: The connection endpoint that was used. For example, `https://outlook.office365.com` for Exchange Online PowerShell or `https://nam12b.ps.compliance.protection.outlook.com` for Security &amp; Compliance PowerShell.</maml:para>
			<maml:para>- AzureAdAuthorizationEndpointUri : The Microsoft Entra authorization endpoint for the connection. For example, `https://login.microsoftonline.com/organizations` for Exchange Online PowerShell or `https://login.microsoftonline.com/organizations` for Security &amp; Compliance PowerShell.</maml:para>
			<maml:para>- TokenExpiryTimeUTC: When the connection token expires. For example, 9/30/2023 6:42:24 PM +00:00.</maml:para>
			<maml:para>- CertificateAuthentication: Whether certificate based authentication (also known as CBA or app-only authentication) was used to connect. Values are True or False.</maml:para>
			<maml:para>- ModuleName: The filename and path of the temporary data for the session. For example, C:\Users\<USER>\AppData\Local\Temp\tmpEXO_a54z135k.qgv</maml:para>
			<maml:para>- ModulePrefix: The value specified using the Prefix parameter in the Connect-ExchangeOnline or Connect-IPPSSession command.</maml:para>
			<maml:para>- Organization: The value specified using the Organization parameter in the Connect-ExchangeOnline or Connect-IPPSSession command for CBA or managed identity connections.</maml:para>
			<maml:para>- DelegatedOrganization: The value specified using the DelegatedOrganization parameter in the Connect-ExchangeOnline or Connect-IPPSSession command.</maml:para>
			<maml:para>- AppId: The value specified using the AppId parameter in the Connect-ExchangeOnline or Connect-IPPSSession command for CBA connections.</maml:para>
			<maml:para>- PageSize: The default maximum number of entries per page in the connection. The default value is 1000, or you can use the PageSize parameter in the Connect-ExchangeOnline command to specify a lower number. Individual cmdlets might also have a PageSize parameter.</maml:para>
			<maml:para>- TenantID: The tenant ID GUID value. For example, 3750b40b-a68b-4632-9fb3-5b1aff664079.</maml:para>
			<maml:para>- TokenStatus: For example, Active.</maml:para>
			<maml:para>- ConnectionUsedForInbuiltCmdlets</maml:para>
			<maml:para>- IsEopSession: For Exchange Online PowerShell connections, the value is False. For Security &amp; Compliance PowerShell connections, the value is True.</maml:para>
		</maml:description>
		<command:syntax>
			<command:syntaxItem>
				<maml:name>Get-ConnectionInformation</maml:name>
				<command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
					<maml:name>ConnectionId</maml:name>
					<maml:description>
						<maml:para>Note : This parameter is available in version 3.2.0 or later of the module.</maml:para>
						<maml:para>The ConnectionId parameter filters the connections by ConnectionId. ConnectionId is a GUID value in the output of the Get-ConnectionInformation cmdlet that uniquely identifies a connection, even if you have multiple connections open. You can specify multiple ConnectionId values separated by commas.</maml:para>
						<maml:para>Don't use this parameter with the ModulePrefix parameter.</maml:para>
					</maml:description>
					<command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
					<dev:type>
						<maml:name>String[]</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>None</dev:defaultValue>
				</command:parameter>
			</command:syntaxItem>
			<command:syntaxItem>
				<maml:name>Get-ConnectionInformation</maml:name>
				<command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
					<maml:name>ModulePrefix</maml:name>
					<maml:description>
						<maml:para>Note : This parameter is available in version 3.2.0 or later of the module.</maml:para>
						<maml:para>The ModulePrefix parameter filters the connections by ModulePrefix. When you use the Prefix parameter with the Connect-ExchangeOnline cmdlet, the specified text is added to the names of all Exchange Online cmdlets (for example, Get-InboundConnector becomes Get-ContosoInboundConnector). The ModulePrefix value is visible in the output of the Get-ConnectionInformation cmdlet. You can specify multiple ModulePrefix values separated by commas.</maml:para>
						<maml:para>This parameter is meaningful only for connections that were created with the Prefix parameter.</maml:para>
						<maml:para>Don't use this parameter with the ConnectionId parameter.</maml:para>
					</maml:description>
					<command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
					<dev:type>
						<maml:name>String[]</maml:name>
						<maml:uri />
					</dev:type>
					<dev:defaultValue>None</dev:defaultValue>
				</command:parameter>
			</command:syntaxItem>
		</command:syntax>
		<command:parameters>
			<command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
				<maml:name>ConnectionId</maml:name>
				<maml:description>
					<maml:para>Note : This parameter is available in version 3.2.0 or later of the module.</maml:para>
					<maml:para>The ConnectionId parameter filters the connections by ConnectionId. ConnectionId is a GUID value in the output of the Get-ConnectionInformation cmdlet that uniquely identifies a connection, even if you have multiple connections open. You can specify multiple ConnectionId values separated by commas.</maml:para>
					<maml:para>Don't use this parameter with the ModulePrefix parameter.</maml:para>
				</maml:description>
				<command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
				<dev:type>
					<maml:name>String[]</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>None</dev:defaultValue>
			</command:parameter>
			<command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
				<maml:name>ModulePrefix</maml:name>
				<maml:description>
					<maml:para>Note : This parameter is available in version 3.2.0 or later of the module.</maml:para>
					<maml:para>The ModulePrefix parameter filters the connections by ModulePrefix. When you use the Prefix parameter with the Connect-ExchangeOnline cmdlet, the specified text is added to the names of all Exchange Online cmdlets (for example, Get-InboundConnector becomes Get-ContosoInboundConnector). The ModulePrefix value is visible in the output of the Get-ConnectionInformation cmdlet. You can specify multiple ModulePrefix values separated by commas.</maml:para>
					<maml:para>This parameter is meaningful only for connections that were created with the Prefix parameter.</maml:para>
					<maml:para>Don't use this parameter with the ConnectionId parameter.</maml:para>
				</maml:description>
				<command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
				<dev:type>
					<maml:name>String[]</maml:name>
					<maml:uri />
				</dev:type>
				<dev:defaultValue>None</dev:defaultValue>
			</command:parameter>
		</command:parameters>
		<command:inputTypes />
		<command:returnValues />
		<maml:alertSet>
			<maml:alert>
				<maml:para></maml:para>
			</maml:alert>
		</maml:alertSet>
		<command:examples>
			<command:example>
				<maml:title>-------------------------- Example 1 --------------------------</maml:title>
				<dev:code>Get-ConnectionInformation</dev:code>
				<dev:remarks>
					<maml:para>This example returns a list of all active REST-based connections with Exchange Online in the current PowerShell instance.</maml:para>
				</dev:remarks>
			</command:example>
			<command:example>
				<maml:title>-------------------------- Example 2 --------------------------</maml:title>
				<dev:code>Get-ConnectionInformation -ConnectionId 1a9e45e8-e7ec-498f-9ac3-0504e987fa85</dev:code>
				<dev:remarks>
					<maml:para>This example returns the active REST-based connection with the specified ConnectionId value.</maml:para>
				</dev:remarks>
			</command:example>
			<command:example>
				<maml:title>-------------------------- Example 3 --------------------------</maml:title>
				<dev:code>Get-ConnectionInformation -ModulePrefix Contoso,Fabrikam</dev:code>
				<dev:remarks>
					<maml:para>This example returns a list of active REST-based connections that are using the specified prefix values.</maml:para>
				</dev:remarks>
			</command:example>
		</command:examples>
		<command:relatedLinks>
			<maml:navigationLink>
				<maml:linkText>Online Version:</maml:linkText>
				<maml:uri>https://learn.microsoft.com/powershell/module/exchange/get-connectioninformation</maml:uri>
			</maml:navigationLink>
		</command:relatedLinks>
	</command:command>
</helpItems>