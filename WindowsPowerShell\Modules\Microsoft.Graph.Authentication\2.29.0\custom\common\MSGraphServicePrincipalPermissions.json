{"loginUrl": null, "displayName": "Microsoft Graph", "signInAudience": "AzureADMultipleOrgs", "disabledByMicrosoftStatus": null, "appRoles": [{"displayName": "Read and write workforce integrations", "value": "WorkforceIntegration.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to manage workforce integrations to synchronize data from Microsoft Teams Shifts, without a signed-in user.", "id": "202bf709-e8e6-478e-bcfd-5d63c50b68e3"}, {"displayName": "Read and write presence information for all users", "value": "Presence.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read all presence information and write activity and availability of all users in the directory without a signed-in user. Presence information includes activity, availability, status note, calendar out-of-office message, time zone and location.", "id": "83cded22-8297-4ff6-a7fa-e97e9545a259"}, {"displayName": "Read and write tags in Teams", "value": "TeamworkTag.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read and write tags in Teams without a signed-in user.", "id": "a3371ca5-911d-46d6-901c-42c8c7a937d8"}, {"displayName": "Read tags in Teams", "value": "TeamworkTag.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read tags in Teams without a signed-in user.", "id": "b74fd6c4-4bde-488e-9695-eeb100e4907f"}, {"displayName": "Read and write all Windows update deployment settings", "value": "WindowsUpdates.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read and write all Windows update deployment settings for the organization without a signed-in user.", "id": "7dd1be58-6e76-4401-bf8d-31d1e8180d5b"}, {"displayName": "Read and write external connections", "value": "ExternalConnection.ReadWrite.OwnedBy", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read and write external connections without a signed-in user. The app can only read and write external connections that it is authorized to, or it can create new external connections. ", "id": "f431331c-49a6-499f-be1c-62af19c34a9d"}, {"displayName": "Read and write external items", "value": "ExternalItem.ReadWrite.OwnedBy", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read and write external items without a signed-in user. The app can only read external items of the connection that it is authorized to.", "id": "8116ae0f-55c2-452d-9944-d18420f5b2c8"}, {"displayName": "Access selected site collections", "value": "Sites.Selected", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allow the application to access a subset of site collections without a signed in user.  The specific site collections and the permissions granted will be configured in SharePoint Online.", "id": "883ea226-0bf2-4a8f-9f9d-92c9162a727d"}, {"displayName": "Read items in all site collections ", "value": "Sites.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read documents and list items in all site collections without a signed in user.", "id": "332a536c-c7ef-4017-ab91-336970924f0d"}, {"displayName": "Read and write items in all site collections", "value": "Sites.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to create, read, update, and delete documents and list items in all site collections without a signed in user.", "id": "9492366f-7969-46a4-8d15-ed1a20078fff"}, {"displayName": "Read and write Cloud PCs", "value": "CloudPC.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read and write the properties of Cloud PCs, without a signed-in user.", "id": "3b4349e1-8cf5-45a3-95b7-69d1751d3e6a"}, {"displayName": "Read Cloud PCs", "value": "CloudPC.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read the properties of Cloud PCs, without a signed-in user.", "id": "a9e09520-8ed4-4cde-838e-4fdea192c227"}, {"displayName": "Read and update service principal endpoints", "value": "ServicePrincipalEndpoint.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to update service principal endpoints", "id": "89c8469c-83ad-45f7-8ff2-6e3d4285709e"}, {"displayName": "Read service principal endpoints", "value": "ServicePrincipalEndpoint.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read service principal endpoints", "id": "5256681e-b7f6-40c0-8447-2d9db68797a0"}, {"displayName": "Send a teamwork activity to any user", "value": "TeamsActivity.Send", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to create new notifications in users' teamwork activity feeds without a signed in user. These notifications may not be discoverable or be held or governed by compliance policies.", "id": "a267235f-af13-44dc-8385-c1dc93023186"}, {"displayName": "Read all terms of use acceptance statuses", "value": "AgreementAcceptance.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read terms of use acceptance statuses, without a signed in user.", "id": "d8e4ec18-f6c0-4620-8122-c8b1f2bf400e"}, {"displayName": "Read and write all terms of use agreements", "value": "Agreement.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read and write terms of use agreements, without a signed in user.", "id": "c9090d00-6101-42f0-a729-c41074260d47"}, {"displayName": "Read all terms of use agreements", "value": "Agreement.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read terms of use agreements, without a signed in user.", "id": "2f3e6f8c-093b-4c57-a58b-ba5ce494a169"}, {"displayName": "Read and write all consent requests", "value": "ConsentRequest.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read app consent requests and approvals, and deny or approve those requests without a signed-in user.", "id": "9f1b81a7-0223-4428-bfa4-0bcb5535f27d"}, {"displayName": "Read and write your organization's consent request policy", "value": "Policy.ReadWrite.ConsentRequest", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read and write your organization's consent requests policy without a signed-in user.", "id": "999f8c63-0a38-4f1b-91fd-ed1947bdd1a9"}, {"displayName": "Read all consent requests", "value": "ConsentRequest.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read consent requests and approvals without a signed-in user.", "id": "1260ad83-98fb-4785-abbb-d6cc1806fd41"}, {"displayName": "Read basic mail in all mailboxes", "value": "Mail.ReadBasic.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read basic mail properties in all mailboxes without a signed-in user. Includes all properties except body, previewBody, attachments and any extended properties.", "id": "693c5e45-0940-467d-9b8a-1022fb9d42ef"}, {"displayName": "Read basic mail in all mailboxes", "value": "Mail.ReadBasic", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read basic mail properties in all mailboxes without a signed-in user. Includes all properties except body, previewBody, attachments and any extended properties.", "id": "6be147d2-ea4f-4b5a-a3fa-3eab6f3c140a"}, {"displayName": "Read and write feature rollout policies", "value": "Policy.ReadWrite.FeatureRollout", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read and write feature rollout policies without a signed-in user. Includes abilities to assign and remove users and groups to rollout of a specific feature.", "id": "2044e4f1-e56c-435b-925c-44cd8f6ba89a"}, {"displayName": "Read and write all directory RBAC settings", "value": "RoleManagement.ReadWrite.Directory", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read and manage the role-based access control (RBAC) settings for your company's directory, without a signed-in user. This includes instantiating directory roles and managing directory role membership, and reading directory role templates, directory roles and memberships.", "id": "9e3f62cf-ca93-4989-b6ce-bf83c28f9fe8"}, {"displayName": "Read all directory RBAC settings", "value": "RoleManagement.Read.Directory", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read the role-based access control (RBAC) settings for your company's directory, without a signed-in user.  This includes reading directory role templates, directory roles and memberships.", "id": "483bed4a-2ad3-4361-a73b-c83ccdbdc53c"}, {"displayName": "Read and write organization information", "value": "Organization.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read and write the organization and related resources, without a signed-in user. Related resources include things like subscribed skus and tenant branding information.", "id": "292d869f-3427-49a8-9dab-8c70152b74e9"}, {"displayName": "Read organization information", "value": "Organization.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read the organization and related resources, without a signed-in user. Related resources include things like subscribed skus and tenant branding information.", "id": "498476ce-e0fe-48b0-b801-37ba7e2685c6"}, {"displayName": "Read all company places", "value": "Place.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read company places (conference rooms and room lists) for calendar events and other applications, without a signed-in user.", "id": "913b9306-0ce1-42b8-9137-6a7df690a760"}, {"displayName": "Read all hidden memberships", "value": "Member<PERSON><PERSON><PERSON>", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read the memberships of hidden groups and administrative units without a signed-in user.", "id": "658aa5d8-239f-45c4-aa12-864f4fc7e490"}, {"displayName": "Read and write items in external datasets", "value": "ExternalItem.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allow the app to read or write items in all external datasets that the app is authorized to access", "id": "38c3d6ee-69ee-422f-b954-e17819665354"}, {"displayName": "Manage access reviews for group and app memberships", "value": "AccessReview.ReadWrite.Membership", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read, update, delete and perform actions on access reviews, reviewers, decisions and settings in the organization for group and app memberships, without a signed-in user.", "id": "18228521-a591-40f1-b215-5fad4488c117"}, {"displayName": "Read Microsoft Intune device configuration and policies", "value": "DeviceManagementConfiguration.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read properties of Microsoft Intune-managed device configuration and device compliance policies and their assignment to groups, without a signed-in user.", "id": "dc377aa6-52d8-4e23-b271-2a7ae04cedf3"}, {"displayName": "Read Microsoft Intune apps", "value": "DeviceManagementApps.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read the properties, group assignments and status of apps, app configurations and app protection policies managed by Microsoft Intune, without a signed-in user.", "id": "7a6ee1e7-141e-4cec-ae74-d9db155731ff"}, {"displayName": "Read Microsoft Intune devices", "value": "DeviceManagementManagedDevices.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read the properties of devices managed by Microsoft Intune, without a signed-in user.", "id": "2f51be20-0bb4-4fed-bf7b-db946066c75e"}, {"displayName": "Read Microsoft Intune RBAC settings", "value": "DeviceManagementRBAC.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read the properties relating to the Microsoft Intune Role-Based Access Control (RBAC) settings, without a signed-in user.", "id": "58ca0d9a-1575-47e1-a3cb-007ef2e4583b"}, {"displayName": "Read Microsoft Intune configuration", "value": "DeviceManagementServiceConfig.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read Microsoft Intune service properties including device enrollment and third party service connection configuration, without a signed-in user.", "id": "06a5fe6d-c49d-46a7-b082-56b1b14103c7"}, {"displayName": "Manage on-premises published resources", "value": "OnPremisesPublishingProfiles.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to create, view, update and delete on-premises published resources, on-premises agents and agent groups, as part of a hybrid identity configuration, without a signed in user.", "id": "0b57845e-aa49-4e6f-8109-ce654fffa618"}, {"displayName": "Read and write trust framework key sets", "value": "TrustFrameworkKeySet.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read and write trust framework key set properties without a signed-in user.", "id": "4a771c9a-1cf2-4609-b88e-3d3e02d539cd"}, {"displayName": "Read trust framework key sets", "value": "TrustFrameworkKeySet.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read trust framework key set properties without a signed-in user.", "id": "fff194f1-7dce-4428-8301-1badb5518201"}, {"displayName": "Read and write your organization's trust framework policies", "value": "Policy.ReadWrite.TrustFramework", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read and write your organization's trust framework policies without a signed in user.", "id": "79a677f7-b79d-40d0-a36a-3e6f8688dd7a"}, {"displayName": "Read your organization's policies", "value": "Policy.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read all your organization's policies without a signed in user.", "id": "246dd0d5-5bd0-4def-940b-0421030a5b68"}, {"displayName": "Read and write identity providers", "value": "IdentityProvider.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read and write your organization’s identity (authentication) providers’ properties without a signed in user.", "id": "90db2b9a-d928-4d33-a4dd-8442ae3d41e4"}, {"displayName": "Read identity providers", "value": "IdentityProvider.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read your organization’s identity (authentication) providers’ properties without a signed in user.", "id": "e321f0bb-e7f7-481e-bb28-e3b0b32d4bd0"}, {"displayName": "Read and write all administrative units", "value": "AdministrativeUnit.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to create, read, update, and delete administrative units and manage administrative unit membership without a signed-in user.", "id": "5eb59dd3-1da2-4329-8733-9dabdc435916"}, {"displayName": "Read all administrative units", "value": "AdministrativeUnit.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read administrative units and administrative unit membership without a signed-in user.", "id": "134fd756-38ce-4afd-ba33-e9623dbe66c2"}, {"displayName": "Read all published labels and label policies for an organization.", "value": "InformationProtectionPolicy.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows an app to read published sensitivity labels and label policy settings for the entire organization or a specific user, without a signed in user.", "id": "19da66cb-0fb0-4390-b071-ebc76a349482"}, {"displayName": "Read all OneNote notebooks", "value": "Notes.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read all the OneNote notebooks in your organization, without a signed-in user.", "id": "3aeca27b-ee3a-4c2b-8ded-80376e2134a4"}, {"displayName": "Invite guest users to the organization", "value": "User.Invite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to invite guest users to the organization, without a signed-in user.", "id": "09850681-111b-4a89-9bed-3f2cae46d706"}, {"displayName": "Read and write files in all site collections", "value": "Files.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read, create, update and delete all files in all site collections without a signed in user. ", "id": "75359482-378d-4052-8f01-80520e7db3cd"}, {"displayName": "Manage threat indicators this app creates or owns", "value": "ThreatIndicators.ReadWrite.OwnedBy", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to create threat indicators, and fully manage those threat indicators (read, update and delete), without a signed-in user.  It cannot update any threat indicators it does not own.", "id": "21792b6c-c986-4ffc-85de-df9da54b52fa"}, {"displayName": "Read and update your organization's security actions", "value": "SecurityActions.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read or update security actions, without a signed-in user.", "id": "f2bf083f-0179-402a-bedb-b2784de8a49b"}, {"displayName": "Read your organization's security actions", "value": "SecurityActions.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read security actions, without a signed-in user.", "id": "5e0edab9-c148-49d0-b423-ac253e121825"}, {"displayName": "Read and update your organization’s security events", "value": "SecurityEvents.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read your organization’s security events without a signed-in user. Also allows the app to update editable properties in security events.", "id": "d903a879-88e0-4c09-b0c9-82f6a1333f84"}, {"displayName": "Read your organization’s security events", "value": "SecurityEvents.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read your organization’s security events without a signed-in user.", "id": "bf394140-e372-4bf9-a898-299cfc7564e5"}, {"displayName": "Read and write all chat messages", "value": "Chat.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows an app to read and write all chat messages in Microsoft Teams, without a signed-in user.", "id": "294ce7c9-31ba-490a-ad7d-97a7d075e4ed"}, {"displayName": "Read and write all risk detection information", "value": "IdentityRiskEvent.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read and update identity risk detection information for your organization without a signed-in user. Update operations include confirming risk event detections. ", "id": "db06fb33-1953-4b7b-a2ac-f1e2c854f7ae"}, {"displayName": "Read and write all risky user information", "value": "IdentityRiskyUser.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read and update identity risky user information for your organization without a signed-in user.  Update operations include dismissing risky users.", "id": "656f6061-f9fe-4807-9708-6a2e0934df76"}, {"displayName": "Read files in all site collections", "value": "Files.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read all files in all site collections without a signed in user.", "id": "01d4889c-1287-42c6-ac1f-5d1e02578ef6"}, {"displayName": "Read all identity risk event information", "value": "IdentityRiskEvent.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read the identity risk event information for your organization without a signed in user.", "id": "6e472fd1-ad78-48da-a0f0-97ab2c6b769e"}, {"displayName": "Read a limited subset of the organization's roster", "value": "EduRoster.ReadBasic.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read a limited subset of properties from both the structure of schools and classes in the organization's roster and education-specific information about all users. Includes name, status, role, email address and photo.", "id": "0d412a8c-a06c-439f-b3ec-8abcf54d2f96"}, {"displayName": "Read the organization's roster", "value": "EduRoster.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read the structure of schools and classes in the organization's roster and education-specific information about all users to be read.", "id": "e0ac9e1b-cb65-4fc5-87c5-1a8bc181f648"}, {"displayName": "Read and write the organization's roster", "value": "EduRoster.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read and write the structure of schools and classes in the organization's roster and education-specific information about all users to be read and written.", "id": "d1808e82-ce13-47af-ae0d-f9b254e6d58a"}, {"displayName": "Read class assignments without grades", "value": "EduAssignments.ReadBasic.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read assignments without grades for all users.", "id": "6e0a958b-b7fc-4348-b7c4-a6ab9fd3dd0e"}, {"displayName": "Read and write class assignments without grades", "value": "EduAssignments.ReadWriteBasic.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read and write assignments without grades for all users.", "id": "f431cc63-a2de-48c4-8054-a34bc093af84"}, {"displayName": "Read class assignments with grades", "value": "EduAssignments.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read assignments and their grades for all users.", "id": "4c37e1b6-35a1-43bf-926a-6f30f2cdf585"}, {"displayName": "Read and write class assignments with grades", "value": "EduAssignments.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read and write assignments and their grades for all users.", "id": "0d22204b-6cad-4dd0-8362-3e3f2ae699d9"}, {"displayName": "Read Education app settings", "value": "EduAdministration.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Read the state and settings of all Microsoft education apps.", "id": "7c9db06a-ec2d-4e7b-a592-5a1e30992566"}, {"displayName": "Manage education app settings", "value": "EduAdministration.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Manage the state and settings of all Microsoft education apps.", "id": "9bc431c3-b8bc-4a8d-a219-40f10f92eff6"}, {"displayName": "Read all identity risky user information", "value": "IdentityRiskyUser.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read the identity risky user information for your organization without a signed in user.", "id": "dc5007c0-2d7d-4c42-879c-2dab87571379"}, {"displayName": "Read and write all users' full profiles", "value": "User.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read and update user profiles without a signed in user.", "id": "741f803b-c850-494e-b5df-cde7c675a1ca"}, {"displayName": "Read all users' full profiles", "value": "User.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read user profiles without a signed in user.", "id": "df021288-bdef-4463-88db-98f22de89214"}, {"displayName": "Read all audit log data", "value": "AuditLog.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read and query your audit log activities, without a signed-in user.", "id": "b0afded3-3588-46d8-8b3d-9842eff778da"}, {"displayName": "Manage apps that this app creates or owns", "value": "Application.ReadWrite.OwnedBy", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to create other applications, and fully manage those applications (read, update, update application secrets and delete), without a signed-in user.  It cannot update any apps that it is not an owner of.", "id": "18a4783c-866b-4cc7-a460-3d5e5662c884"}, {"displayName": "Export user's data", "value": "User.Export.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to export data (e.g. customer content or system-generated logs), associated with any user in your company, when the app is used by a privileged user (e.g. a Company Administrator).", "id": "405a51b5-8d8d-430b-9842-8be4b0e9f324"}, {"displayName": "Manage all programs", "value": "ProgramControl.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read, update, delete and perform actions on programs and program controls in the organization, without a signed-in user.", "id": "60a901ed-09f7-4aa5-a16e-7dd3d6f9de36"}, {"displayName": "Read all programs", "value": "ProgramControl.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read programs and program controls in the organization, without a signed-in user.", "id": "eedb7fdd-7539-4345-a38b-4839e4a84cbd"}, {"displayName": "Manage all access reviews", "value": "AccessReview.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read, update, delete and perform actions on access reviews, reviewers, decisions and settings in the organization, without a signed-in user.", "id": "ef5f7d5c-338f-44b0-86c3-351f46c8bb5f"}, {"displayName": "Read all access reviews", "value": "AccessReview.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read access reviews, reviewers, decisions and settings in the organization, without a signed-in user.", "id": "d07a8cc0-3d51-4b77-b3b0-32704d1f69fa"}, {"displayName": "Read all usage reports", "value": "Reports.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows an app to read all service usage reports without a signed-in user.  Services that provide usage reports include Office 365 and Azure Active Directory.", "id": "230c1aed-a721-4c5d-9cb4-a90514e508ef"}, {"displayName": "Read all users' relevant people lists", "value": "People.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read any user's scored list of relevant people, without a signed-in user. The list can include local contacts, contacts from social networking, your organization's directory, and people from recent communications (such as email and Skype).", "id": "b528084d-ad10-4598-8b93-929746b4d7d6"}, {"displayName": "Flag chat messages for violating policy", "value": "Chat.UpdatePolicyViolation.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to update Microsoft Teams 1-to-1 or group chat messages by patching a set of Data Loss Prevention (DLP) policy violation properties to handle the output of DLP processing.", "id": "7e847308-e030-4183-9899-5235d7270f58"}, {"displayName": "Read all chat messages", "value": "Chat.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read all 1-to-1 or group chat messages in Microsoft Teams.", "id": "6b7d71aa-70aa-4810-a8d9-5d9fb2830017"}, {"displayName": "Read all channel messages", "value": "ChannelMessage.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read all channel messages in Microsoft Teams", "id": "7b2449af-6ccd-4f4d-9f78-e550c193f0d1"}, {"displayName": "Flag channel messages for violating policy", "value": "ChannelMessage.UpdatePolicyViolation.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to update Microsoft Teams channel messages by patching a set of Data Loss Prevention (DLP) policy violation properties to handle the output of DLP processing.", "id": "4d02b0cc-d90b-441f-8d82-4fb55c34d6bb"}, {"displayName": "Read and write all applications", "value": "Application.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to create, read, update and delete applications and service principals without a signed-in user.  Does not allow management of consent grants.", "id": "1bfefb4e-e0b5-418b-a88f-73c46d2cc8e9"}, {"displayName": "Read and write all user mailbox settings", "value": "MailboxSettings.ReadWrite", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to create, read, update, and delete user's mailbox settings without a signed-in user. Does not include permission to send mail.", "id": "6931bccd-447a-43d1-b442-00a195474933"}, {"displayName": "Read and write domains", "value": "Domain.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read and write all domain properties without a signed in user.  Also allows the app to add,  verify and remove domains.", "id": "7e05723c-0bb0-42da-be95-ae9f08a6e53c"}, {"displayName": "Read all user mailbox settings", "value": "MailboxSettings.Read", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read user's mailbox settings without a signed-in user. Does not include permission to send mail.", "id": "40f97065-369a-49f4-947c-6a255697ae91"}, {"displayName": "Read mail in all mailboxes", "value": "Mail.Read", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read mail in all mailboxes without a signed-in user.", "id": "810c84a8-4a9e-49e6-bf7d-12d183f40d01"}, {"displayName": "Read and write mail in all mailboxes", "value": "Mail.ReadWrite", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to create, read, update, and delete mail in all mailboxes without a signed-in user. Does not include permission to send mail.", "id": "e2a3a72e-5f79-4c64-b1b1-878b674786c9"}, {"displayName": "Send mail as any user", "value": "Mail.Send", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to send mail as any user without a signed-in user.", "id": "b633e1c5-b582-4048-a93e-9f11b44c7e96"}, {"displayName": "Read contacts in all mailboxes", "value": "Contacts.Read", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read all contacts in all mailboxes without a signed-in user.", "id": "089fe4d0-434a-44c5-8827-41ba8a0b17f5"}, {"displayName": "Read and write contacts in all mailboxes", "value": "Contacts.ReadWrite", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to create, read, update, and delete all contacts in all mailboxes without a signed-in user.", "id": "6918b873-d17a-4dc1-b314-35f528134491"}, {"displayName": "Read all groups", "value": "Group.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read group properties and memberships, and read the calendar and conversations for all groups, without a signed-in user.", "id": "5b567255-7703-4780-807c-7be8301ae99b"}, {"displayName": "Read and write all groups", "value": "Group.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to create groups, read all group properties and memberships, update group properties and memberships, and delete groups. Also allows the app to read and write group calendar and conversations.  All of these operations can be performed by the app without a signed-in user.", "id": "62a82d76-70ea-41e2-9197-370581804d09"}, {"displayName": "Read directory data", "value": "Directory.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read data in your organization's directory, such as users, groups and apps, without a signed-in user.", "id": "7ab1d382-f21e-4acd-a863-ba3e13f7da61"}, {"displayName": "Read and write directory data", "value": "Directory.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read and write data in your organization's directory, such as users, and groups, without a signed-in user.  Does not allow user or group deletion.", "id": "19dbc75e-c2e2-444c-a770-ec69d8559fc7"}, {"displayName": "Read and write devices", "value": "Device.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read and write all device properties without a signed in user.  Does not allow device creation, device deletion or update of device alternative security identifiers.", "id": "1138cb37-bd11-4084-a2b7-9f71582aeddb"}, {"displayName": "Read calendars in all mailboxes", "value": "Calendars.Read", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read events of all calendars without a signed-in user.", "id": "798ee544-9d2d-430c-a058-570e29e34338"}, {"displayName": "Read and write calendars in all mailboxes", "value": "Calendars.ReadWrite", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to create, read, update, and delete events of all calendars without a signed-in user.", "id": "ef54d2bf-783f-4e0f-bca1-3210c0444d99"}, {"displayName": "Read all identity user flows", "value": "IdentityUserFlow.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read your organization's user flows, without a signed-in user.", "id": "1b0c317f-dd31-4305-9932-259a8b6e8099"}, {"displayName": "Read and write all identity user flows", "value": "IdentityUserFlow.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read or write your organization's user flows, without a signed-in user.", "id": "65319a09-a2be-469d-8782-f6b07debf789"}, {"displayName": "Read and create online meetings", "value": "OnlineMeetings.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read and create online meetings as an application in your organization.", "id": "b8bb2037-6e08-44ac-a4ea-4674e010e2a4"}, {"displayName": "Read online meeting details", "value": "OnlineMeetings.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read online meeting details in your organization, without a signed-in user.", "id": "c1684f21-1984-47fa-9d61-2dc8c296bb70"}, {"displayName": "Access media streams in a call as an app", "value": "Calls.AccessMedia.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to get direct access to media streams in a call, without a signed-in user.", "id": "a7a681dc-756e-4909-b988-f160edc6655f"}, {"displayName": "Join group calls and meetings as a guest", "value": "Calls.JoinGroupCallAsGuest.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to anonymously join group calls and scheduled meetings in your organization, without a signed-in user.  The app will be joined as a guest to meetings in your organization.", "id": "fd7ccf6b-3d28-418b-9701-cd10f5cd2fd4"}, {"displayName": "Join group calls and meetings as an app", "value": "Calls.JoinGroupCall.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to join group calls and scheduled meetings in your organization, without a signed-in user.  The app will be joined with the privileges of a directory user to meetings in your organization.", "id": "f6b49018-60ab-4f81-83bd-22caeabfed2d"}, {"displayName": "Initiate outgoing group calls from the app", "value": "Calls.InitiateGroupCall.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to place outbound calls to multiple users and add participants to meetings in your organization, without a signed-in user.", "id": "4c277553-8a09-487b-8023-29ee378d8324"}, {"displayName": "Initiate outgoing 1 to 1 calls from the app", "value": "Calls.Initiate.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to place outbound calls to a single user and transfer calls to users in your organization’s directory, without a signed-in user.", "id": "284383ee-7f6e-4e40-a2a8-e85dcb029101"}, {"displayName": "Read organizational contacts", "value": "OrgContact.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read all organizational contacts without a signed-in user.  These contacts are managed by the organization and are different from a user's personal contacts.", "id": "e1a88a34-94c4-4418-be12-c87b00e26bea"}, {"displayName": "Read and write Microsoft Intune apps", "value": "DeviceManagementApps.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read and write the properties, group assignments and status of apps, app configurations and app protection policies managed by Microsoft Intune, without a signed-in user.", "id": "78145de6-330d-4800-a6ce-494ff2d33d07"}, {"displayName": "Read and write Microsoft Intune device configuration and policies", "value": "DeviceManagementConfiguration.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read and write properties of Microsoft Intune-managed device configuration and device compliance policies and their assignment to groups, without a signed-in user.", "id": "9241abd9-d0e6-425a-bd4f-47ba86e767a4"}, {"displayName": "Perform user-impacting remote actions on Microsoft Intune devices", "value": "DeviceManagementManagedDevices.PrivilegedOperations.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to perform remote high impact actions such as wiping the device or resetting the passcode on devices managed by Microsoft Intune, without a signed-in user.", "id": "5b07b0dd-2377-4e44-a38d-703f09a0dc3c"}, {"displayName": "Read and write Microsoft Intune devices", "value": "DeviceManagementManagedDevices.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read and write the properties of devices managed by Microsoft Intune, without a signed-in user. Does not allow high impact operations such as remote wipe and password reset on the device’s owner", "id": "243333ab-4d21-40cb-a475-36241daa0842"}, {"displayName": "Read and write Microsoft Intune RBAC settings", "value": "DeviceManagementRBAC.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read and write the properties relating to the Microsoft Intune Role-Based Access Control (RBAC) settings, without a signed-in user.", "id": "e330c4f0-4170-414e-a55a-2f022ec2b57b"}, {"displayName": "Read and write Microsoft Intune configuration", "value": "DeviceManagementServiceConfig.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read and write Microsoft Intune service properties including device enrollment and third party service connection configuration, without a signed-in user.", "id": "5ac13192-7ace-4fcf-b828-1a26f28068ee"}, {"displayName": "Manage app permission grants and app role assignments", "value": "AppRoleAssignment.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to manage permission grants for application permissions to any API (including Microsoft Graph) and application assignments for any app, without a signed-in user.", "id": "06b708a9-e830-4db3-a914-8e69da51d44f"}, {"displayName": "Manage all delegated permission grants", "value": "DelegatedPermissionGrant.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to manage permission grants for delegated permissions exposed by any API (including Microsoft Graph), without a signed-in user.", "id": "8e8e4742-1d95-4f68-9d56-6ee75648c72a"}, {"displayName": "Read all users' teamwork activity feed", "value": "TeamsActivity.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read all users' teamwork activity feed, without a signed-in user.", "id": "70dec828-f620-4914-aa83-a29117306807"}, {"displayName": "Read privileged access to Azure AD roles", "value": "PrivilegedAccess.Read.AzureAD", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read time-based assignment and just-in-time elevation (including scheduled elevation) of Azure AD built-in and custom administrative roles in your organization, without a signed-in user.", "id": "4cdc2547-9148-4295-8d11-be0db1391d6b"}, {"displayName": "Read privileged access to Azure AD groups", "value": "PrivilegedAccess.Read.AzureADGroup", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read time-based assignment and just-in-time elevation (including scheduled elevation) of Azure AD groups in your organization, without a signed-in user.", "id": "01e37dc9-c035-40bd-b438-b2879c4870a6"}, {"displayName": "Read privileged access to Azure resources", "value": "PrivilegedAccess.Read.AzureResources", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read time-based assignment and just-in-time elevation of user privileges to audit Azure resources in your organization, without a signed-in user.", "id": "5df6fe86-1be0-44eb-b916-7bd443a71236"}, {"displayName": "Read and write privileged access to Azure AD roles", "value": "PrivilegedAccess.ReadWrite.AzureAD", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to request and manage time-based assignment and just-in-time elevation (including scheduled elevation) of Azure AD built-in and custom administrative roles in your organization, without a signed-in user.", "id": "854d9ab1-6657-4ec8-be45-823027bcd009"}, {"displayName": "Read and write privileged access to Azure AD groups", "value": "PrivilegedAccess.ReadWrite.AzureADGroup", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to request and manage time-based assignment and just-in-time elevation (including scheduled elevation) of Azure AD groups in your organization, without a signed-in user.", "id": "2f6817f8-7b12-4f0f-bc18-eeaf60705a9e"}, {"displayName": "Read and write privileged access to Azure resources", "value": "PrivilegedAccess.ReadWrite.AzureResources", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to request and manage time-based assignment and just-in-time elevation of Azure resources (like your subscriptions, resource groups, storage, compute) in your organization, without a signed-in user.", "id": "6f9d5abc-2db6-400b-a267-7de22a40fb87"}, {"displayName": "Read all threat indicators", "value": "ThreatIndicators.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read all the indicators for your organization, without a signed-in user.", "id": "197ee4e9-b993-4066-898f-d6aecc55125b"}, {"displayName": "Read all privileged access approval requests", "value": "ApprovalRequest.Read.PriviligedAccess", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read privileged access requests, business flows, and governance policy templates without a signed-in user.", "id": "3f410ed8-2d83-4435-b2c4-c776f44e4ae1"}, {"displayName": "Read all entitlement management approval requests", "value": "ApprovalRequest.Read.EntitlementManagement", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read entitlement management requests, business flows, and governance policy templates without a signed-in user.", "id": "b2a3adf0-5774-4846-986c-a91c705b0141"}, {"displayName": "Read all admin consent approval requests", "value": "ApprovalRequest.Read.AdminConsentRequest", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read admin consent requests, business flows, and governance policy templates without a signed-in user.", "id": "0d9d2e88-e2eb-4ac7-9b1d-9b68ed9f9f4f"}, {"displayName": "Read all customer lockbox approval requests", "value": "ApprovalRequest.Read.CustomerLockbox", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read customer lockbox requests, business flows, and governance policy templates without a signed-in user.", "id": "080ce695-a830-4d5c-a45a-375e3ab11b11"}, {"displayName": "Read and write all privileged access approval requests", "value": "ApprovalRequest.ReadWrite.PriviligedAccess", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read and write privileged access requests, business flows, and governance policy templates without a signed-in user.", "id": "60182ac6-4565-4baa-8b04-9350fe8dbfca"}, {"displayName": "Read and write all entitlement management approval requests", "value": "ApprovalRequest.ReadWrite.EntitlementManagement", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read and write entitlement management requests, business flows, and governance policy templates without a signed-in user.", "id": "fbfdecc9-4b78-4882-bb98-7decbddcbddf"}, {"displayName": "Read and write all admin consent approval requests", "value": "ApprovalRequest.ReadWrite.AdminConsentRequest", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read and write admin consent requests, business flows, and governance policy templates without a signed-in user.", "id": "afe5c674-a576-4b80-818c-e3d7f6afd299"}, {"displayName": "Read and write all customer lockbox approval requests", "value": "ApprovalRequest.ReadWrite.CustomerLockbox", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read and write customer lockbox requests, business flows, and governance policy templates without a signed-in user.", "id": "5f411d27-abad-4dc3-83c6-b84a46ffa434"}, {"displayName": "Read all users' installed Teams apps", "value": "TeamsApp.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read the Teams apps that are installed for any user, without a signed-in user. Does not give the ability to read application-specific settings.", "id": "afdb422a-4b2a-4e07-a708-8ceed48196bf"}, {"displayName": "Manage all users' Teams apps", "value": "TeamsApp.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read, install, upgrade, and uninstall Teams apps for any user, without a signed-in user. Does not give the ability to read or write application-specific settings.", "id": "eb6b3d76-ed75-4be6-ac36-158d04c0a555"}, {"displayName": "Deliver and manage all user's notifications", "value": "UserNotification.ReadWrite.CreatedByApp", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to send, read, update and delete user’s notifications, without a signed-in user.", "id": "4e774092-a092-48d1-90bd-baad67c7eb47"}, {"displayName": "Read all applications", "value": "Application.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read all applications and service principals without a signed-in user.", "id": "9a5d68dd-52b0-4cc2-bd40-abcf44ac3a30"}, {"displayName": "Read all BitLocker keys", "value": "BitlockerKey.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows an app to read BitLocker keys for all devices, without a signed-in user. Allows read of the recovery key.", "id": "57f1cf28-c0c4-4ec3-9a30-19a2eaaf2f6e"}, {"displayName": "Read all BitLocker keys basic information", "value": "BitlockerKey.ReadBasic.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows an app to read basic BitLocker key properties for all devices, without a signed-in user. Does not allow read of the recovery key.", "id": "f690d423-6b29-4d04-98c6-694c42282419"}, {"displayName": "Read all group memberships", "value": "GroupMember.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read memberships and basic group properties for all groups without a signed-in user.", "id": "98830695-27a2-44f7-8c18-0c3ebc9698f6"}, {"displayName": "Read and write all group memberships", "value": "GroupMember.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to list groups, read basic properties, read and update the membership of the groups this app has access to without a signed-in user. Group properties and owners cannot be updated and groups cannot be deleted.", "id": "dbaae8cf-10b5-4b86-a4a1-f871c94c6695"}, {"displayName": "Create groups", "value": "Group.Create", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to create groups without a signed-in user.", "id": "bf7b1a76-6e77-406b-b258-bf5c7720e98f"}, {"displayName": "Read threat assessment requests", "value": "ThreatAssessment.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows an app to read your organization's threat assessment requests, without a signed-in user.", "id": "f8f035bb-2cce-47fb-8bf5-7baf3ecbee48"}, {"displayName": "Read all schedule items", "value": "Schedule.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read all schedules, schedule groups, shifts and associated entities in the Teams or Shifts application without a signed-in user.", "id": "7b2ebf90-d836-437f-b90d-7b62722c4456"}, {"displayName": "Read and write all schedule items", "value": "Schedule.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to manage all schedules, schedule groups, shifts and associated entities in the Teams or Shifts application without a signed-in user.", "id": "b7760610-0545-4e8a-9ec3-cce9e63db01c"}, {"displayName": "Read all call records", "value": "CallRecords.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read call records for all calls and online meetings without a signed-in user.", "id": "45bbb07e-7321-4fd7-a8f6-3ff27e6a81c8"}, {"displayName": "Read and write your organization's conditional access policies", "value": "Policy.ReadWrite.ConditionalAccess", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read and write your organization's conditional access policies, without a signed-in user.", "id": "01c0a623-fc9b-48e9-b794-0756f8e8f067"}, {"displayName": "Read and write all users' authentication methods ", "value": "UserAuthenticationMethod.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the application to read and write authentication methods of all users in your organization, without a signed-in user.                       Authentication methods include things like a user’s phone numbers and Authenticator app settings. This                      does not allow the app to see secret information like passwords, or to sign-in or otherwise use the authentication methods", "id": "50483e42-d915-4231-9639-7fdb7fd190e5"}, {"displayName": " Read all users' authentication methods", "value": "UserAuthenticationMethod.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": " Allows the app to read authentication methods of all users in your organization, without a signed-in user.                       Authentication methods include things like a user’s phone numbers and Authenticator app settings. This does not allow the                      app to see secret information like passwords, or to sign-in or otherwise use the authentication methods.", "id": "38d9df27-64da-44fd-b7c5-a6fbac20248f"}, {"displayName": "Create tabs in Microsoft Teams.", "value": "TeamsTab.Create", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to create tabs in any team in Microsoft Teams, without a signed-in user. This does not grant the ability to read, modify or delete tabs after they are created, or give access to the content inside the tabs.", "id": "49981c42-fd7b-4530-be03-e77b21aed25e"}, {"displayName": "Read tabs in Microsoft Teams.", "value": "TeamsTab.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Read the names and settings of tabs inside any team in Microsoft Teams, without a signed-in user. This does not give access to the content inside the tabs. ", "id": "46890524-499a-4bb2-ad64-1476b4f3e1cf"}, {"displayName": "Read and write tabs in Microsoft Teams.", "value": "TeamsTab.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Read and write tabs in any team in Microsoft Teams, without a signed-in user. This does not give access to the content inside the tabs.", "id": "a96d855f-016b-47d7-b51c-1218a98d791c"}, {"displayName": "Read domains", "value": "Domain.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read all domain properties without a signed-in user.", "id": "dbb9058a-0e50-45d7-ae91-66909b5d4664"}, {"displayName": "Read and write your organization's application configuration policies", "value": "Policy.ReadWrite.ApplicationConfiguration", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read and write your organization's application configuration policies, without a signed-in user.  This includes policies such as activityBasedTimeoutPolicy, claimsMappingPolicy, homeRealmDiscoveryPolicy, tokenIssuancePolicy  and tokenLifetimePolicy.", "id": "be74164b-cff1-491c-8741-e671cb536e13"}, {"displayName": "Read all devices", "value": "Device.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read your organization's devices' configuration information without a signed-in user.", "id": "7438b122-aefc-4978-80ed-43db9fcc7715"}, {"displayName": "Manage all users' identities", "value": "User.ManageIdentities.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read, update and delete identities that are associated with a user's account, without a signed in user. This controls the identities users can sign-in with.", "id": "c529cfca-c91b-489c-af2b-d92990b66ce6"}, {"displayName": "Read all user shift preferences", "value": "UserShiftPreferences.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read all users' shift schedule preferences without a signed-in user.", "id": "de023814-96df-4f53-9376-1e2891ef5a18"}, {"displayName": "Read and write all user shift preferences", "value": "UserShiftPreferences.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to manage all users' shift schedule preferences without a signed-in user.", "id": "d1eec298-80f3-49b0-9efb-d90e224798ac"}, {"displayName": "Read and write all OneNote notebooks", "value": "Notes.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read all the OneNote notebooks in your organization, without a signed-in user.", "id": "0c458cef-11f3-48c2-a568-c66751c238c0"}, {"displayName": "Have full control of all site collections", "value": "Sites.FullControl.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to have full control of all site collections without a signed in user.", "id": "a82116e5-55eb-4c41-a434-62fe8a61c773"}, {"displayName": "Create, edit, and delete items and lists in all site collections", "value": "Sites.Manage.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to create or delete document libraries and lists in all site collections without a signed in user.", "id": "0c0bf378-bf22-4481-8f81-9e89a9b4960a"}, {"displayName": "Read all entitlement management resources", "value": "EntitlementManagement.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read access packages and related entitlement management resources without a signed-in user.", "id": "c74fd47d-ed3c-45c3-9a9e-b8676de685d2"}, {"displayName": "Read and write all entitlement management resources", "value": "EntitlementManagement.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read and write access packages and related entitlement management resources without a signed-in user.", "id": "9acd699f-1e81-4958-b001-93b1d2506e19"}, {"displayName": "Create channels", "value": "Channel.Create", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Create channels in any team, without a signed-in user.", "id": "f3a65bd4-b703-46df-8f7e-0174fea562aa"}, {"displayName": "Delete channels", "value": "Channel.Delete.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Delete channels in any team, without a signed-in user.", "id": "6a118a39-1227-45d4-af0c-ea7b40d210bc"}, {"displayName": "Read the names, descriptions, and settings of all channels", "value": "ChannelSettings.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Read all channel names, channel descriptions, and channel settings, without a signed-in user.", "id": "c97b873f-f59f-49aa-8a0e-52b32d762124"}, {"displayName": "Read and write the names, descriptions, and settings of all channels", "value": "ChannelSettings.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Read and write the names, descriptions, and settings of all channels, without a signed-in user.", "id": "243cded2-bd16-4fd6-a953-ff8177894c3d"}, {"displayName": "Get a list of all teams", "value": "Team.ReadBasic.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Get a list of all teams, without a signed-in user.", "id": "2280dda6-0bfd-44ee-a2f4-cb867cfc4c1e"}, {"displayName": "Read the names and descriptions  of all channels", "value": "Channel.ReadBasic.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Read all channel names and channel descriptions, without a signed-in user.", "id": "59a6b24b-4225-4393-8165-ebaec5f55d7a"}, {"displayName": "Read and change all teams' settings", "value": "TeamSettings.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Read and change all teams' settings, without a signed-in user.", "id": "bdd80a03-d9bc-451d-b7c4-ce7c63fe3c8f"}, {"displayName": "Read all teams' settings", "value": "TeamSettings.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Read all team's settings, without a signed-in user.", "id": "242607bd-1d2c-432c-82eb-bdb27baa23ab"}, {"displayName": "Read the members of all teams", "value": "TeamMember.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Read the members of all teams, without a signed-in user.", "id": "660b7406-55f1-41ca-a0ed-0b035e182f3e"}, {"displayName": "Add and remove members from all teams", "value": "TeamMember.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Add and remove members from all teams, without a signed-in user. Also allows changing a team member's role, for example from owner to non-owner.", "id": "0121dc95-1b9f-4aed-8bac-58c5ac466691"}, {"displayName": "Read the members of all channels", "value": "ChannelMember.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Read the members of all channels, without a signed-in user.", "id": "3b55498e-47ec-484f-8136-9013221c06a9"}, {"displayName": "Add and remove members from all channels", "value": "ChannelMember.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Add and remove members from all channels, without a signed-in user. Also allows changing a member's role, for example from owner to non-owner.", "id": "35930dcf-aceb-4bd1-b99a-8ffed403c974"}, {"displayName": "Read and write authentication flow policies", "value": "Policy.ReadWrite.AuthenticationFlows", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read and write all authentication flow policies for the tenant, without a signed-in user.", "id": "25f85f3c-f66c-4205-8cd5-de92dd7f0cec"}, {"displayName": "Read and write all authentication method policies ", "value": "Policy.ReadWrite.AuthenticationMethod", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read and write all authentication method policies for the tenant, without a signed-in user. ", "id": "29c18626-4985-4dcd-85c0-193eef327366"}, {"displayName": "Read and write your organization's authorization policy", "value": "Policy.ReadWrite.Authorization", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read and write your organization's authorization policy without a signed in user. For example, authorization policies can control some of the permissions that the out-of-the-box user role has by default.", "id": "fb221be6-99f2-473f-bd32-01c6a0e9ca3b"}, {"displayName": "Read names and members of all chat threads", "value": "Chat.ReadBasic.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Read names and members of all one-to-one and group chats in Microsoft Teams, without a signed-in user.", "id": "b2e060da-3baf-4687-9611-f4ebc0f0cbde"}, {"displayName": "Read consent and permission grant policies", "value": "Policy.Read.PermissionGrant", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read policies related to consent and permission grants for applications, without a signed-in user.", "id": "9e640839-a198-48fb-8b9a-013fd6f6cbcd"}, {"displayName": "Manage consent and permission grant policies", "value": "Policy.ReadWrite.PermissionGrant", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to manage policies related to consent and permission grants for applications, without a signed-in user.", "id": "a402ca1c-2696-4531-972d-6e5ee4aa11ea"}, {"displayName": "Read printers", "value": "Printer.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the application to read printers without a signed-in user. ", "id": "9709bb33-4549-49d4-8ed9-a8f65e45bb0f"}, {"displayName": "Read and update printers", "value": "Printer.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the application to read and update printers without a signed-in user. Does not allow creating (registering) or deleting (unregistering) printers.", "id": "f5b3f73d-6247-44df-a74c-866173fddab0"}, {"displayName": "Perform advanced operations on print jobs", "value": "PrintJob.Manage.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the application to perform advanced operations like redirecting a print job to another printer without a signed-in user. Also allows the application to read and update the metadata of print jobs.", "id": "58a52f47-9e36-4b17-9ebe-ce4ef7f3e6c8"}, {"displayName": "Read print jobs", "value": "PrintJob.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the application to read the metadata and document content of print jobs without a signed-in user. ", "id": "ac6f956c-edea-44e4-bd06-64b1b4b9aec9"}, {"displayName": "Read basic information for print jobs", "value": "PrintJob.ReadBasic.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the application to read the metadata of print jobs without a signed-in user. Does not allow access to print job document content.", "id": "fbf67eee-e074-4ef7-b965-ab5ce1c1f689"}, {"displayName": "Read and write print jobs", "value": "PrintJob.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the application to read and update the metadata and document content of print jobs without a signed-in user.", "id": "5114b07b-2898-4de7-a541-53b0004e2e13"}, {"displayName": "Read and write basic information for print jobs", "value": "PrintJob.ReadWriteBasic.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the application to read and update the metadata of print jobs without a signed-in user. Does not allow access to print job document content.", "id": "57878358-37f4-4d3a-8c20-4816e0d457b1"}, {"displayName": "Read, write and update print task definitions", "value": "PrintTaskDefinition.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the application to read and update print task definitions without a signed-in user. ", "id": "456b71a7-0ee0-4588-9842-c123fcc8f664"}, {"displayName": "Create chat and channel messages with anyone's identity and with any timestamp", "value": "Teamwork.Migrate.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to create chat and channel messages, without a signed in user. The app specifies which user appears as the sender, and can backdate the message to appear as if it was sent long ago. The messages can be sent to any chat or channel in the organization.", "id": "dfb0dd15-61de-45b2-be36-d6a69fba3c79"}, {"displayName": "Read installed Teams apps for all chats", "value": "TeamsAppInstallation.ReadForChat.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read the Teams apps that are installed in any chat, without a signed-in user. Does not give the ability to read application-specific settings.", "id": "cc7e7635-2586-41d6-adaa-a8d3bcad5ee5"}, {"displayName": "Read installed Teams apps for all teams", "value": "TeamsAppInstallation.ReadForTeam.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read the Teams apps that are installed in any team, without a signed-in user. Does not give the ability to read application-specific settings.", "id": "1f615aea-6bf9-4b05-84bd-46388e138537"}, {"displayName": "Read installed Teams apps for all users", "value": "TeamsAppInstallation.ReadForUser.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read the Teams apps that are installed for any user, without a signed-in user. Does not give the ability to read application-specific settings.", "id": "9ce09611-f4f7-4abd-a629-a05450422a97"}, {"displayName": "Manage Teams apps for all chats", "value": "TeamsAppInstallation.ReadWriteForChat.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read, install, upgrade, and uninstall Teams apps in any chat, without a signed-in user. Does not give the ability to read application-specific settings.", "id": "9e19bae1-2623-4c4f-ab6e-2664615ff9a0"}, {"displayName": "Manage Teams apps for all teams", "value": "TeamsAppInstallation.ReadWriteForTeam.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read, install, upgrade, and uninstall Teams apps in any team, without a signed-in user. Does not give the ability to read application-specific settings.", "id": "5dad17ba-f6cc-4954-a5a2-a0dcc95154f0"}, {"displayName": "Manage Teams apps for all users", "value": "TeamsAppInstallation.ReadWriteForUser.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read, install, upgrade, and uninstall Teams apps for any user, without a signed-in user. Does not give the ability to read application-specific settings.", "id": "74ef0291-ca83-4d02-8c7e-d2391e6a444f"}, {"displayName": "Allow the Teams app to manage itself for all chats", "value": "TeamsAppInstallation.ReadWriteSelfForChat.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows a Teams app to read, install, upgrade, and uninstall itself for any chat, without a signed-in user.", "id": "73a45059-f39c-4baf-9182-4954ac0e55cf"}, {"displayName": "Allow the Teams app to manage itself for all teams", "value": "TeamsAppInstallation.ReadWriteSelfForTeam.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows a Teams app to read, install, upgrade, and uninstall itself in any team, without a signed-in user.", "id": "9f67436c-5415-4e7f-8ac1-3014a7132630"}, {"displayName": "Allow the app to manage itself for all users", "value": "TeamsAppInstallation.ReadWriteSelfForUser.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows a Teams app to read, install, upgrade, and uninstall itself to any user, without a signed-in user.", "id": "908de74d-f8b2-4d6b-a9ed-2a17b3b78179"}, {"displayName": "Create teams", "value": "Team.Create", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to create teams without a signed-in user. ", "id": "23fc2474-f741-46ce-8465-674744c5c361"}, {"displayName": "Add and remove members with non-owner role for all teams", "value": "TeamMember.ReadWriteNonOwnerRole.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Add and remove members from all teams, without a signed-in user. Does not allow adding or removing a member with the owner role. Additionally, does not allow the app to elevate an existing member to the owner role.", "id": "4437522e-9a86-4a41-a7da-e380edd4a97d"}, {"displayName": "Read all term store data", "value": "TermStore.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read all term store data, without a signed-in user. This includes all sets, groups and terms in the term store.", "id": "ea047cc2-df29-4f3e-83a3-205de61501ca"}, {"displayName": "Read and write all term store data", "value": "TermStore.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read, edit or write all term store data, without a signed-in user. This includes all sets, groups and terms in the term store.", "id": "f12eb8d6-28e3-46e6-b2c0-b7e4dc69fc95"}, {"displayName": "Read service health", "value": "ServiceHealth.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read your tenant's service health information, without a signed-in user. Health information may include service issues or service health overviews.", "id": "79c261e0-fe76-4144-aad5-bdc68fbe4037"}, {"displayName": "Read service messages", "value": "ServiceMessage.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read your tenant's service announcement messages, without a signed-in user. Messages may include information about new or changed features.", "id": "1b620472-6534-4fe6-9df2-4680e8aa28ec"}, {"displayName": "Read all users' short notes", "value": "ShortNotes.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read all the short notes without a signed-in user.", "id": "0c7d31ec-31ca-4f58-b6ec-9950b6b0de69"}, {"displayName": "Read, create, edit, and delete all users' short notes", "value": "ShortNotes.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read, create, edit, and delete all the short notes without a signed-in user.", "id": "842c284c-763d-4a97-838d-79787d129bab"}, {"displayName": "Read your organization's conditional access policies", "value": "Policy.Read.ConditionalAccess", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read your organization's conditional access policies, without a signed-in user.", "id": "********-e9ba-4e46-b07e-8ca78d182097"}, {"displayName": "Read role management data for all RBAC providers", "value": "RoleManagement.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read role-based access control (RBAC) settings for all RBAC providers without a signed-in user. This includes reading role definitions and role assignments.", "id": "c7fbd983-d9aa-4fa7-84b8-17382c103bc4"}, {"displayName": "Read PSTN and direct routing call log data", "value": "CallRecord-PstnCalls.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read all PSTN and direct routing call log data without a signed-in user.", "id": "a2611786-80b3-417e-adaa-707d4261a5f0"}, {"displayName": "Read all chat messages", "value": "ChatMessage.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read all one-to-one and group chats messages in Microsoft Teams, without a signed-in user.", "id": "b9bb2381-47a4-46cd-aafb-00cb12f68504"}, {"displayName": "Allow the Teams app to manage all tabs for all chats", "value": "TeamsTab.ReadWriteForChat.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows a Teams app to read, install, upgrade, and uninstall all tabs for any chat, without a signed-in user.", "id": "fd9ce730-a250-40dc-bd44-8dc8d20f39ea"}, {"displayName": "Allow the Teams app to manage all tabs for all teams", "value": "TeamsTab.ReadWriteForTeam.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows a Teams app to read, install, upgrade, and uninstall all tabs in any team, without a signed-in user.", "id": "6163d4f4-fbf8-43da-a7b4-060fe85ed148"}, {"displayName": "Allow the app to manage all tabs for all users", "value": "TeamsTab.ReadWriteForUser.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows a Teams app to read, install, upgrade, and uninstall all tabs for any user, without a signed-in user.", "id": "425b4b59-d5af-45c8-832f-bb0b7402348a"}, {"displayName": "Read API connectors for authentication flows", "value": "APIConnectors.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read the API connectors used in user authentication flows, without a signed-in user.", "id": "b86848a7-d5b1-41eb-a9b4-54a4e6306e97"}, {"displayName": "Read and write API connectors for authentication flows", "value": "APIConnectors.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to read, create and manage the API connectors used in user authentication flows, without a signed-in user.", "id": "1dfe531a-24a6-4f1b-80f4-7a0dc5a0a171"}, {"displayName": "Read the members of all chats", "value": "ChatMember.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Read the members of all chats, without a signed-in user.", "id": "a3410be2-8e48-4f32-8454-c29a7465209d"}, {"displayName": "Add and remove members from all chats", "value": "ChatMember.ReadWrite.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Add and remove members from all chats, without a signed-in user.", "id": "57257249-34ce-4810-a8a2-a03adf0c5693"}, {"displayName": "Create chats", "value": "Chat.Create", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the app to create chats without a signed-in user. ", "id": "d9c48af6-9ad9-47ad-82c3-63757137b9af"}, {"displayName": "Read tenant-wide print settings", "value": "PrintSettings.Read.All", "isEnabled": true, "allowedMemberTypes": ["Application"], "origin": "Application", "description": "Allows the application to read tenant-wide print settings without a signed-in user.", "id": "b5991872-94cf-4652-9765-29535087c6d8"}], "description": null, "servicePrincipalType": "Application", "homepage": null, "id": "1a6b6cf1-14e4-4f7f-b4ff-8deb2c15c983", "appOwnerOrganizationId": "f8cdef31-a31e-4b4a-93e4-5f571e91255a", "tags": [], "appDisplayName": "Microsoft Graph", "logoutUrl": null, "notificationEmailAddresses": [], "verifiedPublisher": {"addedDateTime": null, "verifiedPublisherId": null, "displayName": null}, "addIns": [], "alternativeNames": [], "appRoleAssignmentRequired": false, "passwordCredentials": [], "samlSingleSignOnSettings": null, "info": {"privacyStatementUrl": null, "marketingUrl": null, "supportUrl": null, "termsOfServiceUrl": null, "logoUrl": null}, "resourceSpecificApplicationPermissions": [], "preferredSingleSignOnMode": null, "createdDateTime": "2021-06-07T19:13:33Z", "applicationTemplateId": null, "servicePrincipalNames": ["********-0000-0000-c000-************/ags.windows.net", "********-0000-0000-c000-************", "https://canary.graph.microsoft.com", "https://graph.microsoft.com", "https://ags.windows.net", "https://graph.microsoft.us", "https://graph.microsoft.com/", "https://dod-graph.microsoft.us", "https://canary.graph.microsoft.com/", "https://graph.microsoft.us/", "https://dod-graph.microsoft.us/"], "replyUrls": [], "accountEnabled": true, "appId": "********-0000-0000-c000-************", "preferredTokenSigningKeyThumbprint": null, "deletedDateTime": null, "notes": null, "oauth2PermissionScopes": [{"adminConsentDescription": "Allows the app to read and manage the active role-based access control (RBAC) assignments for your company's directory, on behalf of the signed-in user. This includes managing active directory role membership, and reading directory role templates, directory roles and active memberships.", "id": "8c026be3-8e26-4774-9372-8d5d6f21daff", "adminConsentDisplayName": "Read, update, and delete all active role assignments for your company's directory", "userConsentDescription": "Allows the app to read and manage the active role-based access control (RBAC) assignments for your company's directory, on your behalf. This includes managing active directory role membership, and reading directory role templates, directory roles and active memberships.", "isEnabled": true, "type": "Admin", "value": "RoleAssignmentSchedule.ReadWrite.Directory", "userConsentDisplayName": "Read, update, and delete all active role assignments for your company's directory"}, {"adminConsentDescription": "Allows the app to read and manage the eligible role-based access control (RBAC) assignments for your company's directory, on behalf of the signed-in user. This includes managing eligible directory role membership, and reading directory role templates, directory roles and eligible memberships.", "id": "62ade113-f8e0-4bf9-a6ba-5acb31db32fd", "adminConsentDisplayName": "Read, update, and delete  all eligible role assignments for your company's directory", "userConsentDescription": "Allows the app to read and manage the eligible role-based access control (RBAC) assignments for your company's directory, on your behalf. This includes managing eligible directory role membership, and reading directory role templates, directory roles and eligible memberships.", "isEnabled": true, "type": "Admin", "value": "RoleEligibilitySchedule.ReadWrite.Directory", "userConsentDisplayName": "Read, update, and delete  all eligible role assignments for your company's directory"}, {"adminConsentDescription": "Allows the app to read, update, and delete policies for privileged role-based access control (RBAC) assignments of your company's directory, on behalf of the signed-in user.", "id": "1ff1be21-34eb-448c-9ac9-ce1f506b2a68", "adminConsentDisplayName": "Read, update, and delete all policies for privileged role assignments of your company's directory", "userConsentDescription": "Allows the app to read, update, and delete policies for privileged role-based access control (RBAC) assignments of your company's directory, on your behalf.", "isEnabled": true, "type": "Admin", "value": "RoleManagementPolicy.ReadWrite.Directory", "userConsentDisplayName": "Read, update, and delete all policies for privileged role assignments of your company's directory"}, {"adminConsentDescription": "Allows the app to read the active role-based access control (RBAC) assignments for your company's directory, on behalf of the signed-in user. This includes reading directory role templates, and directory roles.", "id": "344a729c-0285-42c6-9014-f12b9b8d6129", "adminConsentDisplayName": "Read all active role assignments for your company's directory", "userConsentDescription": "Allows the app to read the active role-based access control (RBAC) assignments for your company's directory, on your behalf. This includes reading directory role templates, and directory roles.", "isEnabled": true, "type": "Admin", "value": "RoleAssignmentSchedule.Read.Directory", "userConsentDisplayName": "Read all active role assignments for your company's directory"}, {"adminConsentDescription": "Allows the app to read the eligible role-based access control (RBAC) assignments for your company's directory, on behalf of the signed-in user. This includes reading directory role templates, and directory roles.", "id": "eb0788c2-6d4e-4658-8c9e-c0fb8053f03d", "adminConsentDisplayName": "Read all eligible role assignments for your company's directory", "userConsentDescription": "Allows the app to read the eligible role-based access control (RBAC) assignments for your company's directory, on your behalf. This includes reading directory role templates, and directory roles.", "isEnabled": true, "type": "Admin", "value": "RoleEligibilitySchedule.Read.Directory", "userConsentDisplayName": "Read all eligible role assignments for your company's directory"}, {"adminConsentDescription": "Allows the app to read policies for privileged role-based access control (RBAC) assignments of your company's directory, on behalf of the signed-in user.", "id": "3de2cdbe-0ff5-47d5-bdee-7f45b4749ead", "adminConsentDisplayName": "Read all policies for privileged role assignments of your company's directory", "userConsentDescription": "Allows the app to read policies for privileged role-based access control (RBAC) assignments of your company's directory, on your behalf.", "isEnabled": true, "type": "Admin", "value": "RoleManagementPolicy.Read.Directory", "userConsentDisplayName": "Read all policies for privileged role assignments of your company's directory"}, {"adminConsentDescription": "Allows the app to read and write all Windows update deployment settings for the organization on behalf of the signed-in user.", "id": "11776c0c-6138-4db3-a668-ee621bea2555", "adminConsentDisplayName": "Read and write all Windows update deployment settings", "userConsentDescription": "Allows the app to read and write all Windows update deployment settings for the organization on your behalf.", "isEnabled": true, "type": "Admin", "value": "WindowsUpdates.ReadWrite.All", "userConsentDisplayName": "Read and write all Windows update deployment settings"}, {"adminConsentDescription": "Allows the app to read and write your organization's mobility management policies on behalf of the signed-in user.  For example, a mobility management policy can set the enrollment scope for a given mobility management application.", "id": "a8ead177-1889-4546-9387-f25e658e2a79", "adminConsentDisplayName": "Read and write your organization's mobility management policies", "userConsentDescription": "Allows the app to read and write your organization's mobility management policies on your behalf.  For example, a mobility management policy can set the enrollment scope for a given mobility management application.", "isEnabled": true, "type": "Admin", "value": "Policy.ReadWrite.MobilityManagement", "userConsentDisplayName": "Read and write your organization's mobility management policies"}, {"adminConsentDescription": "Allows the app to read basic unified group properties, memberships and owners of the group the signed-in guest is a member of.", "id": "73e75199-7c3e-41bb-9357-167164dbb415", "adminConsentDisplayName": "Read unified group memberships as guest", "userConsentDescription": "Allows the app to read basic unified group properties, memberships and owners of the group you are a member of.", "isEnabled": true, "type": "Admin", "value": "UnifiedGroupMember.Read.AsGuest", "userConsentDisplayName": "Read unified group memberships as guest"}, {"adminConsentDescription": "Allows the app to update service principal endpoints", "id": "7297d82c-9546-4aed-91df-3d4f0a9b3ff0", "adminConsentDisplayName": "Read and update service principal endpoints", "userConsentDescription": "Allows the app to update service principal endpoints", "isEnabled": true, "type": "Admin", "value": "ServicePrincipalEndpoint.ReadWrite.All", "userConsentDisplayName": "Read and update service principal endpoints"}, {"adminConsentDescription": "Allows the app to read service principal endpoints", "id": "9f9ce928-e038-4e3b-8faf-7b59049a8ddc", "adminConsentDisplayName": "Read service principal endpoints", "userConsentDescription": "Allows the app to read service principal endpoints", "isEnabled": true, "type": "Admin", "value": "ServicePrincipalEndpoint.Read.All", "userConsentDisplayName": "Read service principal endpoints"}, {"adminConsentDescription": "Allows the app to create new notifications in users' teamwork activity feeds on behalf of the signed in user. These notifications may not be discoverable or be held or governed by compliance policies.", "id": "7ab1d787-bae7-4d5d-8db6-37ea32df9186", "adminConsentDisplayName": "Send a teamwork activity as the user", "userConsentDescription": "Allows the app to create new activities in your teamwork activity feed, and send new activities to other users' activity feed, on your behalf.", "isEnabled": true, "type": "User", "value": "TeamsActivity.Send", "userConsentDisplayName": "Send a teamwork activity"}, {"adminConsentDescription": "Allows the app to read and write eDiscovery objects such as cases, custodians, review sets and other related objects on behalf of the signed-in user.", "id": "acb8f680-0834-4146-b69e-4ab1b39745ad", "adminConsentDisplayName": "Read and write all eDiscovery objects", "userConsentDescription": "Allows the app to read and write eDiscovery objects such as cases, custodians, review sets and other related objects on your behalf.", "isEnabled": true, "type": "Admin", "value": "eDiscovery.ReadWrite.All", "userConsentDisplayName": "Read and write all eDiscovery objects"}, {"adminConsentDescription": "Allows the app to read eDiscovery objects such as cases, custodians, review sets and other related objects on behalf of the signed-in user.", "id": "99201db3-7652-4d5a-809a-bdb94f85fe3c", "adminConsentDisplayName": "Read all eDiscovery objects", "userConsentDescription": "Allows the app to read eDiscovery objects such as cases, custodians, review sets and other related objects on your behalf.", "isEnabled": true, "type": "Admin", "value": "eDiscovery.Read.All", "userConsentDisplayName": "Read all eDiscovery objects"}, {"adminConsentDescription": "Allows the app to read and write custom security attribute assignments for all principals in the tenant on behalf of a signed in user.", "id": "ca46335e-8453-47cd-a001-8459884efeae", "adminConsentDisplayName": "Read and write custom security attribute assignments", "userConsentDescription": "Allows the app to read and write custom security attribute assignments for all principals in the tenant on your behalf.", "isEnabled": true, "type": "Admin", "value": "CustomSecAttributeAssignment.ReadWrite.All", "userConsentDisplayName": "Read and write custom security attribute assignments"}, {"adminConsentDescription": "Allows the app to read and write custom security attribute definitions for the tenant on behalf of a signed in user.", "id": "8b0160d4-5743-482b-bb27-efc0a485ca4a", "adminConsentDisplayName": "Read and write custom security attribute definitions", "userConsentDescription": "Allows the app to read and write custom security attribute definitions for the tenant on your behalf.", "isEnabled": true, "type": "Admin", "value": "CustomSecAttributeDefinition.ReadWrite.All", "userConsentDisplayName": "Read and write custom security attribute definitions"}, {"adminConsentDescription": "Allows the app to read email in the signed-in user's mailbox except body, previewBody, attachments and any extended properties.", "id": "a4b8392a-d8d1-4954-a029-8e668a39a170", "adminConsentDisplayName": "Read user basic mail", "userConsentDescription": "Allows the app to read email in the signed-in user's mailbox except body, previewBody, attachments and any extended properties.", "isEnabled": true, "type": "User", "value": "Mail.ReadBasic", "userConsentDisplayName": "Read user basic mail"}, {"adminConsentDescription": "Allows the app to read and write your organization's feature rollout policies on behalf of the signed-in user. Includes abilities to assign and remove users and groups to rollout of a specific feature.", "id": "92a38652-f13b-4875-bc77-6e1dbb63e1b2", "adminConsentDisplayName": "Read and write your organization's feature rollout policies", "userConsentDescription": "Allows the app to read and write your organization's feature rollout policies on your behalf. Includes abilities to assign and remove users and groups to rollout of a specific feature.", "isEnabled": true, "type": "Admin", "value": "Policy.ReadWrite.FeatureRollout", "userConsentDisplayName": "Read and write your organization's feature rollout policies"}, {"adminConsentDescription": "Allows the app to read and manage the role-based access control (RBAC) settings for your company's directory, on behalf of the signed-in user. This includes instantiating directory roles and managing directory role membership, and reading directory role templates, directory roles and memberships.", "id": "d01b97e9-cbc0-49fe-810a-750afd5527a3", "adminConsentDisplayName": "Read and write directory RBAC settings", "userConsentDescription": "Allows the app to read and manage the role-based access control (RBAC) settings for your company's directory, on your behalf. This includes instantiating directory roles and managing directory role membership, and reading directory role templates, directory roles and memberships.", "isEnabled": true, "type": "Admin", "value": "RoleManagement.ReadWrite.Directory", "userConsentDisplayName": "Read and write directory RBAC settings"}, {"adminConsentDescription": "Allows the app to read the role-based access control (RBAC) settings for your company's directory, on behalf of the signed-in user.  This includes reading directory role templates, directory roles and memberships.", "id": "741c54c3-0c1e-44a1-818b-3f97ab4e8c83", "adminConsentDisplayName": "Read directory RBAC settings", "userConsentDescription": "Allows the app to read the role-based access control (RBAC) settings for your company's directory, on your behalf.  This includes reading directory role templates, directory roles and memberships.", "isEnabled": true, "type": "Admin", "value": "RoleManagement.Read.Directory", "userConsentDisplayName": "Read directory RBAC settings"}, {"adminConsentDescription": "Allows the app to read and write the organization and related resources, on behalf of the signed-in user. Related resources include things like subscribed skus and tenant branding information.", "id": "46ca0847-7e6b-426e-9775-ea810a948356", "adminConsentDisplayName": "Read and write organization information", "userConsentDescription": "Allows the app to read and write the organization and related resources, on your behalf. Related resources include things like subscribed skus and tenant branding information.", "isEnabled": true, "type": "Admin", "value": "Organization.ReadWrite.All", "userConsentDisplayName": "Read and write organization information"}, {"adminConsentDescription": "Allows the app to read the organization and related resources, on behalf of the signed-in user. Related resources include things like subscribed skus and tenant branding information.", "id": "4908d5b9-3fb2-4b1e-9336-1888b7937185", "adminConsentDisplayName": "Read organization information", "userConsentDescription": "Allows the app to read the organization and related resources, on your behalf. Related resources include things like subscribed skus and tenant branding information.", "isEnabled": true, "type": "Admin", "value": "Organization.Read.All", "userConsentDisplayName": "Read organization information"}, {"adminConsentDescription": "Allows the app to read your company's places (conference rooms and room lists) for calendar events and other applications, on behalf of the signed-in user.", "id": "cb8f45a0-5c2e-4ea1-b803-84b870a7d7ec", "adminConsentDisplayName": "Read all company places", "userConsentDescription": "Allows the app to read your company's places (conference rooms and room lists) for calendar events and other applications, on your behalf.", "isEnabled": true, "type": "Admin", "value": "Place.Read.All", "userConsentDisplayName": "Read all company places"}, {"adminConsentDescription": "Allows the app to manage workforce integrations, to synchronize data from Microsoft Teams Shifts, on behalf of the signed-in user.", "id": "08c4b377-0d23-4a8b-be2a-23c1c1d88545", "adminConsentDisplayName": "Read and write workforce integrations", "userConsentDescription": "Allows the app to manage workforce integrations, to synchronize data from Microsoft Teams Shifts, on your behalf.", "isEnabled": true, "type": "Admin", "value": "WorkforceIntegration.ReadWrite.All", "userConsentDisplayName": "Read and write workforce integrations"}, {"adminConsentDescription": "Allows the app to read workforce integrations, to synchronize data from Microsoft Teams Shifts, on behalf of the signed-in user.", "id": "f1ccd5a7-6383-466a-8db8-1a656f7d06fa", "adminConsentDisplayName": "Read workforce integrations", "userConsentDescription": "Allows the app to read workforce integrations, to synchronize data from Microsoft Teams Shifts, on your behalf.", "isEnabled": true, "type": "Admin", "value": "WorkforceIntegration.Read.All", "userConsentDisplayName": "Read workforce integrations"}, {"adminConsentDescription": "Allows the app to read, update, delete and perform actions on access reviews, reviewers, decisions and settings for group and app memberships that the signed-in user has access to in the organization.", "id": "5af8c3f5-baca-439a-97b0-ea58a435e269", "adminConsentDisplayName": "Manage access reviews for group and app memberships", "userConsentDescription": "Allows the app to read, update and perform action on access reviews, reviewers, decisions and settings that you have access to.", "isEnabled": true, "type": "Admin", "value": "AccessReview.ReadWrite.Membership", "userConsentDisplayName": "Manage access reviews for group and app memberships"}, {"adminConsentDescription": "Allows the app to manage hybrid identity service configuration by creating, viewing, updating and deleting on-premises published resources, on-premises agents and agent groups, on behalf of the signed-in user.", "id": "8c4d5184-71c2-4bf8-bb9d-bc3378c9ad42", "adminConsentDisplayName": "Manage on-premises published resources", "userConsentDescription": "Allows the app to manage hybrid identity service configuration by creating, viewing, updating and deleting on-premises published resources, on-premises agents and agent groups, on your behalf.", "isEnabled": true, "type": "Admin", "value": "OnPremisesPublishingProfiles.ReadWrite.All", "userConsentDisplayName": "Manage on-premises published resources"}, {"adminConsentDescription": "Allows an app to read information protection sensitivity labels and label policy settings, on behalf of the signed-in user.", "id": "4ad84827-5578-4e18-ad7a-86530b12f884", "adminConsentDisplayName": "Read user sensitivity labels and label policies.", "userConsentDescription": "Allows an app to read information protection sensitivity labels and label policy settings, on behalf of the signed-in user.", "isEnabled": true, "type": "User", "value": "InformationProtectionPolicy.Read", "userConsentDisplayName": "Read user sensitivity labels and label policies."}, {"adminConsentDescription": "Allows the app to read administrative units and administrative unit membership on behalf of the signed-in user.", "id": "3361d15d-be43-4de6-b441-3c746d05163d", "adminConsentDisplayName": "Read administrative units", "userConsentDescription": "Allows the app to read administrative units and administrative unit membership on your behalf.", "isEnabled": true, "type": "Admin", "value": "AdministrativeUnit.Read.All", "userConsentDisplayName": "Read administrative units"}, {"adminConsentDescription": "Allows the app to create, read, update, and delete administrative units and manage administrative unit membership on behalf of the signed-in user.", "id": "7b8a2d34-6b3f-4542-a343-54651608ad81", "adminConsentDisplayName": "Read and write administrative units", "userConsentDescription": "Allows the app to create, read, update, and delete administrative units and manage administrative unit membership on your behalf.", "isEnabled": true, "type": "Admin", "value": "AdministrativeUnit.ReadWrite.All", "userConsentDisplayName": "Read and write administrative units"}, {"adminConsentDescription": "Allows the app to read your family information, members and their basic profile.", "id": "3a1e4806-a744-4c70-80fc-223bf8582c46", "adminConsentDisplayName": "Read your family info", "userConsentDescription": "Allows the app to read your family information, members and their basic profile.", "isEnabled": true, "type": "User", "value": "Family.Read", "userConsentDisplayName": "Read your family info"}, {"adminConsentDescription": "Allows the app to create threat indicators, and fully manage those threat indicators (read, update and delete), on behalf of the signed-in user.  It cannot update any threat indicators it does not own.", "id": "91e7d36d-022a-490f-a748-f8e011357b42", "adminConsentDisplayName": "Manage threat indicators this app creates or owns", "userConsentDescription": "Allows the app to create threat indicators, and fully manage those threat indicators (read, update and delete), on your behalf.  It cannot update any threat indicators that it is not an owner of.", "isEnabled": true, "type": "Admin", "value": "ThreatIndicators.ReadWrite.OwnedBy", "userConsentDisplayName": "Manage threat indicators this app creates or owns"}, {"adminConsentDescription": "Allows the app to read or update security actions, on behalf of the signed-in user.", "id": "dc38509c-b87d-4da0-bd92-6bec988bac4a", "adminConsentDisplayName": "Read and update your organization's security actions", "userConsentDescription": "Allows the app to read and update security actions, on your behalf.", "isEnabled": true, "type": "Admin", "value": "SecurityActions.ReadWrite.All", "userConsentDisplayName": "Read and update your organization's security actions"}, {"adminConsentDescription": "Allows the app to read security actions, on behalf of the signed-in user.", "id": "1638cddf-07a4-4de2-8645-69c96cacad73", "adminConsentDisplayName": "Read your organization's security actions", "userConsentDescription": "Allows the app to read security actions, on your behalf.", "isEnabled": true, "type": "Admin", "value": "SecurityActions.Read.All", "userConsentDisplayName": "Read your organization's security actions"}, {"adminConsentDescription": "Allows an app to read 1 on 1 or group chats threads, on behalf of the signed-in user.", "id": "f501c180-9344-439a-bca0-6cbf209fd270", "adminConsentDisplayName": "Read user chat messages", "userConsentDescription": "Allows an app to read your 1 on 1 or group chat messages in Microsoft Teams, on your behalf.", "isEnabled": true, "type": "User", "value": "<PERSON><PERSON><PERSON>", "userConsentDisplayName": "Read your chat messages"}, {"adminConsentDescription": "Allows an app to read and write 1 on 1 or group chats threads, on behalf of the signed-in user.", "id": "9ff7295e-131b-4d94-90e1-69fde507ac11", "adminConsentDisplayName": "Read and write user chat messages", "userConsentDescription": "Allows an app to read and write your 1 on 1 or group chat messages in Microsoft Teams, on your behalf.", "isEnabled": true, "type": "User", "value": "Chat.ReadWrite", "userConsentDisplayName": "Read and write your chat messages"}, {"adminConsentDescription": "Allows the app to read and write your organization's trust framework policies on behalf of the signed-in user.", "id": "cefba324-1a70-4a6e-9c1d-fd670b7ae392", "adminConsentDisplayName": "Read and write your organization's trust framework policies", "userConsentDescription": "Allows the app to read and write your organization's trust framework policies on your behalf.", "isEnabled": true, "type": "Admin", "value": "Policy.ReadWrite.TrustFramework", "userConsentDisplayName": "Read and write trust framework policies"}, {"adminConsentDescription": "Allows the app to read trust framework key set properties on behalf of the signed-in user.", "id": "7ad34336-f5b1-44ce-8682-31d7dfcd9ab9", "adminConsentDisplayName": "Read trust framework key sets", "userConsentDescription": "Allows the app to read trust framework key sets, on your behalf.", "isEnabled": true, "type": "Admin", "value": "TrustFrameworkKeySet.Read.All", "userConsentDisplayName": "Read trust framework key sets"}, {"adminConsentDescription": "Allows the app to read and write trust framework key set properties on behalf of the signed-in user.", "id": "39244520-1e7d-4b4a-aee0-57c65826e427", "adminConsentDisplayName": "Read and write trust framework key sets", "userConsentDescription": "Allows the app to read or write trust framework key sets, on your behalf.", "isEnabled": true, "type": "Admin", "value": "TrustFrameworkKeySet.ReadWrite.All", "userConsentDisplayName": "Read and write trust framework key sets"}, {"adminConsentDescription": "Allows the app to read and update identity risk event information for all users in your organization on behalf of the signed-in user. Update operations include confirming risk event detections. ", "id": "9e4862a5-b68f-479e-848a-4e07e25c9916", "adminConsentDisplayName": "Read and write risk event information", "userConsentDescription": "Allows the app to read and update identity risk event information for all users in your organization on your behalf. Update operations include confirming risk event detections. ", "isEnabled": true, "type": "Admin", "value": "IdentityRiskEvent.ReadWrite.All", "userConsentDisplayName": "Read and write risk event information"}, {"adminConsentDescription": "Allows the app to read and update identity risky user information for all users in your organization on behalf of the signed-in user. Update operations include dismissing risky users.", "id": "e0a7cdbb-08b0-4697-8264-0069786e9674", "adminConsentDisplayName": "Read and write risky user information", "userConsentDescription": "Allows the app to read and update identity risky user information for all users in your organization on your behalf. Update operations include dismissing risky users.", "isEnabled": true, "type": "Admin", "value": "IdentityRiskyUser.ReadWrite.All", "userConsentDisplayName": "Read and write identity risky user information"}, {"adminConsentDescription": "Allows the app to read the signed-in user's mailbox.", "id": "570282fd-fa5c-430d-a7fd-fc8dc98a9dca", "adminConsentDisplayName": "Read user mail ", "userConsentDescription": "Allows the app to read email in your mailbox. ", "isEnabled": true, "type": "User", "value": "Mail.Read", "userConsentDisplayName": "Read your mail "}, {"adminConsentDescription": "Allows the app to read identity risky user information for all users in your organization on behalf of the signed-in user.", "id": "d04bb851-cb7c-4146-97c7-ca3e71baf56c", "adminConsentDisplayName": "Read identity risky user information", "userConsentDescription": "Allows the app to read identity risky user information for all users in your organization on behalf of the signed-in user.", "isEnabled": true, "type": "Admin", "value": "IdentityRiskyUser.Read.All", "userConsentDisplayName": "Read identity risky user information"}, {"adminConsentDescription": "Allows the app to read the signed-in user's activity statistics, such as how much time the user has spent on emails, in meetings, or in chat sessions.", "id": "e03cf23f-8056-446a-8994-7d93dfc8b50e", "adminConsentDisplayName": "Read user activity statistics", "userConsentDescription": "Allows the app to read your activity statistics, such as how much time you've spent on emails, in meetings, or in chat sessions.", "isEnabled": true, "type": "User", "value": "Analytics.Read", "userConsentDisplayName": "Read your activity statistics"}, {"adminConsentDescription": "Allows the app to see and update the data you gave it access to, even when users are not currently using the app. This does not give the app any additional permissions.", "id": "7427e0e9-2fba-42fe-b0c0-848c9e6a8182", "adminConsentDisplayName": "Maintain access to data you have given it access to", "userConsentDescription": "Allows the app to see and update the data you gave it access to, even when you are not currently using the app. This does not give the app any additional permissions.", "isEnabled": true, "type": "User", "value": "offline_access", "userConsentDisplayName": "Maintain access to data you have given it access to"}, {"adminConsentDescription": "Allows the app to have the same access to mailboxes as the signed-in user via Exchange Web Services.", "id": "9769c687-087d-48ac-9cb3-c37dde652038", "adminConsentDisplayName": "Access mailboxes as the signed-in user via Exchange Web Services", "userConsentDescription": "Allows the app full access to your mailboxes on your behalf.", "isEnabled": true, "type": "User", "value": "EWS.AccessAsUser.All", "userConsentDisplayName": "Access your mailboxes"}, {"adminConsentDescription": "Allows the app to export data (e.g. customer content or system-generated logs), associated with any user in your company, when the app is used by a privileged user (e.g. a Company Administrator).", "id": "405a51b5-8d8d-430b-9842-8be4b0e9f324", "adminConsentDisplayName": "Export user's data", "userConsentDescription": "Allows the app to export data (e.g. customer content or system-generated logs), associated with any user in your company, when the app is used by a privileged user (e.g. a Company Administrator).", "isEnabled": true, "type": "Admin", "value": "User.Export.All", "userConsentDisplayName": "Export user's data"}, {"adminConsentDescription": "Allows the app to deliver its notifications on behalf of signed-in users. Also allows the app to read, update, and delete the user's notification items for this app.", "id": "89497502-6e42-46a2-8cb2-427fd3df970a", "adminConsentDisplayName": "Deliver and manage user notifications for this app", "userConsentDescription": "Allows the app to deliver its notifications, on your behalf. Also allows the app to read, update, and delete your notification items for this app.", "isEnabled": true, "type": "User", "value": "Notifications.ReadWrite.CreatedByApp", "userConsentDisplayName": "Deliver and manage your notifications for this app"}, {"adminConsentDescription": "Allows the app to read and write your organization's conditional access policies on behalf of the signed-in user.", "id": "ad902697-1014-4ef5-81ef-2b4301988e8c", "adminConsentDisplayName": "Read and write your organization's conditional access policies", "userConsentDescription": "Allows the app to read and write your organization's conditional access policies on your behalf.", "isEnabled": true, "type": "Admin", "value": "Policy.ReadWrite.ConditionalAccess", "userConsentDisplayName": "Read and write your organization's conditional access policies"}, {"adminConsentDescription": "Allows the app to read your organization's policies on behalf of the signed-in user.", "id": "572fea84-0151-49b2-9301-11cb16974376", "adminConsentDisplayName": "Read your organization's policies", "userConsentDescription": "Allows the app to read your organization's policies on your behalf.", "isEnabled": true, "type": "Admin", "value": "Policy.Read.All", "userConsentDisplayName": "Read your organization's policies"}, {"adminConsentDescription": "Allows the app to read access reviews, reviewers, decisions and settings that the signed-in user has access to in the organization.", "id": "ebfcd32b-babb-40f4-a14b-42706e83bd28", "adminConsentDisplayName": "Read all access reviews that user can access", "userConsentDescription": "Allows the app to read information on access reviews, reviewers, decisions and settings that you have access to.", "isEnabled": true, "type": "Admin", "value": "AccessReview.Read.All", "userConsentDisplayName": "Read access reviews that you can access"}, {"adminConsentDescription": "Allows the app to read, update, delete and perform actions on access reviews, reviewers, decisions and settings that the signed-in user has access to in the organization.", "id": "e4aa47b9-9a69-4109-82ed-36ec70d85ff1", "adminConsentDisplayName": "Manage all access reviews that user can access", "userConsentDescription": "Allows the app to read, update and perform action on access reviews, reviewers, decisions and settings that you have access to.", "isEnabled": true, "type": "Admin", "value": "AccessReview.ReadWrite.All", "userConsentDisplayName": "Manage access reviews that you can access"}, {"adminConsentDescription": "Allows the app to read programs and program controls that the signed-in user has access to in the organization.", "id": "c492a2e1-2f8f-4caa-b076-99bbf6e40fe4", "adminConsentDisplayName": "Read all programs that user can access", "userConsentDescription": "Allows the app to read information on programs and program controls that you have access to.", "isEnabled": true, "type": "Admin", "value": "ProgramControl.Read.All", "userConsentDisplayName": "Read programs that you can access"}, {"adminConsentDescription": "Allows the app to read, update, delete and perform actions on programs and program controls that the signed-in user has access to in the organization.", "id": "50fd364f-9d93-4ae1-b170-300e87cccf84", "adminConsentDisplayName": "Manage all programs that user can access", "userConsentDescription": "Allows the app to read, update and perform action on programs and program controls that you have access to.", "isEnabled": true, "type": "Admin", "value": "ProgramControl.ReadWrite.All", "userConsentDisplayName": "Manage programs that you can access"}, {"adminConsentDescription": "Allows the app to create, read, update, and delete apps in the app catalogs.", "id": "1ca167d5-1655-44a1-8adf-1414072e1ef9", "adminConsentDisplayName": "Read and write to all app catalogs", "userConsentDescription": "Allows the app to create, read, update, and delete apps in the app catalogs.", "isEnabled": true, "type": "Admin", "value": "AppCatalog.ReadWrite.All", "userConsentDisplayName": "Read and write to all app catalogs"}, {"adminConsentDescription": "Allows the app to request and manage just in time elevation (including scheduled elevation) of users to Azure AD built-in administrative roles, on behalf of signed-in users.", "id": "3c3c74f5-cdaa-4a97-b7e0-4e788bfcfb37", "adminConsentDisplayName": "Read and write privileged access to Azure AD", "userConsentDescription": "Allows the app to request and manage just in time elevation (including scheduled elevation) of users to Azure AD built-in administrative roles, on your behalf.", "isEnabled": true, "type": "Admin", "value": "PrivilegedAccess.ReadWrite.AzureAD", "userConsentDisplayName": "Read and write privileged access to Azure AD"}, {"adminConsentDescription": "Allows the app to read terms of use agreements on behalf of the signed-in user.", "id": "af2819c9-df71-4dd3-ade7-4d7c9dc653b7", "adminConsentDisplayName": "Read all terms of use agreements", "userConsentDescription": "Allows the app to read terms of use agreements on your behalf.", "isEnabled": true, "type": "Admin", "value": "Agreement.Read.All", "userConsentDisplayName": "Read all terms of use agreements"}, {"adminConsentDescription": "Allows the app to read and write terms of use agreements on behalf of the signed-in user.", "id": "ef4b5d93-3104-4664-9053-a5c49ab44218", "adminConsentDisplayName": "Read and write all terms of use agreements", "userConsentDescription": "Allows the app to read and write terms of use agreements on your behalf.", "isEnabled": true, "type": "Admin", "value": "Agreement.ReadWrite.All", "userConsentDisplayName": "Read and write all terms of use agreements"}, {"adminConsentDescription": "Allows the app to read terms of use acceptance statuses on behalf of the signed-in user.", "id": "0b7643bb-5336-476f-80b5-18fbfbc91806", "adminConsentDisplayName": "Read user terms of use acceptance statuses", "userConsentDescription": "Allows the app to read your terms of use acceptance statuses.", "isEnabled": true, "type": "Admin", "value": "AgreementAcceptance.Read", "userConsentDisplayName": "Read your terms of use acceptance statuses"}, {"adminConsentDescription": "Allows the app to read terms of use acceptance statuses on behalf of the signed-in user.", "id": "a66a5341-e66e-4897-9d52-c2df58c2bfb9", "adminConsentDisplayName": "Read terms of use acceptance statuses that user can access", "userConsentDescription": "Allows the app to read terms of use acceptance statuses on your behalf.", "isEnabled": true, "type": "Admin", "value": "AgreementAcceptance.Read.All", "userConsentDisplayName": "Read all terms of use acceptance statuses"}, {"adminConsentDescription": "Allows the app to read and query your audit log activities, on behalf of the signed-in user.", "id": "e4c9e354-4dc5-45b8-9e7c-e1393b0b1a20", "adminConsentDisplayName": "Read audit log data", "userConsentDescription": "Allows the app to read and query your audit log activities, on your behalf.", "isEnabled": true, "type": "Admin", "value": "AuditLog.Read.All", "userConsentDisplayName": "Read audit log data"}, {"adminConsentDescription": "Allows the app to read and report the signed-in user's activity in the app.", "id": "47607519-5fb1-47d9-99c7-da4b48f369b1", "adminConsentDisplayName": "Read and write app activity to users' activity feed", "userConsentDescription": "Allows the app to read and report your activity in the app.", "isEnabled": true, "type": "User", "value": "UserActivity.ReadWrite.CreatedByApp", "userConsentDisplayName": "Read and write app activity to your activity feed"}, {"adminConsentDescription": "Allows the app to read properties of Microsoft Intune-managed device configuration and device compliance policies and their assignment to groups.", "id": "f1493658-876a-4c87-8fa7-edb559b3476a", "adminConsentDisplayName": "Read Microsoft Intune Device Configuration and Policies", "userConsentDescription": "Allows the app to read properties of Microsoft Intune-managed device configuration and device compliance policies and their assignment to groups.", "isEnabled": true, "type": "Admin", "value": "DeviceManagementConfiguration.Read.All", "userConsentDisplayName": "Read Microsoft Intune Device Configuration and Policies"}, {"adminConsentDescription": "Allows the app to read and write properties of Microsoft Intune-managed device configuration and device compliance policies and their assignment to groups.", "id": "0883f392-0a7a-443d-8c76-16a6d39c7b63", "adminConsentDisplayName": "Read and write Microsoft Intune Device Configuration and Policies", "userConsentDescription": "Allows the app to read and write properties of Microsoft Intune-managed device configuration and device compliance policies and their assignment to groups.", "isEnabled": true, "type": "Admin", "value": "DeviceManagementConfiguration.ReadWrite.All", "userConsentDisplayName": "Read and write Microsoft Intune Device Configuration and Policies"}, {"adminConsentDescription": "Allows the app to read the properties, group assignments and status of apps, app configurations and app protection policies managed by Microsoft Intune.", "id": "4edf5f54-4666-44af-9de9-0144fb4b6e8c", "adminConsentDisplayName": "Read Microsoft Intune apps", "userConsentDescription": "Allows the app to read the properties, group assignments and status of apps, app configurations and app protection policies managed by Microsoft Intune.", "isEnabled": true, "type": "Admin", "value": "DeviceManagementApps.Read.All", "userConsentDisplayName": "Read Microsoft Intune apps"}, {"adminConsentDescription": "Allows the app to read and write the properties, group assignments and status of apps, app configurations and app protection policies managed by Microsoft Intune.", "id": "7b3f05d5-f68c-4b8d-8c59-a2ecd12f24af", "adminConsentDisplayName": "Read and write Microsoft Intune apps", "userConsentDescription": "Allows the app to read and write the properties, group assignments and status of apps, app configurations and app protection policies managed by Microsoft Intune.", "isEnabled": true, "type": "Admin", "value": "DeviceManagementApps.ReadWrite.All", "userConsentDisplayName": "Read and write Microsoft Intune apps"}, {"adminConsentDescription": "Allows the app to read the properties relating to the Microsoft Intune Role-Based Access Control (RBAC) settings.", "id": "49f0cc30-024c-4dfd-ab3e-82e137ee5431", "adminConsentDisplayName": "Read Microsoft Intune RBAC settings", "userConsentDescription": "Allows the app to read the properties relating to the Microsoft Intune Role-Based Access Control (RBAC) settings.", "isEnabled": true, "type": "Admin", "value": "DeviceManagementRBAC.Read.All", "userConsentDisplayName": "Read Microsoft Intune RBAC settings"}, {"adminConsentDescription": "Allows the app to read and write the properties relating to the Microsoft Intune Role-Based Access Control (RBAC) settings.", "id": "0c5e8a55-87a6-4556-93ab-adc52c4d862d", "adminConsentDisplayName": "Read and write Microsoft Intune RBAC settings", "userConsentDescription": "Allows the app to read and write the properties relating to the Microsoft Intune Role-Based Access Control (RBAC) settings.", "isEnabled": true, "type": "Admin", "value": "DeviceManagementRBAC.ReadWrite.All", "userConsentDisplayName": "Read and write Microsoft Intune RBAC settings"}, {"adminConsentDescription": "Allows the app to read the properties of devices managed by Microsoft Intune.", "id": "314874da-47d6-4978-88dc-cf0d37f0bb82", "adminConsentDisplayName": "Read Microsoft Intune devices", "userConsentDescription": "Allows the app to read the properties of devices managed by Microsoft Intune.", "isEnabled": true, "type": "Admin", "value": "DeviceManagementManagedDevices.Read.All", "userConsentDisplayName": "Read devices Microsoft Intune devices"}, {"adminConsentDescription": "Allows the app to read and write the properties of devices managed by Microsoft Intune. Does not allow high impact operations such as remote wipe and password reset on the device’s owner.", "id": "44642bfe-8385-4adc-8fc6-fe3cb2c375c3", "adminConsentDisplayName": "Read and write Microsoft Intune devices", "userConsentDescription": "Allows the app to read and write the properties of devices managed by Microsoft Intune. Does not allow high impact operations such as remote wipe and password reset on the device’s owner.", "isEnabled": true, "type": "Admin", "value": "DeviceManagementManagedDevices.ReadWrite.All", "userConsentDisplayName": "Read and write Microsoft Intune devices"}, {"adminConsentDescription": "Allows the app to perform remote high impact actions such as wiping the device or resetting the passcode on devices managed by Microsoft Intune.", "id": "3404d2bf-2b13-457e-a330-c24615765193", "adminConsentDisplayName": "Perform user-impacting remote actions on Microsoft Intune devices", "userConsentDescription": "Allows the app to perform remote high impact actions such as wiping the device or resetting the passcode on devices managed by Microsoft Intune.", "isEnabled": true, "type": "Admin", "value": "DeviceManagementManagedDevices.PrivilegedOperations.All", "userConsentDisplayName": "Perform user-impacting remote actions on Microsoft Intune devices"}, {"adminConsentDescription": "Allows the app to read and write Microsoft Intune service properties including device enrollment and third party service connection configuration.", "id": "662ed50a-ac44-4eef-ad86-62eed9be2a29", "adminConsentDisplayName": "Read and write Microsoft Intune configuration", "userConsentDescription": "Allows the app to read and write Microsoft Intune service properties including device enrollment and third party service connection configuration.", "isEnabled": true, "type": "Admin", "value": "DeviceManagementServiceConfig.ReadWrite.All", "userConsentDisplayName": "Read and write Microsoft Intune configuration"}, {"adminConsentDescription": "Allows the app to read Microsoft Intune service properties including device enrollment and third party service connection configuration.", "id": "8696daa5-bce5-4b2e-83f9-51b6defc4e1e", "adminConsentDisplayName": "Read Microsoft Intune configuration", "userConsentDescription": "Allows the app to read Microsoft Intune service properties including device enrollment and third party service connection configuration.", "isEnabled": true, "type": "Admin", "value": "DeviceManagementServiceConfig.Read.All", "userConsentDisplayName": "Read Microsoft Intune configuration"}, {"adminConsentDescription": "Allows the app to read your organization’s security events on behalf of the signed-in user.", "id": "64733abd-851e-478a-bffb-e47a14b18235", "adminConsentDisplayName": "Read your organization’s security events", "userConsentDescription": "Allows the app to read your organization’s security events on your behalf.", "isEnabled": true, "type": "Admin", "value": "SecurityEvents.Read.All", "userConsentDisplayName": "Read your organization’s security events"}, {"adminConsentDescription": "Allows the app to read your organization’s security events on behalf of the signed-in user. Also allows the app to update editable properties in security events on behalf of the signed-in user.", "id": "6aedf524-7e1c-45a7-bd76-ded8cab8d0fc", "adminConsentDisplayName": "Read and update your organization’s security events", "userConsentDescription": "Allows the app to read your organization’s security events on your behalf. Also allows you to update editable properties in security events.", "isEnabled": true, "type": "Admin", "value": "SecurityEvents.ReadWrite.All", "userConsentDisplayName": "Read and update your organization’s security events"}, {"adminConsentDescription": "Allows the app to read a scored list of relevant people of the signed-in user or other users in the signed-in user's organization. The list can include local contacts, contacts from social networking, your organization's directory, and people from recent communications (such as email and Skype).", "id": "b89f9189-71a5-4e70-b041-9887f0bc7e4a", "adminConsentDisplayName": "Read all users' relevant people lists", "userConsentDescription": "Allows the app to read a list of people in the order that is most relevant to you. Allows the app to read a list of people in the order that is most relevant to another user in your organization. These can include local contacts, contacts from social networking, people listed in your organization’s directory, and people from recent communications.", "isEnabled": true, "type": "Admin", "value": "People.Read.All", "userConsentDisplayName": "Read all users’ relevant people lists"}, {"adminConsentDescription": "Manage the state and settings of all Microsoft education apps on behalf of the user.", "id": "63589852-04e3-46b4-bae9-15d5b1050748", "adminConsentDisplayName": "Manage education app settings", "userConsentDescription": "Allows the app to manage the state and settings of all Microsoft education apps on your behalf.", "isEnabled": true, "type": "Admin", "value": "EduAdministration.ReadWrite", "userConsentDisplayName": "Manage your education app settings"}, {"adminConsentDescription": "Read the state and settings of all Microsoft education apps on behalf of the user.", "id": "8523895c-6081-45bf-8a5d-f062a2f12c9f", "adminConsentDisplayName": "Read education app settings", "userConsentDescription": "Allows the app to view the state and settings of all Microsoft education apps on your behalf.", "isEnabled": true, "type": "Admin", "value": "EduAdministration.Read", "userConsentDisplayName": "View your education app settings"}, {"adminConsentDescription": "Allows the app to read and write assignments and their grades on behalf of the user.", "id": "2f233e90-164b-4501-8bce-31af2559a2d3", "adminConsentDisplayName": "Read and write users' class assignments and their grades", "userConsentDescription": "Allows the app to view and modify your assignments on your behalf including  grades.", "isEnabled": true, "type": "Admin", "value": "EduAssignments.ReadWrite", "userConsentDisplayName": "View and modify your assignments and grades"}, {"adminConsentDescription": "Allows the app to read assignments and their grades on behalf of the user.", "id": "091460c9-9c4a-49b2-81ef-1f3d852acce2", "adminConsentDisplayName": "Read users' class assignments and their grades", "userConsentDescription": "Allows the app to view your assignments on your behalf including grades.", "isEnabled": true, "type": "Admin", "value": "EduAssignments.Read", "userConsentDisplayName": "View your assignments and grades"}, {"adminConsentDescription": "Allows the app to read and write assignments without grades on behalf of the user.", "id": "2ef770a1-622a-47c4-93ee-28d6adbed3a0", "adminConsentDisplayName": "Read and write users' class assignments without grades", "userConsentDescription": "Allows the app to view and modify your assignments on your behalf without seeing grades.", "isEnabled": true, "type": "Admin", "value": "EduAssignments.ReadWriteBasic", "userConsentDisplayName": "View and modify your assignments without grades"}, {"adminConsentDescription": "Allows the app to read assignments without grades on behalf of the user.", "id": "c0b0103b-c053-4b2e-9973-9f3a544ec9b8", "adminConsentDisplayName": "Read users' class assignments without grades", "userConsentDescription": "Allows the app to view your assignments on your behalf without seeing grades.", "isEnabled": true, "type": "Admin", "value": "EduAssignments.ReadBasic", "userConsentDisplayName": "View your assignments without grades"}, {"adminConsentDescription": "Allows the app to read and write the structure of schools and classes in an organization's roster and education-specific information about users to be read and written on behalf of the user.", "id": "359e19a6-e3fa-4d7f-bcab-d28ec592b51e", "adminConsentDisplayName": "Read and write users' view of the roster", "userConsentDescription": "Allows the app to view and modify information about schools and classes in your organization and education-related information about you and other users on your behalf.", "isEnabled": true, "type": "Admin", "value": "EduRoster.ReadWrite", "userConsentDisplayName": "View and modify your school, class and user information"}, {"adminConsentDescription": "Allows the app to read the structure of schools and classes in an organization's roster and education-specific information about users to be read on behalf of the user.", "id": "a4389601-22d9-4096-ac18-36a927199112", "adminConsentDisplayName": "Read users' view of the roster", "userConsentDescription": "Allows the app to view information about schools and classes in your organization and education-related information about you and other users on your behalf.", "isEnabled": true, "type": "Admin", "value": "EduRoster.Read", "userConsentDisplayName": "View your school, class and user information"}, {"adminConsentDescription": "Allows the app to read a limited subset of the properties from the structure of schools and classes in an organization's roster and a limited subset of properties about users to be read on behalf of the user. Includes name, status, education role, email address and photo.", "id": "5d186531-d1bf-4f07-8cea-7c42119e1bd9", "adminConsentDisplayName": "Read a limited subset of users' view of the roster", "userConsentDescription": "Allows the app to view minimal  information about both schools and classes in your organization and education-related information about you and other users on your behalf.", "isEnabled": true, "type": "Admin", "value": "EduRoster.ReadBasic", "userConsentDisplayName": "View a limited subset of your school, class and user information"}, {"adminConsentDescription": "Allows the app to report the signed-in user's app activity information to Microsoft Timeline.", "id": "367492fc-594d-4972-a9b5-0d58c622c91c", "adminConsentDisplayName": "Write app activity to users' timeline", "userConsentDescription": "Allows the app to report your app activity information to Microsoft Timeline.", "isEnabled": true, "type": "User", "value": "UserTimelineActivity.Write.CreatedByApp", "userConsentDisplayName": "Write app activity to your timeline"}, {"adminConsentDescription": "Allows the app to create, read, update, and delete user's mailbox settings. Does not include permission to send mail.", "id": "818c620a-27a9-40bd-a6a5-d96f7d610b4b", "adminConsentDisplayName": "Read and write user mailbox settings", "userConsentDescription": "Allows the app to read, update, create, and delete your mailbox settings.", "isEnabled": true, "type": "User", "value": "MailboxSettings.ReadWrite", "userConsentDisplayName": "Read and write to your mailbox settings"}, {"adminConsentDescription": "Allows the app to launch another app or communicate with another app on a user's device on behalf of the signed-in user.", "id": "bac3b9c2-b516-4ef4-bd3b-c2ef73d8d804", "adminConsentDisplayName": "Communicate with user devices", "userConsentDescription": "Allows the app to launch another app or communicate with another app on a device that you own.", "isEnabled": true, "type": "User", "value": "Device.Command", "userConsentDisplayName": "Communicate with your other devices"}, {"adminConsentDescription": "Allows the app to read a user's list of devices on behalf of the signed-in user.", "id": "11d4cd79-5ba5-460f-803f-e22c8ab85ccd", "adminConsentDisplayName": "Read user devices", "userConsentDescription": "Allows the app to see your list of devices.", "isEnabled": true, "type": "User", "value": "Device.Read", "userConsentDisplayName": "View your list of devices"}, {"adminConsentDescription": "Allows the app to read, share, and modify OneNote notebooks that the signed-in user has access to in the organization.", "id": "64ac0503-b4fa-45d9-b544-71a463f05da0", "adminConsentDisplayName": "Read and write all OneNote notebooks that user can access", "userConsentDescription": "Allows the app to read, share, and modify all the OneNote notebooks that you have access to.", "isEnabled": true, "type": "User", "value": "Notes.ReadWrite.All", "userConsentDisplayName": "Read and write all OneNote notebooks that you can access"}, {"adminConsentDescription": "Allows the app to read OneNote notebooks that the signed-in user has access to in the organization.", "id": "dfabfca6-ee36-4db2-8208-7a28381419b3", "adminConsentDisplayName": "Read all OneNote notebooks that user can access", "userConsentDescription": "Allows the app to read all the OneNote notebooks that you have access to.", "isEnabled": true, "type": "User", "value": "Notes.Read.All", "userConsentDisplayName": "Read all OneNote notebooks that you can access"}, {"adminConsentDescription": "Allows the app to read, share, and modify OneNote notebooks on behalf of the signed-in user.", "id": "615e26af-c38a-4150-ae3e-c3b0d4cb1d6a", "adminConsentDisplayName": "Read and write user OneNote notebooks", "userConsentDescription": "Allows the app to read, share, and modify OneNote notebooks on your behalf.", "isEnabled": true, "type": "User", "value": "Notes.ReadWrite", "userConsentDisplayName": "Read and write your OneNote notebooks"}, {"adminConsentDescription": "Allows the app to read OneNote notebooks on behalf of the signed-in user.", "id": "371361e4-b9e2-4a3f-8315-2a301a3b0a3d", "adminConsentDisplayName": "Read user OneNote notebooks", "userConsentDescription": "Allows the app to read OneNote notebooks on your behalf.", "isEnabled": true, "type": "User", "value": "Notes.Read", "userConsentDisplayName": "Read your OneNote notebooks"}, {"adminConsentDescription": "This is deprecated!  Do not use! This permission no longer has any effect. You can safely consent to it. No additional privileges will be granted to the app.", "id": "ed68249d-017c-4df5-9113-e684c7f8760b", "adminConsentDisplayName": "Limited notebook access (deprecated)", "userConsentDescription": "This permission no longer has any effect. You can safely consent to it. No additional privileges will be granted to the app.", "isEnabled": true, "type": "User", "value": "Notes.ReadWrite.CreatedByApp", "userConsentDisplayName": "Limited access to your OneNote notebooks for this app (preview)"}, {"adminConsentDescription": "Allows the app to read the titles of OneNote notebooks and sections and to create new pages, notebooks, and sections on behalf of the signed-in user.", "id": "9d822255-d64d-4b7a-afdb-833b9a97ed02", "adminConsentDisplayName": "Create user OneNote notebooks", "userConsentDescription": "Allows the app to view the titles of your OneNote notebooks and sections and to create new pages, notebooks, and sections on your behalf.", "isEnabled": true, "type": "User", "value": "Notes.Create", "userConsentDisplayName": "Create your OneNote notebooks"}, {"adminConsentDescription": "Allows the app to invite guest users to the organization, on behalf of the signed-in user.", "id": "63dd7cd9-b489-4adf-a28c-ac38b9a0f962", "adminConsentDisplayName": "Invite guest users to the organization", "userConsentDescription": "Allows the app to invite guest users to the organization, on your behalf.", "isEnabled": true, "type": "Admin", "value": "User.Invite.All", "userConsentDisplayName": "Invite guest users to the organization"}, {"adminConsentDescription": "Allows the app to the read user's mailbox settings. Does not include permission to send mail.", "id": "87f447af-9fa4-4c32-9dfa-4a57a73d18ce", "adminConsentDisplayName": "Read user mailbox settings", "userConsentDescription": "Allows the app to read your mailbox settings.", "isEnabled": true, "type": "User", "value": "MailboxSettings.Read", "userConsentDisplayName": "Read your mailbox settings"}, {"adminConsentDescription": "(Preview) Allows the app to read files that the user selects. The app has access for several hours after the user selects a file.", "id": "5447fe39-cb82-4c1a-b977-520e67e724eb", "adminConsentDisplayName": "Read files that the user selects (preview)", "userConsentDescription": "(Preview) Allows the app to read files that you select. After you select a file, the app has access to the file for several hours.", "isEnabled": true, "type": "User", "value": "Files.Read.Selected", "userConsentDisplayName": "Read selected files"}, {"adminConsentDescription": "(Preview) Allows the app to read and write files that the user selects. The app has access for several hours after the user selects a file.", "id": "17dde5bd-8c17-420f-a486-969730c1b827", "adminConsentDisplayName": "Read and write files that the user selects (preview)", "userConsentDescription": "(Preview) Allows the app to read and write files that you select. After you select a file, the app has access to the file for several hours.", "isEnabled": true, "type": "User", "value": "Files.ReadWrite.Selected", "userConsentDisplayName": "Read and write selected files"}, {"adminConsentDescription": "(Preview) Allows the app to read, create, update and delete files in the application's folder.", "id": "8019c312-3263-48e6-825e-2b833497195b", "adminConsentDisplayName": "Have full access to the application's folder (preview)", "userConsentDescription": "(Preview) Allows the app to read, create, update and delete files in the application's folder.", "isEnabled": true, "type": "User", "value": "Files.ReadWrite.AppFolder", "userConsentDisplayName": "Have full access to the application's folder"}, {"adminConsentDescription": "Allows an app to read all service usage reports on behalf of the signed-in user.  Services that provide usage reports include Office 365 and Azure Active Directory.", "id": "02e97553-ed7b-43d0-ab3c-f8bace0d040c", "adminConsentDisplayName": "Read all usage reports", "userConsentDescription": "Allows an app to read all service usage reports on your behalf. Services that provide usage reports include Office 365 and Azure Active Directory.", "isEnabled": true, "type": "Admin", "value": "Reports.Read.All", "userConsentDisplayName": "Read all usage reports"}, {"adminConsentDescription": "Allows the application to edit or delete documents and list items in all site collections on behalf of the signed-in user.", "id": "89fe6a52-be36-487e-b7d8-d061c450a026", "adminConsentDisplayName": "Edit or delete items in all site collections", "userConsentDescription": "Allow the application to edit or delete documents and list items in all site collections on your behalf.", "isEnabled": true, "type": "User", "value": "Sites.ReadWrite.All", "userConsentDisplayName": "Edit or delete items in all site collections"}, {"adminConsentDescription": "Allows the app to create, read, update, and delete tasks a user has permissions to, including their own and shared tasks.", "id": "c5ddf11b-c114-4886-8558-8a4e557cd52b", "adminConsentDisplayName": "Read and write user and shared tasks", "userConsentDescription": "Allows the app to read, update, create, and delete tasks you have permissions to access, including your own and shared tasks.", "isEnabled": true, "type": "User", "value": "Tasks.ReadWrite.Shared", "userConsentDisplayName": "Read and write to your and shared tasks"}, {"adminConsentDescription": "Allows the app to read tasks a user has permissions to access, including their own and shared tasks.", "id": "88d21fd4-8e5a-4c32-b5e2-4a1c95f34f72", "adminConsentDisplayName": "Read user and shared tasks", "userConsentDescription": "Allows the app to read tasks you have permissions to access, including your own and shared tasks.", "isEnabled": true, "type": "User", "value": "Tasks.Read.Shared", "userConsentDisplayName": "Read your and shared tasks"}, {"adminConsentDescription": "Allows the app to create, read, update, and delete contacts a user has permissions to, including their own and shared contacts.", "id": "afb6c84b-06be-49af-80bb-8f3f77004eab", "adminConsentDisplayName": "Read and write user and shared contacts", "userConsentDescription": "Allows the app to read, update, create, and delete contacts you have permissions to access, including your own and shared contacts.", "isEnabled": true, "type": "User", "value": "Contacts.ReadWrite.Shared", "userConsentDisplayName": "Read and write to your and shared contacts"}, {"adminConsentDescription": "Allows the app to read contacts a user has permissions to access, including their own and shared contacts.", "id": "242b9d9e-ed24-4d09-9a52-f43769beb9d4", "adminConsentDisplayName": "Read user and shared contacts", "userConsentDescription": "Allows the app to read contacts you have permissions to access, including your own and shared contacts.", "isEnabled": true, "type": "User", "value": "Contacts.Read.Shared", "userConsentDisplayName": "Read your and shared contacts"}, {"adminConsentDescription": "Allows the app to create, read, update and delete events in all calendars in the organization user has permissions to access. This includes delegate and shared calendars.", "id": "12466101-c9b8-439a-8589-dd09ee67e8e9", "adminConsentDisplayName": "Read and write user and shared calendars", "userConsentDescription": "Allows the app to read, update, create and delete events in all calendars in your organization you have permissions to access. This includes delegate and shared calendars.", "isEnabled": true, "type": "User", "value": "Calendars.ReadWrite.Shared", "userConsentDisplayName": "Read and write to your and shared calendars"}, {"adminConsentDescription": "Allows the app to read events in all calendars that the user can access, including delegate and shared calendars.", "id": "2b9c4092-424d-4249-948d-b43879977640", "adminConsentDisplayName": "Read user and shared calendars", "userConsentDescription": "Allows the app to read events in all calendars that you can access, including delegate and shared calendars. ", "isEnabled": true, "type": "User", "value": "Calendars.Read.Shared", "userConsentDisplayName": "Read calendars you can access"}, {"adminConsentDescription": "Allows the app to send mail as the signed-in user, including sending on-behalf of others.", "id": "a367ab51-6b49-43bf-a716-a1fb06d2a174", "adminConsentDisplayName": "Send mail on behalf of others", "userConsentDescription": "Allows the app to send mail as you or on-behalf of someone else.", "isEnabled": true, "type": "User", "value": "Mail.Send.Shared", "userConsentDisplayName": "Send mail on behalf of others or yourself"}, {"adminConsentDescription": "Allows the app to create, read, update, and delete mail a user has permission to access, including their own and shared mail. Does not include permission to send mail.", "id": "5df07973-7d5d-46ed-9847-1271055cbd51", "adminConsentDisplayName": "Read and write user and shared mail", "userConsentDescription": "Allows the app to read, update, create, and delete mail you have permission to access, including your own and shared mail. Does not allow the app to send mail on your behalf.", "isEnabled": true, "type": "User", "value": "Mail.ReadWrite.Shared", "userConsentDisplayName": "Read and write mail you can access"}, {"adminConsentDescription": "Allows the app to read mail a user can access, including their own and shared mail.", "id": "7b9103a5-4610-446b-9670-80643382c1fa", "adminConsentDisplayName": "Read user and shared mail", "userConsentDescription": "Allows the app to read mail you can access, including shared mail.", "isEnabled": true, "type": "User", "value": "Mail.Read.Shared", "userConsentDisplayName": "Read mail you can access"}, {"adminConsentDescription": "Allows users to sign-in to the app, and allows the app to read the profile of signed-in users. It also allows the app to read basic company information of signed-in users.", "id": "e1fe6dd8-ba31-4d61-89e7-88639da4683d", "adminConsentDisplayName": "Sign in and read user profile", "userConsentDescription": "Allows you to sign in to the app with your organizational account and let the app read your profile. It also allows the app to read basic company information.", "isEnabled": true, "type": "User", "value": "User.Read", "userConsentDisplayName": "Sign you in and read your profile"}, {"adminConsentDescription": "Allows the app to read your profile. It also allows the app to update your profile information on your behalf.", "id": "b4e74841-8e56-480b-be8b-910348b18b4c", "adminConsentDisplayName": "Read and write access to user profile", "userConsentDescription": "Allows the app to read your profile, and discover your group membership, reports and manager. It also allows the app to update your profile information on your behalf.", "isEnabled": true, "type": "User", "value": "User.ReadWrite", "userConsentDisplayName": "Read and update your profile"}, {"adminConsentDescription": "Allows the app to read a basic set of profile properties of other users in your organization on behalf of the signed-in user. This includes display name, first and last name, email address and photo.", "id": "b340eb25-3456-403f-be2f-af7a0d370277", "adminConsentDisplayName": "Read all users' basic profiles", "userConsentDescription": "Allows the app to read a basic set of profile properties of other users in your organization on your behalf. Includes display name, first and last name, email address and photo.", "isEnabled": true, "type": "User", "value": "User.ReadBasic.All", "userConsentDisplayName": "Read all users' basic profiles"}, {"adminConsentDescription": "Allows the app to read the full set of profile properties, reports, and managers of other users in your organization, on behalf of the signed-in user.", "id": "a154be20-db9c-4678-8ab7-66f6cc099a59", "adminConsentDisplayName": "Read all users' full profiles", "userConsentDescription": "Allows the app to read the full set of profile properties, reports, and managers of other users in your organization, on your behalf.", "isEnabled": true, "type": "Admin", "value": "User.Read.All", "userConsentDisplayName": "Read all users' full profiles"}, {"adminConsentDescription": "Allows the app to read and write the full set of profile properties, reports, and managers of other users in your organization, on behalf of the signed-in user.", "id": "204e0828-b5ca-4ad8-b9f3-f32a958e7cc4", "adminConsentDisplayName": "Read and write all users' full profiles", "userConsentDescription": "Allows the app to read and write the full set of profile properties, reports, and managers of other users in your organization, on your behalf.", "isEnabled": true, "type": "Admin", "value": "User.ReadWrite.All", "userConsentDisplayName": "Read and write all users' full profiles"}, {"adminConsentDescription": "Allows the app to list groups, and to read their properties and all group memberships on behalf of the signed-in user.  Also allows the app to read calendar, conversations, files, and other group content for all groups the signed-in user can access. ", "id": "5f8c59db-677d-491f-a6b8-5f174b11ec1d", "adminConsentDisplayName": "Read all groups", "userConsentDescription": "Allows the app to list groups, and to read their properties and all group memberships on your behalf.  Also allows the app to read calendar, conversations, files, and other group content for all groups you can access.  ", "isEnabled": true, "type": "Admin", "value": "Group.Read.All", "userConsentDisplayName": "Read all groups"}, {"adminConsentDescription": "Allows the app to create groups and read all group properties and memberships on behalf of the signed-in user.  Additionally allows group owners to manage their groups and allows group members to update group content.", "id": "4e46008b-f24c-477d-8fff-7bb4ec7aafe0", "adminConsentDisplayName": "Read and write all groups", "userConsentDescription": "Allows the app to create groups and read all group properties and memberships on your behalf.  Additionally allows the app to manage your groups and to update group content for groups you are a member of.", "isEnabled": true, "type": "Admin", "value": "Group.ReadWrite.All", "userConsentDisplayName": "Read and write all groups"}, {"adminConsentDescription": "Allows the app to read data in your organization's directory, such as users, groups and apps.", "id": "06da0dbc-49e2-44d2-8312-53f166ab848a", "adminConsentDisplayName": "Read directory data", "userConsentDescription": "Allows the app to read data in your organization's directory.", "isEnabled": true, "type": "Admin", "value": "Directory.Read.All", "userConsentDisplayName": "Read directory data"}, {"adminConsentDescription": "Allows the app to read and write data in your organization's directory, such as users, and groups.  It does not allow the app to delete users or groups, or reset user passwords.", "id": "c5366453-9fb0-48a5-a156-24f0c49a4b84", "adminConsentDisplayName": "Read and write directory data", "userConsentDescription": "Allows the app to read and write data in your organization's directory, such as other users, groups.  It does not allow the app to delete users or groups, or reset user passwords.", "isEnabled": true, "type": "Admin", "value": "Directory.ReadWrite.All", "userConsentDisplayName": "Read and write directory data"}, {"adminConsentDescription": "Allows the app to have the same access to information in the directory as the signed-in user.", "id": "0e263e50-5827-48a4-b97c-d940288653c7", "adminConsentDisplayName": "Access directory as the signed in user", "userConsentDescription": "Allows the app to have the same access to information in your work or school directory as you do.", "isEnabled": true, "type": "Admin", "value": "Directory.AccessAsUser.All", "userConsentDisplayName": "Access the directory as you"}, {"adminConsentDescription": "Allows the app to create, read, update, and delete email in user mailboxes. Does not include permission to send mail. ", "id": "024d486e-b451-40bb-833d-3e66d98c5c73", "adminConsentDisplayName": "Read and write access to user mail ", "userConsentDescription": "Allows the app to read, update, create and delete email in your mailbox. Does not include permission to send mail. ", "isEnabled": true, "type": "User", "value": "Mail.ReadWrite", "userConsentDisplayName": "Read and write access to your mail "}, {"adminConsentDescription": "Allows the app to send mail as users in the organization. ", "id": "e383f46e-2787-4529-855e-0e479a3ffac0", "adminConsentDisplayName": "Send mail as a user ", "userConsentDescription": "Allows the app to send mail as you. ", "isEnabled": true, "type": "User", "value": "Mail.Send", "userConsentDisplayName": "Send mail as you "}, {"adminConsentDescription": "Allows the app to read events in user calendars . ", "id": "465a38f9-76ea-45b9-9f34-9e8b0d4b0b42", "adminConsentDisplayName": "Read user calendars ", "userConsentDescription": "Allows the app to read events in your calendars. ", "isEnabled": true, "type": "User", "value": "Calendars.Read", "userConsentDisplayName": "Read your calendars "}, {"adminConsentDescription": "Allows the app to create, read, update, and delete events in user calendars. ", "id": "1ec239c2-d7c9-4623-a91a-a9775856bb36", "adminConsentDisplayName": "Have full access to user calendars ", "userConsentDescription": "Allows the app to read, update, create and delete events in your calendars. ", "isEnabled": true, "type": "User", "value": "Calendars.ReadWrite", "userConsentDisplayName": "Have full access to your calendars  "}, {"adminConsentDescription": "Allows the app to read user contacts.  ", "id": "ff74d97f-43af-4b68-9f2a-b77ee6968c5d", "adminConsentDisplayName": "Read user contacts ", "userConsentDescription": "Allows the app to read contacts in your contact folders. ", "isEnabled": true, "type": "User", "value": "Contacts.Read", "userConsentDisplayName": "Read your contacts "}, {"adminConsentDescription": "Allows the app to create, read, update, and delete user contacts. ", "id": "d56682ec-c09e-4743-aaf4-1a3aac4caa21", "adminConsentDisplayName": "Have full access to user contacts ", "userConsentDescription": "Allows the app to read, update, create and delete contacts in your contact folders. ", "isEnabled": true, "type": "User", "value": "Contacts.ReadWrite", "userConsentDisplayName": "Have full access of your contacts "}, {"adminConsentDescription": "Allows the app to read the signed-in user's files.", "id": "10465720-29dd-4523-a11a-6a75c743c9d9", "adminConsentDisplayName": "Read user files", "userConsentDescription": "Allows the app to read your files.", "isEnabled": true, "type": "User", "value": "Files.Read", "userConsentDisplayName": "Read your files"}, {"adminConsentDescription": "Allows the app to read, create, update and delete the signed-in user's files.", "id": "5c28f0bf-8a70-41f1-8ab2-9032436ddb65", "adminConsentDisplayName": "Have full access to user files", "userConsentDescription": "Allows the app to read, create, update, and delete your files.", "isEnabled": true, "type": "User", "value": "Files.ReadWrite", "userConsentDisplayName": "Have full access to your files"}, {"adminConsentDescription": "Allows the app to read all files the signed-in user can access.", "id": "df85f4d6-205c-4ac5-a5ea-6bf408dba283", "adminConsentDisplayName": "Read all files that user can access", "userConsentDescription": "Allows the app to read all files you can access.", "isEnabled": true, "type": "User", "value": "Files.Read.All", "userConsentDisplayName": "Read all files that you have access to"}, {"adminConsentDescription": "Allows the app to read, create, update and delete all files the signed-in user can access.", "id": "863451e7-0667-486c-a5d6-d135439485f0", "adminConsentDisplayName": "Have full access to all files user can access", "userConsentDescription": "Allows the app to read, create, update and delete all files that you can access.", "isEnabled": true, "type": "User", "value": "Files.ReadWrite.All", "userConsentDisplayName": "Have full access to all files you have access to"}, {"adminConsentDescription": "Allows the application to read documents and list  items in all site collections on behalf of the signed-in user", "id": "205e70e5-aba6-4c52-a976-6d2d46c48043", "adminConsentDisplayName": "Read items in all site collections", "userConsentDescription": "Allow the application to read documents and list items in all site collections on your behalf", "isEnabled": true, "type": "User", "value": "Sites.Read.All", "userConsentDisplayName": "Read items in all site collections"}, {"adminConsentDescription": "Allows users to sign in to the app with their work or school accounts and allows the app to see basic user profile information.", "id": "37f7f235-527c-4136-accd-4a02d197296e", "adminConsentDisplayName": "Sign users in", "userConsentDescription": "Allows you to sign in to the app with your work or school account and allows the app to read your basic profile information.", "isEnabled": true, "type": "User", "value": "openid", "userConsentDisplayName": "Sign in as you"}, {"adminConsentDescription": "Allows the app to read your users' primary email address", "id": "64a6cdd6-aab1-4aaf-94b8-3cc8405e90d0", "adminConsentDisplayName": "View users' email address", "userConsentDescription": "Allows the app to read your primary email address", "isEnabled": true, "type": "User", "value": "email", "userConsentDisplayName": "View your email address"}, {"adminConsentDescription": "Allows the app to see your users' basic profile (name, picture, user name)", "id": "14dad69e-099b-42c9-810b-d002981feec1", "adminConsentDisplayName": "View users' basic profile", "userConsentDescription": "Allows the app to see your basic profile (name, picture, user name)", "isEnabled": true, "type": "User", "value": "profile", "userConsentDisplayName": "View your basic profile"}, {"adminConsentDescription": "Allows the app to read identity risk event information for all users in your organization on behalf of the signed-in user. ", "id": "8f6a01e7-0391-4ee5-aa22-a3af122cef27", "adminConsentDisplayName": "Read identity risk event information", "userConsentDescription": "Allows the app to read identity risk event information for all users in your organization on behalf of the signed-in user. ", "isEnabled": true, "type": "Admin", "value": "IdentityRiskEvent.Read.All", "userConsentDisplayName": "Read identity risk event information"}, {"adminConsentDescription": "Allows the app to read the memberships of hidden groups and administrative units on behalf of the signed-in user, for those hidden groups and administrative units that the signed-in user has access to.", "id": "f6a3db3e-f7e8-4ed2-a414-557c8c9830be", "adminConsentDisplayName": "Read hidden memberships", "userConsentDescription": "Allows the app to read the memberships of hidden groups or administrative units on your behalf, for those hidden groups or adminstrative units that you have access to.", "isEnabled": true, "type": "Admin", "value": "Member<PERSON><PERSON><PERSON>", "userConsentDisplayName": "Read your hidden memberships"}, {"adminConsentDescription": "Allows the app to read a ranked list of relevant people of the signed-in user. The list includes local contacts, contacts from social networking, your organization's directory, and people from recent communications (such as email and Skype).", "id": "ba47897c-39ec-4d83-8086-ee8256fa737d", "adminConsentDisplayName": "Read users' relevant people lists", "userConsentDescription": "Allows the app to read a list of people in the order that's most relevant to you. This includes your local contacts, your contacts from social networking, people listed in your organization's directory, and people from recent communications.", "isEnabled": true, "type": "User", "value": "People.Read", "userConsentDisplayName": "Read your relevant people list"}, {"adminConsentDescription": "Allows the application to create or delete document libraries and lists in all site collections on behalf of the signed-in user.", "id": "65e50fdc-43b7-4915-933e-e8138f11f40a", "adminConsentDisplayName": "Create, edit, and delete items and lists in all site collections", "userConsentDescription": "Allow the application to create or delete document libraries and lists in all site collections on your behalf.", "isEnabled": true, "type": "User", "value": "Sites.Manage.All", "userConsentDisplayName": "Create, edit, and delete items and lists in all your site collections"}, {"adminConsentDescription": "Allows the application to have full control of all site collections on behalf of the signed-in user.", "id": "5a54b8b3-347c-476d-8f8e-42d5c7424d29", "adminConsentDisplayName": "Have full control of all site collections", "userConsentDescription": "Allow the application to have full control of all site collections on your behalf.", "isEnabled": true, "type": "Admin", "value": "Sites.FullControl.All", "userConsentDisplayName": "Have full control of all your site collections"}, {"adminConsentDescription": "Allows the app to read and write your organization’s identity (authentication) providers’ properties on behalf of the user.", "id": "f13ce604-1677-429f-90bd-8a10b9f01325", "adminConsentDisplayName": "Read and write identity providers", "userConsentDescription": "Allows the app to read and write your organization’s identity (authentication) providers’ properties on your behalf.", "isEnabled": true, "type": "Admin", "value": "IdentityProvider.ReadWrite.All", "userConsentDisplayName": "Read and write identity providers"}, {"adminConsentDescription": "Allows the app to read your organization’s identity (authentication) providers’ properties on behalf of the user.", "id": "43781733-b5a7-4d1b-98f4-e8edff23e1a9", "adminConsentDisplayName": "Read identity providers", "userConsentDescription": "Allows the app to read your organization’s identity (authentication) providers’ properties on your behalf.", "isEnabled": true, "type": "Admin", "value": "IdentityProvider.Read.All", "userConsentDisplayName": "Read identity providers"}, {"adminConsentDescription": "Allows an app to read bookings appointments, businesses, customers, services, and staff on behalf of the signed-in user.", "id": "33b1df99-4b29-4548-9339-7a7b83eaeebc", "adminConsentDisplayName": "Read bookings information", "userConsentDescription": "Allows an app to read bookings appointments, businesses, customers, services, and staff on your behalf.", "isEnabled": true, "type": "User", "value": "Bookings.Read.All", "userConsentDisplayName": "Read bookings information"}, {"adminConsentDescription": "Allows an app to read and write bookings appointments and customers, and additionally allows read businesses information, services, and staff on behalf of the signed-in user.", "id": "02a5a114-36a6-46ff-a102-954d89d9ab02", "adminConsentDisplayName": "Read and write booking appointments", "userConsentDescription": "Allows an app to read and write bookings appointments and customers, and additionally allows read businesses information, services, and staff on your behalf.", "isEnabled": true, "type": "User", "value": "BookingsAppointment.ReadWrite.All", "userConsentDisplayName": "Read and write booking appointments"}, {"adminConsentDescription": "Allows an app to read and write bookings appointments, businesses, customers, services, and staff on behalf of the signed-in user. Does not allow create, delete and publish of booking businesses.", "id": "948eb538-f19d-4ec5-9ccc-f059e1ea4c72", "adminConsentDisplayName": "Read and write bookings information", "userConsentDescription": "Allows an app to read and write Bookings appointments, businesses, customers, services, and staff on your behalf. Does not allow create, delete and publish of booking businesses.", "isEnabled": true, "type": "User", "value": "Bookings.ReadWrite.All", "userConsentDisplayName": "Read and write bookings information"}, {"adminConsentDescription": "Allows an app to read, write and manage bookings appointments, businesses, customers, services, and staff on behalf of the signed-in user.", "id": "7f36b48e-542f-4d3b-9bcb-8406f0ab9fdb", "adminConsentDisplayName": "Manage bookings information", "userConsentDescription": "Allows an app to read, write and manage bookings appointments, businesses, customers, services, and staff on your behalf.", "isEnabled": true, "type": "User", "value": "Bookings.Manage.All", "userConsentDisplayName": "Manage bookings information"}, {"adminConsentDescription": "Allows the app to have the same access to mailboxes as the signed-in user via Exchange ActiveSync.", "id": "ff91d191-45a0-43fd-b837-bd682c4a0b0f", "adminConsentDisplayName": "Access mailboxes via Exchange ActiveSync", "userConsentDescription": "Allows the app full access to your mailboxes on your behalf.", "isEnabled": true, "type": "User", "value": "EAS.AccessAsUser.All", "userConsentDisplayName": "Access your mailboxes"}, {"adminConsentDescription": "Allows the app to read and write financials data on behalf of the signed-in user.", "id": "f534bf13-55d4-45a9-8f3c-c92fe64d6131", "adminConsentDisplayName": "Read and write financials data", "userConsentDescription": "Allows the app to read and write financials data on your behalf.", "isEnabled": true, "type": "User", "value": "Financials.ReadWrite.All", "userConsentDisplayName": "Read and write financials data"}, {"adminConsentDescription": "Allows the app to read your organization's user flows, on behalf of the signed-in user.", "id": "2903d63d-4611-4d43-99ce-a33f3f52e343", "adminConsentDisplayName": "Read all identity user flows", "userConsentDescription": "Allows the app to read your organization's user flows, on your behalf.", "isEnabled": true, "type": "Admin", "value": "IdentityUserFlow.Read.All", "userConsentDisplayName": "Read all identity user flows"}, {"adminConsentDescription": "Allows the app to read or write your organization's user flows, on behalf of the signed-in user.", "id": "281892cc-4dbf-4e3a-b6cc-b21029bb4e82", "adminConsentDisplayName": "Read and write all identity user flows", "userConsentDescription": "Allows the app to read or write your organization's user flows, on your behalf.", "isEnabled": true, "type": "Admin", "value": "IdentityUserFlow.ReadWrite.All", "userConsentDisplayName": "Read and write all identity user flows"}, {"adminConsentDescription": "Allows the app to read all organizational contacts on behalf of the signed-in user.  These contacts are managed by the organization and are different from a user's personal contacts.", "id": "08432d1b-5911-483c-86df-7980af5cdee0", "adminConsentDisplayName": "Read organizational contacts", "userConsentDescription": "Allows the app to read all organizational contacts on your behalf.  These contacts are managed by the organization and are different from your personal contacts.", "isEnabled": true, "type": "Admin", "value": "OrgContact.Read.All", "userConsentDisplayName": "Read organizational contacts"}, {"adminConsentDescription": "Allows the app to manage permission grants for application permissions to any API (including Microsoft Graph) and application assignments for any app, on behalf of the signed-in user.", "id": "84bccea3-f856-4a8a-967b-dbe0a3d53a64", "adminConsentDisplayName": "Manage app permission grants and app role assignments", "userConsentDescription": "Allows the app to manage permission grants for application permissions to any API (including Microsoft Graph) and application assignments for any app, on your behalf.", "isEnabled": true, "type": "Admin", "value": "AppRoleAssignment.ReadWrite.All", "userConsentDisplayName": "Manage app permission grants and app role assignments"}, {"adminConsentDescription": "Allows the app to manage permission grants for delegated permissions exposed by any API (including Microsoft Graph), on behalf of the signed in user.", "id": "41ce6ca6-6826-4807-84f1-1c82854f7ee5", "adminConsentDisplayName": "Manage all delegated permission grants", "userConsentDescription": "Allows the app to manage permission grants for delegated permissions exposed by any API (including Microsoft Graph), on your behalf. ", "isEnabled": true, "type": "Admin", "value": "DelegatedPermissionGrant.ReadWrite.All", "userConsentDisplayName": "Manage all delegated permission grants"}, {"adminConsentDescription": "Allows the app to read online meeting details on behalf of the signed-in user.", "id": "9be106e1-f4e3-4df5-bdff-e4bc531cbe43", "adminConsentDisplayName": "Read user's online meetings", "userConsentDescription": "Allows the app to read online meeting details on your behalf.", "isEnabled": true, "type": "User", "value": "OnlineMeetings.Read", "userConsentDisplayName": "Read your online meetings"}, {"adminConsentDescription": "Allows the app to read and create online meetings on behalf of the signed-in user.", "id": "a65f2972-a4f8-4f5e-afd7-69ccb046d5dc", "adminConsentDisplayName": "Read and create user's online meetings", "userConsentDescription": "Allows the app to read and create online meetings on your behalf.", "isEnabled": true, "type": "User", "value": "OnlineMeetings.ReadWrite", "userConsentDisplayName": "Read and create your online meetings"}, {"adminConsentDescription": "Allows the app to read the signed-in user's teamwork activity feed.", "id": "0e755559-83fb-4b44-91d0-4cc721b9323e", "adminConsentDisplayName": "Read user's teamwork activity feed", "userConsentDescription": "Allows the app to read your teamwork activity feed.", "isEnabled": true, "type": "User", "value": "TeamsActivity.Read", "userConsentDisplayName": "Read your teamwork activity feed"}, {"adminConsentDescription": "Allows the app to request and manage time-based assignment and just-in-time elevation of user privileges to manage Azure resources (like subscriptions, resource groups, storage, compute) on behalf of the signed-in users.", "id": "a84a9652-ffd3-496e-a991-22ba5529156a", "adminConsentDisplayName": "Read and write privileged access to Azure resources", "userConsentDescription": "Allows the app to request and manage time-based assignment and just-in-time elevation of user privileges to manage  your Azure resources (like your subscriptions, resource groups, storage, compute) on your behalf.", "isEnabled": true, "type": "Admin", "value": "PrivilegedAccess.ReadWrite.AzureResources", "userConsentDisplayName": "Read and write privileged access to Azure resources"}, {"adminConsentDescription": "Allows the app to read time-based assignment and just-in-time elevation (including scheduled elevation) of Azure AD built-in and custom administrative roles, on behalf of the signed-in user.", "id": "b3a539c9-59cb-4ad5-825a-041ddbdc2bdb", "adminConsentDisplayName": "Read privileged access to Azure AD", "userConsentDescription": "Allows the app to read time-based assignment and just-in-time elevation (including scheduled elevation) of Azure AD built-in and custom administrative roles, on your behalf.", "isEnabled": true, "type": "Admin", "value": "PrivilegedAccess.Read.AzureAD", "userConsentDisplayName": "Read privileged access to Azure AD"}, {"adminConsentDescription": "Allows the app to read time-based assignment and just-in-time elevation (including scheduled elevation) of Azure AD groups, on behalf of the signed-in user.", "id": "d329c81c-20ad-4772-abf9-3f6fdb7e5988", "adminConsentDisplayName": "Read privileged access to Azure AD groups", "userConsentDescription": "Allows the app to read time-based assignment and just-in-time elevation (including scheduled elevation) of Azure AD groups, on your behalf.", "isEnabled": true, "type": "Admin", "value": "PrivilegedAccess.Read.AzureADGroup", "userConsentDisplayName": "Read privileged access to Azure AD groups"}, {"adminConsentDescription": "Allows the app to read time-based assignment and just-in-time elevation of Azure resources (like your subscriptions, resource groups, storage, compute) on behalf of the signed-in user.", "id": "1d89d70c-dcac-4248-b214-903c457af83a", "adminConsentDisplayName": "Read privileged access to Azure resources", "userConsentDescription": "Allows the app to read time-based assignment and just-in-time elevation of Azure resources (like your subscriptions, resource groups, storage, compute) on your behalf.", "isEnabled": true, "type": "Admin", "value": "PrivilegedAccess.Read.AzureResources", "userConsentDisplayName": "Read privileged access to your Azure resources"}, {"adminConsentDescription": "Allows the app to request and manage time-based assignment and just-in-time elevation (including scheduled elevation) of Azure AD groups, on behalf of the signed-in user.", "id": "32531c59-1f32-461f-b8df-6f8a3b89f73b", "adminConsentDisplayName": "Read and write privileged access to Azure AD groups", "userConsentDescription": "Allows the app to request and manage time-based assignment and just-in-time elevation (including scheduled elevation) of Azure AD groups, on your behalf.", "isEnabled": true, "type": "Admin", "value": "PrivilegedAccess.ReadWrite.AzureADGroup", "userConsentDisplayName": "Read and write privileged access to Azure AD groups"}, {"adminConsentDescription": "Allows the app to read all the indicators for your organization, on behalf of the signed-in user.", "id": "9cc427b4-2004-41c5-aa22-757b755e9796", "adminConsentDisplayName": "Read all threat indicators", "userConsentDescription": "Allows the app to read all the indicators for your organization, on your behalf.", "isEnabled": true, "type": "Admin", "value": "ThreatIndicators.Read.All", "userConsentDisplayName": "Read all threat indicators"}, {"adminConsentDescription": "Allows the app to read privileged access requests, business flows, and governance policy templates on behalf of the signed-in user.", "id": "31df746c-3cfa-4b19-b243-36a6fb2b6a66", "adminConsentDisplayName": "Read privileged access approval requests", "userConsentDescription": "Allows the app to read privileged access requests, business flows, and governance policy templates on your behalf.", "isEnabled": true, "type": "Admin", "value": "ApprovalRequest.Read.PriviligedAccess", "userConsentDisplayName": "Read privileged access approval requests"}, {"adminConsentDescription": "Allows the app to read entitlement management requests, business flows, and governance policy templates on behalf of the signed-in user.", "id": "95b85e04-9c5c-4554-a3ad-2e933c8a81cd", "adminConsentDisplayName": "Read entitlement management approval requests", "userConsentDescription": "Allows the app to read entitlement management requests, business flows, and governance policy templates on your behalf.", "isEnabled": true, "type": "Admin", "value": "ApprovalRequest.Read.EntitlementManagement", "userConsentDisplayName": "Read entitlement management approval requests"}, {"adminConsentDescription": "Allows the app to read admin consent requests, business flows, and governance policy templates on behalf of the signed-in user.", "id": "fad55eff-94e6-4517-9859-439301f0bad2", "adminConsentDisplayName": "Read admin consent approval requests", "userConsentDescription": "Allows the app to read admin consent requests, business flows, and governance policy templates on your behalf.", "isEnabled": true, "type": "Admin", "value": "ApprovalRequest.Read.AdminConsentRequest", "userConsentDisplayName": "Read admin consent approval requests"}, {"adminConsentDescription": "Allows the app to read customer lockbox requests, business flows and governance policy templates on behalf of the signed-in user.", "id": "8123bef2-defe-4f3a-8d33-02baa9e6fcfc", "adminConsentDisplayName": "Read customer lockbox approval requests", "userConsentDescription": "Allows the app to read customer lockbox requests, business flows and governance policy templates on your behalf.", "isEnabled": true, "type": "Admin", "value": "ApprovalRequest.Read.CustomerLockbox", "userConsentDisplayName": "Read customer lockbox approval requests"}, {"adminConsentDescription": "Allows the app to read and write privileged access requests, business flows, and governance policy templates on behalf of the signed-in user.", "id": "51e5d7dc-745e-4986-aa03-63d64036a7a5", "adminConsentDisplayName": "Read and write privileged access approval requests", "userConsentDescription": "Allows the app to read and write privileged access requests, business flows, and governance policy templates on your behalf.", "isEnabled": true, "type": "Admin", "value": "ApprovalRequest.ReadWrite.PriviligedAccess", "userConsentDisplayName": "Read and write privileged access approval requests"}, {"adminConsentDescription": "Allows the app to read and write entitlement management requests, business flows, and governance policy templates on behalf of the signed-in user.", "id": "15dc7bc3-a26c-40b1-8b58-b2a764eb06c1", "adminConsentDisplayName": "Read and write entitlement management approval requests", "userConsentDescription": "Allows the app to read and write entitlement management requests, business flows, and governance policy templates on your behalf.", "isEnabled": true, "type": "Admin", "value": "ApprovalRequest.ReadWrite.EntitlementManagement", "userConsentDisplayName": "Read and write entitlement management approval requests"}, {"adminConsentDescription": "Allows the app to read and write admin consent requests, business flows, and governance policy templates on behalf of the signed-in user.", "id": "0c940179-817f-401c-9a44-277f3fc38e2b", "adminConsentDisplayName": "Read and write admin consent approval requests", "userConsentDescription": "Allows the app to read and write admin consent requests, business flows, and governance policy templates on your behalf.", "isEnabled": true, "type": "Admin", "value": "ApprovalRequest.ReadWrite.AdminConsentRequest", "userConsentDisplayName": "Read and write admin consent approval requests"}, {"adminConsentDescription": "Allows the app to read and write admin consent requests, business flows, and governance policy templates on behalf of the signed-in user.", "id": "115b3477-4404-4685-a45d-4cf6a6092533", "adminConsentDisplayName": "Read and write customer lockbox approval requests", "userConsentDescription": "Allows the app to read and write customer lockbox requests, business flows and governance policy templates on your behalf.", "isEnabled": true, "type": "Admin", "value": "ApprovalRequest.ReadWrite.CustomerLockbox", "userConsentDisplayName": "Read and write customer lockbox approval requests"}, {"adminConsentDescription": "Allow the app to read external datasets and content, on behalf of the signed-in user.", "id": "922f9392-b1b7-483c-a4be-0089be7704fb", "adminConsentDisplayName": "Read items in external datasets", "userConsentDescription": "Allows the app to read external datasets and content that you have access to.", "isEnabled": true, "type": "Admin", "value": "ExternalItem.Read.All", "userConsentDisplayName": "Read items in external datasets"}, {"adminConsentDescription": "Allows an app to delete channel messages in Microsoft Teams, on behalf of the signed-in user.", "id": "32ea53ac-4a89-4cde-bac4-727c6fb9ac29", "adminConsentDisplayName": "Delete user's channel messages", "userConsentDescription": "Allows the app to delete channel messages in Microsoft Teams, on your behalf.", "isEnabled": true, "type": "User", "value": "ChannelMessage.Delete", "userConsentDisplayName": "Delete your channel messages"}, {"adminConsentDescription": "Allows an app to edit channel messages in Microsoft Teams, on behalf of the signed-in user.", "id": "2b61aa8a-6d36-4b2f-ac7b-f29867937c53", "adminConsentDisplayName": "Edit user's channel messages", "userConsentDescription": "Allows the app to edit channel messages in Microsoft Teams, on your behalf.", "isEnabled": true, "type": "User", "value": "ChannelMessage.Edit", "userConsentDisplayName": "Edit your channel messages"}, {"adminConsentDescription": "Allows an app to send channel messages in Microsoft Teams, on behalf of the signed-in user.", "id": "ebf0f66e-9fb1-49e4-a278-222f76911cf4", "adminConsentDisplayName": "Send channel messages", "userConsentDescription": "Allows the app to send channel messages in Microsoft Teams, on your behalf.", "isEnabled": true, "type": "User", "value": "ChannelMessage.Send", "userConsentDisplayName": "Send channel messages"}, {"adminConsentDescription": "Allows the app to read the Teams apps that are installed for the signed-in user. Does not give the ability to read application-specific settings.", "id": "daef10fc-047a-48b0-b1a5-da4b5e72fabc", "adminConsentDisplayName": "Read user's installed Teams apps", "userConsentDescription": "Allows the app to read the Teams apps that are installed for you. Does not give the ability to read application-specific settings.", "isEnabled": true, "type": "User", "value": "TeamsApp.Read", "userConsentDisplayName": "Read your installed Teams apps"}, {"adminConsentDescription": "Allows the app to read, install, upgrade, and uninstall Teams apps, on behalf of the signed-in user. Does not give the ability to read or write application-specific settings.", "id": "2a5addc2-4d9e-4d7d-8527-5215aec410f3", "adminConsentDisplayName": "Manage user's Teams apps", "userConsentDescription": "Allows the app to read, install, upgrade, and uninstall Teams apps, on your behalf. Does not give the ability to read or write application-specific settings.", "isEnabled": true, "type": "User", "value": "TeamsApp.ReadWrite", "userConsentDisplayName": "Manage your Teams apps"}, {"adminConsentDescription": "Allows the app to read the signed-in user’s personal places.", "id": "40f6bacc-b201-40da-90a5-09775cc4a863", "adminConsentDisplayName": "Read user places", "userConsentDescription": "Allows the app to read your personal places.", "isEnabled": true, "type": "User", "value": "Place.Read", "userConsentDisplayName": "Read your places"}, {"adminConsentDescription": "Allows the app to create, read, and update the signed-in user’s personal places.", "id": "012ba4a5-ca82-4a76-95ba-6c27f44364c3", "adminConsentDisplayName": "Read and write user places", "userConsentDescription": "Allows the app to create, read, and update personal places on your behalf.", "isEnabled": true, "type": "User", "value": "Place.ReadWrite", "userConsentDisplayName": "Read and write your places"}, {"adminConsentDescription": "Allows the app to manage organization places (conference rooms and room lists) for calendar events and other applications, on behalf of the signed-in user.", "id": "4c06a06a-098a-4063-868e-5dfee3827264", "adminConsentDisplayName": "Read and write organization places", "userConsentDescription": "Allows the app to manage organization places (conference rooms and room lists) for calendar events and other applications, on your behalf.", "isEnabled": true, "type": "Admin", "value": "Place.ReadWrite.All", "userConsentDisplayName": "Read and write organization places"}, {"adminConsentDescription": "Allows the app to read other users’ personal places that the signed-in user has delegate access to. Also allows read of the signed-in user’s personal places.", "id": "0b3f56bc-fecd-4036-8930-660fc672e342", "adminConsentDisplayName": "Read user places for delegates", "userConsentDescription": "Allows the app to read your personal places and other users’ personal places that you have delegate access to.", "isEnabled": true, "type": "User", "value": "Place.Read.Shared", "userConsentDisplayName": "Read user delegate places"}, {"adminConsentDescription": "Allows the app to request access to and management of access packages and related entitlement management resources on behalf of the signed-in user.", "id": "ae7a573d-81d7-432b-ad44-4ed5c9d89038", "adminConsentDisplayName": "Read and write entitlement management resources", "userConsentDescription": "Allows the app to request access to and management of access packages and related entitlement management resources that you have access to.", "isEnabled": true, "type": "Admin", "value": "EntitlementManagement.ReadWrite.All", "userConsentDisplayName": "Read and write entitlement management resources"}, {"adminConsentDescription": "Allows the app to send, read, update and delete user’s notifications.", "id": "26e2f3e8-b2a1-47fc-9620-89bb5b042024", "adminConsentDisplayName": "Deliver and manage user's notifications", "userConsentDescription": "Allows the app to send, read, update and delete your app-specific notifications.", "isEnabled": true, "type": "User", "value": "UserNotification.ReadWrite.CreatedByApp", "userConsentDisplayName": "Deliver and manage your notifications"}, {"adminConsentDescription": "Allows the app to read applications and service principals on behalf of the signed-in user.", "id": "c79f8feb-a9db-4090-85f9-90d820caa0eb", "adminConsentDisplayName": "Read applications", "userConsentDescription": "Allows the app to read applications and service principals on your behalf.", "isEnabled": true, "type": "Admin", "value": "Application.Read.All", "userConsentDisplayName": "Read applications"}, {"adminConsentDescription": "Allows the app to create, read, update and delete applications and service principals on behalf of the signed-in user. Does not allow management of consent grants.", "id": "bdfbf15f-ee85-4955-8675-146e8e5296b5", "adminConsentDisplayName": "Read and write all applications", "userConsentDescription": "Allows the app to create, read, update and delete applications and service principals on your behalf. Does not allow management of consent grants.", "isEnabled": true, "type": "Admin", "value": "Application.ReadWrite.All", "userConsentDisplayName": "Read and write applications"}, {"adminConsentDescription": "Allows the app to read BitLocker keys on behalf of the signed-in user, for their owned devices. Allows read of the recovery key.", "id": "b27a61ec-b99c-4d6a-b126-c4375d08ae30", "adminConsentDisplayName": "Read BitLocker keys", "userConsentDescription": "Allows the app to read BitLocker keys for your owned devices. Allows read of the recovery key.", "isEnabled": true, "type": "Admin", "value": "BitlockerKey.Read.All", "userConsentDisplayName": "Read your BitLocker keys"}, {"adminConsentDescription": "Allows the app to read basic BitLocker key properties on behalf of the signed-in user, for their owned devices. Does not allow read of the recovery key itself.", "id": "5a107bfc-4f00-4e1a-b67e-66451267bc68", "adminConsentDisplayName": "Read BitLocker keys basic information", "userConsentDescription": "Allows the app to read basic BitLocker key properties for your owned devices. Does not allow read of the recovery key itself.", "isEnabled": true, "type": "Admin", "value": "BitlockerKey.ReadBasic.All", "userConsentDisplayName": "Read your BitLocker keys basic information"}, {"adminConsentDescription": "Allows the app to list groups, read basic group properties and read membership of all groups the signed-in user has access to.", "id": "bc024368-1153-4739-b217-4326f2e966d0", "adminConsentDisplayName": "Read group memberships", "userConsentDescription": "Allows the app to list groups, read basic group properties and read membership of all your groups.", "isEnabled": true, "type": "Admin", "value": "GroupMember.Read.All", "userConsentDisplayName": "Read group memberships"}, {"adminConsentDescription": "Allows the app to list groups, read basic properties, read and update the membership of the groups the signed-in user has access to. Group properties and owners cannot be updated and groups cannot be deleted.", "id": "f81125ac-d3b7-4573-a3b2-7099cc39df9e", "adminConsentDisplayName": "Read and write group memberships", "userConsentDescription": "Allows the app to list groups, read basic properties, read and update the membership of your groups. Group properties and owners cannot be updated and groups cannot be deleted.", "isEnabled": true, "type": "Admin", "value": "GroupMember.ReadWrite.All", "userConsentDisplayName": "Read and write group memberships"}, {"adminConsentDescription": "Allows an app to read your organization's threat assessment requests on behalf of the signed-in user. Also allows the app to create new requests to assess threats received by your organization on behalf of the signed-in user.", "id": "cac97e40-6730-457d-ad8d-4852fddab7ad", "adminConsentDisplayName": "Read and write threat assessment requests", "userConsentDescription": "Allows an app to read your organization's threat assessment requests on your behalf. Also allows the app to create new requests to assess threats received by your organization on your behalf.", "isEnabled": true, "type": "Admin", "value": "ThreatAssessment.ReadWrite.All", "userConsentDisplayName": "Read and write threat assessment requests"}, {"adminConsentDescription": "Allows the app to read schedule, schedule groups, shifts and associated entities in the Teams or Shifts application on behalf of the signed-in user.", "id": "fccf6dd8-5706-49fa-811f-69e2e1b585d0", "adminConsentDisplayName": "Read user schedule items", "userConsentDescription": "Allows the app to read schedule, schedule groups, shifts and associated entities in the Teams or Shifts application on your behalf.", "isEnabled": true, "type": "Admin", "value": "Schedule.Read.All", "userConsentDisplayName": "Read your schedule items"}, {"adminConsentDescription": "Allows the app to manage schedule, schedule groups, shifts and associated entities in the Teams or Shifts application on behalf of the signed-in user.", "id": "63f27281-c9d9-4f29-94dd-6942f7f1feb0", "adminConsentDisplayName": "Read and write user schedule items", "userConsentDescription": "Allows the app to manage schedule, schedule groups, shifts and associated entities in the Teams or Shifts application on your behalf.", "isEnabled": true, "type": "Admin", "value": "Schedule.ReadWrite.All", "userConsentDisplayName": "Read and write your schedule items"}, {"adminConsentDescription": " Allows the app to read and write authentication methods of all users in your organization that the signed-in user has access to.                       Authentication methods include things like a user’s phone numbers and Authenticator app settings. This                      does not allow the app to see secret information like passwords, or to sign-in or otherwise use the authentication methods.", "id": "b7887744-6746-4312-813d-72daeaee7e2d", "adminConsentDisplayName": "Read and write all users' authentication methods.", "userConsentDescription": "Allows the app to read and write authentication methods of all users you have access to in your organization.                       Authentication methods include things like a user’s phone numbers and Authenticator app settings. This does not allow                      the app to see secret information like passwords, or to sign-in or otherwise use the authentication methods.", "isEnabled": true, "type": "Admin", "value": "UserAuthenticationMethod.ReadWrite.All", "userConsentDisplayName": "Read and write all users' authentication methods"}, {"adminConsentDescription": "Allows the app to read and write the signed-in user's authentication methods, including phone numbers and Authenticator app settings.                       This does not allow the app to see secret information like the signed-in user's passwords, or                      to sign-in or otherwise use the signed-in user's authentication methods.  ", "id": "48971fc1-70d7-4245-af77-0beb29b53ee2", "adminConsentDisplayName": "Read and write user authentication methods", "userConsentDescription": "Allows the app to read and write your authentication methods, including phone numbers and Authenticator app settings.This does not allow the app to see secret information like your passwords, or to sign-in or otherwise use your authentication methods.", "isEnabled": true, "type": "Admin", "value": "UserAuthenticationMethod.ReadWrite", "userConsentDisplayName": "Read and write your authentication methods"}, {"adminConsentDescription": "Allows the app to read authentication methods of all users in your organization that the signed-in user has access to. Authentication methods include things like a user’s phone numbers and Authenticator app settings. This does not allow the app to see secret information like passwords, or to sign-in or otherwise use the authentication methods.", "id": "aec28ec7-4d02-4e8c-b864-50163aea77eb", "adminConsentDisplayName": "Read all users' authentication methods", "userConsentDescription": "Allows the app to read authentication methods of all users you have access to in your organization. Authentication methods include things like a user’s phone numbers and Authenticator app settings. This does not allow the app to see secret information like passwords, or to sign-in or otherwise use the authentication methods.", "isEnabled": true, "type": "Admin", "value": "UserAuthenticationMethod.Read.All", "userConsentDisplayName": "Read all users' authentication methods"}, {"adminConsentDescription": "Allows the app to read the signed-in user's authentication methods, including phone numbers and Authenticator app settings. This does not allow the app to see secret information like the signed-in user's passwords, or to sign-in  or otherwise use the signed-in user's authentication methods.", "id": "1f6b61c5-2f65-4135-9c9f-31c0f8d32b52", "adminConsentDisplayName": "Read user authentication methods.", "userConsentDescription": "Allows the app to read your authentication methods, including phone numbers and Authenticator app settings. This does not allow the app to see secret information like your passwords, or to sign-in or otherwise use your authentication methods.", "isEnabled": true, "type": "Admin", "value": "UserAuthenticationMethod.Read", "userConsentDisplayName": "Read your authentication methods."}, {"adminConsentDescription": "Allows the app to read, install, upgrade, and uninstall Teams apps, on behalf of the signed-in user and also for teams the user is a member of. Does not give the ability to read or write application-specific settings.", "id": "d3f0af02-b22d-4778-a433-14f7e3f2e1e2", "adminConsentDisplayName": "Manage all Teams apps", "userConsentDescription": "Allows the app to read, install, upgrade, and uninstall Teams apps, on your behalf. Does not give the ability to read or write application-specific settings.", "isEnabled": true, "type": "Admin", "value": "TeamsApp.ReadWrite.All", "userConsentDisplayName": "Manage all Teams apps"}, {"adminConsentDescription": "Allows the app to read the Teams apps that are installed for the signed-in user, and in all teams the user is a member of. Does not give the ability to read application-specific settings.", "id": "9127ba42-f79f-43b1-be80-f23ecd42377e", "adminConsentDisplayName": "Read all installed Teams apps", "userConsentDescription": "Allows the app to read the Teams apps that are installed for you, and in teams you are a member of. Does not give the ability to read application-specific settings.", "isEnabled": true, "type": "Admin", "value": "TeamsApp.Read.All", "userConsentDisplayName": "Read all installed Teams apps"}, {"adminConsentDescription": "Allows the app to create tabs in any team in Microsoft Teams, on behalf of the signed-in user. This does not grant the ability to read, modify or delete tabs after they are created, or give access to the content inside the tabs.", "id": "a9ff19c2-f369-4a95-9a25-ba9d460efc8e", "adminConsentDisplayName": "Create tabs in Microsoft Teams.", "userConsentDescription": "Allows the app to create tabs in any team in Microsoft Teams, on your behalf. This does not grant the ability to read, modify or delete tabs after they are created, or give access to the content inside the tabs.", "isEnabled": true, "type": "Admin", "value": "TeamsTab.Create", "userConsentDisplayName": "Create tabs in Microsoft Teams."}, {"adminConsentDescription": "Read the names and settings of tabs inside any team in Microsoft Teams, on behalf of the signed-in user. This does not give access to the content inside the tabs.", "id": "59dacb05-e88d-4c13-a684-59f1afc8cc98", "adminConsentDisplayName": "Read tabs in Microsoft Teams.", "userConsentDescription": "Read the names and settings of tabs inside any team in Microsoft Teams, on your behalf. This does not give access to the content inside the tabs.", "isEnabled": true, "type": "Admin", "value": "TeamsTab.Read.All", "userConsentDisplayName": "Read tabs in Microsoft Teams."}, {"adminConsentDescription": "Read and write tabs in any team in Microsoft Teams, on behalf of the signed-in user. This does not give access to the content inside the tabs.", "id": "b98bfd41-87c6-45cc-b104-e2de4f0dafb9", "adminConsentDisplayName": "Read and write tabs in Microsoft Teams.", "userConsentDescription": "Read and write tabs in any team in Microsoft Teams, on your behalf. This does not give access to the content inside the tabs.", "isEnabled": true, "type": "Admin", "value": "TeamsTab.ReadWrite.All", "userConsentDisplayName": "Read and write tabs in Microsoft Teams."}, {"adminConsentDescription": "Allows the app to have the same access to mailboxes as the signed-in user via IMAP protocol.", "id": "652390e4-393a-48de-9484-05f9b1212954", "adminConsentDisplayName": "Read and write access to mailboxes via IMAP.", "userConsentDescription": "Allows the app to read, update, create and delete email in your mailbox. Does not include permission to send mail.", "isEnabled": true, "type": "User", "value": "IMAP.AccessAsUser.All", "userConsentDisplayName": "Read and write access to your mail."}, {"adminConsentDescription": "Allows the app to have the same access to mailboxes as the signed-in user via POP protocol.", "id": "d7b7f2d9-0f45-4ea1-9d42-e50810c06991", "adminConsentDisplayName": "Read and write access to mailboxes via POP.", "userConsentDescription": "Allows the app to read, update, create and delete email in your mailbox. Does not include permission to send mail.", "isEnabled": true, "type": "User", "value": "POP.AccessAsUser.All", "userConsentDisplayName": "Read and write access to your mail."}, {"adminConsentDescription": "Allows the app to be able to send emails from the user’s mailbox using the SMTP AUTH client submission protocol.", "id": "258f6531-6087-4cc4-bb90-092c5fb3ed3f", "adminConsentDisplayName": "Send emails from mailboxes using SMTP AUTH.", "userConsentDescription": "Allows the app to send emails on your behalf from your mailbox.", "isEnabled": true, "type": "User", "value": "SMTP.Send", "userConsentDisplayName": "Access to sending emails from your mailbox."}, {"adminConsentDescription": "Allows the app to read all domain properties on behalf of the signed-in user.", "id": "2f9ee017-59c1-4f1d-9472-bd5529a7b311", "adminConsentDisplayName": "Read domains.", "userConsentDescription": "Allows the app to read all domain properties on your behalf.", "isEnabled": true, "type": "Admin", "value": "Domain.Read.All", "userConsentDisplayName": "Read domains."}, {"adminConsentDescription": "Allows the app to read and write all domain properties on behalf of the signed-in user. Also allows the app to add, verify and remove domains.", "id": "0b5d694c-a244-4bde-86e6-eb5cd07730fe", "adminConsentDisplayName": "Read and write domains", "userConsentDescription": "Allows the app to read and write all domain properties on your behalf. Also allows the app to add, verify and remove domains.", "isEnabled": true, "type": "Admin", "value": "Domain.ReadWrite.All", "userConsentDisplayName": "Read and write domains"}, {"adminConsentDescription": "Allows the app to read and write your organization's application configuration policies on behalf of the signed-in user.  This includes policies such as activityBasedTimeoutPolicy, claimsMappingPolicy, homeRealmDiscoveryPolicy,  tokenIssuancePolicy and tokenLifetimePolicy.", "id": "b27add92-efb2-4f16-84f5-8108ba77985c", "adminConsentDisplayName": "Read and write your organization's application configuration policies", "userConsentDescription": "Allows the app to read and write your organization's application configuration policies on your behalf.  This includes policies such as activityBasedTimeoutPolicy, claimsMappingPolicy, homeRealmDiscoveryPolicy, tokenIssuancePolicy  and tokenLifetimePolicy.", "isEnabled": true, "type": "Admin", "value": "Policy.ReadWrite.ApplicationConfiguration", "userConsentDisplayName": "Read and write your organization's application configuration policies"}, {"adminConsentDescription": "Allows the app to read your organization's devices' configuration information on behalf of the signed-in user.", "id": "951183d1-1a61-466f-a6d1-1fde911bfd95", "adminConsentDisplayName": "Read all devices", "userConsentDescription": "Allows the app to read devices' configuration information on your behalf.", "isEnabled": true, "type": "Admin", "value": "Device.Read.All", "userConsentDisplayName": "Read all devices"}, {"adminConsentDescription": "Allows the app to read, update and delete identities that are associated with a user's account that the signed-in user has access to. This controls the identities users can sign-in with.", "id": "637d7bec-b31e-4deb-acc9-24275642a2c9", "adminConsentDisplayName": "Manage  user identities", "userConsentDescription": "Allows the app to read, update and delete identities that are associated with a user's account that you have access to. This controls the identities users can sign-in with.", "isEnabled": true, "type": "Admin", "value": "User.ManageIdentities.All", "userConsentDisplayName": "Manage  user identities"}, {"adminConsentDescription": "Allows the app to read access packages and related entitlement management resources on behalf of the signed-in user.", "id": "5449aa12-1393-4ea2-a7c7-d0e06c1a56b2", "adminConsentDisplayName": "Read all entitlement management resources", "userConsentDescription": "Allows the app to read access packages and related entitlement management resources that you have access to.", "isEnabled": true, "type": "Admin", "value": "EntitlementManagement.Read.All", "userConsentDisplayName": "Read all entitlement management resources"}, {"adminConsentDescription": "Create channels in any team, on behalf of the signed-in user.", "id": "101147cf-4178-4455-9d58-02b5c164e759", "adminConsentDisplayName": "Create channels", "userConsentDescription": "Create channels in any team, on your behalf.", "isEnabled": true, "type": "Admin", "value": "Channel.Create", "userConsentDisplayName": "Create channels"}, {"adminConsentDescription": "Delete channels in any team, on behalf of the signed-in user.", "id": "cc83893a-e232-4723-b5af-bd0b01bcfe65", "adminConsentDisplayName": "Delete channels", "userConsentDescription": "Delete channels in any team, on your behalf.", "isEnabled": true, "type": "Admin", "value": "Channel.Delete.All", "userConsentDisplayName": "Delete channels"}, {"adminConsentDescription": "Read all channel names, channel descriptions, and channel settings, on behalf of the signed-in user.", "id": "233e0cf1-dd62-48bc-b65b-b38fe87fcf8e", "adminConsentDisplayName": "Read the names, descriptions, and settings of channels", "userConsentDescription": "Read all channel names, channel descriptions, and channel settings, on your behalf.", "isEnabled": true, "type": "Admin", "value": "ChannelSettings.Read.All", "userConsentDisplayName": "Read the names, descriptions, and settings of channels"}, {"adminConsentDescription": "Read and write the names, descriptions, and settings of all channels, on behalf of the signed-in user.", "id": "d649fb7c-72b4-4eec-b2b4-b15acf79e378", "adminConsentDisplayName": "Read and write the names, descriptions, and settings of channels", "userConsentDescription": "Read and write the names, descriptions, and settings of all channels, on your behalf.", "isEnabled": true, "type": "Admin", "value": "ChannelSettings.ReadWrite.All", "userConsentDisplayName": "Read and write the names, descriptions, and settings of channels"}, {"adminConsentDescription": "Allows the app to read all webhook subscriptions on behalf of the signed-in user.", "id": "5f88184c-80bb-4d52-9ff2-757288b2e9b7", "adminConsentDisplayName": "Read all webhook subscriptions ", "userConsentDescription": "Allows the app to read all webhook subscriptions on your behalf.", "isEnabled": true, "type": "Admin", "value": "Subscription.Read.All", "userConsentDisplayName": "Read all webhook subscriptions "}, {"adminConsentDescription": "Read the names and  descriptions of teams, on behalf of the signed-in user.", "id": "485be79e-c497-4b35-9400-0e3fa7f2a5d4", "adminConsentDisplayName": "Read the names and descriptions of teams", "userConsentDescription": "Read the names and  descriptions of teams, on your behalf.", "isEnabled": true, "type": "User", "value": "Team.ReadBasic.All", "userConsentDisplayName": "Read the names and descriptions of teams"}, {"adminConsentDescription": "Read channel names and channel descriptions, on behalf of the signed-in user.", "id": "9d8982ae-4365-4f57-95e9-d6032a4c0b87", "adminConsentDisplayName": "Read the names and descriptions of channels", "userConsentDescription": "Read channel names and channel descriptions, on your behalf.", "isEnabled": true, "type": "User", "value": "Channel.ReadBasic.All", "userConsentDisplayName": "Read the names and descriptions of channels"}, {"adminConsentDescription": "Read all teams' settings, on behalf of the signed-in user.", "id": "48638b3c-ad68-4383-8ac4-e6880ee6ca57", "adminConsentDisplayName": "Read teams' settings", "userConsentDescription": "Read all teams' settings, on your behalf.", "isEnabled": true, "type": "Admin", "value": "TeamSettings.Read.All", "userConsentDisplayName": "Read teams' settings"}, {"adminConsentDescription": "Read and change all teams' settings, on behalf of the signed-in user.", "id": "39d65650-9d3e-4223-80db-a335590d027e", "adminConsentDisplayName": "Read and change teams' settings", "userConsentDescription": "Read and change all teams' settings, on your behalf.", "isEnabled": true, "type": "Admin", "value": "TeamSettings.ReadWrite.All", "userConsentDisplayName": "Read and change teams' settings"}, {"adminConsentDescription": "Allows the app to read approvals on behalf of the signed-in user.", "id": "1196552e-b226-4363-b01e-b8901fe10a11", "adminConsentDisplayName": "Read approvals", "userConsentDescription": "Allows the app to read approvals on your behalf.", "isEnabled": true, "type": "Admin", "value": "Approval.Read.All", "userConsentDisplayName": "Read approvals"}, {"adminConsentDescription": "Allows the app to read and write approvals on behalf of the signed-in user.", "id": "1d3d0bc7-4b3a-427a-ae9f-6de4e1edc95f", "adminConsentDisplayName": "Read and write approvals", "userConsentDescription": "Allows the app to read and write approvals on your behalf.", "isEnabled": true, "type": "Admin", "value": "Approval.ReadWrite.All", "userConsentDisplayName": "Read and write approvals"}, {"adminConsentDescription": "Read the members of teams, on behalf of the signed-in user.", "id": "2497278c-d82d-46a2-b1ce-39d4cdde5570", "adminConsentDisplayName": "Read the members of teams", "userConsentDescription": "Read the members of teams, on your behalf.", "isEnabled": true, "type": "Admin", "value": "TeamMember.Read.All", "userConsentDisplayName": "Read the members of teams"}, {"adminConsentDescription": "Add and remove members from teams, on behalf of the signed-in user. Also allows changing a member's role, for example from owner to non-owner.", "id": "4a06efd2-f825-4e34-813e-82a57b03d1ee", "adminConsentDisplayName": "Add and remove members from teams", "userConsentDescription": "Add and remove members from teams, on your behalf. Also allows changing a member's role, for example from owner to non-owner.", "isEnabled": true, "type": "Admin", "value": "TeamMember.ReadWrite.All", "userConsentDisplayName": "Add and remove members from teams and channels"}, {"adminConsentDescription": "Allows the app to read consent requests and approvals on behalf of the signed-in user.", "id": "f3bfad56-966e-4590-a536-82ecf548ac1e", "adminConsentDisplayName": "Read consent requests", "userConsentDescription": "Allows the app to read consent requests and approvals, on your behalf.", "isEnabled": true, "type": "Admin", "value": "ConsentRequest.Read.All", "userConsentDisplayName": "Read consent requests"}, {"adminConsentDescription": "Allows the app to read app consent requests and approvals, and deny or approve those requests on behalf of the signed-in user.", "id": "497d9dfa-3bd1-481a-baab-90895e54568c", "adminConsentDisplayName": "Read and write consent requests", "userConsentDescription": "Allows the app to read app consent requests for your approval, and deny or approve those request on your behalf.", "isEnabled": true, "type": "Admin", "value": "ConsentRequest.ReadWrite.All", "userConsentDisplayName": "Read and write consent requests"}, {"adminConsentDescription": "Allows the app to read and write your organization's consent requests policy on behalf of the signed-in user.", "id": "4d135e65-66b8-41a8-9f8b-081452c91774", "adminConsentDisplayName": "Read and write consent request policy", "userConsentDescription": "Allows the app to read and write your organization's consent request policy on your behalf.", "isEnabled": true, "type": "Admin", "value": "Policy.ReadWrite.ConsentRequest", "userConsentDisplayName": "Read and write consent request policy"}, {"adminConsentDescription": "Allows the app to read presence information on behalf of the signed-in user. Presence information includes activity, availability, status note, calendar out-of-office message, timezone and location.", "id": "76bc735e-aecd-4a1d-8b4c-2b915deabb79", "adminConsentDisplayName": "Read user's presence information", "userConsentDescription": "Allows the app to read your presence information on your behalf. Presence information includes activity, availability, status note, calendar out-of-office message, timezone and location.", "isEnabled": true, "type": "User", "value": "Presence.Read", "userConsentDisplayName": "Read your presence information"}, {"adminConsentDescription": "Allows the app to read presence information of all users in the directory on behalf of the signed-in user. Presence information includes activity, availability, status note, calendar out-of-office message, timezone and location.", "id": "9c7a330d-35b3-4aa1-963d-cb2b9f927841", "adminConsentDisplayName": "Read presence information of all users in your organization", "userConsentDescription": "Allows the app to read presence information of all users in the directory on your behalf. Presence information includes activity, availability, status note, calendar out-of-office message, timezone and location.", "isEnabled": true, "type": "User", "value": "Presence.Read.All", "userConsentDisplayName": "Read presence information of all users in your organization"}, {"adminConsentDescription": "Read the members of channels, on behalf of the signed-in user.", "id": "2eadaff8-0bce-4198-a6b9-2cfc35a30075", "adminConsentDisplayName": "Read the members of channels", "userConsentDescription": "Read the members of channels, on your behalf.", "isEnabled": true, "type": "Admin", "value": "ChannelMember.Read.All", "userConsentDisplayName": "Read the members of teams and channels"}, {"adminConsentDescription": "Add and remove members from channels, on behalf of the signed-in user. Also allows changing a member's role, for example from owner to non-owner.", "id": "0c3e411a-ce45-4cd1-8f30-f99a3efa7b11", "adminConsentDisplayName": "Add and remove members from channels", "userConsentDescription": "Add and remove members from channels, on your behalf. Also allows changing a member's role, for example from owner to non-owner.", "isEnabled": true, "type": "Admin", "value": "ChannelMember.ReadWrite.All", "userConsentDisplayName": "Add and remove members from teams and channels"}, {"adminConsentDescription": "Allows the app to read and write the authentication flow policies, on behalf of the signed-in user. ", "id": "edb72de9-4252-4d03-a925-451deef99db7", "adminConsentDisplayName": "Read and write authentication flow policies", "userConsentDescription": "Allows the app to read and write the authentication flow policies for your tenant, on your behalf.", "isEnabled": true, "type": "Admin", "value": "Policy.ReadWrite.AuthenticationFlows", "userConsentDisplayName": "Read and write your authentication flow policies"}, {"adminConsentDescription": "Allows an app to read a channel's messages in Microsoft Teams, on behalf of the signed-in user.", "id": "767156cb-16ae-4d10-8f8b-41b657c8c8c8", "adminConsentDisplayName": "Read user channel messages", "userConsentDescription": "Allows the app to read a channel's messages in Microsoft Teams, on your behalf.", "isEnabled": true, "type": "Admin", "value": "ChannelMessage.Read.All", "userConsentDisplayName": "Read your channel messages"}, {"adminConsentDescription": "Allows the app to read the apps in the app catalogs.", "id": "88e58d74-d3df-44f3-ad47-e89edf4472e4", "adminConsentDisplayName": "Read all app catalogs", "userConsentDescription": "Allows the app to read apps in the app catalogs.", "isEnabled": true, "type": "User", "value": "AppCatalog.Read.All", "userConsentDisplayName": "Read all app catalogs"}, {"adminConsentDescription": "Allows the app to read and write the authentication method policies, on behalf of the signed-in user. ", "id": "7e823077-d88e-468f-a337-e18f1f0e6c7c", "adminConsentDisplayName": "Read and write authentication method policies", "userConsentDescription": "Allows the app to read and write the authentication method policies for your tenant, on your behalf.", "isEnabled": true, "type": "Admin", "value": "Policy.ReadWrite.AuthenticationMethod", "userConsentDisplayName": "Read and write your authentication method policies "}, {"adminConsentDescription": "Allows the app to read and write your organization's authorization policy on behalf of the signed-in user.  For example, authorization policies can control some of the permissions that the out-of-the-box user role has by default.", "id": "edd3c878-b384-41fd-95ad-e7407dd775be", "adminConsentDisplayName": "Read and write your organization's authorization policy", "userConsentDescription": "Allows the app to read and write your organization's authorization policy on your behalf. For example, authorization policies can control some of the permissions that the out-of-the-box user role has by default.", "isEnabled": true, "type": "Admin", "value": "Policy.ReadWrite.Authorization", "userConsentDisplayName": "Read and write your organization's authorization policy"}, {"adminConsentDescription": "Allows the app to read policies related to consent and permission grants for applications, on behalf of the signed-in user.", "id": "414de6ea-2d92-462f-b120-6e2a809a6d01", "adminConsentDisplayName": "Read consent and permission grant policies", "userConsentDescription": "Allows the app to read policies related to consent and permission grants for applications, on your behalf.", "isEnabled": true, "type": "Admin", "value": "Policy.Read.PermissionGrant", "userConsentDisplayName": "Read consent and permission grant policies"}, {"adminConsentDescription": "Allows the app to manage policies related to consent and permission grants for applications, on behalf of the signed-in user.", "id": "2672f8bb-fd5e-42e0-85e1-ec764dd2614e", "adminConsentDisplayName": "Manage consent and permission grant policies", "userConsentDescription": "Allows the app to manage policies related to consent and permission grants for applications, on behalf of the signed-in user.", "isEnabled": true, "type": "Admin", "value": "Policy.ReadWrite.PermissionGrant", "userConsentDisplayName": "Manage consent and permission grant policies"}, {"adminConsentDescription": "Allows the application to create (register) printers on behalf of the signed-in user. ", "id": "90c30bed-6fd1-4279-bf39-714069619721", "adminConsentDisplayName": "Register printers  ", "userConsentDescription": "Allows the application to create (register) printers on your behalf. ", "isEnabled": true, "type": "Admin", "value": "Printer.Create", "userConsentDisplayName": "Register printers  "}, {"adminConsentDescription": "Allows the application to create (register), read, update, and delete (unregister) printers on behalf of the signed-in user. ", "id": "93dae4bd-43a1-4a23-9a1a-92957e1d9121", "adminConsentDisplayName": "Register, read, update, and unregister printers", "userConsentDescription": "Allows the application to create (register), read, update, and delete (unregister) printers on your behalf.  ", "isEnabled": true, "type": "Admin", "value": "Printer.FullControl.All", "userConsentDisplayName": "Register, read, update, and unregister printers"}, {"adminConsentDescription": "Allows the application to read printers on behalf of the signed-in user. ", "id": "3a736c8a-018e-460a-b60c-863b2683e8bf", "adminConsentDisplayName": "Read printers", "userConsentDescription": "Allows the application to read printers on your behalf. ", "isEnabled": true, "type": "Admin", "value": "Printer.Read.All", "userConsentDisplayName": "Read printers"}, {"adminConsentDescription": "Allows the application to read and update printers on behalf of the signed-in user. Does not allow creating (registering) or deleting (unregistering) printers.", "id": "89f66824-725f-4b8f-928e-e1c5258dc565", "adminConsentDisplayName": "Read and update printers", "userConsentDescription": "Allows the application to read and update printers on your behalf. Does not allow creating (registering) or deleting (unregistering) printers.", "isEnabled": true, "type": "Admin", "value": "Printer.ReadWrite.All", "userConsentDisplayName": "Read and update printers"}, {"adminConsentDescription": "Allows the application to read printer shares on behalf of the signed-in user. ", "id": "ed11134d-2f3f-440d-a2e1-411efada2502", "adminConsentDisplayName": "Read printer shares", "userConsentDescription": "Allows the application to read printer shares on your behalf. ", "isEnabled": true, "type": "User", "value": "PrinterShare.Read.All", "userConsentDisplayName": "Read printer shares"}, {"adminConsentDescription": "Allows the application to read and update printer shares on behalf of the signed-in user. ", "id": "06ceea37-85e2-40d7-bec3-91337a46038f", "adminConsentDisplayName": "Read and write printer shares", "userConsentDescription": "Allows the application to read and update printer shares on your behalf. ", "isEnabled": true, "type": "Admin", "value": "PrinterShare.ReadWrite.All", "userConsentDisplayName": "Read and update printer shares"}, {"adminConsentDescription": "Allows the application to read the metadata and document content of print jobs that the signed-in user created.", "id": "248f5528-65c0-4c88-8326-876c7236df5e", "adminConsentDisplayName": "Read user's print jobs", "userConsentDescription": "Allows the application to read the metadata and document content of print jobs that you created.", "isEnabled": true, "type": "User", "value": "PrintJob<PERSON>", "userConsentDisplayName": "Read your print jobs"}, {"adminConsentDescription": "Allows the application to read the metadata and document content of print jobs on behalf of the signed-in user. ", "id": "afdd6933-a0d8-40f7-bd1a-b5d778e8624b", "adminConsentDisplayName": "Read print jobs", "userConsentDescription": "Allows the application to read the metadata and document content of print jobs on your behalf. ", "isEnabled": true, "type": "Admin", "value": "PrintJob.Read.All", "userConsentDisplayName": "Read print jobs"}, {"adminConsentDescription": "Allows the application to read the metadata of print jobs that the signed-in user created. Does not allow access to print job document content.", "id": "6a71a747-280f-4670-9ca0-a9cbf882b274", "adminConsentDisplayName": "Read basic information of user's print jobs", "userConsentDescription": "Allows the application to read the metadata of print jobs that you created. Does not allow access to print job document content.", "isEnabled": true, "type": "User", "value": "PrintJob.ReadBasic", "userConsentDisplayName": "Read basic information of your print jobs"}, {"adminConsentDescription": "Allows the application to read the metadata of print jobs on behalf of the signed-in user. Does not allow access to print job document content.", "id": "04ce8d60-72ce-4867-85cf-6d82f36922f3", "adminConsentDisplayName": "Read basic information of print jobs", "userConsentDescription": "Allows the application to read the metadata of print jobs on your behalf. Does not allow access to print job document content.", "isEnabled": true, "type": "Admin", "value": "PrintJob.ReadBasic.All", "userConsentDisplayName": "Read basic information of print jobs"}, {"adminConsentDescription": "Allows the application to read and update the metadata and document content of print jobs that the signed-in user created.", "id": "b81dd597-8abb-4b3f-a07a-820b0316ed04", "adminConsentDisplayName": "Read and write user's print jobs", "userConsentDescription": "Allows the application to read and update the metadata and document content of print jobs that you created.", "isEnabled": true, "type": "User", "value": "PrintJob.ReadWrite", "userConsentDisplayName": "Read and update your print jobs"}, {"adminConsentDescription": "Allows the application to read and update the metadata and document content of print jobs on behalf of the signed-in user. ", "id": "036b9544-e8c5-46ef-900a-0646cc42b271", "adminConsentDisplayName": "Read and write print jobs", "userConsentDescription": "Allows the application to read and update the metadata and document content of print jobs on your behalf. ", "isEnabled": true, "type": "Admin", "value": "PrintJob.ReadWrite.All", "userConsentDisplayName": "Read and update print jobs"}, {"adminConsentDescription": "Allows the application to read and update the metadata of print jobs that the signed-in user created. Does not allow access to print job document content.", "id": "6f2d22f2-1cb6-412c-a17c-3336817eaa82", "adminConsentDisplayName": "Read and write basic information of user's print jobs", "userConsentDescription": "Allows the application to read and update the metadata of print jobs that you created. Does not allow access to print job document content.", "isEnabled": true, "type": "User", "value": "PrintJob.ReadWriteBasic", "userConsentDisplayName": "Read and write basic information of your print jobs"}, {"adminConsentDescription": "Allows the application to read and update the metadata of print jobs on behalf of the signed-in user. Does not allow access to print job document content.", "id": "3a0db2f6-0d2a-4c19-971b-49109b19ad3d", "adminConsentDisplayName": "Read and write basic information of print jobs", "userConsentDescription": "Allows the application to read and update the metadata of print jobs on your behalf. Does not allow access to print job document content.", "isEnabled": true, "type": "Admin", "value": "PrintJob.ReadWriteBasic.All", "userConsentDisplayName": "Read and write basic information of print jobs"}, {"adminConsentDescription": "Allows the app to read and write your organization's device configuration policies on behalf of the signed-in user.  For example, device registration policy can limit initial provisioning controls using quota restrictions, additional authentication and authorization checks.", "id": "40b534c3-9552-4550-901b-23879c90bcf9", "adminConsentDisplayName": "Read and write your organization's device configuration policies", "userConsentDescription": "Allows the app to read and write your organization's device configuration policies on your behalf.  For example, device registration policy can limit initial provisioning controls using quota restrictions, additional authentication and authorization checks.", "isEnabled": true, "type": "Admin", "value": "Policy.ReadWrite.DeviceConfiguration", "userConsentDisplayName": "Read and write your organization's device configuration policies"}, {"adminConsentDescription": "Allows the app to submit application packages to the catalog and cancel submissions that are pending review on behalf of the signed-in user.", "id": "3db89e36-7fa6-4012-b281-85f3d9d9fd2e", "adminConsentDisplayName": "Submit application packages to the catalog and cancel pending submissions", "userConsentDescription": "Allows the app to submit application packages to the catalog and cancel submissions that are pending review on your behalf.", "isEnabled": true, "type": "User", "value": "AppCatalog.Submit", "userConsentDisplayName": "Submit application packages to your organization's catalog and cancel pending submissions"}, {"adminConsentDescription": "Allows the app to read the Teams apps that are installed in chats the signed-in user can access. Does not give the ability to read application-specific settings.", "id": "bf3fbf03-f35f-4e93-963e-47e4d874c37a", "adminConsentDisplayName": "Read installed Teams apps in chats", "userConsentDescription": "Allows the app to read the Teams apps that are installed in chats that you can access. Does not give the ability to read application-specific settings.", "isEnabled": true, "type": "User", "value": "TeamsAppInstallation.ReadForChat", "userConsentDisplayName": "Read installed Teams apps in chats"}, {"adminConsentDescription": "Allows the app to read the Teams apps that are installed in teams the signed-in user can access. Does not give the ability to read application-specific settings.", "id": "5248dcb1-f83b-4ec3-9f4d-a4428a961a72", "adminConsentDisplayName": "Read installed Teams apps in teams", "userConsentDescription": "Allows the app to read the Teams apps that are installed in teams that you can access. Does not give the ability to read application-specific settings.", "isEnabled": true, "type": "Admin", "value": "TeamsAppInstallation.ReadForTeam", "userConsentDisplayName": "Read installed Teams apps in teams"}, {"adminConsentDescription": "Allows the app to read the Teams apps that are installed for the signed-in user. Does not give the ability to read application-specific settings.", "id": "c395395c-ff9a-4dba-bc1f-8372ba9dca84", "adminConsentDisplayName": "Read user's installed Teams apps", "userConsentDescription": "Allows the app to read the Teams apps that are installed for you. Does not give the ability to read application-specific settings.", "isEnabled": true, "type": "User", "value": "TeamsAppInstallation.ReadForUser", "userConsentDisplayName": "Read your installed Teams apps"}, {"adminConsentDescription": "Allows the app to read, install, upgrade, and uninstall Teams apps in teams the signed-in user can access. Does not give the ability to read application-specific settings.", "id": "2e25a044-2580-450d-8859-42eeb6e996c0", "adminConsentDisplayName": "Manage installed Teams apps in teams", "userConsentDescription": "Allows the app to read, install, upgrade, and uninstall Teams apps in teams you can access. Does not give the ability to read application-specific settings.", "isEnabled": true, "type": "Admin", "value": "TeamsAppInstallation.ReadWriteForTeam", "userConsentDisplayName": "Manage installed Teams apps in teams"}, {"adminConsentDescription": "Allows a Teams app to read, install, upgrade, and uninstall itself in chats the signed-in user can access.", "id": "0ce33576-30e8-43b7-99e5-62f8569a4002", "adminConsentDisplayName": "Allow the Teams app to manage itself in chats", "userConsentDescription": "Allows a Teams app to read, install, upgrade, and uninstall itself in chats you can access.", "isEnabled": true, "type": "Admin", "value": "TeamsAppInstallation.ReadWriteSelfForChat", "userConsentDisplayName": "Allow the Teams app to manage itself in chats"}, {"adminConsentDescription": "Allows a Teams app to read, install, upgrade, and uninstall itself to teams the signed-in user can access.", "id": "0f4595f7-64b1-4e13-81bc-11a249df07a9", "adminConsentDisplayName": "Allow the app to manage itself in teams", "userConsentDescription": "Allows a Teams app to read, install, upgrade, and uninstall itself to teams you can access.", "isEnabled": true, "type": "Admin", "value": "TeamsAppInstallation.ReadWriteSelfForTeam", "userConsentDisplayName": "Allow the app to manage itself in teams"}, {"adminConsentDescription": "Allows a Teams app to read, install, upgrade, and uninstall itself for the signed-in user.", "id": "207e0cb1-3ce7-4922-b991-5a760c346ebc", "adminConsentDisplayName": "Allow the Teams app to manage itself for a user", "userConsentDescription": "Allows a Teams app to read, install, upgrade, and uninstall itself for you.", "isEnabled": true, "type": "User", "value": "TeamsAppInstallation.ReadWriteSelfForUser", "userConsentDisplayName": "Allow the Teams app to manage itself for you"}, {"adminConsentDescription": "Allows the app to read, install, upgrade, and uninstall Teams apps installed for the signed-in user. Does not give the ability to read application-specific settings.", "id": "093f8818-d05f-49b8-95bc-9d2a73e9a43c", "adminConsentDisplayName": "Manage user's installed Teams apps", "userConsentDescription": "Allows the app to read, install, upgrade, and uninstall Teams apps installed for you. Does not give the ability to read application-specific settings.", "isEnabled": true, "type": "Admin", "value": "TeamsAppInstallation.ReadWriteForUser", "userConsentDisplayName": "Manage your installed Teams apps"}, {"adminConsentDescription": "Allows the app to create teams on behalf of the signed-in user.", "id": "7825d5d6-6049-4ce7-bdf6-3b8d53f4bcd0", "adminConsentDisplayName": "Create teams", "userConsentDescription": "Allows the app to create teams on your behalf. ", "isEnabled": true, "type": "User", "value": "Team.Create", "userConsentDisplayName": "Create teams"}, {"adminConsentDescription": "Add and remove members from all teams, on behalf of the signed-in user. Does not allow adding or removing a member with the owner role. Additionally, does not allow the app to elevate an existing member to the owner role.", "id": "2104a4db-3a2f-4ea0-9dba-143d457dc666", "adminConsentDisplayName": "Add and remove members with non-owner role for all teams", "userConsentDescription": "Add and remove members from all teams, on your behalf. Does not allow adding or removing a member with the owner role. Additionally, does not allow the app to elevate an existing member to the owner role.", "isEnabled": true, "type": "Admin", "value": "TeamMember.ReadWriteNonOwnerRole.All", "userConsentDisplayName": "Add and remove members with non-owner role for all teams"}, {"adminConsentDescription": "Allows the app to read the term store data that the signed-in user has access to. This includes all sets, groups and terms in the term store.", "id": "297f747b-0005-475b-8fef-c890f5152b38", "adminConsentDisplayName": "Read term store data", "userConsentDescription": "Allows the app to read the term store data that you have access to. This includes all sets, groups and terms in the term store.", "isEnabled": true, "type": "Admin", "value": "TermStore.Read.All", "userConsentDisplayName": "Read term store data"}, {"adminConsentDescription": "Allows the app to read or modify data that the signed-in user has access to. This includes all sets, groups and terms in the term store.", "id": "6c37c71d-f50f-4bff-8fd3-8a41da390140", "adminConsentDisplayName": "Read and write term store data", "userConsentDescription": "Allows the app to read or modify data that you have access to. This includes all sets, groups and terms in the term store.", "isEnabled": true, "type": "Admin", "value": "TermStore.ReadWrite.All", "userConsentDisplayName": "Read and write term store data"}, {"adminConsentDescription": "Allows the app to read your tenant's service announcement messages on behalf of the signed-in user. Messages may include information about new or changed features.", "id": "eda39fa6-f8cf-4c3c-a909-432c683e4c9b", "adminConsentDisplayName": "Read service announcement messages", "userConsentDescription": "Allows the app to read your tenant's service announcement messages on your behalf. Messages may include information about new or changed features.", "isEnabled": true, "type": "Admin", "value": "ServiceMessage.Read.All", "userConsentDisplayName": "Read service messages"}, {"adminConsentDescription": "Allows the app to read your tenant's service health information on behalf of the signed-in user. Health information may include service issues or service health overviews.", "id": "55896846-df78-47a7-aa94-8d3d4442ca7f", "adminConsentDisplayName": "Read service health", "userConsentDescription": "Allows the app to read your tenant's service health information on your behalf.Health information may include service issues or service health overviews.", "isEnabled": true, "type": "Admin", "value": "ServiceHealth.Read.All", "userConsentDisplayName": "Read service health"}, {"adminConsentDescription": "Allows the app to read all the short notes a sign-in user has access to.", "id": "50f66e47-eb56-45b7-aaa2-75057d9afe08", "adminConsentDisplayName": "Read short notes of the signed-in user", "userConsentDescription": "Allows the app to read your short notes.", "isEnabled": true, "type": "User", "value": "ShortNotes.Read", "userConsentDisplayName": "Read your short notes"}, {"adminConsentDescription": "Allows the app to read, create, edit, and delete short notes of a signed-in user.", "id": "328438b7-4c01-4c07-a840-e625a749bb89", "adminConsentDisplayName": "Read, create, edit, and delete short notes of the signed-in user", "userConsentDescription": "Allows the app to read, create, edit, and delete your short notes.", "isEnabled": true, "type": "User", "value": "ShortNotes.ReadWrite", "userConsentDisplayName": "Read, create, edit, and delete your short notes"}, {"adminConsentDescription": "Allows the app to read your organization's conditional access policies on behalf of the signed-in user.", "id": "633e0fce-8c58-4cfb-9495-12bbd5a24f7c", "adminConsentDisplayName": "Read your organization's conditional access policies", "userConsentDescription": "Allows the app to read your organization's conditional access policies on your behalf.", "isEnabled": true, "type": "User", "value": "Policy.Read.ConditionalAccess", "userConsentDisplayName": "Read your organization's conditional access policies"}, {"adminConsentDescription": "Allows the app to read the role-based access control (RBAC) settings for all RBAC providers, on behalf of the signed-in user.  This includes reading role definitions and role assignments.", "id": "48fec646-b2ba-4019-8681-8eb31435aded", "adminConsentDisplayName": "Read role management data for all RBAC providers", "userConsentDescription": "Allows the app to read the role-based access control (RBAC) settings for all RBAC providers, on your behalf.  This includes reading role definitions and role assignments.", "isEnabled": true, "type": "Admin", "value": "RoleManagement.Read.All", "userConsentDisplayName": "Read role management data for all RBAC providers"}, {"adminConsentDescription": "Allows an app to send one-to-one and group chat messages in Microsoft Teams, on behalf of the signed-in user.", "id": "116b7235-7cc6-461e-b163-8e55691d839e", "adminConsentDisplayName": "Send user chat messages", "userConsentDescription": "Allows an app to send one-to-one and group chat messages in Microsoft Teams, on your behalf.", "isEnabled": true, "type": "User", "value": "ChatMessage.Send", "userConsentDisplayName": "Send chat messages"}, {"adminConsentDescription": "Allows an app to read the members and descriptions of one-to-one and group chat threads, on behalf of the signed-in user.", "id": "9547fcb5-d03f-419d-9948-5928bbf71b0f", "adminConsentDisplayName": "Read names and members of user chat threads", "userConsentDescription": "Allows an app to read the members and descriptions of one-to-one and group chat threads, on your behalf.", "isEnabled": true, "type": "User", "value": "<PERSON><PERSON><PERSON>", "userConsentDisplayName": "Read names and members of your chat threads"}, {"adminConsentDescription": "Allows the app to read and write the properties of Cloud PCs on behalf of the signed-in user.", "id": "9d77138f-f0e2-47ba-ab33-cd246c8b79d1", "adminConsentDisplayName": "Read and write Cloud PCs", "userConsentDescription": "Allows the app to read and write the properties of Cloud PCs, on your behalf.", "isEnabled": true, "type": "Admin", "value": "CloudPC.ReadWrite.All", "userConsentDisplayName": "Read and write Cloud PCs"}, {"adminConsentDescription": "Allows the app to read the properties of Cloud PCs on behalf of the signed-in user.", "id": "5252ec4e-fd40-4d92-8c68-89dd1d3c6110", "adminConsentDisplayName": "Read Cloud PCs", "userConsentDescription": "Allows the app to read the properties of Cloud PCs, on your behalf.", "isEnabled": true, "type": "User", "value": "CloudPC.Read.All", "userConsentDisplayName": "Read Cloud PCs"}, {"adminConsentDescription": "Allows the app to read, install, upgrade, and uninstall Teams apps in chats the signed-in user can access. Does not give the ability to read application-specific settings.", "id": "aa85bf13-d771-4d5d-a9e6-bca04ce44edf", "adminConsentDisplayName": "Manage installed Teams apps in chats", "userConsentDescription": "Allows the app to read, install, upgrade, and uninstall Teams apps in chats you can access. Does not give the ability to read application-specific settings.", "isEnabled": true, "type": "Admin", "value": "TeamsAppInstallation.ReadWriteForChat", "userConsentDisplayName": "Manage installed Teams apps in chats"}, {"adminConsentDescription": "Allows the app to create, read, update, and delete the signed-in user's tasks and task lists, including any shared with the user.", "id": "2219042f-cab5-40cc-b0d2-16b1540b4c5f", "adminConsentDisplayName": "Create, read, update, and delete user’s tasks and task lists", "userConsentDescription": "Allows the app to create, read, update, and delete your tasks and task lists, including any shared with you.", "isEnabled": true, "type": "User", "value": "Tasks.ReadWrite", "userConsentDisplayName": "Create, read, update, and delete your tasks and task lists"}, {"adminConsentDescription": "Allows the app to read the signed-in user’s tasks and task lists, including any shared with the user. Doesn't include permission to create, delete, or update anything.", "id": "f45671fb-e0fe-4b4b-be20-3d3ce43f1bcb", "adminConsentDisplayName": "Read user's tasks and task lists", "userConsentDescription": "Allows the app to read your tasks and task lists, including any shared with you. Doesn't include permission to create, delete, or update anything.", "isEnabled": true, "type": "User", "value": "Tasks.Read", "userConsentDisplayName": "Read your tasks and task lists"}, {"adminConsentDescription": "Allows an app to read one-to-one and group chat messages, on behalf of the signed-in user.", "id": "cdcdac3a-fd45-410d-83ef-554db620e5c7", "adminConsentDisplayName": "Read user chat messages", "userConsentDescription": "Allows an app to read one-to-one or group chat messages in Microsoft Teams, on your behalf.", "isEnabled": true, "type": "User", "value": "ChatMessage.Read", "userConsentDisplayName": "Read user chat messages"}, {"adminConsentDescription": "Allows a Teams app to read, install, upgrade, and uninstall all tabs in chats the signed-in user can access.", "id": "ee928332-e9c2-4747-b4a0-f8c164b68de6", "adminConsentDisplayName": "Allow the Teams app to manage all tabs in chats", "userConsentDescription": "Allows a Teams app to read, install, upgrade, and uninstall all tabs in chats you can access.", "isEnabled": true, "type": "Admin", "value": "TeamsTab.ReadWriteForChat", "userConsentDisplayName": "Allow the Teams app to manage all tabs in chats"}, {"adminConsentDescription": "Allows a Teams app to read, install, upgrade, and uninstall all tabs to teams the signed-in user can access.", "id": "c975dd04-a06e-4fbb-9704-62daad77bb49", "adminConsentDisplayName": "Allow the Teams app to manage all tabs in teams", "userConsentDescription": "Allows a Teams app to read, install, upgrade, and uninstall all tabs to teams you can access.", "isEnabled": true, "type": "Admin", "value": "TeamsTab.ReadWriteForTeam", "userConsentDisplayName": "Allow the app to manage all tabs in teams"}, {"adminConsentDescription": "Allows a Teams app to read, install, upgrade, and uninstall all tabs for the signed-in user.", "id": "c37c9b61-7762-4bff-a156-afc0005847a0", "adminConsentDisplayName": "Allow the Teams app to manage all tabs for a user", "userConsentDescription": "Allows a Teams app to read, install, upgrade, and uninstall all tabs for you.", "isEnabled": true, "type": "User", "value": "TeamsTab.ReadWriteForUser", "userConsentDisplayName": "Allow the Teams app to manage all tabs for you"}, {"adminConsentDescription": "Allows the app to read the API connectors used in user authentication flows, on behalf of the signed-in user.", "id": "1b6ff35f-31df-4332-8571-d31ea5a4893f", "adminConsentDisplayName": "Read API connectors for authentication flows", "userConsentDescription": "Allows the app to read the API connectors used in user authentication flows, on your behalf.", "isEnabled": true, "type": "Admin", "value": "APIConnectors.Read.All", "userConsentDisplayName": "Read API connectors for authentication flows"}, {"adminConsentDescription": "Allows the app to read, create and manage the API connectors used in user authentication flows, on behalf of the signed-in user.", "id": "c67b52c5-7c69-48b6-9d48-7b3af3ded914", "adminConsentDisplayName": "Read and write API connectors for authentication flows", "userConsentDescription": "Allows the app to read, create and manage the API connectors used in user authentication flows, on your behalf.", "isEnabled": true, "type": "Admin", "value": "APIConnectors.ReadWrite.All", "userConsentDisplayName": "Read and write API connectors for authentication flows"}, {"adminConsentDescription": "Read the members of chats, on behalf of the signed-in user.", "id": "c5a9e2b1-faf6-41d4-8875-d381aa549b24", "adminConsentDisplayName": "Read the members of chats", "userConsentDescription": "Read the members of chats, on your behalf.", "isEnabled": true, "type": "Admin", "value": "ChatMember.Read", "userConsentDisplayName": "Read the members of chats"}, {"adminConsentDescription": "Add and remove members from chats, on behalf of the signed-in user.", "id": "dea13482-7ea6-488f-8b98-eb5bbecf033d", "adminConsentDisplayName": "Add and remove members from chats", "userConsentDescription": "Add and remove members from chats, on your behalf.", "isEnabled": true, "type": "Admin", "value": "ChatMember.ReadWrite", "userConsentDisplayName": "Add and remove members from chats"}, {"adminConsentDescription": "Allows the app to create chats on behalf of the signed-in user.", "id": "38826093-1258-4dea-98f0-00003be2b8d0", "adminConsentDisplayName": "Create chats", "userConsentDescription": "Allows the app to create chats on your behalf. ", "isEnabled": true, "type": "User", "value": "Chat.Create", "userConsentDisplayName": "Create chats"}, {"adminConsentDescription": "Allows the application to read and write tenant-wide print settings on behalf of the signed-in user.", "id": "9ccc526a-c51c-4e5c-a1fd-74726ef50b8f", "adminConsentDisplayName": "Read and write tenant-wide print settings", "userConsentDescription": "Allows the application to read and write tenant-wide print settings on your behalf.", "isEnabled": true, "type": "Admin", "value": "PrintSettings.ReadWrite.All", "userConsentDisplayName": "Read and write tenant-wide print settings"}, {"adminConsentDescription": "Allows the application to read tenant-wide print settings on behalf of the signed-in user.", "id": "490f32fd-d90f-4dd7-a601-ff6cdc1a3f6c", "adminConsentDisplayName": "Read tenant-wide print settings", "userConsentDescription": "Allows the application to read tenant-wide print settings on your behalf.", "isEnabled": true, "type": "Admin", "value": "PrintSettings.Read.All", "userConsentDisplayName": "Read tenant-wide print settings"}, {"adminConsentDescription": "Allows the application to read and write print connectors on behalf of the signed-in user. ", "id": "79ef9967-7d59-4213-9c64-4b10687637d8", "adminConsentDisplayName": "Read and write print connectors", "userConsentDescription": "Allows the application to read and write print connectors on your behalf.", "isEnabled": true, "type": "Admin", "value": "PrintConnector.ReadWrite.All", "userConsentDisplayName": "Read and write print connectors"}, {"adminConsentDescription": "Allows the application to read print connectors on behalf of the signed-in user.", "id": "d69c2d6d-4f72-4f99-a6b9-663e32f8cf68", "adminConsentDisplayName": "Read print connectors", "userConsentDescription": "Allows the application to read print connectors on your behalf.", "isEnabled": true, "type": "Admin", "value": "PrintConnector.Read.All", "userConsentDisplayName": "Read print connectors"}, {"adminConsentDescription": "Allows the application to read basic information about printer shares on behalf of the signed-in user. Does not allow reading access control information.", "id": "5fa075e9-b951-4165-947b-c63396ff0a37", "adminConsentDisplayName": "Read basic information about printer shares", "userConsentDescription": "Allows the application to read basic information about printer shares on your behalf.", "isEnabled": true, "type": "User", "value": "PrinterShare.ReadBasic.All", "userConsentDisplayName": "Read basic information about printer shares"}, {"adminConsentDescription": "Allows the application to create print jobs on behalf of the signed-in user and upload document content to print jobs that the signed-in user created.", "id": "21f0d9c0-9f13-48b3-94e0-b6b231c7d320", "adminConsentDisplayName": "Create print jobs", "userConsentDescription": "Allows the application to create print jobs on your behalf and upload document content to print jobs  that you created.", "isEnabled": true, "type": "User", "value": "PrintJob.Create", "userConsentDisplayName": "Create your print jobs"}], "keyCredentials": [], "appDescription": null, "tokenEncryptionKeyId": null}