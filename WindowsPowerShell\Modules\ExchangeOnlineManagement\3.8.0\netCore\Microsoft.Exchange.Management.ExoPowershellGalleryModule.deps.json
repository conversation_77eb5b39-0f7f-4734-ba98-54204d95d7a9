{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"Microsoft.Exchange.Management.ExoPowershellGalleryModule/1.0.0": {"dependencies": {"BldSync": "1.0.3", "CodeSign.Exchange": "1.3.24", "Microsoft.CodeAnalysis.FxCopAnalyzers": "3.3.2", "Microsoft.Exchange.Management.AdminApiProvider": "0.0.0", "Microsoft.Exchange.Management.RestApiClient": "0.0.0", "Microsoft.Internal.Analyzers": "2.9.7", "Microsoft.NETFramework.ReferenceAssemblies": "1.0.3", "Microsoft.Online.CSE.RestApiPowerShellModule.Instrumentation": "0.0.0", "MsBuildRetail.Exchange": "2.0.0", "MsBuild.Exchange": "18.0.0", "NSubstitute.Analyzers.CSharp": "1.0.17", "Newtonsoft.Json": "13.0.3", "OSS.RoslynAnalysis": "1.4.10", "ReferenceTrimmer": "3.3.10", "RoslynCQ": "1.34.2", "Microsoft.Exchange.Management.AdminApiProvider.Reference": "********", "Microsoft.Exchange.Management.RestApiClient.Reference": "********", "Microsoft.Online.CSE.RestApiPowerShellModule.Instrumentation.Reference": "********"}, "runtime": {"Microsoft.Exchange.Management.ExoPowershellGalleryModule.dll": {}}}, "Antlr4/4.6.4": {"dependencies": {"Antlr4.CodeGenerator": "4.6.4", "Antlr4.Runtime": "4.6.4"}}, "Antlr4.CodeGenerator/4.6.4": {}, "Antlr4.Runtime/4.6.4": {"dependencies": {"NETStandard.Library": "1.6.1"}, "runtime": {"lib/netstandard1.1/Antlr4.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Azure.Core/1.19.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "System.Buffers": "4.5.1", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Memory": "4.5.4", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.6.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net5.0/Azure.Core.dll": {"assemblyVersion": "********", "fileVersion": "1.1900.21.45105"}}}, "BldSync/1.0.3": {}, "CodeSign.Exchange/1.3.24": {}, "Microsoft.Azure.Cosmos/3.37.0": {"dependencies": {"Azure.Core": "1.19.0", "Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.Bcl.HashCode": "1.1.0", "Newtonsoft.Json": "13.0.3", "System.Buffers": "4.5.1", "System.Collections.Immutable": "1.7.0", "System.Configuration.ConfigurationManager": "7.0.0", "System.Memory": "4.5.4", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Threading.Tasks.Extensions": "4.5.4", "System.ValueTuple": "4.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Azure.Cosmos.Client.dll": {"assemblyVersion": "3.37.0.0", "fileVersion": "3.37.0.0"}, "lib/netstandard2.0/Microsoft.Azure.Cosmos.Core.dll": {"assemblyVersion": "2.11.0.0", "fileVersion": "2.11.0.0"}, "lib/netstandard2.0/Microsoft.Azure.Cosmos.Direct.dll": {"assemblyVersion": "3.31.5.0", "fileVersion": "3.31.5.0"}, "lib/netstandard2.0/Microsoft.Azure.Cosmos.Serialization.HybridRow.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.1.0.0"}}, "runtimeTargets": {"runtimes/win-x64/native/Cosmos.CRTCompat.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "2.0.0.0"}, "runtimes/win-x64/native/Microsoft.Azure.Cosmos.ServiceInterop.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "2.14.0.0"}, "runtimes/win-x64/native/msvcp140.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "14.34.31931.0"}, "runtimes/win-x64/native/vcruntime140.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "14.34.31931.0"}, "runtimes/win-x64/native/vcruntime140_1.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "14.34.31931.0"}}}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Bcl.HashCode/1.1.0": {"runtime": {"lib/netcoreapp2.1/Microsoft.Bcl.HashCode.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "4.700.19.56404"}}}, "Microsoft.CodeAnalysis.FxCopAnalyzers/3.3.2": {"dependencies": {"Microsoft.CodeAnalysis.VersionCheckAnalyzer": "3.3.2", "Microsoft.CodeQuality.Analyzers": "3.3.2", "Microsoft.NetCore.Analyzers": "3.3.2", "Microsoft.NetFramework.Analyzers": "3.3.2"}}, "Microsoft.CodeAnalysis.VersionCheckAnalyzer/3.3.2": {}, "Microsoft.CodeQuality.Analyzers/3.3.2": {}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.Extensions.ObjectPool/6.0.3": {"runtime": {"lib/net6.0/Microsoft.Extensions.ObjectPool.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.322.12401"}}}, "Microsoft.Identity.Client/4.66.1": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.0.1", "System.Diagnostics.DiagnosticSource": "6.0.1"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.66.1.0", "fileVersion": "4.66.1.0"}}}, "Microsoft.Identity.Client.Broker/4.61.3": {"dependencies": {"Microsoft.Identity.Client": "4.66.1", "Microsoft.Identity.Client.NativeInterop": "0.16.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Identity.Client.Broker.dll": {"assemblyVersion": "4.61.3.0", "fileVersion": "4.61.3.0"}}}, "Microsoft.Identity.Client.NativeInterop/0.16.1": {"runtime": {"lib/netstandard2.0/Microsoft.Identity.Client.NativeInterop.dll": {"assemblyVersion": "0.16.1.0", "fileVersion": "0.16.1.0"}}, "runtimeTargets": {"runtimes/win-arm64/native/msalruntime_arm64.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/msalruntime.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/msalruntime_x86.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "Microsoft.IdentityModel.Abstractions/8.0.1": {"runtime": {"lib/net8.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "8.0.1.0", "fileVersion": "8.0.1.50722"}}}, "Microsoft.IdentityModel.Authorization/13.10.8897": {"dependencies": {"Antlr4": "4.6.4", "Antlr4.Runtime": "4.6.4", "Newtonsoft.Json": "13.0.3", "System.Memory": "4.5.4", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Authorization.dll": {"assemblyVersion": "13.10.0.0", "fileVersion": "13.10.8897.0"}}}, "Microsoft.IdentityModel.JsonWebTokens/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.0.1"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "8.0.1.0", "fileVersion": "8.0.1.50722"}}}, "Microsoft.IdentityModel.Logging/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.0.1"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "8.0.1.0", "fileVersion": "8.0.1.50722"}}}, "Microsoft.IdentityModel.Tokens/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Logging": "8.0.1"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "8.0.1.0", "fileVersion": "8.0.1.50722"}}}, "Microsoft.Internal.Analyzers/2.9.7": {}, "Microsoft.Management.Infrastructure/1.0.0": {"dependencies": {"NETStandard.Library": "1.6.1", "System.Runtime.CompilerServices.VisualC": "4.3.0", "System.Runtime.Serialization.Xml": "4.3.0", "System.Security.SecureString": "4.3.0", "System.Threading.ThreadPool": "4.3.0"}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.6/Microsoft.Management.Infrastructure.Native.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}, "runtimes/unix/lib/netstandard1.6/Microsoft.Management.Infrastructure.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "1.0.0.0", "fileVersion": "10.0.10011.16384"}, "runtimes/win-arm/lib/netstandard1.6/Microsoft.Management.Infrastructure.Native.dll": {"rid": "win-arm", "assetType": "runtime", "assemblyVersion": "1.0.0.0", "fileVersion": "10.0.16299.15"}, "runtimes/win-arm/lib/netstandard1.6/Microsoft.Management.Infrastructure.dll": {"rid": "win-arm", "assetType": "runtime", "assemblyVersion": "1.0.0.0", "fileVersion": "10.0.16299.15"}, "runtimes/win-arm64/lib/netstandard1.6/Microsoft.Management.Infrastructure.Native.dll": {"rid": "win-arm64", "assetType": "runtime", "assemblyVersion": "1.0.0.0", "fileVersion": "10.0.16299.15"}, "runtimes/win-arm64/lib/netstandard1.6/Microsoft.Management.Infrastructure.dll": {"rid": "win-arm64", "assetType": "runtime", "assemblyVersion": "1.0.0.0", "fileVersion": "10.0.16299.15"}, "runtimes/win10-x64/lib/netstandard1.6/Microsoft.Management.Infrastructure.Native.dll": {"rid": "win10-x64", "assetType": "runtime", "assemblyVersion": "1.0.0.0", "fileVersion": "10.0.16299.15"}, "runtimes/win10-x64/lib/netstandard1.6/Microsoft.Management.Infrastructure.dll": {"rid": "win10-x64", "assetType": "runtime", "assemblyVersion": "1.0.0.0", "fileVersion": "10.0.16299.15"}, "runtimes/win10-x86/lib/netstandard1.6/Microsoft.Management.Infrastructure.Native.dll": {"rid": "win10-x86", "assetType": "runtime", "assemblyVersion": "1.0.0.0", "fileVersion": "10.0.16299.15"}, "runtimes/win10-x86/lib/netstandard1.6/Microsoft.Management.Infrastructure.dll": {"rid": "win10-x86", "assetType": "runtime", "assemblyVersion": "1.0.0.0", "fileVersion": "10.0.16299.15"}, "runtimes/win7-x64/lib/netstandard1.6/Microsoft.Management.Infrastructure.Native.dll": {"rid": "win7-x64", "assetType": "runtime", "assemblyVersion": "1.0.0.0", "fileVersion": "10.0.16299.15"}, "runtimes/win7-x64/lib/netstandard1.6/Microsoft.Management.Infrastructure.dll": {"rid": "win7-x64", "assetType": "runtime", "assemblyVersion": "1.0.0.0", "fileVersion": "10.0.16299.15"}, "runtimes/win7-x86/lib/netstandard1.6/Microsoft.Management.Infrastructure.Native.dll": {"rid": "win7-x86", "assetType": "runtime", "assemblyVersion": "1.0.0.0", "fileVersion": "10.0.16299.15"}, "runtimes/win7-x86/lib/netstandard1.6/Microsoft.Management.Infrastructure.dll": {"rid": "win7-x86", "assetType": "runtime", "assemblyVersion": "1.0.0.0", "fileVersion": "10.0.16299.15"}, "runtimes/win8-x64/lib/netstandard1.6/Microsoft.Management.Infrastructure.Native.dll": {"rid": "win8-x64", "assetType": "runtime", "assemblyVersion": "1.0.0.0", "fileVersion": "10.0.16299.15"}, "runtimes/win8-x64/lib/netstandard1.6/Microsoft.Management.Infrastructure.dll": {"rid": "win8-x64", "assetType": "runtime", "assemblyVersion": "1.0.0.0", "fileVersion": "10.0.16299.15"}, "runtimes/win8-x86/lib/netstandard1.6/Microsoft.Management.Infrastructure.Native.dll": {"rid": "win8-x86", "assetType": "runtime", "assemblyVersion": "1.0.0.0", "fileVersion": "10.0.16299.15"}, "runtimes/win8-x86/lib/netstandard1.6/Microsoft.Management.Infrastructure.dll": {"rid": "win8-x86", "assetType": "runtime", "assemblyVersion": "1.0.0.0", "fileVersion": "10.0.16299.15"}, "runtimes/win81-x64/lib/netstandard1.6/Microsoft.Management.Infrastructure.Native.dll": {"rid": "win81-x64", "assetType": "runtime", "assemblyVersion": "1.0.0.0", "fileVersion": "10.0.16299.15"}, "runtimes/win81-x64/lib/netstandard1.6/Microsoft.Management.Infrastructure.dll": {"rid": "win81-x64", "assetType": "runtime", "assemblyVersion": "1.0.0.0", "fileVersion": "10.0.16299.15"}, "runtimes/win81-x86/lib/netstandard1.6/Microsoft.Management.Infrastructure.Native.dll": {"rid": "win81-x86", "assetType": "runtime", "assemblyVersion": "1.0.0.0", "fileVersion": "10.0.16299.15"}, "runtimes/win81-x86/lib/netstandard1.6/Microsoft.Management.Infrastructure.dll": {"rid": "win81-x86", "assetType": "runtime", "assemblyVersion": "1.0.0.0", "fileVersion": "10.0.16299.15"}, "runtimes/win-arm/native/Microsoft.Management.Infrastructure.Native.Unmanaged.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "10.0.16299.15"}, "runtimes/win-arm/native/mi.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "10.0.16299.15"}, "runtimes/win-arm/native/miutils.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "10.0.16299.15"}, "runtimes/win-arm64/native/Microsoft.Management.Infrastructure.Native.Unmanaged.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "10.0.16299.15"}, "runtimes/win-arm64/native/mi.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "10.0.16299.15"}, "runtimes/win-arm64/native/miutils.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "10.0.16299.15"}, "runtimes/win10-x64/native/Microsoft.Management.Infrastructure.Native.Unmanaged.dll": {"rid": "win10-x64", "assetType": "native", "fileVersion": "10.0.14886.1000"}, "runtimes/win10-x64/native/mi.dll": {"rid": "win10-x64", "assetType": "native", "fileVersion": "10.0.14886.1000"}, "runtimes/win10-x64/native/miutils.dll": {"rid": "win10-x64", "assetType": "native", "fileVersion": "10.0.14886.1000"}, "runtimes/win10-x86/native/Microsoft.Management.Infrastructure.Native.Unmanaged.dll": {"rid": "win10-x86", "assetType": "native", "fileVersion": "10.0.14394.1000"}, "runtimes/win10-x86/native/mi.dll": {"rid": "win10-x86", "assetType": "native", "fileVersion": "10.0.14394.1000"}, "runtimes/win10-x86/native/miutils.dll": {"rid": "win10-x86", "assetType": "native", "fileVersion": "10.0.14394.1000"}, "runtimes/win7-x64/native/Microsoft.Management.Infrastructure.Native.Unmanaged.dll": {"rid": "win7-x64", "assetType": "native", "fileVersion": "10.0.14394.1000"}, "runtimes/win7-x64/native/mi.dll": {"rid": "win7-x64", "assetType": "native", "fileVersion": "10.0.14394.1000"}, "runtimes/win7-x64/native/miutils.dll": {"rid": "win7-x64", "assetType": "native", "fileVersion": "10.0.14394.1000"}, "runtimes/win7-x86/native/Microsoft.Management.Infrastructure.Native.Unmanaged.dll": {"rid": "win7-x86", "assetType": "native", "fileVersion": "10.0.14394.1000"}, "runtimes/win7-x86/native/mi.dll": {"rid": "win7-x86", "assetType": "native", "fileVersion": "10.0.14394.1000"}, "runtimes/win7-x86/native/miutils.dll": {"rid": "win7-x86", "assetType": "native", "fileVersion": "10.0.14394.1000"}, "runtimes/win8-x64/native/Microsoft.Management.Infrastructure.Native.Unmanaged.dll": {"rid": "win8-x64", "assetType": "native", "fileVersion": "10.0.14394.1000"}, "runtimes/win8-x64/native/mi.dll": {"rid": "win8-x64", "assetType": "native", "fileVersion": "10.0.14394.1000"}, "runtimes/win8-x64/native/miutils.dll": {"rid": "win8-x64", "assetType": "native", "fileVersion": "10.0.14394.1000"}, "runtimes/win8-x86/native/Microsoft.Management.Infrastructure.Native.Unmanaged.dll": {"rid": "win8-x86", "assetType": "native", "fileVersion": "10.0.14394.1000"}, "runtimes/win8-x86/native/mi.dll": {"rid": "win8-x86", "assetType": "native", "fileVersion": "10.0.14394.1000"}, "runtimes/win8-x86/native/miutils.dll": {"rid": "win8-x86", "assetType": "native", "fileVersion": "10.0.14394.1000"}, "runtimes/win81-x64/native/Microsoft.Management.Infrastructure.Native.Unmanaged.dll": {"rid": "win81-x64", "assetType": "native", "fileVersion": "10.0.14394.1000"}, "runtimes/win81-x64/native/mi.dll": {"rid": "win81-x64", "assetType": "native", "fileVersion": "10.0.14394.1000"}, "runtimes/win81-x64/native/miutils.dll": {"rid": "win81-x64", "assetType": "native", "fileVersion": "10.0.14394.1000"}, "runtimes/win81-x86/native/Microsoft.Management.Infrastructure.Native.Unmanaged.dll": {"rid": "win81-x86", "assetType": "native", "fileVersion": "10.0.14394.1000"}, "runtimes/win81-x86/native/mi.dll": {"rid": "win81-x86", "assetType": "native", "fileVersion": "10.0.14394.1000"}, "runtimes/win81-x86/native/miutils.dll": {"rid": "win81-x86", "assetType": "native", "fileVersion": "10.0.14394.1000"}}}, "Microsoft.NetCore.Analyzers/3.3.2": {}, "Microsoft.NETCore.Platforms/2.1.2": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.NetFramework.Analyzers/3.3.2": {}, "Microsoft.NETFramework.ReferenceAssemblies/1.0.3": {"dependencies": {"Microsoft.NETFramework.ReferenceAssemblies.net461": "1.0.3"}}, "Microsoft.NETFramework.ReferenceAssemblies.net461/1.0.3": {}, "Microsoft.OData.Client/7.21.3": {"dependencies": {"Microsoft.OData.Core": "7.21.3"}, "runtime": {"lib/netstandard2.0/Microsoft.OData.Client.dll": {"assemblyVersion": "********", "fileVersion": "7.21.3.50603"}}}, "Microsoft.OData.Core/7.21.3": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.Extensions.ObjectPool": "6.0.3", "Microsoft.OData.Edm": "7.21.3", "Microsoft.Spatial": "7.21.3"}, "runtime": {"lib/netcoreapp3.1/Microsoft.OData.Core.dll": {"assemblyVersion": "********", "fileVersion": "7.21.3.50603"}}}, "Microsoft.OData.Edm/7.21.3": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.6.0"}, "runtime": {"lib/netstandard2.0/Microsoft.OData.Edm.dll": {"assemblyVersion": "********", "fileVersion": "7.21.3.50603"}}}, "Microsoft.PowerShell.CoreCLR.Eventing/6.2.3": {"dependencies": {"System.Security.Principal.Windows": "4.5.1"}, "runtimeTargets": {"runtimes/linux-arm/lib/netcoreapp2.1/Microsoft.PowerShell.CoreCLR.Eventing.dll": {"rid": "linux-arm", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "*******"}, "runtimes/linux-x64/lib/netcoreapp2.1/Microsoft.PowerShell.CoreCLR.Eventing.dll": {"rid": "linux-x64", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "0.0.0.0"}, "runtimes/osx/lib/netcoreapp2.1/Microsoft.PowerShell.CoreCLR.Eventing.dll": {"rid": "osx", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "0.0.0.0"}, "runtimes/win-arm/lib/netcoreapp2.1/Microsoft.PowerShell.CoreCLR.Eventing.dll": {"rid": "win-arm", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "*******"}, "runtimes/win-arm64/lib/netcoreapp2.1/Microsoft.PowerShell.CoreCLR.Eventing.dll": {"rid": "win-arm64", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "*******"}, "runtimes/win-x64/lib/netcoreapp2.1/Microsoft.PowerShell.CoreCLR.Eventing.dll": {"rid": "win-x64", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "*******"}, "runtimes/win-x86/lib/netcoreapp2.1/Microsoft.PowerShell.CoreCLR.Eventing.dll": {"rid": "win-x86", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.PowerShell.Native/6.2.0": {"runtimeTargets": {"runtimes/linux-arm/native/libpsl-native.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libpsl-native.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libpsl-native.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libmi.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libpsl-native.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libpsrpclient.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx/native/libmi.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx/native/libpsl-native.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx/native/libpsrpclient.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm/native/PowerShell.Core.Instrumentation.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "10.0.10011.16384"}, "runtimes/win-arm/native/pwrshplugin.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "10.0.10011.16384"}, "runtimes/win-arm/native/pwrshplugin.pdb": {"rid": "win-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/PowerShell.Core.Instrumentation.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "10.0.10011.16384"}, "runtimes/win-arm64/native/pwrshplugin.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "10.0.10011.16384"}, "runtimes/win-arm64/native/pwrshplugin.pdb": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/PowerShell.Core.Instrumentation.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "10.0.10011.16384"}, "runtimes/win-x64/native/pwrshplugin.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "10.0.10011.16384"}, "runtimes/win-x64/native/pwrshplugin.pdb": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/PowerShell.Core.Instrumentation.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "10.0.10011.16384"}, "runtimes/win-x86/native/pwrshplugin.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "10.0.10011.16384"}, "runtimes/win-x86/native/pwrshplugin.pdb": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "Microsoft.Spatial/7.21.3": {"runtime": {"lib/netstandard2.0/Microsoft.Spatial.dll": {"assemblyVersion": "********", "fileVersion": "7.21.3.50603"}}}, "Microsoft.Win32.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "Microsoft.Win32.Registry/4.5.0": {"dependencies": {"System.Security.AccessControl": "4.5.0", "System.Security.Principal.Windows": "4.5.1"}}, "Microsoft.Win32.Registry.AccessControl/4.5.0": {"dependencies": {"Microsoft.Win32.Registry": "4.5.0", "System.Security.AccessControl": "4.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.Registry.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.AccessControl.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}}, "Microsoft.Win32.SystemEvents/7.0.0": {"runtime": {"lib/net7.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/win/lib/net7.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "MsBuild.Exchange/18.0.0": {}, "MsBuildRetail.Exchange/2.0.0": {}, "NETStandard.Library/1.6.1": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.Win32.Primitives": "4.3.0", "System.AppContext": "4.3.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Console": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.Compression.ZipFile": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Net.Http": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Net.Sockets": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Timer": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0"}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.3.27908"}}}, "NSubstitute.Analyzers.CSharp/1.0.17": {}, "OSS.RoslynAnalysis/1.4.10": {}, "ReferenceTrimmer/3.3.10": {}, "RoslynCQ/1.34.2": {}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.native.System/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"dependencies": {"runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "4.3.0"}}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "System.AppContext/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Buffers/4.5.1": {}, "System.CodeDom/4.5.0": {"runtime": {"lib/netstandard2.0/System.CodeDom.dll": {"assemblyVersion": "4.0.1.0", "fileVersion": "4.6.26515.6"}}}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Collections.Concurrent/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Collections.Immutable/1.7.0": {}, "System.Configuration.ConfigurationManager/7.0.0": {"dependencies": {"System.Diagnostics.EventLog": "7.0.0", "System.Security.Cryptography.ProtectedData": "7.0.0", "System.Security.Permissions": "7.0.0"}, "runtime": {"lib/net7.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Console/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.DiagnosticSource/6.0.1": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Diagnostics.EventLog/7.0.0": {"runtime": {"lib/net7.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Diagnostics.EventLog.Messages.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "0.0.0.0"}, "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Diagnostics.PerformanceCounter/7.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "7.0.0"}, "runtime": {"lib/net7.0/System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Diagnostics.PerformanceCounter.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Diagnostics.Tools/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.Tracing/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.DirectoryServices/4.5.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "System.IO.FileSystem.AccessControl": "4.5.0", "System.Security.AccessControl": "4.5.0", "System.Security.Principal.Windows": "4.5.1"}, "runtime": {"lib/netstandard2.0/System.DirectoryServices.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.DirectoryServices.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}}, "System.Drawing.Common/7.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "7.0.0"}, "runtime": {"lib/net7.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Drawing.Common.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Globalization.Calendars/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Globalization.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "System.IdentityModel.Tokens.Jwt/8.0.1": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.0.1", "Microsoft.IdentityModel.Tokens": "8.0.1"}, "runtime": {"lib/net8.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "8.0.1.0", "fileVersion": "8.0.1.50722"}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "System.Buffers": "4.5.1", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.IO.Compression": "4.3.0"}}, "System.IO.Compression.ZipFile/4.3.0": {"dependencies": {"System.Buffers": "4.5.1", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.IO.FileSystem/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem.AccessControl/4.5.0": {"dependencies": {"System.Security.AccessControl": "4.5.0", "System.Security.Principal.Windows": "4.5.1"}}, "System.IO.FileSystem.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Expressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Management/4.5.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.Win32.Registry": "4.5.0", "System.CodeDom": "4.5.0"}, "runtime": {"lib/netstandard2.0/System.Management.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Management.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}}, "System.Management.Automation/6.2.3": {"dependencies": {"Microsoft.Management.Infrastructure": "1.0.0", "Microsoft.PowerShell.CoreCLR.Eventing": "6.2.3", "Microsoft.PowerShell.Native": "6.2.0", "Microsoft.Win32.Registry.AccessControl": "4.5.0", "Newtonsoft.Json": "13.0.3", "System.Configuration.ConfigurationManager": "7.0.0", "System.DirectoryServices": "4.5.0", "System.IO.FileSystem.AccessControl": "4.5.0", "System.Management": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Security.AccessControl": "4.5.0", "System.Security.Cryptography.Pkcs": "4.5.2", "System.Security.Permissions": "7.0.0", "System.Text.Encoding.CodePages": "4.5.1"}, "runtimeTargets": {"runtimes/linux-arm/lib/netcoreapp2.1/System.Management.Automation.dll": {"rid": "linux-arm", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "*******"}, "runtimes/linux-x64/lib/netcoreapp2.1/System.Management.Automation.dll": {"rid": "linux-x64", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "0.0.0.0"}, "runtimes/osx/lib/netcoreapp2.1/System.Management.Automation.dll": {"rid": "osx", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "0.0.0.0"}, "runtimes/win-arm/lib/netcoreapp2.1/System.Management.Automation.dll": {"rid": "win-arm", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "*******"}, "runtimes/win-arm64/lib/netcoreapp2.1/System.Management.Automation.dll": {"rid": "win-arm64", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "*******"}, "runtimes/win-x64/lib/netcoreapp2.1/System.Management.Automation.dll": {"rid": "win-x64", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "*******"}, "runtimes/win-x86/lib/netcoreapp2.1/System.Management.Automation.dll": {"rid": "win-x86", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.Memory/4.5.4": {}, "System.Memory.Data/1.0.2": {"dependencies": {"System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.6.0"}, "runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"assemblyVersion": "1.0.2.0", "fileVersion": "1.0.221.20802"}}}, "System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Net.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Net.Sockets/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Numerics.Vectors/4.5.0": {}, "System.ObjectModel/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}}, "System.Private.DataContractSerialization/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Serialization.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0", "System.Xml.XmlDocument": "4.3.0", "System.Xml.XmlSerializer": "4.3.0"}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.Lightweight/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Reflection.TypeExtensions/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Runtime.CompilerServices.VisualC/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Runtime.Numerics/4.3.0": {"dependencies": {"System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Runtime.Serialization.Primitives/4.3.0": {"dependencies": {"System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Serialization.Xml/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Private.DataContractSerialization": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Serialization.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "System.Security.AccessControl/4.5.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "System.Security.Principal.Windows": "4.5.1"}}, "System.Security.Cryptography.Algorithms/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.Apple": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.Cng/4.5.0": {}, "System.Security.Cryptography.Csp/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Security.Cryptography.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.Pkcs/4.5.2": {"dependencies": {"System.Security.Cryptography.Cng": "4.5.0"}, "runtime": {"lib/netcoreapp2.1/System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "4.0.3.2", "fileVersion": "4.6.27129.4"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.1/System.Security.Cryptography.Pkcs.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "4.0.3.2", "fileVersion": "4.6.27129.4"}}}, "System.Security.Cryptography.Primitives/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Security.Cryptography.ProtectedData/7.0.0": {"runtime": {"lib/net7.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Security.Cryptography.X509Certificates/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Cng": "4.5.0", "System.Security.Cryptography.Csp": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Permissions/7.0.0": {"dependencies": {"System.Windows.Extensions": "7.0.0"}, "runtime": {"lib/net7.0/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Security.Principal.Windows/4.5.1": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2"}}, "System.Security.SecureString/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding.CodePages/4.5.1": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Encoding.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Text.Encodings.Web/4.7.2": {}, "System.Text.Json/4.6.0": {}, "System.Text.RegularExpressions/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks.Extensions/4.5.4": {}, "System.Threading.ThreadPool/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Threading.Timer/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.ValueTuple/4.5.0": {}, "System.Windows.Extensions/7.0.0": {"dependencies": {"System.Drawing.Common": "7.0.0"}, "runtime": {"lib/net7.0/System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Windows.Extensions.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Xml.ReaderWriter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Extensions": "4.5.4"}}, "System.Xml.XDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "System.Xml.XmlDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "System.Xml.XmlSerializer/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}}, "Microsoft.Exchange.Management.AdminApiProvider/0.0.0": {"dependencies": {"Microsoft.Identity.Client": "4.66.1", "Microsoft.Identity.Client.Broker": "4.61.3", "Microsoft.Identity.Client.NativeInterop": "0.16.1", "Microsoft.OData.Client": "7.21.3", "Microsoft.OData.Core": "7.21.3", "Microsoft.OData.Edm": "7.21.3", "Microsoft.Online.CSE.RestApiPowerShellModule.Instrumentation": "0.0.0", "Microsoft.Spatial": "7.21.3", "System.IdentityModel.Tokens.Jwt": "8.0.1", "System.Management.Automation": "6.2.3"}, "runtime": {"Microsoft.Exchange.Management.AdminApiProvider.dll": {"assemblyVersion": "0.0.0", "fileVersion": ""}}}, "Microsoft.Exchange.Management.RestApiClient/0.0.0": {"dependencies": {"Microsoft.Azure.Cosmos": "3.37.0", "Microsoft.CSharp": "4.7.0", "Microsoft.Exchange.Management.AdminApiProvider": "0.0.0", "Microsoft.IdentityModel.Authorization": "13.10.8897", "Microsoft.OData.Client": "7.21.3", "Microsoft.OData.Core": "7.21.3", "Microsoft.OData.Edm": "7.21.3", "Microsoft.Online.CSE.RestApiPowerShellModule.Instrumentation": "0.0.0", "Microsoft.Spatial": "7.21.3", "Newtonsoft.Json": "13.0.3", "System.Management.Automation": "6.2.3"}, "runtime": {"Microsoft.Exchange.Management.RestApiClient.dll": {"assemblyVersion": "0.0.0", "fileVersion": ""}}}, "Microsoft.Online.CSE.RestApiPowerShellModule.Instrumentation/0.0.0": {"dependencies": {"System.Diagnostics.PerformanceCounter": "7.0.0", "System.Management.Automation": "6.2.3"}, "runtime": {"Microsoft.Online.CSE.RestApiPowerShellModule.Instrumentation.dll": {"assemblyVersion": "0.0.0", "fileVersion": ""}}}, "Microsoft.Exchange.Management.AdminApiProvider.Reference/********": {"runtime": {"Microsoft.Exchange.Management.AdminApiProvider.dll": {"assemblyVersion": "********", "fileVersion": "15.20.8699.0"}}}, "Microsoft.Exchange.Management.RestApiClient.Reference/********": {"runtime": {"Microsoft.Exchange.Management.RestApiClient.dll": {"assemblyVersion": "********", "fileVersion": "15.20.8699.0"}}}, "Microsoft.Online.CSE.RestApiPowerShellModule.Instrumentation.Reference/********": {"runtime": {"Microsoft.Online.CSE.RestApiPowerShellModule.Instrumentation.dll": {"assemblyVersion": "********", "fileVersion": "15.20.8699.0"}}}}}, "libraries": {"Microsoft.Exchange.Management.ExoPowershellGalleryModule/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Antlr4/4.6.4": {"type": "package", "serviceable": true, "sha512": "sha512-8DtLvhApg7mEHoDXt1wCeL/zV5JyQPJr7e0ZDcl46RRGdnKKZuMSfwXX+PCx+AU9lnlXX0r5qSRsNzEGCFFOBw==", "path": "antlr4/4.6.4", "hashPath": "antlr4.4.6.4.nupkg.sha512"}, "Antlr4.CodeGenerator/4.6.4": {"type": "package", "serviceable": true, "sha512": "sha512-9JhKPf0STtpBzmxGKIc+nBTPVh033CmGStJza4NtBFKDzTCId039QnVelKfDXzocwbu96efinjYdtzv173Xx2g==", "path": "antlr4.codegenerator/4.6.4", "hashPath": "antlr4.codegenerator.4.6.4.nupkg.sha512"}, "Antlr4.Runtime/4.6.4": {"type": "package", "serviceable": true, "sha512": "sha512-DO/MViJ4xpfuIUDBHWWCxpPg14h1Wg9vSD4rUgfVsrqAzPfci2vL0NdR+uNZUlL3O4gsZHYbZq21ZnUM7VjdbA==", "path": "antlr4.runtime/4.6.4", "hashPath": "antlr4.runtime.4.6.4.nupkg.sha512"}, "Azure.Core/1.19.0": {"type": "package", "serviceable": true, "sha512": "sha512-lcDjG635DPE4fU5tqSueVMmzrx0QrIfPuY0+y6evHN5GanQ0GB+/4nuMHMmoNPwEow6OUPkJu4cZQxfHJQXPdA==", "path": "azure.core/1.19.0", "hashPath": "azure.core.1.19.0.nupkg.sha512"}, "BldSync/1.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-mxqQKhO2V0jZZ47rsQlDa8tB1VYad/WFtCLj5Qi+kD4dHfveevdBJOGkpwi+GTNZs57yKKoP1h6oGZVPnu6U7Q==", "path": "bldsync/1.0.3", "hashPath": "bldsync.1.0.3.nupkg.sha512"}, "CodeSign.Exchange/1.3.24": {"type": "package", "serviceable": true, "sha512": "sha512-bV6yPHK3DhWvuebalHZHOEImyoIKIodp0Hf41loHCGlAHUnh7nZuPKpZD1fW6cg+fVR8S4SMFQP5RAkWQLWvyw==", "path": "codesign.exchange/1.3.24", "hashPath": "codesign.exchange.1.3.24.nupkg.sha512"}, "Microsoft.Azure.Cosmos/3.37.0": {"type": "package", "serviceable": true, "sha512": "sha512-NTNIGWBMVc/gwS2utRvSQ2pULeNC+BomgYh4sb5lQAVH/kkWUDcRgHwd3/YQE9xuo5U6jkEe1VHFME0szC1z3g==", "path": "microsoft.azure.cosmos/3.37.0", "hashPath": "microsoft.azure.cosmos.3.37.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3WA9q9yVqJp222P3x1wYIGDAkpjAku0TMUaaQV22g6L67AI0LdOIrVS7Ht2vJfLHGSPVuqN94vIr15qn+HEkHw==", "path": "microsoft.bcl.asyncinterfaces/8.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.8.0.0.nupkg.sha512"}, "Microsoft.Bcl.HashCode/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-J2G1k+u5unBV+aYcwxo94ip16Rkp65pgWFb0R6zwJipzWNMgvqlWeuI7/+R+e8bob66LnSG+llLJ+z8wI94cHg==", "path": "microsoft.bcl.hashcode/1.1.0", "hashPath": "microsoft.bcl.hashcode.1.1.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.FxCopAnalyzers/3.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-QlaP2SgpkiV5fnDgC1WwG3blfXIvz5WSPkA/R/AjKRwOLTGU1YLE3PArkvTz1ZtLCuXs29Qp3iY2fja7wF0iEg==", "path": "microsoft.codeanalysis.fxcopanalyzers/3.3.2", "hashPath": "microsoft.codeanalysis.fxcopanalyzers.3.3.2.nupkg.sha512"}, "Microsoft.CodeAnalysis.VersionCheckAnalyzer/3.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-KTqeVJjGfwDX7/AGDgBXErYX/8Etjwu8Zg2TgmmjVPZReVZk4KLv5fpEiTtoBXis3AO+OM/Qu4cQfz828RSmDQ==", "path": "microsoft.codeanalysis.versioncheckanalyzer/3.3.2", "hashPath": "microsoft.codeanalysis.versioncheckanalyzer.3.3.2.nupkg.sha512"}, "Microsoft.CodeQuality.Analyzers/3.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-WwR96abpowLKCJ/+hREuBu58zbTBCiFLQx5FjAUAYrgtuIQsg+jRtv4n9gKw6zxydnO+jd5aFJB6H+eqGqQufw==", "path": "microsoft.codequality.analyzers/3.3.2", "hashPath": "microsoft.codequality.analyzers.3.3.2.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/6.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-IbQUEZr/LxxpPxkXDmKaemMeQNPjdHfk87HtTsI18a3RVgad0NOJSRaJ20hcesqL45PLcpQHR8xrPP7wZKbFQQ==", "path": "microsoft.extensions.objectpool/6.0.3", "hashPath": "microsoft.extensions.objectpool.6.0.3.nupkg.sha512"}, "Microsoft.Identity.Client/4.66.1": {"type": "package", "serviceable": true, "sha512": "sha512-mE+m3pZ7zSKocSubKXxwZcUrCzLflC86IdLxrVjS8tialy0b1L+aECBqRBC/ykcPlB4y7skg49TaTiA+O2UfDw==", "path": "microsoft.identity.client/4.66.1", "hashPath": "microsoft.identity.client.4.66.1.nupkg.sha512"}, "Microsoft.Identity.Client.Broker/4.61.3": {"type": "package", "serviceable": true, "sha512": "sha512-VX8YJYmjNZzWjLuwAwBWzTtfNRTqjj5DKPEci6eNa2tedBWSyOGvcsVMVUL/vy1oRSAjfpwzUIFlXCmhWnCAeA==", "path": "microsoft.identity.client.broker/4.61.3", "hashPath": "microsoft.identity.client.broker.4.61.3.nupkg.sha512"}, "Microsoft.Identity.Client.NativeInterop/0.16.1": {"type": "package", "serviceable": true, "sha512": "sha512-OvY/+/AHESi24f5zOCf9kL4HXwPxXVQ3A+tMQsJvFk2DmP+sc88FYWL49zlku5q0bvx5yFvBLNpHQeRT9a6A5g==", "path": "microsoft.identity.client.nativeinterop/0.16.1", "hashPath": "microsoft.identity.client.nativeinterop.0.16.1.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-OtlIWcyX01olfdevPKZdIPfBEvbcioDyBiE/Z2lHsopsMD7twcKtlN9kMevHmI5IIPhFpfwCIiR6qHQz1WHUIw==", "path": "microsoft.identitymodel.abstractions/8.0.1", "hashPath": "microsoft.identitymodel.abstractions.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Authorization/13.10.8897": {"type": "package", "serviceable": true, "sha512": "sha512-j3YBLlLqesLJSPtAFy26Mm2tVQrQcL9j18NKaUlCdC6D04NqULf1t3fnOgvoEjmmdKLGwMKmYQtoyKn6R0aiUg==", "path": "microsoft.identitymodel.authorization/13.10.8897", "hashPath": "microsoft.identitymodel.authorization.13.10.8897.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-s6++gF9x0rQApQzOBbSyp4jUaAlwm+DroKfL8gdOHxs83k8SJfUXhuc46rDB3rNXBQ1MVRxqKUrqFhO/M0E97g==", "path": "microsoft.identitymodel.jsonwebtokens/8.0.1", "hashPath": "microsoft.identitymodel.jsonwebtokens.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-UCPF2exZqBXe7v/6sGNiM6zCQOUXXQ9+v5VTb9gPB8ZSUPnX53BxlN78v2jsbIvK9Dq4GovQxo23x8JgWvm/Qg==", "path": "microsoft.identitymodel.logging/8.0.1", "hashPath": "microsoft.identitymodel.logging.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-kDimB6Dkd3nkW2oZPDkMkVHfQt3IDqO5gL0oa8WVy3OP4uE8Ij+8TXnqg9TOd9ufjsY3IDiGz7pCUbnfL18tjg==", "path": "microsoft.identitymodel.tokens/8.0.1", "hashPath": "microsoft.identitymodel.tokens.8.0.1.nupkg.sha512"}, "Microsoft.Internal.Analyzers/2.9.7": {"type": "package", "serviceable": true, "sha512": "sha512-XzC63sRREhSyMdJYfSplcRi3kgJFV2DWTHdzAyBH/xIZs1WK/PFbo2Cy6GrAS7divSqSUvHc4a90T02fQNkCIQ==", "path": "microsoft.internal.analyzers/2.9.7", "hashPath": "microsoft.internal.analyzers.2.9.7.nupkg.sha512"}, "Microsoft.Management.Infrastructure/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-EhQ4sbjNu4rL39YVhRSKMzCaMNBSwSJi17t8LtAOW94WQTkhQCEhlMukFU2AtWOBW3zGIrvg85XRS+w0nuGxKw==", "path": "microsoft.management.infrastructure/1.0.0", "hashPath": "microsoft.management.infrastructure.1.0.0.nupkg.sha512"}, "Microsoft.NetCore.Analyzers/3.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-L9lU2E9SaK8znn8ZkstOx8jjpYmsBTvt3xIW6btPM/Fi8m7zSK80itHV0p6f23q84uvyXS8ibECjP0Vra99zsQ==", "path": "microsoft.netcore.analyzers/3.3.2", "hashPath": "microsoft.netcore.analyzers.3.3.2.nupkg.sha512"}, "Microsoft.NETCore.Platforms/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-mOJy3M0UN+LUG21dLGMxaWZEP6xYpQEpLuvuEQBaownaX4YuhH6NmNUlN9si+vNkAS6dwJ//N1O4DmLf2CikVg==", "path": "microsoft.netcore.platforms/2.1.2", "hashPath": "microsoft.netcore.platforms.2.1.2.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.NetFramework.Analyzers/3.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-NfmC8NoxrRtw2PSmqSu+kVTcsJuMhspxWKbVzrtPxw+O8hjpCPzD0IttCUJclDf36qkmScvvd1BgRHYE17zF9g==", "path": "microsoft.netframework.analyzers/3.3.2", "hashPath": "microsoft.netframework.analyzers.3.3.2.nupkg.sha512"}, "Microsoft.NETFramework.ReferenceAssemblies/1.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-vUc9Npcs14QsyOD01tnv/m8sQUnGTGOw1BCmKcv77LBJY7OxhJ+zJF7UD/sCL3lYNFuqmQEVlkfS4Quif6FyYg==", "path": "microsoft.netframework.referenceassemblies/1.0.3", "hashPath": "microsoft.netframework.referenceassemblies.1.0.3.nupkg.sha512"}, "Microsoft.NETFramework.ReferenceAssemblies.net461/1.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-AmOJZwCqnOCNp6PPcf9joyogScWLtwy0M1WkqfEQ0M9nYwyDD7EX9ZjscKS5iYnyvteX7kzSKFCKt9I9dXA6mA==", "path": "microsoft.netframework.referenceassemblies.net461/1.0.3", "hashPath": "microsoft.netframework.referenceassemblies.net461.1.0.3.nupkg.sha512"}, "Microsoft.OData.Client/7.21.3": {"type": "package", "serviceable": true, "sha512": "sha512-hf9lTBz48ft2Kw2pbKBclEu+0w2Q4E87ma/cMtuyR9pPnKM2D6suIQ7k0IryYcuvtM+sGQnZVZR50dTrF9B9yQ==", "path": "microsoft.odata.client/7.21.3", "hashPath": "microsoft.odata.client.7.21.3.nupkg.sha512"}, "Microsoft.OData.Core/7.21.3": {"type": "package", "serviceable": true, "sha512": "sha512-d3JLnRL6xiENrF4NMdNNBzZURDpYVXD6QKryiKv6dgxMlEE5Kgc4OlCcLFKqy2HqHsjcvfNomDijHU+xwacJ3g==", "path": "microsoft.odata.core/7.21.3", "hashPath": "microsoft.odata.core.7.21.3.nupkg.sha512"}, "Microsoft.OData.Edm/7.21.3": {"type": "package", "serviceable": true, "sha512": "sha512-9ATrRrhAKYzwu0huo1C7poTeCiZNOfMTMxcORxnkfL5o5TlNJ/iMg76yVfp8ZRRhsKj7uELIjXt54kMPaaeAuw==", "path": "microsoft.odata.edm/7.21.3", "hashPath": "microsoft.odata.edm.7.21.3.nupkg.sha512"}, "Microsoft.PowerShell.CoreCLR.Eventing/6.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-QjM7JAyfrJCn+MBOfXKRa4JdJVA2XAkb07SDRhUejr0BniCjkiraUsAMcwm2MAM62mXtp9e6ZF8TZsotx7Y/Bg==", "path": "microsoft.powershell.coreclr.eventing/6.2.3", "hashPath": "microsoft.powershell.coreclr.eventing.6.2.3.nupkg.sha512"}, "Microsoft.PowerShell.Native/6.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-5YPhSaW4j6R7oJ53RxOroB51k7zKBpi4owEKAY9rI8Cb+cGIIGN/37DoDOZfbKL4DNW0MeAchgNsAEML049y7Q==", "path": "microsoft.powershell.native/6.2.0", "hashPath": "microsoft.powershell.native.6.2.0.nupkg.sha512"}, "Microsoft.Spatial/7.21.3": {"type": "package", "serviceable": true, "sha512": "sha512-6YdZ8GYqzv6bZmxSct6lu4M6OYQDQqQLBsVbES2/St03yisr+CRxvI8/7ymwizYDcThaAtG/2fIzeW3HiPXNZA==", "path": "microsoft.spatial/7.21.3", "hashPath": "microsoft.spatial.7.21.3.nupkg.sha512"}, "Microsoft.Win32.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ZQKCWxH7Ijp9BfahvL2Zyf1cJIk8XYLF6Yjzr2yi0b2cOut/HQ31qf1ThHAgCc3WiZMdnWcfJCgN82/0UunxA==", "path": "microsoft.win32.primitives/4.3.0", "hashPath": "microsoft.win32.primitives.4.3.0.nupkg.sha512"}, "Microsoft.Win32.Registry/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-+FWlwd//+Tt56316p00hVePBCouXyEzT86Jb3+AuRotTND0IYn0OO3obs1gnQEs/txEnt+rF2JBGLItTG+Be6A==", "path": "microsoft.win32.registry/4.5.0", "hashPath": "microsoft.win32.registry.4.5.0.nupkg.sha512"}, "Microsoft.Win32.Registry.AccessControl/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-f2aT8NLc9DsB1VEaM4jlYF9DFLA12+ccmWzWA0HEVC3Vgac7tGLSgrU6jtUG+VO1/R5zA6AomQ2pKqJ+5SDWyQ==", "path": "microsoft.win32.registry.accesscontrol/4.5.0", "hashPath": "microsoft.win32.registry.accesscontrol.4.5.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-2nXPrhdAyAzir0gLl8Yy8S5Mnm/uBSQQA7jEsILOS1MTyS7DbmV1NgViMtvV1sfCD1ebITpNwb1NIinKeJgUVQ==", "path": "microsoft.win32.systemevents/7.0.0", "hashPath": "microsoft.win32.systemevents.7.0.0.nupkg.sha512"}, "MsBuild.Exchange/18.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-8cnQ4Q3lkj8TiSxb51/bVTvtXp80X4IxGxIQDa4iPoOPdhA5GbTSTs3vFpX5CsyrU6xgs7KYPoqpuRMjNrKnZw==", "path": "msbuild.exchange/18.0.0", "hashPath": "msbuild.exchange.18.0.0.nupkg.sha512"}, "MsBuildRetail.Exchange/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rKz9aYAXn4+0MOobdOWLUGXt60YBlWxb0egurQE61O21xoMVtlBiHL790o7u3xv9ye0vBk9xunOHPaVjD4tYSQ==", "path": "msbuildretail.exchange/2.0.0", "hashPath": "msbuildretail.exchange.2.0.0.nupkg.sha512"}, "NETStandard.Library/1.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-WcSp3+vP+yHNgS8EV5J7pZ9IRpeDuARBPN28by8zqff1wJQXm26PVU8L3/fYLBJVU7BtDyqNVWq2KlCVvSSR4A==", "path": "netstandard.library/1.6.1", "hashPath": "netstandard.library.1.6.1.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "NSubstitute.Analyzers.CSharp/1.0.17": {"type": "package", "serviceable": true, "sha512": "sha512-Pwz0MD7CAM/G/fvJjM3ceOfI+S0IgjanHcK7evwyrW9qAWUG8fgiEXYfSX1/s3h2JUNDOw6ik0G8zp+RT61Y1g==", "path": "nsubstitute.analyzers.csharp/1.0.17", "hashPath": "nsubstitute.analyzers.csharp.1.0.17.nupkg.sha512"}, "OSS.RoslynAnalysis/1.4.10": {"type": "package", "serviceable": true, "sha512": "sha512-hA2Zwbd8kXJ/P8FnNcyLoWB4Kx4dxaFKeKvuw2kPQHhmO2OEUJ1i8Zrq8da2qJAvN1VzJtYDVzlDGV+/B6c1HA==", "path": "oss.roslynanalysis/1.4.10", "hashPath": "oss.roslynanalysis.1.4.10.nupkg.sha512"}, "ReferenceTrimmer/3.3.10": {"type": "package", "serviceable": true, "sha512": "sha512-+hLknHjUAGx6pBElpPkgckLS9qW0LsHzLUZvg+ZHjIqA4w5K8q+KgvDmeN8JdqaObmiEaTx2nF/QKLrT4h+xFA==", "path": "referencetrimmer/3.3.10", "hashPath": "referencetrimmer.3.3.10.nupkg.sha512"}, "RoslynCQ/1.34.2": {"type": "package", "serviceable": true, "sha512": "sha512-hSYQOvSOZEDYWyYkZ6SG36KbAFFxVXBTjmmve1Rbw832Ac9WhkFjrav5b6j9uZMCMrHO+H1Nzd8soEjYH0x1/w==", "path": "roslyncq/1.34.2", "hashPath": "roslyncq.1.34.2.nupkg.sha512"}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-HdSSp5MnJSsg08KMfZThpuLPJpPwE5hBXvHwoKWosyHHfe8Mh5WKT0ylEOf6yNzX6Ngjxe4Whkafh5q7Ymac4Q==", "path": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-+yH1a49wJMy8Zt4yx5RhJrxO/DBDByAiCzNwiETI+1S4mPdCu0OY4djdciC7Vssk0l22wQaDLrXxXkp+3+7bVA==", "path": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c3YNH1GQJbfIPJeCnr4avseugSqPrxwIqzthYyZDN6EuOyNOzq+y2KSUfRcXauya1sF4foESTgwM5e1A8arAKw==", "path": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.native.System/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "path": "runtime.native.system/4.3.0", "hashPath": "runtime.native.system.4.3.0.nupkg.sha512"}, "runtime.native.System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-INBPonS5QPEgn7naufQFXJEp3zX6L4bwHgJ/ZH78aBTpeNfQMtf7C6VrAFhlq2xxWBveIOWyFzQjJ8XzHMhdOQ==", "path": "runtime.native.system.io.compression/4.3.0", "hashPath": "runtime.native.system.io.compression.4.3.0.nupkg.sha512"}, "runtime.native.System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZVuZJqnnegJhd2k/PtAbbIcZ3aZeITq3sj06oKfMBSfphW3HDmk/t4ObvbOk/JA/swGR0LNqMksAh/f7gpTROg==", "path": "runtime.native.system.net.http/4.3.0", "hashPath": "runtime.native.system.net.http.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DloMk88juo0OuOWr56QG7MNchmafTLYWvABy36izkrLI5VledI0rq28KGs1i9wbpeT9NPQrx/wTf8U2vazqQ3Q==", "path": "runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-NS1U+700m4KFRHR5o4vo9DSlTmlCKu/u7dtE5sUHVIPB+xpXxYQvgBgA6wEIeCz6Yfn0Z52/72WYsToCEPJnrw==", "path": "runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-b3pthNgxxFcD+Pc0WSEoC0+md3MyhRS6aCEeenvNE3Fdw1HyJ18ZhRFVJJzIeR/O/jpxPboB805Ho0T3Ul7w8A==", "path": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KeLz4HClKf+nFS7p/6Fi/CqyLXh81FpiGzcmuS8DGi9lUqSnZ6Es23/gv2O+1XVGfrbNmviF7CckBpavkBoIFQ==", "path": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kVXCuMTrTlxq4XOOMAysuNwsXWpYeboGddNGpIgNSZmv1b6r/s/DPk0fYMB7Q5Qo4bY68o48jt4T4y5BVecbCQ==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X7IdhILzr4ROXd8mI1BUCQMSHSQwelUlBjF1JyTKCjXaOGn2fB4EKBxQbCK2VjO3WaWIdlXZL3W6TiIVnrhX4g==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-nyFNiCk/r+VOiIqreLix8yN+q3Wga9+SE8BCgkf+2BwEKiNx6DyvFjCgkfV743/grxv8jHJ8gUK4XEQw7yzRYg==", "path": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ytoewC6wGorL7KoCAvRfsgoJPJbNq+64k2SqW6JcOAebWsFUvCCYgfzQMrnpvPiEl4OrblUlhF2ji+Q1+SVLrQ==", "path": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-I8bKw2I8k58Wx7fMKQJn2R8lamboCAiHfHeV/pS65ScKWMMI0+wJkLYlEKvgW1D/XvSl/221clBoR2q9QNNM7A==", "path": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VB5cn/7OzUfzdnC8tqAIMQciVLiq2epm2NrAm1E9OjNRyG4lVhfR61SMcLizejzQP8R8Uf/0l5qOIbUEi+RdEg==", "path": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "System.AppContext/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-fKC+rmaLfeIzUhagxY17Q9siv/sPrjjKcfNg1Ic8IlQkZLipo8ljcaZQu4VtI4Jqbzjc2VTjzGLF6WmsRXAEgA==", "path": "system.appcontext/4.3.0", "hashPath": "system.appcontext.4.3.0.nupkg.sha512"}, "System.Buffers/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "path": "system.buffers/4.5.1", "hashPath": "system.buffers.4.5.1.nupkg.sha512"}, "System.CodeDom/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-gqpR1EeXOuzNQWL7rOzmtdIz3CaXVjSQCiaGOs2ivjPwynKSJYm39X81fdlp7WuojZs/Z5t1k5ni7HtKQurhjw==", "path": "system.codedom/4.5.0", "hashPath": "system.codedom.4.5.0.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Concurrent/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "path": "system.collections.concurrent/4.3.0", "hashPath": "system.collections.concurrent.4.3.0.nupkg.sha512"}, "System.Collections.Immutable/1.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-RVSM6wZUo6L2y6P3vN6gjUtyJ2IF2RVtrepF3J7nrDKfFQd5u/SnSUFclchYQis8/k5scHy9E+fVeKVQLnnkzw==", "path": "system.collections.immutable/1.7.0", "hashPath": "system.collections.immutable.1.7.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-WvRUdlL1lB0dTRZSs5XcQOd5q9MYNk90GkbmRmiCvRHThWiojkpGqWdmEDJdXyHbxG/BhE5hmVbMfRLXW9FJVA==", "path": "system.configuration.configurationmanager/7.0.0", "hashPath": "system.configuration.configurationmanager.7.0.0.nupkg.sha512"}, "System.Console/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHDrIxiqk1h03m6khKWV2X8p/uvN79rgSqpilL6uzpmSfxfU5ng8VcPtW4qsDsQDHiTv6IPV9TmD5M/vElPNLg==", "path": "system.console/4.3.0", "hashPath": "system.console.4.3.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KiLYDu2k2J82Q9BJpWiuQqCkFjRBWVq4jDzKKWawVi9KWzyD0XG3cmfX0vqTQlL14Wi9EufJrbL0+KCLTbqWiQ==", "path": "system.diagnostics.diagnosticsource/6.0.1", "hashPath": "system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512"}, "System.Diagnostics.EventLog/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eUDP47obqQm3SFJfP6z+Fx2nJ4KKTQbXB4Q9Uesnzw9SbYdhjyoGXuvDn/gEmFY6N5Z3bFFbpAQGA7m6hrYJCw==", "path": "system.diagnostics.eventlog/7.0.0", "hashPath": "system.diagnostics.eventlog.7.0.0.nupkg.sha512"}, "System.Diagnostics.PerformanceCounter/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-L+zIMEaXp1vA4wZk1KLMpk6tvU0xy94R0IfmhkmTWeC4KwShsmAfbg5I19LgjsCTYp6GVdXZ2aHluVWL0QqBdA==", "path": "system.diagnostics.performancecounter/7.0.0", "hashPath": "system.diagnostics.performancecounter.7.0.0.nupkg.sha512"}, "System.Diagnostics.Tools/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-UUvkJfSYJMM6x527dJg2VyWPSRqIVB0Z7dbjHst1zmwTXz5CcXSYJFWRpuigfbO1Lf7yfZiIaEUesfnl/g5EyA==", "path": "system.diagnostics.tools/4.3.0", "hashPath": "system.diagnostics.tools.4.3.0.nupkg.sha512"}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "path": "system.diagnostics.tracing/4.3.0", "hashPath": "system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "System.DirectoryServices/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-6Uty9sMaeBG0/GIRTW4+DUTvuB/od5E6rY45JbEz1c2xMoPSA9GLov4KdiBEpjsEcsgORa6sC3vfh70tEJJaOw==", "path": "system.directoryservices/4.5.0", "hashPath": "system.directoryservices.4.5.0.nupkg.sha512"}, "System.Drawing.Common/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-K<PERSON>+oBU38pxkKPxvLcLfIkOV5Ien8ReN78wro7OF5/erwcmortzeFx+iBswlh2Vz6gVne0khocQudGwaO1Ey6A==", "path": "system.drawing.common/7.0.0", "hashPath": "system.drawing.common.7.0.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.Globalization.Calendars/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GUlBtdOWT4LTV3I+9/PJW+56AnnChTaOqqTLFtdmype/L500M2LIyXgmtd9X2P2VOkmJd5c67H5SaC2QcL1bFA==", "path": "system.globalization.calendars/4.3.0", "hashPath": "system.globalization.calendars.4.3.0.nupkg.sha512"}, "System.Globalization.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-FhKmdR6MPG+pxow6wGtNAWdZh7noIOpdD5TwQ3CprzgIE1bBBoim0vbR1+AWsWjQmU7zXHgQo4TWSP6lCeiWcQ==", "path": "system.globalization.extensions/4.3.0", "hashPath": "system.globalization.extensions.4.3.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-GJw3bYkWpOgvN3tJo5X4lYUeIFA2HD293FPUhKmp7qxS+g5ywAb34Dnd3cDAFLkcMohy5XTpoaZ4uAHuw0uSPQ==", "path": "system.identitymodel.tokens.jwt/8.0.1", "hashPath": "system.identitymodel.tokens.jwt.8.0.1.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YHndyoiV90iu4iKG115ibkhrG+S3jBm8Ap9OwoUAzO5oPDAWcr0SFwQFm0HjM8WkEZWo0zvLTyLmbvTkW1bXgg==", "path": "system.io.compression/4.3.0", "hashPath": "system.io.compression.4.3.0.nupkg.sha512"}, "System.IO.Compression.ZipFile/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-G4HwjEsgIwy3JFBduZ9quBkAu+eUwjIdJleuNSgmUojbH6O3mlvEIme+GHx/cLlTAPcrnnL7GqvB9pTlWRfhOg==", "path": "system.io.compression.zipfile/4.3.0", "hashPath": "system.io.compression.zipfile.4.3.0.nupkg.sha512"}, "System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "path": "system.io.filesystem/4.3.0", "hashPath": "system.io.filesystem.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.AccessControl/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-TYe6xstoqT5MlTly0OtPU6u9zWuNScLVMEx6sTCjjx+Hqdp0wCXoG6fnzMpTPMQACXQzi9pd2N5Tloow+5jQdQ==", "path": "system.io.filesystem.accesscontrol/4.5.0", "hashPath": "system.io.filesystem.accesscontrol.4.5.0.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "path": "system.io.filesystem.primitives/4.3.0", "hashPath": "system.io.filesystem.primitives.4.3.0.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Expressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "path": "system.linq.expressions/4.3.0", "hashPath": "system.linq.expressions.4.3.0.nupkg.sha512"}, "System.Management/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-Z6ac0qPGr3yJtwZEX1SRkhwWa0Kf5NJxx7smLboYsGrApQFECNFdqhGy252T4lrZ5Nwzhd9VQiaifndR3bfHdg==", "path": "system.management/4.5.0", "hashPath": "system.management.4.5.0.nupkg.sha512"}, "System.Management.Automation/6.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-U/ufrBiMZGDO0gz/g6o715O5Cy4yknAD/orPPz2Uf/m7omkAZbQQ/PnNeoAeLNoe3jcJjCL5P1QmvJRx/CcwEA==", "path": "system.management.automation/6.2.3", "hashPath": "system.management.automation.6.2.3.nupkg.sha512"}, "System.Memory/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "path": "system.memory/4.5.4", "hashPath": "system.memory.4.5.4.nupkg.sha512"}, "System.Memory.Data/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "path": "system.memory.data/1.0.2", "hashPath": "system.memory.data.1.0.2.nupkg.sha512"}, "System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-sYg+FtILtRQuYWSIAuNOELwVuVsxVyJGWQyOnlAzhV4xvhyFnON1bAzYYC+jjRW8JREM45R0R5Dgi8MTC5sEwA==", "path": "system.net.http/4.3.0", "hashPath": "system.net.http.4.3.0.nupkg.sha512"}, "System.Net.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-qOu+hDwFwoZPbzPvwut2qATe3ygjeQBDQj91xlsaqGFQUI5i4ZnZb8yyQuLGpDGivEPIt8EJkd1BVzVoP31FXA==", "path": "system.net.primitives/4.3.0", "hashPath": "system.net.primitives.4.3.0.nupkg.sha512"}, "System.Net.Sockets/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-m6icV6TqQOAdgt5N/9I5KNpjom/5NFtkmGseEH+AK/hny8XrytLH3+b5M8zL/Ycg3fhIocFpUMyl/wpFnVRvdw==", "path": "system.net.sockets/4.3.0", "hashPath": "system.net.sockets.4.3.0.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.ObjectModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "path": "system.objectmodel/4.3.0", "hashPath": "system.objectmodel.4.3.0.nupkg.sha512"}, "System.Private.DataContractSerialization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yDaJ2x3mMmjdZEDB4IbezSnCsnjQ4BxinKhRAaP6kEgL6Bb6jANWphs5SzyD8imqeC/3FxgsuXT6ykkiH1uUmA==", "path": "system.private.datacontractserialization/4.3.0", "hashPath": "system.private.datacontractserialization.4.3.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Emit/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-228FG0jLcIwTVJyz8CLFKueVqQK36ANazUManGaJHkO0icjiIypKW7YLWLIWahyIkdh5M7mV2dJepllLyA1SKg==", "path": "system.reflection.emit/4.3.0", "hashPath": "system.reflection.emit.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "path": "system.reflection.emit.lightweight/4.3.0", "hashPath": "system.reflection.emit.lightweight.4.3.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "path": "system.reflection.typeextensions/4.3.0", "hashPath": "system.reflection.typeextensions.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Runtime.CompilerServices.VisualC/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/dcn1oXqK/p/VnTYWNSf4OXlFIfzCRE/kqWz4+/r5B2S4zlKifB1FqklEEYs5zmE1JE3syvrJ5U4syOwsDQZbA==", "path": "system.runtime.compilerservices.visualc/4.3.0", "hashPath": "system.runtime.compilerservices.visualc.4.3.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cbz4YJMqRDR7oLeMRbdYv7mYzc++17lNhScCX0goO2XpGWdvAt60CGN+FHdePUEHCe/Jy9jUlvNAiNdM+7jsOw==", "path": "system.runtime.interopservices.runtimeinformation/4.3.0", "hashPath": "system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512"}, "System.Runtime.Numerics/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yMH+MfdzHjy17l2KESnPiF2dwq7T+xLnSJar7slyimAkUh/gTrS9/UQOtv7xarskJ2/XDSNvfLGOBQPjL7PaHQ==", "path": "system.runtime.numerics/4.3.0", "hashPath": "system.runtime.numerics.4.3.0.nupkg.sha512"}, "System.Runtime.Serialization.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Wz+0KOukJGAlXjtKr+5Xpuxf8+c8739RI1C+A2BoQZT+wMCCoMDDdO8/4IRHfaVINqL78GO8dW8G2lW/e45Mcw==", "path": "system.runtime.serialization.primitives/4.3.0", "hashPath": "system.runtime.serialization.primitives.4.3.0.nupkg.sha512"}, "System.Runtime.Serialization.Xml/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-nUQx/5OVgrqEba3+j7OdiofvVq9koWZAC7Z3xGI8IIViZqApWnZ5+lLcwYgTlbkobrl/Rat+Jb8GeD4WQESD2A==", "path": "system.runtime.serialization.xml/4.3.0", "hashPath": "system.runtime.serialization.xml.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-vW8Eoq0TMyz5vAG/6ce483x/CP83fgm4SJe5P8Tb1tZaobcvPrbMEL7rhH1DRdrYbbb6F0vq3OlzmK0Pkwks5A==", "path": "system.security.accesscontrol/4.5.0", "hashPath": "system.security.accesscontrol.4.5.0.nupkg.sha512"}, "System.Security.Cryptography.Algorithms/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-W1kd2Y8mYSCgc3ULTAZ0hOP2dSdG5YauTb1089T0/kRcN2MpSAW1izOFROrJgxSlMn3ArsgHXagigyi+ibhevg==", "path": "system.security.cryptography.algorithms/4.3.0", "hashPath": "system.security.cryptography.algorithms.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-WG3r7EyjUe9CMPFSs6bty5doUqT+q9pbI80hlNzo2SkPkZ4VTuZkGWjpp77JB8+uaL4DFPRdBsAY+DX3dBK92A==", "path": "system.security.cryptography.cng/4.5.0", "hashPath": "system.security.cryptography.cng.4.5.0.nupkg.sha512"}, "System.Security.Cryptography.Csp/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X4s/FCkEUnRGnwR3aSfVIkldBmtURMhmexALNTwpjklzxWU7yjMk7GHLKOZTNkgnWnE0q7+BCf9N2LVRWxewaA==", "path": "system.security.cryptography.csp/4.3.0", "hashPath": "system.security.cryptography.csp.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "path": "system.security.cryptography.encoding/4.3.0", "hashPath": "system.security.cryptography.encoding.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-h4CEgOgv5PKVF/HwaHzJRiVboL2THYCou97zpmhjghx5frc7fIvlkY1jL+lnIQyChrJDMNEXS6r7byGif8Cy4w==", "path": "system.security.cryptography.openssl/4.3.0", "hashPath": "system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/4.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-lIo52x0AAsZs8r1L58lPXaqN6PP51Z/XJts0kZtbZRNYcMguupxqRGjvc/GoqSKTbYa+aBwbkT4xoqQ7EsfN0A==", "path": "system.security.cryptography.pkcs/4.5.2", "hashPath": "system.security.cryptography.pkcs.4.5.2.nupkg.sha512"}, "System.Security.Cryptography.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "path": "system.security.cryptography.primitives/4.3.0", "hashPath": "system.security.cryptography.primitives.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xSPiLNlHT6wAHtugASbKAJwV5GVqQK351crnILAucUioFqqieDN79evO1rku1ckt/GfjIn+b17UaSskoY03JuA==", "path": "system.security.cryptography.protecteddata/7.0.0", "hashPath": "system.security.cryptography.protecteddata.7.0.0.nupkg.sha512"}, "System.Security.Cryptography.X509Certificates/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-t2Tmu6Y2NtJ2um0RtcuhP7ZdNNxXEgUm2JeoA/0NvlMjAhKCnM1NX07TDl3244mVp3QU6LPEhT3HTtH1uF7IYw==", "path": "system.security.cryptography.x509certificates/4.3.0", "hashPath": "system.security.cryptography.x509certificates.4.3.0.nupkg.sha512"}, "System.Security.Permissions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Vmp0iRmCEno9BWiskOW5pxJ3d9n+jUqKxvX4GhLwFhnQaySZmBN2FuC0N5gjFHgyFMUjC5sfIJ8KZfoJwkcMmA==", "path": "system.security.permissions/7.0.0", "hashPath": "system.security.permissions.7.0.0.nupkg.sha512"}, "System.Security.Principal.Windows/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-L3R/xk1s+AJB/ZRrwXyjXMmKp+RNgLP92LSidWEM0iCouznJoiiBloDCxRTjmo8hTDAKSWaMrltsttUzg1RhjA==", "path": "system.security.principal.windows/4.5.1", "hashPath": "system.security.principal.windows.4.5.1.nupkg.sha512"}, "System.Security.SecureString/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PnXp38O9q/2Oe4iZHMH60kinScv6QiiL2XH54Pj2t0Y6c2zKPEiAZsM/M3wBOHLNTBDFP0zfy13WN2M0qFz5jg==", "path": "system.security.securestring/4.3.0", "hashPath": "system.security.securestring.4.3.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-4J2JQXbftjPMppIHJ7IC+VXQ9XfEagN92vZZNoG12i+zReYlim5dMoXFC1Zzg7tsnKDM7JPo5bYfFK4Jheq44w==", "path": "system.text.encoding.codepages/4.5.1", "hashPath": "system.text.encoding.codepages.4.5.1.nupkg.sha512"}, "System.Text.Encoding.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YVMK0Bt/A43RmwizJoZ22ei2nmrhobgeiYwFzC4YAN+nue8RF6djXDMog0UCn+brerQoYVyaS+ghy9P/MUVcmw==", "path": "system.text.encoding.extensions/4.3.0", "hashPath": "system.text.encoding.extensions.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/4.7.2": {"type": "package", "serviceable": true, "sha512": "sha512-iTUgB/WtrZ1sWZs84F2hwyQhiRH6QNjQv2DkwrH+WP6RoFga2Q1m3f9/Q7FG8cck8AdHitQkmkXSY8qylcDmuA==", "path": "system.text.encodings.web/4.7.2", "hashPath": "system.text.encodings.web.4.7.2.nupkg.sha512"}, "System.Text.Json/4.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-4F8Xe+JIkVoDJ8hDAZ7HqLkjctN/6WItJIzQaifBwClC7wmoLSda/Sv2i6i1kycqDb3hWF4JCVbpAweyOKHEUA==", "path": "system.text.json/4.6.0", "hashPath": "system.text.json.4.6.0.nupkg.sha512"}, "System.Text.RegularExpressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-RpT2DA+L660cBt1FssIE9CAGpLFdFPuheB7pLpKpn6ZXNby7jDERe8Ua/Ne2xGiwLVG2JOqziiaVCGDon5sKFA==", "path": "system.text.regularexpressions/4.3.0", "hashPath": "system.text.regularexpressions.4.3.0.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "System.Threading.ThreadPool/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-k/+g4b7vjdd4aix83sTgC9VG6oXYKAktSfNIJUNGxPEj7ryEOfzHHhfnmsZvjxawwcD9HyWXKCXmPjX8U4zeSw==", "path": "system.threading.threadpool/4.3.0", "hashPath": "system.threading.threadpool.4.3.0.nupkg.sha512"}, "System.Threading.Timer/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Z6YfyYTCg7lOZjJzBjONJTFKGN9/NIYKSxhU5GRd+DTwHSZyvWp1xuI5aR+dLg+ayyC5Xv57KiY4oJ0tMO89fQ==", "path": "system.threading.timer/4.3.0", "hashPath": "system.threading.timer.4.3.0.nupkg.sha512"}, "System.ValueTuple/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-okurQJO6NRE/apDIP23ajJ0hpiNmJ+f0BwOlB/cSqTLQlw5upkf+5+96+iG2Jw40G1fCVCyPz/FhIABUjMR+RQ==", "path": "system.valuetuple/4.5.0", "hashPath": "system.valuetuple.4.5.0.nupkg.sha512"}, "System.Windows.Extensions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bR4qdCmssMMbo9Fatci49An5B1UaVJZHKNq70PRgzoLYIlitb8Tj7ns/Xt5Pz1CkERiTjcVBDU2y1AVrPBYkaw==", "path": "system.windows.extensions/7.0.0", "hashPath": "system.windows.extensions.7.0.0.nupkg.sha512"}, "System.Xml.ReaderWriter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GrprA+Z0RUXaR4N7/eW71j1rgMnEnEVlgii49GZyAjTH7uliMnrOU3HNFBr6fEDBCJCIdlVNq9hHbaDR621XBA==", "path": "system.xml.readerwriter/4.3.0", "hashPath": "system.xml.readerwriter.4.3.0.nupkg.sha512"}, "System.Xml.XDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5zJ0XDxAIg8iy+t4aMnQAu0MqVbqyvfoUVl1yDV61xdo3Vth45oA2FoY4pPkxYAH5f8ixpmTqXeEIya95x0aCQ==", "path": "system.xml.xdocument/4.3.0", "hashPath": "system.xml.xdocument.4.3.0.nupkg.sha512"}, "System.Xml.XmlDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lJ8AxvkX7GQxpC6GFCeBj8ThYVyQczx2+f/cWHJU8tjS7YfI6Cv6bon70jVEgs2CiFbmmM8b9j1oZVx0dSI2Ww==", "path": "system.xml.xmldocument/4.3.0", "hashPath": "system.xml.xmldocument.4.3.0.nupkg.sha512"}, "System.Xml.XmlSerializer/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-MYoTCP7EZ98RrANESW05J5ZwskKDoN0AuZ06ZflnowE50LTpbR5yRg3tHckTVm5j/m47stuGgCrCHWePyHS70Q==", "path": "system.xml.xmlserializer/4.3.0", "hashPath": "system.xml.xmlserializer.4.3.0.nupkg.sha512"}, "Microsoft.Exchange.Management.AdminApiProvider/0.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.Exchange.Management.RestApiClient/0.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.Online.CSE.RestApiPowerShellModule.Instrumentation/0.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.Exchange.Management.AdminApiProvider.Reference/********": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Exchange.Management.RestApiClient.Reference/********": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Online.CSE.RestApiPowerShellModule.Instrumentation.Reference/********": {"type": "reference", "serviceable": false, "sha512": ""}}}