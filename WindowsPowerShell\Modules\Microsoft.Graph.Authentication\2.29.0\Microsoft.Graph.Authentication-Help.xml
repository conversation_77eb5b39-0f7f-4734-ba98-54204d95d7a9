<?xml version="1.0" encoding="utf-8"?>
<helpItems schema="maml" xmlns="http://msh">
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Find-MgGraphCommand</command:name>
      <command:verb>Find</command:verb>
      <command:noun>MgGraphCommand</command:noun>
      <maml:description>
        <maml:para>Find-MgGraphCommand aims to make it easier for you to discover which API path a command calls, by providing a URI or a command name. The Find-MgGraphCommand allows to:  - Pass a Microsoft Graph URL (relative and absolute) and get an equivalent Microsoft Graph PowerShell command. - Pass a command and get the URL it calls. - Pass a command or URI wildcard (.*) to find all commands that match it.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>Find-MgGraphCommand aims to make it easier for you to discover which API path a command calls, by providing a URI or a command name.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Find-MgGraphCommand</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ApiVersion</maml:name>
          <maml:description>
            <maml:para>The service API version.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Command</maml:name>
          <maml:description>
            <maml:para>The name of a command. e.g., Get-MgUser.}</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
          <dev:type>
            <maml:name>String[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="proga">
          <maml:name>ProgressAction</maml:name>
          <maml:description>
            <maml:para>Treat this as a common parameter.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
          <dev:type>
            <maml:name>ActionPreference</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
      <command:syntaxItem>
        <maml:name>Find-MgGraphCommand</maml:name>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByValue)" position="1" aliases="none">
          <maml:name>InputObject</maml:name>
          <maml:description>
            <maml:para>Pipeline input object</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">Object[]</command:parameterValue>
          <dev:type>
            <maml:name>Object[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ApiVersion</maml:name>
          <maml:description>
            <maml:para>The service API version.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="proga">
          <maml:name>ProgressAction</maml:name>
          <maml:description>
            <maml:para>Treat this as a common parameter.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
          <dev:type>
            <maml:name>ActionPreference</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
      <command:syntaxItem>
        <maml:name>Find-MgGraphCommand</maml:name>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByValue)" position="1" aliases="none">
          <maml:name>Uri</maml:name>
          <maml:description>
            <maml:para>The API path a command calls. e.g., /users.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
          <dev:type>
            <maml:name>String[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ApiVersion</maml:name>
          <maml:description>
            <maml:para>The service API version.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Method</maml:name>
          <maml:description>
            <maml:para>The HTTP method a command makes.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="proga">
          <maml:name>ProgressAction</maml:name>
          <maml:description>
            <maml:para>Treat this as a common parameter.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
          <dev:type>
            <maml:name>ActionPreference</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>ApiVersion</maml:name>
        <maml:description>
          <maml:para>The service API version.</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Command</maml:name>
        <maml:description>
          <maml:para>The name of a command. e.g., Get-MgUser.}</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
        <dev:type>
          <maml:name>String[]</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByValue)" position="1" aliases="none">
        <maml:name>InputObject</maml:name>
        <maml:description>
          <maml:para>Pipeline input object</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">Object[]</command:parameterValue>
        <dev:type>
          <maml:name>Object[]</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Method</maml:name>
        <maml:description>
          <maml:para>The HTTP method a command makes.</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="proga">
        <maml:name>ProgressAction</maml:name>
        <maml:description>
          <maml:para>Treat this as a common parameter.</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
        <dev:type>
          <maml:name>ActionPreference</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByValue)" position="1" aliases="none">
        <maml:name>Uri</maml:name>
        <maml:description>
          <maml:para>The API path a command calls. e.g., /users.</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
        <dev:type>
          <maml:name>String[]</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>Pipeline input accepts API URIs as an array of strings.</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>Microsoft.Graph.PowerShell.Authentication.Models.IGraphCommand with the following properties:</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
      <command:returnValue>
        <dev:type>
          <maml:name>1. Command: Name of command.</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
      <command:returnValue>
        <dev:type>
          <maml:name>2. Module: Module in which a command is defined.</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
      <command:returnValue>
        <dev:type>
          <maml:name>3. Method: The HTTP method a command makes.</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
      <command:returnValue>
        <dev:type>
          <maml:name>4. Uri: The Microsoft Graph API URI a command calls.</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
      <command:returnValue>
        <dev:type>
          <maml:name>5. OutputType: The return type of a command.</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
      <command:returnValue>
        <dev:type>
          <maml:name>6. Permissions: Permissions needed to use a command. This field can be empty if the permissions are not yet available in Graph Explorer.</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
      <command:returnValue>
        <dev:type>
          <maml:name>7. Variants: The parameter sets of a command.</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>------- Example 1: Use a URI to get all related cmdlets -------</maml:title>
        <dev:code>PS C:\&gt; Find-MgGraphCommand -Uri "/users/{id}"

 APIVersion: v1.0

Command       Module Method URI              OutputType           Permissions                                                                                                                                                    Variants
-------       ------ ------ ---              ----------           -----------                                                                                                                                                    --------
Get-MgUser    Users  GET    /users/{user-id} IMicrosoftGraphUser1 {DeviceManagementApps.Read.All, DeviceManagementApps.ReadWrite.All, DeviceManagementManagedDevices.Read.All, DeviceManagementManagedDevices.ReadWrite.All...}  {Get1, GetViaIdentity1}
Remove-MgUser Users  DELETE /users/{user-id}                      {DeviceManagementApps.ReadWrite.All, DeviceManagementManagedDevices.ReadWrite.All, DeviceManagementServiceConfig.ReadWrite.All, Directory.AccessAsUser.All}    {Delete, DeleteViaIdentity}
Update-MgUser Users  PATCH  /users/{user-id}                      {DeviceManagementApps.ReadWrite.All, DeviceManagementManagedDevices.ReadWrite.All, DeviceManagementServiceConfig.ReadWrite.All, Directory.AccessAsUser.All...} {Update, UpdateExpanded, UpdateViaIdentity, UpdateViaIdentityExpanded}


   APIVersion: beta

Command       Module Method URI              OutputType          Permissions                                                                                                                                                    Variants
-------       ------ ------ ---              ----------          -----------                                                                                                                                                    --------
Get-MgUser    Users  GET    /users/{user-id} IMicrosoftGraphUser {DeviceManagementApps.Read.All, DeviceManagementApps.ReadWrite.All, DeviceManagementManagedDevices.Read.All, DeviceManagementManagedDevices.ReadWrite.All...}  {Get, GetViaIdentity}
Remove-MgUser Users  DELETE /users/{user-id}                     {DeviceManagementApps.ReadWrite.All, DeviceManagementManagedDevices.ReadWrite.All, DeviceManagementServiceConfig.ReadWrite.All, Directory.AccessAsUser.All}    {Delete1, DeleteViaIdentity1}
Update-MgUser Users  PATCH  /users/{user-id}                     {DeviceManagementApps.ReadWrite.All, DeviceManagementManagedDevices.ReadWrite.All, DeviceManagementServiceConfig.ReadWrite.All, Directory.AccessAsUser.All...} {Update1, UpdateExpanded1, UpdateViaIdentity1, UpdateViaIdentityExpanded1}</dev:code>
        <dev:remarks>
          <maml:para>This example finds all commands that call the provided Microsoft Graph URI.</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>------ Example 2: Pass a command and get the URI it calls ------</maml:title>
        <dev:code>PS C:\&gt; Find-MgGraphCommand -Command 'Get-MgUser'

APIVersion: v1.0

Command    Module Method URI              OutputType           Permissions                                                                                                                                                   Variants
-------    ------ ------ ---              ----------           -----------                                                                                                                                                   --------
Get-MgUser Users  GET    /users           IMicrosoftGraphUser1 {DeviceManagementApps.Read.All, DeviceManagementApps.ReadWrite.All, DeviceManagementManagedDevices.Read.All, DeviceManagementManagedDevices.ReadWrite.All...} {List1}
Get-MgUser Users  GET    /users/{user-id} IMicrosoftGraphUser1 {DeviceManagementApps.Read.All, DeviceManagementApps.ReadWrite.All, DeviceManagementManagedDevices.Read.All, DeviceManagementManagedDevices.ReadWrite.All...} {Get1, GetViaIdentity1}


   APIVersion: beta

Command    Module Method URI              OutputType          Permissions                                                                                                                                                   Variants
-------    ------ ------ ---              ----------          -----------                                                                                                                                                   --------
Get-MgUser Users  GET    /users/{user-id} IMicrosoftGraphUser {DeviceManagementApps.Read.All, DeviceManagementApps.ReadWrite.All, DeviceManagementManagedDevices.Read.All, DeviceManagementManagedDevices.ReadWrite.All...} {Get, GetViaIdentity}
Get-MgUser Users  GET    /users           IMicrosoftGraphUser {DeviceManagementApps.Read.All, DeviceManagementApps.ReadWrite.All, DeviceManagementManagedDevices.Read.All, DeviceManagementManagedDevices.ReadWrite.All...} {List}</dev:code>
        <dev:remarks>
          <maml:para>This example looks up a command with the provided command name that calls both beta and v1.0 version of the API.</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>-- Example 3: Pass a command and get the permissions required --</maml:title>
        <dev:code>PS C:\&gt; Find-MgGraphCommand -command Get-MgUser | Select -First 1 -ExpandProperty Permissions

Name                                         IsAdmin Description                                   FullDescription
----                                         ------- -----------                                   ---------------
Directory.AccessAsUser.All                   True    Access the directory as you                   Allows the app to have the same access to information in your work or school directory as you do.
Directory.Read.All                           True    Read directory data                           Allows the app to read data in your organization's directory.
Directory.ReadWrite.All                      True    Read and write directory data                 Allows the app to read and write data in your organization's directory, such as other users, groups.  It does not allow the app to delete users or groups, or reset user...
User.Read.All                                True    Read all users' full profiles                 Allows the app to read the full set of profile properties, reports, and managers of other users in your organization, on your behalf.
User.ReadBasic.All                           False   Read all users' basic profiles                Allows the app to read a basic set of profile properties of other users in your organization on your behalf. Includes display name, first and last name, email address a...
User.ReadWrite.All                           True    Read and write all users' full profiles       Allows the app to read and write the full set of profile properties, reports, and managers of other users in your organization, on your behalf.</dev:code>
        <dev:remarks>
          <maml:para>This example retrieves the scopes required for a particular command.</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>Example 4: Find Microsoft Graph PowerShell commands using a command wildcard</maml:title>
        <dev:code>PS C:\&gt; Find-MgGraphCommand -Command .*UserToDo.* -APIVersion 'v1.0'
   APIVersion: v1.0

Command                                        Module          Method URI
-------                                        ------          ------ ---
Get-MgUserTodoList                             Users           GET    /users/{user-id}/todo/lists
Get-MgUserTodoList                             Users           GET    /users/{user-id}/todo/lists/{todoTaskList-id}
Get-MgUserTodoListDelta                        Users.Functions GET    /users/{user-id}/todo/lists/delta
Get-MgUserTodoListExtension                    Users           GET    /users/{user-id}/todo/lists/{todoTaskList-id}/extensions
Get-MgUserTodoListExtension                    Users           GET    /users/{user-id}/todo/lists/{todoTaskList-id}/extensions/{extension-id}
Get-MgUserTodoListTask                         Users           GET    /users/{user-id}/todo/lists/{todoTaskList-id}/tasks
Get-MgUserTodoListTask                         Users           GET    /users/{user-id}/todo/lists/{todoTaskList-id}/tasks/{todoTask-id}</dev:code>
        <dev:remarks>
          <maml:para>Uses a wildcard syntax to search for commands.</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>Example 5: Find Microsoft Graph PowerShell commands using a URI wildcard</maml:title>
        <dev:code>PS C:\&gt; Find-MgGraphCommand -Uri ".*users.*" -Method 'Get' -ApiVersion 'v1.0'
Command                               Module                       Method URI
-------                               ------                       ------ ---
Get-MgUser                            Users                        GET    /users/{user-id}
Get-MgUser                            Users                        GET    /users
Get-MgUserActivity                    CrossDeviceExperiences       GET    /users/{user-id}/activities/{userActivity-id}
Get-MgUserActivity                    CrossDeviceExperiences       GET    /users/{user-id}/activities
Get-MgUserActivityHistoryItem         CrossDeviceExperiences       GET    /users/{user-id}/activities/{userActivity-id}/historyItems/{activityHistoryItem-id}</dev:code>
        <dev:remarks>
          <maml:para>Searches for commands using URI wildcard.</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks>
      <maml:navigationLink>
        <maml:linkText>https://learn.microsoft.com/en-us/powershell/module/microsoft.graph.authentication/find-mggraphcommand</maml:linkText>
        <maml:uri>https://learn.microsoft.com/en-us/powershell/module/microsoft.graph.authentication/find-mggraphcommand</maml:uri>
      </maml:navigationLink>
    </command:relatedLinks>
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Find-MgGraphPermission</command:name>
      <command:verb>Find</command:verb>
      <command:noun>MgGraphPermission</command:noun>
      <maml:description>
        <maml:para>The Microsoft Graph PowerShell SDK application requires users to have domain knowledge of both the semantics and syntax of Microsoft Graph API permissions used to authorize access to the API. This cmdlet helps to answer the following questions:  - How do I find the values to supply to the permission-related parameters of commands like New-MgApplication and other application and consent related commands? - What permissions are applicable to a certain domain, for example, application, directory? To use Microsoft Graph PowerShell SDK to access Microsoft Graph, users must sign in to an Azure AD application using the Connect-MgGraph command. Use the Find-MgGraphCommand to find which permissions to use for a specific cmdlet or API.-  Currently PowerShell commands and scripts, including those implemented with Microsoft Graph PowerShell SDK itself, have no way of validating user input that refers to permissions or providing "auto-complete" user experiences to help users accurately supply input to commands</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>Retrieves permissions that are applicable to a certain domain. For example application, directory.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Find-MgGraphPermission</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>All</maml:name>
          <maml:description>
            <maml:para>To return all possible permissions rather than just those that match the SearchString parameter, specify the All parameter. The All parameter may also be used with the PermissionType to enumerate all applicaition permissions or all delegated permissions.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Online</maml:name>
          <maml:description>
            <maml:para>Specify the Online parameter in addition to SearchString to force Find-MgGraphPermission to update its set of permissions by requesting the latest permissions data from Microsoft Graph itself before searching for the permissions specified the SearchString parameter. This ensures that Find-MgGraphPermission returns the most accurate search results as new permissions are added to Microsoft Graph for new APIs. The command uses the existing access to Microsoft Graph as enabled by a previous invocation of the Connect-MgGraph command to issue the request for updated permissions. If your current connection does not already have access to read this data from Microsoft Graph or if there is no network connectivity to Microsoft Graph, the command will fail. If the command is successful in updating the set of permissions prior to searching for permissions, Find-MgGraphPermission will continue to use the updated list for all future invocations of the command even if they do not specify the Online parameter</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>PermissionType</maml:name>
          <maml:description>
            <maml:para>Specify the PermissionType to determine whether application permissions, delegated permisisons, or both are returned by Find-MgGraphPermission. By default, the value of this parameter is Any, which includes both delegated and application permissions. Other valid values for PermissionType are Application and Delegated to return those specify types of permissions.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="proga">
          <maml:name>ProgressAction</maml:name>
          <maml:description>
            <maml:para>Treat this as a common parameter.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
          <dev:type>
            <maml:name>ActionPreference</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
      <command:syntaxItem>
        <maml:name>Find-MgGraphPermission</maml:name>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByValue)" position="1" aliases="none">
          <maml:name>SearchString</maml:name>
          <maml:description>
            <maml:para>The SearchString parameter allows you to specify a string such as 'user' or 'mail' that represents the subject or domain of the permission you're searching for. Since permissions usually have names such as 'User.Read' or 'Mail.ReadWrite', the command uses the SearchString parameter to return all permissions that contain the value specified for SearchString in the name of the permission.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ExactMatch</maml:name>
          <maml:description>
            <maml:para>Specify the ExactMatch parameter to restrict the permissions emitted to those that exactly match the value specified for SearchString.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Online</maml:name>
          <maml:description>
            <maml:para>Specify the Online parameter in addition to SearchString to force Find-MgGraphPermission to update its set of permissions by requesting the latest permissions data from Microsoft Graph itself before searching for the permissions specified the SearchString parameter. This ensures that Find-MgGraphPermission returns the most accurate search results as new permissions are added to Microsoft Graph for new APIs. The command uses the existing access to Microsoft Graph as enabled by a previous invocation of the Connect-MgGraph command to issue the request for updated permissions. If your current connection does not already have access to read this data from Microsoft Graph or if there is no network connectivity to Microsoft Graph, the command will fail. If the command is successful in updating the set of permissions prior to searching for permissions, Find-MgGraphPermission will continue to use the updated list for all future invocations of the command even if they do not specify the Online parameter</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>PermissionType</maml:name>
          <maml:description>
            <maml:para>Specify the PermissionType to determine whether application permissions, delegated permisisons, or both are returned by Find-MgGraphPermission. By default, the value of this parameter is Any, which includes both delegated and application permissions. Other valid values for PermissionType are Application and Delegated to return those specify types of permissions.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="proga">
          <maml:name>ProgressAction</maml:name>
          <maml:description>
            <maml:para>Treat this as a common parameter.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
          <dev:type>
            <maml:name>ActionPreference</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>All</maml:name>
        <maml:description>
          <maml:para>To return all possible permissions rather than just those that match the SearchString parameter, specify the All parameter. The All parameter may also be used with the PermissionType to enumerate all applicaition permissions or all delegated permissions.</maml:para>
        </maml:description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>ExactMatch</maml:name>
        <maml:description>
          <maml:para>Specify the ExactMatch parameter to restrict the permissions emitted to those that exactly match the value specified for SearchString.</maml:para>
        </maml:description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Online</maml:name>
        <maml:description>
          <maml:para>Specify the Online parameter in addition to SearchString to force Find-MgGraphPermission to update its set of permissions by requesting the latest permissions data from Microsoft Graph itself before searching for the permissions specified the SearchString parameter. This ensures that Find-MgGraphPermission returns the most accurate search results as new permissions are added to Microsoft Graph for new APIs. The command uses the existing access to Microsoft Graph as enabled by a previous invocation of the Connect-MgGraph command to issue the request for updated permissions. If your current connection does not already have access to read this data from Microsoft Graph or if there is no network connectivity to Microsoft Graph, the command will fail. If the command is successful in updating the set of permissions prior to searching for permissions, Find-MgGraphPermission will continue to use the updated list for all future invocations of the command even if they do not specify the Online parameter</maml:para>
        </maml:description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>PermissionType</maml:name>
        <maml:description>
          <maml:para>Specify the PermissionType to determine whether application permissions, delegated permisisons, or both are returned by Find-MgGraphPermission. By default, the value of this parameter is Any, which includes both delegated and application permissions. Other valid values for PermissionType are Application and Delegated to return those specify types of permissions.</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="proga">
        <maml:name>ProgressAction</maml:name>
        <maml:description>
          <maml:para>Treat this as a common parameter.</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
        <dev:type>
          <maml:name>ActionPreference</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByValue)" position="1" aliases="none">
        <maml:name>SearchString</maml:name>
        <maml:description>
          <maml:para>The SearchString parameter allows you to specify a string such as 'user' or 'mail' that represents the subject or domain of the permission you're searching for. Since permissions usually have names such as 'User.Read' or 'Mail.ReadWrite', the command uses the SearchString parameter to return all permissions that contain the value specified for SearchString in the name of the permission.</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>You can pipe permission names in the form of strings to Find-MgGraphPermission.</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>This command returns a collection of items with the following fields:</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
      <command:returnValue>
        <dev:type>
          <maml:name>* Name: The name of the permission as found in Microsoft Graph permissions reference documentation. Names will typically</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
      <command:returnValue>
        <dev:type>
          <maml:name>have a form like 'User.Read.All', or 'Files.ReadWrite' for instance.</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
      <command:returnValue>
        <dev:type>
          <maml:name>* Description: Provides a description of what access is granted by the permission</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
      <command:returnValue>
        <dev:type>
          <maml:name>* Id: The unique identifier for the permission in the form of a Guid. The unique identifier is required for using</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
      <command:returnValue>
        <dev:type>
          <maml:name>certain Microsoft Graph REST API resources or Microsoft Graph-based PowerShell commands that manage application consent.</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
      <command:returnValue>
        <dev:type>
          <maml:name>* Consent: Denotes whether the specified permission requires administrator or user consent. The valid values are 'Admin' and 'User'.</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
      <command:returnValue>
        <dev:type>
          <maml:name>* PermissionType: Valid values are 'Delegated' or 'Application' depending on whether the permission is one that is delegated by</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
      <command:returnValue>
        <dev:type>
          <maml:name>the user to the application ('Delegated'), or is directly assigned to the application without the consent of a user ('Application').</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>---- Example 1: Find permissions related to a given domain ----</maml:title>
        <dev:code>PS C:\&gt; Find-MgGraphPermission application
PermissionType: Delegated

Id                                   Consent Name                                      Description
--                                   ------- ----                                      -----------
c79f8feb-a9db-4090-85f9-90d820caa0eb Admin   Application.Read.All                      Allows the app to read applications and service principals on behalf of the signed-in user.
bdfbf15f-ee85-4955-8675-146e8e5296b5 Admin   Application.ReadWrite.All                 Allows the app to create, read, update and delete applications and service principals on behalf of the signed-in user. Does not allow management of consent grants.
b27add92-efb2-4f16-84f5-8108ba77985c Admin   Policy.ReadWrite.ApplicationConfiguration Allows the app to read and write your organization's application configuration policies on behalf of the signed-in user.  This includes policies such as activityBasedTimeoutPolicy, claimsMappingPolicy, homeRealmDiscoveryPolicy,  tokenIssuancePolicy and tokenLifetimePolicy.


   PermissionType: Application

Id                                   Consent Name                                      Description
--                                   ------- ----                                      -----------
9a5d68dd-52b0-4cc2-bd40-abcf44ac3a30 Admin   Application.Read.All                      Allows the app to read all applications and service principals without a signed-in user.
1bfefb4e-e0b5-418b-a88f-73c46d2cc8e9 Admin   Application.ReadWrite.All                 Allows the app to create, read, update and delete applications and service principals without a signed-in user.  Does not allow management of consent grants.
18a4783c-866b-4cc7-a460-3d5e5662c884 Admin   Application.ReadWrite.OwnedBy             Allows the app to create other applications, and fully manage those applications (read, update, update application secrets and delete), without a signed-in user.  It cannot update any apps that it is not an owner of.
be74164b-cff1-491c-8741-e671cb536e13 Admin   Policy.ReadWrite.ApplicationConfiguration Allows the app to read and write your organization's application configuration policies, without a signed-in user.  This includes policies such as activityBasedTimeoutPolicy, claimsMappingPolicy, homeRealmDiscoveryPolicy, tokenIssuancePolicy  and tokenLifetimePolicy.</dev:code>
        <dev:remarks>
          <maml:para>Retrieves permissions related to a given domain.</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>--- Example 2: Find the identifier for a specific permission ---</maml:title>
        <dev:code>PS C:\&gt; Find-MgGraphPermission application.Read | Format-List
Id             : c79f8feb-a9db-4090-85f9-90d820caa0eb
PermissionType : Delegated
Consent        : Admin
Name           : Application.Read.All
Description    : Allows the app to read applications and service principals on behalf of the signed-in user.

Id             : bdfbf15f-ee85-4955-8675-146e8e5296b5
PermissionType : Delegated
Consent        : Admin
Name           : Application.ReadWrite.All
Description    : Allows the app to create, read, update and delete applications and service principals on behalf of the signed-in user. Does not allow management of consent grants.

Id             : 9a5d68dd-52b0-4cc2-bd40-abcf44ac3a30
PermissionType : Application
Consent        : Admin
Name           : Application.Read.All
Description    : Allows the app to read all applications and service principals without a signed-in user.</dev:code>
        <dev:remarks>
          <maml:para>Retrieves identifier for a specific permission.</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks>
      <maml:navigationLink>
        <maml:linkText>https://learn.microsoft.com/en-us/powershell/module/microsoft.graph.authentication/find-mggraphpermission</maml:linkText>
        <maml:uri>https://learn.microsoft.com/en-us/powershell/module/microsoft.graph.authentication/find-mggraphpermission</maml:uri>
      </maml:navigationLink>
    </command:relatedLinks>
  </command:command>
</helpItems>
