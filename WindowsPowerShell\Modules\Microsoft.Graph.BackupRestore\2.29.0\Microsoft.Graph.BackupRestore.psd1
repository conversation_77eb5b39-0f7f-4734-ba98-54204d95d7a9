#
# Module manifest for module 'Microsoft.Graph.BackupRestore'
#
# Generated by: Microsoft Corporation
#
# Generated on: 7/9/2025
#

@{

# Script module or binary module file associated with this manifest.
RootModule = './Microsoft.Graph.BackupRestore.psm1'

# Version number of this module.
ModuleVersion = '2.29.0'

# Supported PSEditions
CompatiblePSEditions = 'Core', 'Desktop'

# ID used to uniquely identify this module
GUID = 'a891fdeb-65dd-4a27-b9e2-a4b617ad6d2e'

# Author of this module
Author = 'Microsoft Corporation'

# Company or vendor of this module
CompanyName = 'Microsoft Corporation'

# Copyright statement for this module
Copyright = 'Microsoft Corporation. All rights reserved.'

# Description of the functionality provided by this module
Description = 'Microsoft Graph PowerShell Cmdlets'

# Minimum version of the PowerShell engine required by this module
PowerShellVersion = '5.1'

# Name of the PowerShell host required by this module
# PowerShellHostName = ''

# Minimum version of the PowerShell host required by this module
# PowerShellHostVersion = ''

# Minimum version of Microsoft .NET Framework required by this module. This prerequisite is valid for the PowerShell Desktop edition only.
DotNetFrameworkVersion = '4.7.2'

# Minimum version of the common language runtime (CLR) required by this module. This prerequisite is valid for the PowerShell Desktop edition only.
# ClrVersion = ''

# Processor architecture (None, X86, Amd64) required by this module
# ProcessorArchitecture = ''

# Modules that must be imported into the global environment prior to importing this module
RequiredModules = @(@{ModuleName = 'Microsoft.Graph.Authentication'; RequiredVersion = '2.29.0'; })

# Assemblies that must be loaded prior to importing this module
RequiredAssemblies = './bin/Microsoft.Graph.BackupRestore.private.dll'

# Script files (.ps1) that are run in the caller's environment prior to importing this module.
# ScriptsToProcess = @()

# Type files (.ps1xml) to be loaded when importing this module
# TypesToProcess = @()

# Format files (.ps1xml) to be loaded when importing this module
FormatsToProcess = './Microsoft.Graph.BackupRestore.format.ps1xml'

# Modules to import as nested modules of the module specified in RootModule/ModuleToProcess
# NestedModules = @()

# Functions to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no functions to export.
FunctionsToExport = 'Enable-MgSolutionBackupRestore', 'Get-MgSolutionBackupRestore', 
               'Get-MgSolutionBackupRestoreDriveInclusionRule', 
               'Get-MgSolutionBackupRestoreDriveInclusionRuleCount', 
               'Get-MgSolutionBackupRestoreDriveProtectionUnit', 
               'Get-MgSolutionBackupRestoreDriveProtectionUnitBulkAdditionJob', 
               'Get-MgSolutionBackupRestoreDriveProtectionUnitBulkAdditionJobCount', 
               'Get-MgSolutionBackupRestoreDriveProtectionUnitCount', 
               'Get-MgSolutionBackupRestoreExchangeProtectionPolicy', 
               'Get-MgSolutionBackupRestoreExchangeProtectionPolicyCount', 
               'Get-MgSolutionBackupRestoreExchangeProtectionPolicyMailboxInclusionRule', 
               'Get-MgSolutionBackupRestoreExchangeProtectionPolicyMailboxInclusionRuleCount', 
               'Get-MgSolutionBackupRestoreExchangeProtectionPolicyMailboxProtectionUnit', 
               'Get-MgSolutionBackupRestoreExchangeProtectionPolicyMailboxProtectionUnitBulkAdditionJob', 
               'Get-MgSolutionBackupRestoreExchangeProtectionPolicyMailboxProtectionUnitBulkAdditionJobCount', 
               'Get-MgSolutionBackupRestoreExchangeProtectionPolicyMailboxProtectionUnitCount', 
               'Get-MgSolutionBackupRestoreExchangeRestoreSession', 
               'Get-MgSolutionBackupRestoreExchangeRestoreSessionCount', 
               'Get-MgSolutionBackupRestoreExchangeRestoreSessionGranularMailboxRestoreArtifact', 
               'Get-MgSolutionBackupRestoreExchangeRestoreSessionGranularMailboxRestoreArtifactCount', 
               'Get-MgSolutionBackupRestoreExchangeRestoreSessionGranularMailboxRestoreArtifactRestorePoint', 
               'Get-MgSolutionBackupRestoreExchangeRestoreSessionMailboxRestoreArtifact', 
               'Get-MgSolutionBackupRestoreExchangeRestoreSessionMailboxRestoreArtifactBulkAdditionRequest', 
               'Get-MgSolutionBackupRestoreExchangeRestoreSessionMailboxRestoreArtifactBulkAdditionRequestCount', 
               'Get-MgSolutionBackupRestoreExchangeRestoreSessionMailboxRestoreArtifactCount', 
               'Get-MgSolutionBackupRestoreExchangeRestoreSessionMailboxRestoreArtifactRestorePoint', 
               'Get-MgSolutionBackupRestoreMailboxInclusionRule', 
               'Get-MgSolutionBackupRestoreMailboxInclusionRuleCount', 
               'Get-MgSolutionBackupRestoreMailboxProtectionUnit', 
               'Get-MgSolutionBackupRestoreMailboxProtectionUnitBulkAdditionJob', 
               'Get-MgSolutionBackupRestoreMailboxProtectionUnitBulkAdditionJobCount', 
               'Get-MgSolutionBackupRestoreMailboxProtectionUnitCount', 
               'Get-MgSolutionBackupRestoreOneDriveForBusinessProtectionPolicy', 
               'Get-MgSolutionBackupRestoreOneDriveForBusinessProtectionPolicyCount', 
               'Get-MgSolutionBackupRestoreOneDriveForBusinessProtectionPolicyDriveInclusionRule', 
               'Get-MgSolutionBackupRestoreOneDriveForBusinessProtectionPolicyDriveInclusionRuleCount', 
               'Get-MgSolutionBackupRestoreOneDriveForBusinessProtectionPolicyDriveProtectionUnit', 
               'Get-MgSolutionBackupRestoreOneDriveForBusinessProtectionPolicyDriveProtectionUnitBulkAdditionJob', 
               'Get-MgSolutionBackupRestoreOneDriveForBusinessProtectionPolicyDriveProtectionUnitBulkAdditionJobCount', 
               'Get-MgSolutionBackupRestoreOneDriveForBusinessProtectionPolicyDriveProtectionUnitCount', 
               'Get-MgSolutionBackupRestoreOneDriveForBusinessRestoreSession', 
               'Get-MgSolutionBackupRestoreOneDriveForBusinessRestoreSessionCount', 
               'Get-MgSolutionBackupRestoreOneDriveForBusinessRestoreSessionDriveRestoreArtifact', 
               'Get-MgSolutionBackupRestoreOneDriveForBusinessRestoreSessionDriveRestoreArtifactBulkAdditionRequest', 
               'Get-MgSolutionBackupRestoreOneDriveForBusinessRestoreSessionDriveRestoreArtifactBulkAdditionRequestCount', 
               'Get-MgSolutionBackupRestoreOneDriveForBusinessRestoreSessionDriveRestoreArtifactCount', 
               'Get-MgSolutionBackupRestoreOneDriveForBusinessRestoreSessionDriveRestoreArtifactRestorePoint', 
               'Get-MgSolutionBackupRestorePoint', 
               'Get-MgSolutionBackupRestorePointCount', 
               'Get-MgSolutionBackupRestorePointProtectionUnit', 
               'Get-MgSolutionBackupRestoreProtectionPolicy', 
               'Get-MgSolutionBackupRestoreProtectionPolicyCount', 
               'Get-MgSolutionBackupRestoreProtectionUnit', 
               'Get-MgSolutionBackupRestoreProtectionUnitAsDriveProtectionUnit', 
               'Get-MgSolutionBackupRestoreProtectionUnitAsMailboxProtectionUnit', 
               'Get-MgSolutionBackupRestoreProtectionUnitAsSiteProtectionUnit', 
               'Get-MgSolutionBackupRestoreProtectionUnitCount', 
               'Get-MgSolutionBackupRestoreProtectionUnitCountAsDriveProtectionUnit', 
               'Get-MgSolutionBackupRestoreProtectionUnitCountAsMailboxProtectionUnit', 
               'Get-MgSolutionBackupRestoreProtectionUnitCountAsSiteProtectionUnit', 
               'Get-MgSolutionBackupRestoreServiceApp', 
               'Get-MgSolutionBackupRestoreServiceAppCount', 
               'Get-MgSolutionBackupRestoreSession', 
               'Get-MgSolutionBackupRestoreSessionCount', 
               'Get-MgSolutionBackupRestoreSharePointProtectionPolicy', 
               'Get-MgSolutionBackupRestoreSharePointProtectionPolicyCount', 
               'Get-MgSolutionBackupRestoreSharePointProtectionPolicySiteInclusionRule', 
               'Get-MgSolutionBackupRestoreSharePointProtectionPolicySiteInclusionRuleCount', 
               'Get-MgSolutionBackupRestoreSharePointProtectionPolicySiteProtectionUnit', 
               'Get-MgSolutionBackupRestoreSharePointProtectionPolicySiteProtectionUnitBulkAdditionJob', 
               'Get-MgSolutionBackupRestoreSharePointProtectionPolicySiteProtectionUnitBulkAdditionJobCount', 
               'Get-MgSolutionBackupRestoreSharePointProtectionPolicySiteProtectionUnitCount', 
               'Get-MgSolutionBackupRestoreSharePointRestoreSession', 
               'Get-MgSolutionBackupRestoreSharePointRestoreSessionCount', 
               'Get-MgSolutionBackupRestoreSharePointRestoreSessionSiteRestoreArtifact', 
               'Get-MgSolutionBackupRestoreSharePointRestoreSessionSiteRestoreArtifactBulkAdditionRequest', 
               'Get-MgSolutionBackupRestoreSharePointRestoreSessionSiteRestoreArtifactBulkAdditionRequestCount', 
               'Get-MgSolutionBackupRestoreSharePointRestoreSessionSiteRestoreArtifactCount', 
               'Get-MgSolutionBackupRestoreSharePointRestoreSessionSiteRestoreArtifactRestorePoint', 
               'Get-MgSolutionBackupRestoreSiteInclusionRule', 
               'Get-MgSolutionBackupRestoreSiteInclusionRuleCount', 
               'Get-MgSolutionBackupRestoreSiteProtectionUnit', 
               'Get-MgSolutionBackupRestoreSiteProtectionUnitBulkAdditionJob', 
               'Get-MgSolutionBackupRestoreSiteProtectionUnitBulkAdditionJobCount', 
               'Get-MgSolutionBackupRestoreSiteProtectionUnitCount', 
               'Initialize-MgSolutionBackupRestoreProtectionPolicy', 
               'Initialize-MgSolutionBackupRestoreServiceApp', 
               'Initialize-MgSolutionBackupRestoreSession', 
               'Invoke-MgDeactivateSolutionBackupRestoreProtectionPolicy', 
               'Invoke-MgDeactivateSolutionBackupRestoreServiceApp', 
               'New-MgSolutionBackupRestoreDriveInclusionRule', 
               'New-MgSolutionBackupRestoreDriveProtectionUnit', 
               'New-MgSolutionBackupRestoreDriveProtectionUnitBulkAdditionJob', 
               'New-MgSolutionBackupRestoreExchangeProtectionPolicy', 
               'New-MgSolutionBackupRestoreExchangeRestoreSession', 
               'New-MgSolutionBackupRestoreExchangeRestoreSessionGranularMailboxRestoreArtifact', 
               'New-MgSolutionBackupRestoreExchangeRestoreSessionMailboxRestoreArtifact', 
               'New-MgSolutionBackupRestoreExchangeRestoreSessionMailboxRestoreArtifactBulkAdditionRequest', 
               'New-MgSolutionBackupRestoreMailboxInclusionRule', 
               'New-MgSolutionBackupRestoreMailboxProtectionUnit', 
               'New-MgSolutionBackupRestoreMailboxProtectionUnitBulkAdditionJob', 
               'New-MgSolutionBackupRestoreOneDriveForBusinessProtectionPolicy', 
               'New-MgSolutionBackupRestoreOneDriveForBusinessRestoreSession', 
               'New-MgSolutionBackupRestoreOneDriveForBusinessRestoreSessionDriveRestoreArtifact', 
               'New-MgSolutionBackupRestoreOneDriveForBusinessRestoreSessionDriveRestoreArtifactBulkAdditionRequest', 
               'New-MgSolutionBackupRestorePoint', 
               'New-MgSolutionBackupRestoreProtectionPolicy', 
               'New-MgSolutionBackupRestoreServiceApp', 
               'New-MgSolutionBackupRestoreSession', 
               'New-MgSolutionBackupRestoreSharePointProtectionPolicy', 
               'New-MgSolutionBackupRestoreSharePointRestoreSession', 
               'New-MgSolutionBackupRestoreSharePointRestoreSessionSiteRestoreArtifact', 
               'New-MgSolutionBackupRestoreSharePointRestoreSessionSiteRestoreArtifactBulkAdditionRequest', 
               'New-MgSolutionBackupRestoreSiteInclusionRule', 
               'New-MgSolutionBackupRestoreSiteProtectionUnit', 
               'New-MgSolutionBackupRestoreSiteProtectionUnitBulkAdditionJob', 
               'Remove-MgSolutionBackupRestore', 
               'Remove-MgSolutionBackupRestoreDriveInclusionRule', 
               'Remove-MgSolutionBackupRestoreDriveProtectionUnit', 
               'Remove-MgSolutionBackupRestoreDriveProtectionUnitBulkAdditionJob', 
               'Remove-MgSolutionBackupRestoreExchangeProtectionPolicy', 
               'Remove-MgSolutionBackupRestoreExchangeRestoreSession', 
               'Remove-MgSolutionBackupRestoreExchangeRestoreSessionGranularMailboxRestoreArtifact', 
               'Remove-MgSolutionBackupRestoreExchangeRestoreSessionMailboxRestoreArtifact', 
               'Remove-MgSolutionBackupRestoreExchangeRestoreSessionMailboxRestoreArtifactBulkAdditionRequest', 
               'Remove-MgSolutionBackupRestoreMailboxInclusionRule', 
               'Remove-MgSolutionBackupRestoreMailboxProtectionUnit', 
               'Remove-MgSolutionBackupRestoreMailboxProtectionUnitBulkAdditionJob', 
               'Remove-MgSolutionBackupRestoreOneDriveForBusinessProtectionPolicy', 
               'Remove-MgSolutionBackupRestoreOneDriveForBusinessRestoreSession', 
               'Remove-MgSolutionBackupRestoreOneDriveForBusinessRestoreSessionDriveRestoreArtifact', 
               'Remove-MgSolutionBackupRestoreOneDriveForBusinessRestoreSessionDriveRestoreArtifactBulkAdditionRequest', 
               'Remove-MgSolutionBackupRestorePoint', 
               'Remove-MgSolutionBackupRestoreProtectionPolicy', 
               'Remove-MgSolutionBackupRestoreServiceApp', 
               'Remove-MgSolutionBackupRestoreSession', 
               'Remove-MgSolutionBackupRestoreSharePointProtectionPolicy', 
               'Remove-MgSolutionBackupRestoreSharePointRestoreSession', 
               'Remove-MgSolutionBackupRestoreSharePointRestoreSessionSiteRestoreArtifact', 
               'Remove-MgSolutionBackupRestoreSharePointRestoreSessionSiteRestoreArtifactBulkAdditionRequest', 
               'Remove-MgSolutionBackupRestoreSiteInclusionRule', 
               'Remove-MgSolutionBackupRestoreSiteProtectionUnit', 
               'Remove-MgSolutionBackupRestoreSiteProtectionUnitBulkAdditionJob', 
               'Search-MgSolutionBackupRestorePoint', 
               'Update-MgSolutionBackupRestore', 
               'Update-MgSolutionBackupRestoreDriveInclusionRule', 
               'Update-MgSolutionBackupRestoreDriveProtectionUnit', 
               'Update-MgSolutionBackupRestoreDriveProtectionUnitBulkAdditionJob', 
               'Update-MgSolutionBackupRestoreExchangeProtectionPolicy', 
               'Update-MgSolutionBackupRestoreExchangeRestoreSession', 
               'Update-MgSolutionBackupRestoreExchangeRestoreSessionGranularMailboxRestoreArtifact', 
               'Update-MgSolutionBackupRestoreExchangeRestoreSessionMailboxRestoreArtifact', 
               'Update-MgSolutionBackupRestoreExchangeRestoreSessionMailboxRestoreArtifactBulkAdditionRequest', 
               'Update-MgSolutionBackupRestoreMailboxInclusionRule', 
               'Update-MgSolutionBackupRestoreMailboxProtectionUnit', 
               'Update-MgSolutionBackupRestoreMailboxProtectionUnitBulkAdditionJob', 
               'Update-MgSolutionBackupRestoreOneDriveForBusinessProtectionPolicy', 
               'Update-MgSolutionBackupRestoreOneDriveForBusinessRestoreSession', 
               'Update-MgSolutionBackupRestoreOneDriveForBusinessRestoreSessionDriveRestoreArtifact', 
               'Update-MgSolutionBackupRestoreOneDriveForBusinessRestoreSessionDriveRestoreArtifactBulkAdditionRequest', 
               'Update-MgSolutionBackupRestorePoint', 
               'Update-MgSolutionBackupRestoreProtectionPolicy', 
               'Update-MgSolutionBackupRestoreServiceApp', 
               'Update-MgSolutionBackupRestoreSession', 
               'Update-MgSolutionBackupRestoreSharePointProtectionPolicy', 
               'Update-MgSolutionBackupRestoreSharePointRestoreSession', 
               'Update-MgSolutionBackupRestoreSharePointRestoreSessionSiteRestoreArtifact', 
               'Update-MgSolutionBackupRestoreSharePointRestoreSessionSiteRestoreArtifactBulkAdditionRequest', 
               'Update-MgSolutionBackupRestoreSiteInclusionRule', 
               'Update-MgSolutionBackupRestoreSiteProtectionUnit', 
               'Update-MgSolutionBackupRestoreSiteProtectionUnitBulkAdditionJob'

# Cmdlets to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no cmdlets to export.
CmdletsToExport = @()

# Variables to export from this module
# VariablesToExport = @()

# Aliases to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no aliases to export.
AliasesToExport = 'Enable-MgBackupRestore', 
               'Initialize-MgBackupRestoreProtectionPolicy', 
               'Initialize-MgBackupRestoreServiceApp', 
               'Initialize-MgBackupRestoreSession', 'Search-MgBackupRestorePoint'

# DSC resources to export from this module
# DscResourcesToExport = @()

# List of all modules packaged with this module
# ModuleList = @()

# List of all files packaged with this module
# FileList = @()

# Private data to pass to the module specified in RootModule/ModuleToProcess. This may also contain a PSData hashtable with additional module metadata used by PowerShell.
PrivateData = @{

    PSData = @{

        # Tags applied to this module. These help with module discovery in online galleries.
        Tags = 'Microsoft','Office365','Graph','PowerShell','PSModule','PSIncludes_Cmdlet'

        # A URL to the license for this module.
        LicenseUri = 'https://aka.ms/devservicesagreement'

        # A URL to the main website for this project.
        ProjectUri = 'https://github.com/microsoftgraph/msgraph-sdk-powershell'

        # A URL to an icon representing this module.
        IconUri = 'https://raw.githubusercontent.com/microsoftgraph/msgraph-sdk-powershell/dev/docs/images/graph_color256.png'

        # ReleaseNotes of this module
        ReleaseNotes = 'See https://aka.ms/GraphPowerShell-Release.'

        # Prerelease string of this module
        # Prerelease = ''

        # Flag to indicate whether the module requires explicit user acceptance for install/update/save
        # RequireLicenseAcceptance = $false

        # External dependent modules of this module
        # ExternalModuleDependencies = @()

    } # End of PSData hashtable

 } # End of PrivateData hashtable

# HelpInfo URI of this module
# HelpInfoURI = ''

# Default prefix for commands exported from this module. Override the default prefix using Import-Module -Prefix.
# DefaultCommandPrefix = ''

}


# SIG # Begin signature block
# MIIoKQYJKoZIhvcNAQcCoIIoGjCCKBYCAQExDzANBglghkgBZQMEAgEFADB5Bgor
# BgEEAYI3AgEEoGswaTA0BgorBgEEAYI3AgEeMCYCAwEAAAQQH8w7YFlLCE63JNLG
# KX7zUQIBAAIBAAIBAAIBAAIBADAxMA0GCWCGSAFlAwQCAQUABCAg7MA1Koujlkwr
# tY9050FlDe6vGHRgH67CyFqlzmfwH6CCDXYwggX0MIID3KADAgECAhMzAAAEBGx0
# Bv9XKydyAAAAAAQEMA0GCSqGSIb3DQEBCwUAMH4xCzAJBgNVBAYTAlVTMRMwEQYD
# VQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNy
# b3NvZnQgQ29ycG9yYXRpb24xKDAmBgNVBAMTH01pY3Jvc29mdCBDb2RlIFNpZ25p
# bmcgUENBIDIwMTEwHhcNMjQwOTEyMjAxMTE0WhcNMjUwOTExMjAxMTE0WjB0MQsw
# CQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9u
# ZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMR4wHAYDVQQDExVNaWNy
# b3NvZnQgQ29ycG9yYXRpb24wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIB
# AQC0KDfaY50MDqsEGdlIzDHBd6CqIMRQWW9Af1LHDDTuFjfDsvna0nEuDSYJmNyz
# NB10jpbg0lhvkT1AzfX2TLITSXwS8D+mBzGCWMM/wTpciWBV/pbjSazbzoKvRrNo
# DV/u9omOM2Eawyo5JJJdNkM2d8qzkQ0bRuRd4HarmGunSouyb9NY7egWN5E5lUc3
# a2AROzAdHdYpObpCOdeAY2P5XqtJkk79aROpzw16wCjdSn8qMzCBzR7rvH2WVkvF
# HLIxZQET1yhPb6lRmpgBQNnzidHV2Ocxjc8wNiIDzgbDkmlx54QPfw7RwQi8p1fy
# 4byhBrTjv568x8NGv3gwb0RbAgMBAAGjggFzMIIBbzAfBgNVHSUEGDAWBgorBgEE
# AYI3TAgBBggrBgEFBQcDAzAdBgNVHQ4EFgQU8huhNbETDU+ZWllL4DNMPCijEU4w
# RQYDVR0RBD4wPKQ6MDgxHjAcBgNVBAsTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEW
# MBQGA1UEBRMNMjMwMDEyKzUwMjkyMzAfBgNVHSMEGDAWgBRIbmTlUAXTgqoXNzci
# tW2oynUClTBUBgNVHR8ETTBLMEmgR6BFhkNodHRwOi8vd3d3Lm1pY3Jvc29mdC5j
# b20vcGtpb3BzL2NybC9NaWNDb2RTaWdQQ0EyMDExXzIwMTEtMDctMDguY3JsMGEG
# CCsGAQUFBwEBBFUwUzBRBggrBgEFBQcwAoZFaHR0cDovL3d3dy5taWNyb3NvZnQu
# Y29tL3BraW9wcy9jZXJ0cy9NaWNDb2RTaWdQQ0EyMDExXzIwMTEtMDctMDguY3J0
# MAwGA1UdEwEB/wQCMAAwDQYJKoZIhvcNAQELBQADggIBAIjmD9IpQVvfB1QehvpC
# Ge7QeTQkKQ7j3bmDMjwSqFL4ri6ae9IFTdpywn5smmtSIyKYDn3/nHtaEn0X1NBj
# L5oP0BjAy1sqxD+uy35B+V8wv5GrxhMDJP8l2QjLtH/UglSTIhLqyt8bUAqVfyfp
# h4COMRvwwjTvChtCnUXXACuCXYHWalOoc0OU2oGN+mPJIJJxaNQc1sjBsMbGIWv3
# cmgSHkCEmrMv7yaidpePt6V+yPMik+eXw3IfZ5eNOiNgL1rZzgSJfTnvUqiaEQ0X
# dG1HbkDv9fv6CTq6m4Ty3IzLiwGSXYxRIXTxT4TYs5VxHy2uFjFXWVSL0J2ARTYL
# E4Oyl1wXDF1PX4bxg1yDMfKPHcE1Ijic5lx1KdK1SkaEJdto4hd++05J9Bf9TAmi
# u6EK6C9Oe5vRadroJCK26uCUI4zIjL/qG7mswW+qT0CW0gnR9JHkXCWNbo8ccMk1
# sJatmRoSAifbgzaYbUz8+lv+IXy5GFuAmLnNbGjacB3IMGpa+lbFgih57/fIhamq
# 5VhxgaEmn/UjWyr+cPiAFWuTVIpfsOjbEAww75wURNM1Imp9NJKye1O24EspEHmb
# DmqCUcq7NqkOKIG4PVm3hDDED/WQpzJDkvu4FrIbvyTGVU01vKsg4UfcdiZ0fQ+/
# V0hf8yrtq9CkB8iIuk5bBxuPMIIHejCCBWKgAwIBAgIKYQ6Q0gAAAAAAAzANBgkq
# hkiG9w0BAQsFADCBiDELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldhc2hpbmd0b24x
# EDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlv
# bjEyMDAGA1UEAxMpTWljcm9zb2Z0IFJvb3QgQ2VydGlmaWNhdGUgQXV0aG9yaXR5
# IDIwMTEwHhcNMTEwNzA4MjA1OTA5WhcNMjYwNzA4MjEwOTA5WjB+MQswCQYDVQQG
# EwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwG
# A1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSgwJgYDVQQDEx9NaWNyb3NvZnQg
# Q29kZSBTaWduaW5nIFBDQSAyMDExMIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIIC
# CgKCAgEAq/D6chAcLq3YbqqCEE00uvK2WCGfQhsqa+laUKq4BjgaBEm6f8MMHt03
# a8YS2AvwOMKZBrDIOdUBFDFC04kNeWSHfpRgJGyvnkmc6Whe0t+bU7IKLMOv2akr
# rnoJr9eWWcpgGgXpZnboMlImEi/nqwhQz7NEt13YxC4Ddato88tt8zpcoRb0Rrrg
# OGSsbmQ1eKagYw8t00CT+OPeBw3VXHmlSSnnDb6gE3e+lD3v++MrWhAfTVYoonpy
# 4BI6t0le2O3tQ5GD2Xuye4Yb2T6xjF3oiU+EGvKhL1nkkDstrjNYxbc+/jLTswM9
# sbKvkjh+0p2ALPVOVpEhNSXDOW5kf1O6nA+tGSOEy/S6A4aN91/w0FK/jJSHvMAh
# dCVfGCi2zCcoOCWYOUo2z3yxkq4cI6epZuxhH2rhKEmdX4jiJV3TIUs+UsS1Vz8k
# A/DRelsv1SPjcF0PUUZ3s/gA4bysAoJf28AVs70b1FVL5zmhD+kjSbwYuER8ReTB
# w3J64HLnJN+/RpnF78IcV9uDjexNSTCnq47f7Fufr/zdsGbiwZeBe+3W7UvnSSmn
# Eyimp31ngOaKYnhfsi+E11ecXL93KCjx7W3DKI8sj0A3T8HhhUSJxAlMxdSlQy90
# lfdu+HggWCwTXWCVmj5PM4TasIgX3p5O9JawvEagbJjS4NaIjAsCAwEAAaOCAe0w
# ggHpMBAGCSsGAQQBgjcVAQQDAgEAMB0GA1UdDgQWBBRIbmTlUAXTgqoXNzcitW2o
# ynUClTAZBgkrBgEEAYI3FAIEDB4KAFMAdQBiAEMAQTALBgNVHQ8EBAMCAYYwDwYD
# VR0TAQH/BAUwAwEB/zAfBgNVHSMEGDAWgBRyLToCMZBDuRQFTuHqp8cx0SOJNDBa
# BgNVHR8EUzBRME+gTaBLhklodHRwOi8vY3JsLm1pY3Jvc29mdC5jb20vcGtpL2Ny
# bC9wcm9kdWN0cy9NaWNSb29DZXJBdXQyMDExXzIwMTFfMDNfMjIuY3JsMF4GCCsG
# AQUFBwEBBFIwUDBOBggrBgEFBQcwAoZCaHR0cDovL3d3dy5taWNyb3NvZnQuY29t
# L3BraS9jZXJ0cy9NaWNSb29DZXJBdXQyMDExXzIwMTFfMDNfMjIuY3J0MIGfBgNV
# HSAEgZcwgZQwgZEGCSsGAQQBgjcuAzCBgzA/BggrBgEFBQcCARYzaHR0cDovL3d3
# dy5taWNyb3NvZnQuY29tL3BraW9wcy9kb2NzL3ByaW1hcnljcHMuaHRtMEAGCCsG
# AQUFBwICMDQeMiAdAEwAZQBnAGEAbABfAHAAbwBsAGkAYwB5AF8AcwB0AGEAdABl
# AG0AZQBuAHQALiAdMA0GCSqGSIb3DQEBCwUAA4ICAQBn8oalmOBUeRou09h0ZyKb
# C5YR4WOSmUKWfdJ5DJDBZV8uLD74w3LRbYP+vj/oCso7v0epo/Np22O/IjWll11l
# hJB9i0ZQVdgMknzSGksc8zxCi1LQsP1r4z4HLimb5j0bpdS1HXeUOeLpZMlEPXh6
# I/MTfaaQdION9MsmAkYqwooQu6SpBQyb7Wj6aC6VoCo/KmtYSWMfCWluWpiW5IP0
# wI/zRive/DvQvTXvbiWu5a8n7dDd8w6vmSiXmE0OPQvyCInWH8MyGOLwxS3OW560
# STkKxgrCxq2u5bLZ2xWIUUVYODJxJxp/sfQn+N4sOiBpmLJZiWhub6e3dMNABQam
# ASooPoI/E01mC8CzTfXhj38cbxV9Rad25UAqZaPDXVJihsMdYzaXht/a8/jyFqGa
# J+HNpZfQ7l1jQeNbB5yHPgZ3BtEGsXUfFL5hYbXw3MYbBL7fQccOKO7eZS/sl/ah
# XJbYANahRr1Z85elCUtIEJmAH9AAKcWxm6U/RXceNcbSoqKfenoi+kiVH6v7RyOA
# 9Z74v2u3S5fi63V4GuzqN5l5GEv/1rMjaHXmr/r8i+sLgOppO6/8MO0ETI7f33Vt
# Y5E90Z1WTk+/gFcioXgRMiF670EKsT/7qMykXcGhiJtXcVZOSEXAQsmbdlsKgEhr
# /Xmfwb1tbWrJUnMTDXpQzTGCGgkwghoFAgEBMIGVMH4xCzAJBgNVBAYTAlVTMRMw
# EQYDVQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVN
# aWNyb3NvZnQgQ29ycG9yYXRpb24xKDAmBgNVBAMTH01pY3Jvc29mdCBDb2RlIFNp
# Z25pbmcgUENBIDIwMTECEzMAAAQEbHQG/1crJ3IAAAAABAQwDQYJYIZIAWUDBAIB
# BQCgga4wGQYJKoZIhvcNAQkDMQwGCisGAQQBgjcCAQQwHAYKKwYBBAGCNwIBCzEO
# MAwGCisGAQQBgjcCARUwLwYJKoZIhvcNAQkEMSIEIBFrr2gFB78z3YtqsaJ0EYiX
# L2Jfdm7GFIiXpLvmXbwpMEIGCisGAQQBgjcCAQwxNDAyoBSAEgBNAGkAYwByAG8A
# cwBvAGYAdKEagBhodHRwOi8vd3d3Lm1pY3Jvc29mdC5jb20wDQYJKoZIhvcNAQEB
# BQAEggEAq4N9/1hhxof7h7g/3bgmYGugkzzIXvQVMS2W/94Kz+2ZA1e484IzS3Xg
# 5AbaaivQipnV7Qf0kNDxv7ghTUc1rp/X3z3oniPLwwoblIddOnP59+bMokSqGmG5
# KLKYHVE5w8zHF9m9RXt0zeWR8bdaMn13sJZ8+JzqsTf9rRLc/ydq1eU1JZIp1YDD
# ihlqiho2tmMZS5aC2KGCHGg5L3/WgChqX1SCpvRpAcZ7JcSGMMET00DHsjq6UuwN
# KW/z84SSSiZuJXyEM3uJMNnFmXun3+ylwmKMkBhVuqeKsHueNJsZ3iiU/x683gwB
# PuQ59EH18Myd3R9Zw7IpPxKOMhUi0KGCF5MwghePBgorBgEEAYI3AwMBMYIXfzCC
# F3sGCSqGSIb3DQEHAqCCF2wwghdoAgEDMQ8wDQYJYIZIAWUDBAIBBQAwggFRBgsq
# hkiG9w0BCRABBKCCAUAEggE8MIIBOAIBAQYKKwYBBAGEWQoDATAxMA0GCWCGSAFl
# AwQCAQUABCBWsolkWylQgdroQMt57YThcHqv//niVCg7LVRbsx1DLgIGaErVV/pP
# GBIyMDI1MDcwOTExMDcyMy40MlowBIACAfSggdGkgc4wgcsxCzAJBgNVBAYTAlVT
# MRMwEQYDVQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQK
# ExVNaWNyb3NvZnQgQ29ycG9yYXRpb24xJTAjBgNVBAsTHE1pY3Jvc29mdCBBbWVy
# aWNhIE9wZXJhdGlvbnMxJzAlBgNVBAsTHm5TaGllbGQgVFNTIEVTTjo5MjAwLTA1
# RTAtRDk0NzElMCMGA1UEAxMcTWljcm9zb2Z0IFRpbWUtU3RhbXAgU2VydmljZaCC
# EeowggcgMIIFCKADAgECAhMzAAACCQgH4PlcjOZVAAEAAAIJMA0GCSqGSIb3DQEB
# CwUAMHwxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQH
# EwdSZWRtb25kMR4wHAYDVQQKExVNaWNyb3NvZnQgQ29ycG9yYXRpb24xJjAkBgNV
# BAMTHU1pY3Jvc29mdCBUaW1lLVN0YW1wIFBDQSAyMDEwMB4XDTI1MDEzMDE5NDI1
# NVoXDTI2MDQyMjE5NDI1NVowgcsxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNo
# aW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNyb3NvZnQgQ29y
# cG9yYXRpb24xJTAjBgNVBAsTHE1pY3Jvc29mdCBBbWVyaWNhIE9wZXJhdGlvbnMx
# JzAlBgNVBAsTHm5TaGllbGQgVFNTIEVTTjo5MjAwLTA1RTAtRDk0NzElMCMGA1UE
# AxMcTWljcm9zb2Z0IFRpbWUtU3RhbXAgU2VydmljZTCCAiIwDQYJKoZIhvcNAQEB
# BQADggIPADCCAgoCggIBAMKUSjD3Lgzd/VL3PXG00QRPBYvW8SKLDSgPtJcR2/ix
# 0/TGxXKJ2/ojauYSXw9iz0txmPOxY4cjt1CREvbwY/cJdy9jRmrqdawdjZBqYJkU
# sXYiVEfoEfHZGQ3tlEMqazsE6jggYFGUIyRS/033+3A7MCSlY2wzdv8FDFzCFWCx
# Cq1Dw0Q9S6JH4ZXmt1AdRPimOKFlOQnCtqWLPRltilRMfk6SLd3cGnH2qI+uIHqG
# E18Y+OXQ8inbcPnv2ulbpmY+o9PyPXYpfvJJnA27Gzc9i8X/DXcaxFeTMhsjIsoQ
# /OP2XOaasXbCO+9SvH0BnDsYtJeTbwOfVdJ/raFuQW5QbA8UuncRtGohWYFnjbBz
# PmZIggLLdCz+HCERiFSd2cAGA2kPlq8As5XuxR8mscNldfp/2CBuMgDqPaeFIBIi
# qXwXkuwoHDRE+0O7LePYI/G1OZmjNssrxMy3EOIwKDFOl+DmJhS/KFXhqpoMvBEG
# ygFGE7/6HDJsqdjBfEp546uw7BAudo4TkGYUlhYE4XPd3zwsEr1BEGB0QfkItWHv
# CSAwh6H3pwfn4fTES+aDq3u7O2VdfZJXvF1Rg/EDe+ONXcSRXtptIcPkcdBlOt3c
# WqwP9U5gAJRUE+vEX6RStkZfFgidlOmtgxgSrpQgbUNPikJU/0NxoIsYg5gQnWDT
# AgMBAAGjggFJMIIBRTAdBgNVHQ4EFgQUSYvo0cRdOOW98C9AzbV3MxaTytIwHwYD
# VR0jBBgwFoAUn6cVXQBeYl2D9OXSZacbUzUZ6XIwXwYDVR0fBFgwVjBUoFKgUIZO
# aHR0cDovL3d3dy5taWNyb3NvZnQuY29tL3BraW9wcy9jcmwvTWljcm9zb2Z0JTIw
# VGltZS1TdGFtcCUyMFBDQSUyMDIwMTAoMSkuY3JsMGwGCCsGAQUFBwEBBGAwXjBc
# BggrBgEFBQcwAoZQaHR0cDovL3d3dy5taWNyb3NvZnQuY29tL3BraW9wcy9jZXJ0
# cy9NaWNyb3NvZnQlMjBUaW1lLVN0YW1wJTIwUENBJTIwMjAxMCgxKS5jcnQwDAYD
# VR0TAQH/BAIwADAWBgNVHSUBAf8EDDAKBggrBgEFBQcDCDAOBgNVHQ8BAf8EBAMC
# B4AwDQYJKoZIhvcNAQELBQADggIBAFxefG84PCTiH+NtQGycWUW2tK4EFlvvBJl9
# rmUpExM182WZoALht3tajQjmEzGwQlTK6kfCHiQPmqRFlzMhzSMgAFBDXENQFr5Z
# PGun9QCoLXuKMUJ49kphWM2sd/8GaPPsVo4jjWTG55GHAs0hxDaCYGoNHlbhNLaG
# 1EljJkCzuN8mZsO1NxQ4yESXU5aXH8We9xBui3lU/NpTCJPo2J7yXo9mOhCy7GJq
# y5ICbEohB2wecnlCiSrB3KpeLUVkO0RNW9td8Oyh/NO1rh6fap/jyHMRnBS9uTPm
# ya3z3SdUAruTPZyuvM3eGmd8W5+2n+tctZO/E9Bx9ZeIS4hR3YaDt5HxC3Iq0kNT
# z48PAQKTOhomNsYIqrH0RKAUnPOtc3CGFfpFzyDYRT/7reaapZ4IX+Qk4WDZ4nDt
# q79psRKCrcRrPIPVWUv4dpf4wEcbNCYe286bdCXjBVM3darxfxsJHryqIXmsVqyb
# hHEXrNqNl5IcL+pLnffr/howOqxXo7zpGU88JgYk4+1/Yxso7tckl4v9RA3Rze6L
# HlExOjrp1sBPE9QUQbk+Hg8fMaNRsQ7sPfku4QGKIbxiuUxE6QaXd8FCX1tZuDD0
# IhRBvCrlxNoTGV8Skx1KjJ0miVRNAPkQsobPVMlqFOJ13bTCXCLkGTfpcibOwfhi
# zXmJdF8CMIIHcTCCBVmgAwIBAgITMwAAABXF52ueAptJmQAAAAAAFTANBgkqhkiG
# 9w0BAQsFADCBiDELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldhc2hpbmd0b24xEDAO
# BgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEy
# MDAGA1UEAxMpTWljcm9zb2Z0IFJvb3QgQ2VydGlmaWNhdGUgQXV0aG9yaXR5IDIw
# MTAwHhcNMjEwOTMwMTgyMjI1WhcNMzAwOTMwMTgzMjI1WjB8MQswCQYDVQQGEwJV
# UzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwGA1UE
# ChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSYwJAYDVQQDEx1NaWNyb3NvZnQgVGlt
# ZS1TdGFtcCBQQ0EgMjAxMDCCAiIwDQYJKoZIhvcNAQEBBQADggIPADCCAgoCggIB
# AOThpkzntHIhC3miy9ckeb0O1YLT/e6cBwfSqWxOdcjKNVf2AX9sSuDivbk+F2Az
# /1xPx2b3lVNxWuJ+Slr+uDZnhUYjDLWNE893MsAQGOhgfWpSg0S3po5GawcU88V2
# 9YZQ3MFEyHFcUTE3oAo4bo3t1w/YJlN8OWECesSq/XJprx2rrPY2vjUmZNqYO7oa
# ezOtgFt+jBAcnVL+tuhiJdxqD89d9P6OU8/W7IVWTe/dvI2k45GPsjksUZzpcGkN
# yjYtcI4xyDUoveO0hyTD4MmPfrVUj9z6BVWYbWg7mka97aSueik3rMvrg0XnRm7K
# MtXAhjBcTyziYrLNueKNiOSWrAFKu75xqRdbZ2De+JKRHh09/SDPc31BmkZ1zcRf
# NN0Sidb9pSB9fvzZnkXftnIv231fgLrbqn427DZM9ituqBJR6L8FA6PRc6ZNN3SU
# HDSCD/AQ8rdHGO2n6Jl8P0zbr17C89XYcz1DTsEzOUyOArxCaC4Q6oRRRuLRvWoY
# WmEBc8pnol7XKHYC4jMYctenIPDC+hIK12NvDMk2ZItboKaDIV1fMHSRlJTYuVD5
# C4lh8zYGNRiER9vcG9H9stQcxWv2XFJRXRLbJbqvUAV6bMURHXLvjflSxIUXk8A8
# FdsaN8cIFRg/eKtFtvUeh17aj54WcmnGrnu3tz5q4i6tAgMBAAGjggHdMIIB2TAS
# BgkrBgEEAYI3FQEEBQIDAQABMCMGCSsGAQQBgjcVAgQWBBQqp1L+ZMSavoKRPEY1
# Kc8Q/y8E7jAdBgNVHQ4EFgQUn6cVXQBeYl2D9OXSZacbUzUZ6XIwXAYDVR0gBFUw
# UzBRBgwrBgEEAYI3TIN9AQEwQTA/BggrBgEFBQcCARYzaHR0cDovL3d3dy5taWNy
# b3NvZnQuY29tL3BraW9wcy9Eb2NzL1JlcG9zaXRvcnkuaHRtMBMGA1UdJQQMMAoG
# CCsGAQUFBwMIMBkGCSsGAQQBgjcUAgQMHgoAUwB1AGIAQwBBMAsGA1UdDwQEAwIB
# hjAPBgNVHRMBAf8EBTADAQH/MB8GA1UdIwQYMBaAFNX2VsuP6KJcYmjRPZSQW9fO
# mhjEMFYGA1UdHwRPME0wS6BJoEeGRWh0dHA6Ly9jcmwubWljcm9zb2Z0LmNvbS9w
# a2kvY3JsL3Byb2R1Y3RzL01pY1Jvb0NlckF1dF8yMDEwLTA2LTIzLmNybDBaBggr
# BgEFBQcBAQROMEwwSgYIKwYBBQUHMAKGPmh0dHA6Ly93d3cubWljcm9zb2Z0LmNv
# bS9wa2kvY2VydHMvTWljUm9vQ2VyQXV0XzIwMTAtMDYtMjMuY3J0MA0GCSqGSIb3
# DQEBCwUAA4ICAQCdVX38Kq3hLB9nATEkW+Geckv8qW/qXBS2Pk5HZHixBpOXPTEz
# tTnXwnE2P9pkbHzQdTltuw8x5MKP+2zRoZQYIu7pZmc6U03dmLq2HnjYNi6cqYJW
# AAOwBb6J6Gngugnue99qb74py27YP0h1AdkY3m2CDPVtI1TkeFN1JFe53Z/zjj3G
# 82jfZfakVqr3lbYoVSfQJL1AoL8ZthISEV09J+BAljis9/kpicO8F7BUhUKz/Aye
# ixmJ5/ALaoHCgRlCGVJ1ijbCHcNhcy4sa3tuPywJeBTpkbKpW99Jo3QMvOyRgNI9
# 5ko+ZjtPu4b6MhrZlvSP9pEB9s7GdP32THJvEKt1MMU0sHrYUP4KWN1APMdUbZ1j
# dEgssU5HLcEUBHG/ZPkkvnNtyo4JvbMBV0lUZNlz138eW0QBjloZkWsNn6Qo3GcZ
# KCS6OEuabvshVGtqRRFHqfG3rsjoiV5PndLQTHa1V1QJsWkBRH58oWFsc/4Ku+xB
# Zj1p/cvBQUl+fpO+y/g75LcVv7TOPqUxUYS8vwLBgqJ7Fx0ViY1w/ue10CgaiQuP
# Ntq6TPmb/wrpNPgkNWcr4A245oyZ1uEi6vAnQj0llOZ0dFtq0Z4+7X6gMTN9vMvp
# e784cETRkPHIqzqKOghif9lwY1NNje6CbaUFEMFxBmoQtB1VM1izoXBm8qGCA00w
# ggI1AgEBMIH5oYHRpIHOMIHLMQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGlu
# Z3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBv
# cmF0aW9uMSUwIwYDVQQLExxNaWNyb3NvZnQgQW1lcmljYSBPcGVyYXRpb25zMScw
# JQYDVQQLEx5uU2hpZWxkIFRTUyBFU046OTIwMC0wNUUwLUQ5NDcxJTAjBgNVBAMT
# HE1pY3Jvc29mdCBUaW1lLVN0YW1wIFNlcnZpY2WiIwoBATAHBgUrDgMCGgMVAHzv
# ras9NB3sicMJB1vWSAUpCQJEoIGDMIGApH4wfDELMAkGA1UEBhMCVVMxEzARBgNV
# BAgTCldhc2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jv
# c29mdCBDb3Jwb3JhdGlvbjEmMCQGA1UEAxMdTWljcm9zb2Z0IFRpbWUtU3RhbXAg
# UENBIDIwMTAwDQYJKoZIhvcNAQELBQACBQDsGEFGMCIYDzIwMjUwNzA5MDExNTUw
# WhgPMjAyNTA3MTAwMTE1NTBaMHQwOgYKKwYBBAGEWQoEATEsMCowCgIFAOwYQUYC
# AQAwBwIBAAICFCAwBwIBAAICElowCgIFAOwZksYCAQAwNgYKKwYBBAGEWQoEAjEo
# MCYwDAYKKwYBBAGEWQoDAqAKMAgCAQACAwehIKEKMAgCAQACAwGGoDANBgkqhkiG
# 9w0BAQsFAAOCAQEANRyltl3U09btGD8EIn0QIIAG+X6J+Hhbc0gMOQvb3kJQDswv
# 9Tu1EYtOqI3ALRP6LN4ccWB+stNRFfayDINPo9Mhe8Rp+P9R8wePnf3w550toM6O
# cxhnxXH96sa/Os1ocTQaRgBLHUfqTAi9X9K4c9Cfq+1l9mxj0au/QubMItUuxaT3
# XoZOYVphZazlSr1HIfUCm0LAwHPlfe3YO4Im8DFWrpVO3o9WDmzzaa76yfLAPKUV
# LPdTa7MCsidkk4ni2ytkyNNnORp5pAqOWzpae8e+dqk4pxM9Ctdel+++rHPEY3Bw
# esZhCkxUudL43RkTYTfgve9FYOyz6/cThAtKODGCBA0wggQJAgEBMIGTMHwxCzAJ
# BgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25k
# MR4wHAYDVQQKExVNaWNyb3NvZnQgQ29ycG9yYXRpb24xJjAkBgNVBAMTHU1pY3Jv
# c29mdCBUaW1lLVN0YW1wIFBDQSAyMDEwAhMzAAACCQgH4PlcjOZVAAEAAAIJMA0G
# CWCGSAFlAwQCAQUAoIIBSjAaBgkqhkiG9w0BCQMxDQYLKoZIhvcNAQkQAQQwLwYJ
# KoZIhvcNAQkEMSIEIO3TLZTyNFwSRpcy79QOgy/3DoQbMm3xgFSuC2Nqx8FtMIH6
# BgsqhkiG9w0BCRACLzGB6jCB5zCB5DCBvQQgaBssHsi99AIuZQ5RmGN1SorxuKR8
# HplVV2hOM3CFEz4wgZgwgYCkfjB8MQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2Fz
# aGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENv
# cnBvcmF0aW9uMSYwJAYDVQQDEx1NaWNyb3NvZnQgVGltZS1TdGFtcCBQQ0EgMjAx
# MAITMwAAAgkIB+D5XIzmVQABAAACCTAiBCC4bv2oAS+D2yYX2y0YU6Y8avTexwRX
# o0Uu/0k1QPPc4DANBgkqhkiG9w0BAQsFAASCAgAiYDyU/LUBykWlcNwpzKTTIGSP
# uePdiaNCbkXn08lgrwTbkXyaeXBr/aKmdPcHgoeiKJuDIegv+MVJGrgBzcvmo3F2
# Na/wq0FbgDldT3AygDlOPiLzJU/0ZuYxRUMLApKmApbfgeqbUB9eDaNtaxgaUacE
# kn5dRhSmcmGnU/HiuK/oBJE/IZTwmrjiV28Pgx1Q8vMbTf2lkE8z2DjLo1YqPMC9
# WwmQ9X4ETfZJfVZGTUBCuKimo34tJT5muFBLB5g7/DEkWyTroofGY1ZNw8yTlaxJ
# YuD1ga8TPwy8IDIW15djOs5ERJoByxCvG3Anl+EROEVzA3T2B+wQTP6M+tzm1uzN
# 2nXWMmFiQXzty2poH1Dd1ke63ARpH5OY9Vs2rmRmTGtOHYdayLaqX3ZB64+Z5CH3
# IfKYXg1k38UFzdtNcXQDQRtzBxhJxNJKYLTZOCBp67Clu1cXpDsTdj4ZXBwJOPox
# pR84B+OjtsC3xVfq4DBRd0FmtRrJXR7Yy0NZHzTAkfuoLO/iGJ4jjYmbvaLDps9i
# L+/jwcGWQnT5sEqw6uCFVTunFF20wPGhRquzbjL/rgWnvA1R2lSy72SrzxjZGQ0z
# wdbzuODv0i35t/V4UMjhYuVeg1MqU6fi6xj5xQQcnB/OfP/ASn/j8z2QRQSQ0VuB
# 1r/BG4qfmSE/GxybuA==
# SIG # End signature block
