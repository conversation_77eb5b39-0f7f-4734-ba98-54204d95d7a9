#
# Module manifest for module 'Microsoft.Graph.Identity.SignIns'
#
# Generated by: Microsoft Corporation
#
# Generated on: 7/9/2025
#

@{

# Script module or binary module file associated with this manifest.
RootModule = './Microsoft.Graph.Identity.SignIns.psm1'

# Version number of this module.
ModuleVersion = '2.29.0'

# Supported PSEditions
CompatiblePSEditions = 'Core', 'Desktop'

# ID used to uniquely identify this module
GUID = '60f889fa-f873-43ad-b7d3-b7fc1273a44f'

# Author of this module
Author = 'Microsoft Corporation'

# Company or vendor of this module
CompanyName = 'Microsoft Corporation'

# Copyright statement for this module
Copyright = 'Microsoft Corporation. All rights reserved.'

# Description of the functionality provided by this module
Description = 'Microsoft Graph PowerShell Cmdlets'

# Minimum version of the PowerShell engine required by this module
PowerShellVersion = '5.1'

# Name of the PowerShell host required by this module
# PowerShellHostName = ''

# Minimum version of the PowerShell host required by this module
# PowerShellHostVersion = ''

# Minimum version of Microsoft .NET Framework required by this module. This prerequisite is valid for the PowerShell Desktop edition only.
DotNetFrameworkVersion = '4.7.2'

# Minimum version of the common language runtime (CLR) required by this module. This prerequisite is valid for the PowerShell Desktop edition only.
# ClrVersion = ''

# Processor architecture (None, X86, Amd64) required by this module
# ProcessorArchitecture = ''

# Modules that must be imported into the global environment prior to importing this module
RequiredModules = @(@{ModuleName = 'Microsoft.Graph.Authentication'; RequiredVersion = '2.29.0'; })

# Assemblies that must be loaded prior to importing this module
RequiredAssemblies = './bin/Microsoft.Graph.Identity.SignIns.private.dll'

# Script files (.ps1) that are run in the caller's environment prior to importing this module.
# ScriptsToProcess = @()

# Type files (.ps1xml) to be loaded when importing this module
# TypesToProcess = @()

# Format files (.ps1xml) to be loaded when importing this module
FormatsToProcess = './Microsoft.Graph.Identity.SignIns.format.ps1xml'

# Modules to import as nested modules of the module specified in RootModule/ModuleToProcess
# NestedModules = @()

# Functions to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no functions to export.
FunctionsToExport = 'Confirm-MgRiskyServicePrincipalCompromised', 
               'Confirm-MgRiskyUserCompromised', 'Confirm-MgRiskyUserSafe', 
               'Disable-MgUserAuthenticationPhoneMethodSmsSignIn', 
               'Enable-MgUserAuthenticationPhoneMethodSmsSignIn', 
               'Get-MgDataPolicyOperation', 'Get-MgDataPolicyOperationCount', 
               'Get-MgIdentityApiConnector', 'Get-MgIdentityApiConnectorCount', 
               'Get-MgIdentityAuthenticationEventFlow', 
               'Get-MgIdentityAuthenticationEventFlowAsExternalUserSelfServiceSignUpEventFlow', 
               'Get-MgIdentityAuthenticationEventFlowAsExternalUserSelfServiceSignUpEventFlowCondition', 
               'Get-MgIdentityAuthenticationEventFlowAsExternalUserSelfServiceSignUpEventFlowIncludeApplication', 
               'Get-MgIdentityAuthenticationEventFlowAsExternalUserSelfServiceSignUpEventFlowIncludeApplicationCount', 
               'Get-MgIdentityAuthenticationEventFlowAsExternalUserSelfServiceSignUpEventFlowOnAttributeCollection', 
               'Get-MgIdentityAuthenticationEventFlowAsExternalUserSelfServiceSignUpEventFlowOnAuthenticationMethodLoadStart', 
               'Get-MgIdentityAuthenticationEventFlowAsOnAttributeCollectionExternalUserSelfServiceSignUpAttribute', 
               'Get-MgIdentityAuthenticationEventFlowAsOnAttributeCollectionExternalUserSelfServiceSignUpAttributeCount', 
               'Get-MgIdentityAuthenticationEventFlowAsOnAuthenticationMethodLoadStartExternalUserSelfServiceSignUpIdentityProvider', 
               'Get-MgIdentityAuthenticationEventFlowAsOnAuthenticationMethodLoadStartExternalUserSelfServiceSignUpIdentityProviderByRef', 
               'Get-MgIdentityAuthenticationEventFlowAsOnAuthenticationMethodLoadStartExternalUserSelfServiceSignUpIdentityProviderCount', 
               'Get-MgIdentityAuthenticationEventFlowAsOnGraphAPretributeCollectionExternalUserSelfServiceSignUpAttributeByRef', 
               'Get-MgIdentityAuthenticationEventFlowCondition', 
               'Get-MgIdentityAuthenticationEventFlowCount', 
               'Get-MgIdentityAuthenticationEventFlowCountAsExternalUserSelfServiceSignUpEventFlow', 
               'Get-MgIdentityAuthenticationEventFlowIncludeApplication', 
               'Get-MgIdentityAuthenticationEventFlowIncludeApplicationCount', 
               'Get-MgIdentityAuthenticationEventFlowOnAttributeCollectionAsOnAttributeCollectionExternalUserSelfServiceSignUp', 
               'Get-MgIdentityAuthenticationEventFlowOnAuthenticationMethodLoadStartAsOnAuthenticationMethodLoadStartExternalUserSelfServiceSignUp', 
               'Get-MgIdentityAuthenticationEventListener', 
               'Get-MgIdentityAuthenticationEventListenerCount', 
               'Get-MgIdentityB2XUserFlow', 
               'Get-MgIdentityB2XUserFlowApiConnectorConfiguration', 
               'Get-MgIdentityB2XUserFlowCount', 
               'Get-MgIdentityB2XUserFlowIdentityProvider', 
               'Get-MgIdentityB2XUserFlowIdentityProviderByRef', 
               'Get-MgIdentityB2XUserFlowIdentityProviderCount', 
               'Get-MgIdentityB2XUserFlowLanguage', 
               'Get-MgIdentityB2XUserFlowLanguageCount', 
               'Get-MgIdentityB2XUserFlowLanguageDefaultPage', 
               'Get-MgIdentityB2XUserFlowLanguageDefaultPageContent', 
               'Get-MgIdentityB2XUserFlowLanguageDefaultPageCount', 
               'Get-MgIdentityB2XUserFlowLanguageOverridePage', 
               'Get-MgIdentityB2XUserFlowLanguageOverridePageContent', 
               'Get-MgIdentityB2XUserFlowLanguageOverridePageCount', 
               'Get-MgIdentityB2XUserFlowPostAttributeCollection', 
               'Get-MgIdentityB2XUserFlowPostAttributeCollectionByRef', 
               'Get-MgIdentityB2XUserFlowPostFederationSignup', 
               'Get-MgIdentityB2XUserFlowPostFederationSignupByRef', 
               'Get-MgIdentityB2XUserFlowUserAttributeAssignment', 
               'Get-MgIdentityB2XUserFlowUserAttributeAssignmentCount', 
               'Get-MgIdentityB2XUserFlowUserAttributeAssignmentOrder', 
               'Get-MgIdentityB2XUserFlowUserAttributeAssignmentUserAttribute', 
               'Get-MgIdentityConditionalAccessAuthenticationContextClassReference', 
               'Get-MgIdentityConditionalAccessAuthenticationContextClassReferenceCount', 
               'Get-MgIdentityConditionalAccessNamedLocation', 
               'Get-MgIdentityConditionalAccessNamedLocationCount', 
               'Get-MgIdentityConditionalAccessPolicy', 
               'Get-MgIdentityConditionalAccessPolicyCount', 
               'Get-MgIdentityConditionalAccessTemplate', 
               'Get-MgIdentityConditionalAccessTemplateCount', 
               'Get-MgIdentityCustomAuthenticationExtension', 
               'Get-MgIdentityCustomAuthenticationExtensionCount', 
               'Get-MgIdentityProvider', 'Get-MgIdentityProviderCount', 
               'Get-MgIdentityUserFlowAttribute', 
               'Get-MgIdentityUserFlowAttributeCount', 
               'Get-MgInformationProtection', 
               'Get-MgInformationProtectionBitlocker', 
               'Get-MgInformationProtectionBitlockerRecoveryKey', 
               'Get-MgInformationProtectionBitlockerRecoveryKeyCount', 
               'Get-MgInformationProtectionThreatAssessmentRequest', 
               'Get-MgInformationProtectionThreatAssessmentRequestCount', 
               'Get-MgInformationProtectionThreatAssessmentRequestResult', 
               'Get-MgInformationProtectionThreatAssessmentRequestResultCount', 
               'Get-MgInvitation', 'Get-MgInvitationCount', 
               'Get-MgInvitationInvitedUserMailboxSetting', 
               'Get-MgInvitationInvitedUserServiceProvisioningError', 
               'Get-MgInvitationInvitedUserServiceProvisioningErrorCount', 
               'Get-MgInvitationInvitedUserSponsor', 
               'Get-MgInvitationInvitedUserSponsorCount', 
               'Get-MgOauth2PermissionGrant', 'Get-MgOauth2PermissionGrantCount', 
               'Get-MgOauth2PermissionGrantDelta', 
               'Get-MgOrganizationCertificateBasedAuthConfiguration', 
               'Get-MgOrganizationCertificateBasedAuthConfigurationCount', 
               'Get-MgPolicyActivityBasedTimeoutPolicy', 
               'Get-MgPolicyActivityBasedTimeoutPolicyApplyTo', 
               'Get-MgPolicyActivityBasedTimeoutPolicyApplyToCount', 
               'Get-MgPolicyActivityBasedTimeoutPolicyCount', 
               'Get-MgPolicyAdminConsentRequestPolicy', 
               'Get-MgPolicyAppManagementPolicy', 
               'Get-MgPolicyAppManagementPolicyApplyTo', 
               'Get-MgPolicyAppManagementPolicyApplyToCount', 
               'Get-MgPolicyAppManagementPolicyCount', 
               'Get-MgPolicyAuthenticationFlowPolicy', 
               'Get-MgPolicyAuthenticationMethodPolicy', 
               'Get-MgPolicyAuthenticationMethodPolicyAuthenticationMethodConfiguration', 
               'Get-MgPolicyAuthenticationMethodPolicyAuthenticationMethodConfigurationCount', 
               'Get-MgPolicyAuthenticationStrengthPolicy', 
               'Get-MgPolicyAuthenticationStrengthPolicyCombinationConfiguration', 
               'Get-MgPolicyAuthenticationStrengthPolicyCombinationConfigurationCount', 
               'Get-MgPolicyAuthenticationStrengthPolicyCount', 
               'Get-MgPolicyAuthorizationPolicy', 'Get-MgPolicyClaimMappingPolicy', 
               'Get-MgPolicyClaimMappingPolicyApplyTo', 
               'Get-MgPolicyClaimMappingPolicyApplyToCount', 
               'Get-MgPolicyClaimMappingPolicyCount', 
               'Get-MgPolicyConditionalAccessPolicyCount', 
               'Get-MgPolicyCrossTenantAccessPolicy', 
               'Get-MgPolicyCrossTenantAccessPolicyDefault', 
               'Get-MgPolicyCrossTenantAccessPolicyPartner', 
               'Get-MgPolicyCrossTenantAccessPolicyPartnerCount', 
               'Get-MgPolicyCrossTenantAccessPolicyPartnerIdentitySynchronization', 
               'Get-MgPolicyCrossTenantAccessPolicyTemplate', 
               'Get-MgPolicyCrossTenantAccessPolicyTemplateMultiTenantOrganizationIdentitySynchronization', 
               'Get-MgPolicyCrossTenantAccessPolicyTemplateMultiTenantOrganizationPartnerConfiguration', 
               'Get-MgPolicyDefaultAppManagementPolicy', 
               'Get-MgPolicyDeviceRegistrationPolicy', 
               'Get-MgPolicyFeatureRolloutPolicy', 
               'Get-MgPolicyFeatureRolloutPolicyApplyTo', 
               'Get-MgPolicyFeatureRolloutPolicyApplyToByRef', 
               'Get-MgPolicyFeatureRolloutPolicyApplyToCount', 
               'Get-MgPolicyFeatureRolloutPolicyCount', 
               'Get-MgPolicyHomeRealmDiscoveryPolicy', 
               'Get-MgPolicyHomeRealmDiscoveryPolicyApplyTo', 
               'Get-MgPolicyHomeRealmDiscoveryPolicyApplyToCount', 
               'Get-MgPolicyHomeRealmDiscoveryPolicyCount', 
               'Get-MgPolicyIdentitySecurityDefaultEnforcementPolicy', 
               'Get-MgPolicyPermissionGrantPolicy', 
               'Get-MgPolicyPermissionGrantPolicyCount', 
               'Get-MgPolicyPermissionGrantPolicyExclude', 
               'Get-MgPolicyPermissionGrantPolicyExcludeCount', 
               'Get-MgPolicyPermissionGrantPolicyInclude', 
               'Get-MgPolicyPermissionGrantPolicyIncludeCount', 
               'Get-MgPolicyRoleManagementPolicy', 
               'Get-MgPolicyRoleManagementPolicyAssignment', 
               'Get-MgPolicyRoleManagementPolicyAssignmentCount', 
               'Get-MgPolicyRoleManagementPolicyAssignmentPolicy', 
               'Get-MgPolicyRoleManagementPolicyCount', 
               'Get-MgPolicyRoleManagementPolicyEffectiveRule', 
               'Get-MgPolicyRoleManagementPolicyEffectiveRuleCount', 
               'Get-MgPolicyRoleManagementPolicyRule', 
               'Get-MgPolicyRoleManagementPolicyRuleCount', 
               'Get-MgPolicyTokenIssuancePolicy', 
               'Get-MgPolicyTokenIssuancePolicyApplyTo', 
               'Get-MgPolicyTokenIssuancePolicyApplyToCount', 
               'Get-MgPolicyTokenIssuancePolicyCount', 
               'Get-MgPolicyTokenLifetimePolicy', 
               'Get-MgPolicyTokenLifetimePolicyApplyTo', 
               'Get-MgPolicyTokenLifetimePolicyApplyToCount', 
               'Get-MgPolicyTokenLifetimePolicyCount', 'Get-MgRiskDetection', 
               'Get-MgRiskDetectionCount', 'Get-MgRiskyServicePrincipal', 
               'Get-MgRiskyServicePrincipalCount', 
               'Get-MgRiskyServicePrincipalHistory', 
               'Get-MgRiskyServicePrincipalHistoryCount', 'Get-MgRiskyUser', 
               'Get-MgRiskyUserCount', 'Get-MgRiskyUserHistory', 
               'Get-MgRiskyUserHistoryCount', 
               'Get-MgServicePrincipalRiskDetection', 
               'Get-MgServicePrincipalRiskDetectionCount', 
               'Get-MgTenantRelationshipMultiTenantOrganization', 
               'Get-MgTenantRelationshipMultiTenantOrganizationJoinRequest', 
               'Get-MgTenantRelationshipMultiTenantOrganizationTenant', 
               'Get-MgTenantRelationshipMultiTenantOrganizationTenantCount', 
               'Get-MgUserAuthenticationEmailMethod', 
               'Get-MgUserAuthenticationEmailMethodCount', 
               'Get-MgUserAuthenticationFido2Method', 
               'Get-MgUserAuthenticationFido2MethodCount', 
               'Get-MgUserAuthenticationMethod', 
               'Get-MgUserAuthenticationMethodCount', 
               'Get-MgUserAuthenticationMicrosoftAuthenticatorMethod', 
               'Get-MgUserAuthenticationMicrosoftAuthenticatorMethodCount', 
               'Get-MgUserAuthenticationMicrosoftAuthenticatorMethodDevice', 
               'Get-MgUserAuthenticationOperation', 
               'Get-MgUserAuthenticationOperationCount', 
               'Get-MgUserAuthenticationPasswordMethod', 
               'Get-MgUserAuthenticationPasswordMethodCount', 
               'Get-MgUserAuthenticationPhoneMethod', 
               'Get-MgUserAuthenticationPhoneMethodCount', 
               'Get-MgUserAuthenticationPlatformCredentialMethod', 
               'Get-MgUserAuthenticationPlatformCredentialMethodCount', 
               'Get-MgUserAuthenticationPlatformCredentialMethodDevice', 
               'Get-MgUserAuthenticationSoftwareOathMethod', 
               'Get-MgUserAuthenticationSoftwareOathMethodCount', 
               'Get-MgUserAuthenticationTemporaryAccessPassMethod', 
               'Get-MgUserAuthenticationTemporaryAccessPassMethodCount', 
               'Get-MgUserAuthenticationWindowsHelloForBusinessMethod', 
               'Get-MgUserAuthenticationWindowsHelloForBusinessMethodCount', 
               'Get-MgUserAuthenticationWindowsHelloForBusinessMethodDevice', 
               'Invoke-MgAvailableIdentityProviderType', 
               'Invoke-MgDismissRiskyServicePrincipal', 
               'Invoke-MgDismissRiskyUser', 
               'Invoke-MgUploadIdentityApiConnectorClientCertificate', 
               'Invoke-MgUploadIdentityB2XUserFlowApiConnectorConfigurationPostAttributeCollectionClientCertificate', 
               'Invoke-MgUploadIdentityB2XUserFlowApiConnectorConfigurationPostFederationSignupClientCertificate', 
               'Invoke-MgUsageIdentityConditionalAccessAuthenticationStrengthPolicy', 
               'Invoke-MgUsagePolicyAuthenticationStrengthPolicy', 
               'New-MgDataPolicyOperation', 'New-MgIdentityApiConnector', 
               'New-MgIdentityAuthenticationEventFlow', 
               'New-MgIdentityAuthenticationEventFlowAsExternalUserSelfServiceSignUpEventFlowIncludeApplication', 
               'New-MgIdentityAuthenticationEventFlowAsOnAuthenticationMethodLoadStartExternalUserSelfServiceSignUpIdentityProviderByRef', 
               'New-MgIdentityAuthenticationEventFlowAsOnGraphAPretributeCollectionExternalUserSelfServiceSignUpAttributeByRef', 
               'New-MgIdentityAuthenticationEventFlowIncludeApplication', 
               'New-MgIdentityAuthenticationEventListener', 
               'New-MgIdentityB2XUserFlow', 
               'New-MgIdentityB2XUserFlowIdentityProviderByRef', 
               'New-MgIdentityB2XUserFlowLanguage', 
               'New-MgIdentityB2XUserFlowLanguageDefaultPage', 
               'New-MgIdentityB2XUserFlowLanguageOverridePage', 
               'New-MgIdentityB2XUserFlowUserAttributeAssignment', 
               'New-MgIdentityConditionalAccessAuthenticationContextClassReference', 
               'New-MgIdentityConditionalAccessAuthenticationStrengthPolicyCombinationConfiguration', 
               'New-MgIdentityConditionalAccessNamedLocation', 
               'New-MgIdentityConditionalAccessPolicy', 
               'New-MgIdentityCustomAuthenticationExtension', 
               'New-MgIdentityProvider', 'New-MgIdentityUserFlowAttribute', 
               'New-MgInformationProtectionThreatAssessmentRequest', 
               'New-MgInformationProtectionThreatAssessmentRequestResult', 
               'New-MgInvitation', 'New-MgOauth2PermissionGrant', 
               'New-MgOrganizationCertificateBasedAuthConfiguration', 
               'New-MgPolicyActivityBasedTimeoutPolicy', 
               'New-MgPolicyAppManagementPolicy', 
               'New-MgPolicyAuthenticationMethodPolicyAuthenticationMethodConfiguration', 
               'New-MgPolicyAuthenticationStrengthPolicy', 
               'New-MgPolicyAuthenticationStrengthPolicyCombinationConfiguration', 
               'New-MgPolicyClaimMappingPolicy', 
               'New-MgPolicyCrossTenantAccessPolicyPartner', 
               'New-MgPolicyFeatureRolloutPolicy', 
               'New-MgPolicyFeatureRolloutPolicyApplyTo', 
               'New-MgPolicyFeatureRolloutPolicyApplyToByRef', 
               'New-MgPolicyHomeRealmDiscoveryPolicy', 
               'New-MgPolicyPermissionGrantPolicy', 
               'New-MgPolicyPermissionGrantPolicyExclude', 
               'New-MgPolicyPermissionGrantPolicyInclude', 
               'New-MgPolicyRoleManagementPolicy', 
               'New-MgPolicyRoleManagementPolicyAssignment', 
               'New-MgPolicyRoleManagementPolicyEffectiveRule', 
               'New-MgPolicyRoleManagementPolicyRule', 
               'New-MgPolicyTokenIssuancePolicy', 
               'New-MgPolicyTokenLifetimePolicy', 'New-MgRiskDetection', 
               'New-MgRiskyServicePrincipal', 'New-MgRiskyServicePrincipalHistory', 
               'New-MgRiskyUser', 'New-MgRiskyUserHistory', 
               'New-MgServicePrincipalRiskDetection', 
               'New-MgTenantRelationshipMultiTenantOrganizationTenant', 
               'New-MgUserAuthenticationEmailMethod', 
               'New-MgUserAuthenticationMethod', 
               'New-MgUserAuthenticationOperation', 
               'New-MgUserAuthenticationPhoneMethod', 
               'New-MgUserAuthenticationTemporaryAccessPassMethod', 
               'Remove-MgDataPolicyOperation', 'Remove-MgIdentityApiConnector', 
               'Remove-MgIdentityAuthenticationEventFlow', 
               'Remove-MgIdentityAuthenticationEventFlowAsExternalUserSelfServiceSignUpEventFlowIncludeApplication', 
               'Remove-MgIdentityAuthenticationEventFlowAsOnAuthenticationMethodLoadStartExternalUserSelfServiceSignUpIdentityProviderBaseByRef', 
               'Remove-MgIdentityAuthenticationEventFlowAsOnAuthenticationMethodLoadStartExternalUserSelfServiceSignUpIdentityProviderByRef', 
               'Remove-MgIdentityAuthenticationEventFlowAsOnGraphAPretributeCollectionExternalUserSelfServiceSignUpAttributeByRef', 
               'Remove-MgIdentityAuthenticationEventFlowAsOnGraphAPretributeCollectionExternalUserSelfServiceSignUpAttributeIdentityUserFlowAttributeByRef', 
               'Remove-MgIdentityAuthenticationEventFlowIncludeApplication', 
               'Remove-MgIdentityAuthenticationEventListener', 
               'Remove-MgIdentityB2XUserFlow', 
               'Remove-MgIdentityB2XUserFlowIdentityProviderBaseByRef', 
               'Remove-MgIdentityB2XUserFlowLanguage', 
               'Remove-MgIdentityB2XUserFlowLanguageDefaultPage', 
               'Remove-MgIdentityB2XUserFlowLanguageDefaultPageContent', 
               'Remove-MgIdentityB2XUserFlowLanguageOverridePage', 
               'Remove-MgIdentityB2XUserFlowLanguageOverridePageContent', 
               'Remove-MgIdentityB2XUserFlowPostAttributeCollection', 
               'Remove-MgIdentityB2XUserFlowPostAttributeCollectionByRef', 
               'Remove-MgIdentityB2XUserFlowPostFederationSignup', 
               'Remove-MgIdentityB2XUserFlowPostFederationSignupByRef', 
               'Remove-MgIdentityB2XUserFlowUserAttributeAssignment', 
               'Remove-MgIdentityConditionalAccessAuthenticationContextClassReference', 
               'Remove-MgIdentityConditionalAccessNamedLocation', 
               'Remove-MgIdentityConditionalAccessPolicy', 
               'Remove-MgIdentityCustomAuthenticationExtension', 
               'Remove-MgIdentityProvider', 'Remove-MgIdentityUserFlowAttribute', 
               'Remove-MgInformationProtectionThreatAssessmentRequest', 
               'Remove-MgInformationProtectionThreatAssessmentRequestResult', 
               'Remove-MgOauth2PermissionGrant', 
               'Remove-MgOrganizationCertificateBasedAuthConfiguration', 
               'Remove-MgPolicyActivityBasedTimeoutPolicy', 
               'Remove-MgPolicyAdminConsentRequestPolicy', 
               'Remove-MgPolicyAppManagementPolicy', 
               'Remove-MgPolicyAuthenticationFlowPolicy', 
               'Remove-MgPolicyAuthenticationMethodPolicy', 
               'Remove-MgPolicyAuthenticationMethodPolicyAuthenticationMethodConfiguration', 
               'Remove-MgPolicyAuthenticationStrengthPolicy', 
               'Remove-MgPolicyAuthenticationStrengthPolicyCombinationConfiguration', 
               'Remove-MgPolicyAuthorizationPolicy', 
               'Remove-MgPolicyClaimMappingPolicy', 
               'Remove-MgPolicyCrossTenantAccessPolicy', 
               'Remove-MgPolicyCrossTenantAccessPolicyDefault', 
               'Remove-MgPolicyCrossTenantAccessPolicyPartner', 
               'Remove-MgPolicyCrossTenantAccessPolicyPartnerIdentitySynchronization', 
               'Remove-MgPolicyCrossTenantAccessPolicyTemplate', 
               'Remove-MgPolicyCrossTenantAccessPolicyTemplateMultiTenantOrganizationIdentitySynchronization', 
               'Remove-MgPolicyCrossTenantAccessPolicyTemplateMultiTenantOrganizationPartnerConfiguration', 
               'Remove-MgPolicyDefaultAppManagementPolicy', 
               'Remove-MgPolicyFeatureRolloutPolicy', 
               'Remove-MgPolicyFeatureRolloutPolicyApplyToDirectoryObjectByRef', 
               'Remove-MgPolicyHomeRealmDiscoveryPolicy', 
               'Remove-MgPolicyIdentitySecurityDefaultEnforcementPolicy', 
               'Remove-MgPolicyPermissionGrantPolicy', 
               'Remove-MgPolicyPermissionGrantPolicyExclude', 
               'Remove-MgPolicyPermissionGrantPolicyInclude', 
               'Remove-MgPolicyRoleManagementPolicy', 
               'Remove-MgPolicyRoleManagementPolicyAssignment', 
               'Remove-MgPolicyRoleManagementPolicyEffectiveRule', 
               'Remove-MgPolicyRoleManagementPolicyRule', 
               'Remove-MgPolicyTokenIssuancePolicy', 
               'Remove-MgPolicyTokenLifetimePolicy', 'Remove-MgRiskDetection', 
               'Remove-MgRiskyServicePrincipal', 
               'Remove-MgRiskyServicePrincipalHistory', 'Remove-MgRiskyUser', 
               'Remove-MgRiskyUserHistory', 
               'Remove-MgServicePrincipalRiskDetection', 
               'Remove-MgTenantRelationshipMultiTenantOrganizationTenant', 
               'Remove-MgUserAuthenticationEmailMethod', 
               'Remove-MgUserAuthenticationFido2Method', 
               'Remove-MgUserAuthenticationMicrosoftAuthenticatorMethod', 
               'Remove-MgUserAuthenticationOperation', 
               'Remove-MgUserAuthenticationPhoneMethod', 
               'Remove-MgUserAuthenticationPlatformCredentialMethod', 
               'Remove-MgUserAuthenticationSoftwareOathMethod', 
               'Remove-MgUserAuthenticationTemporaryAccessPassMethod', 
               'Remove-MgUserAuthenticationWindowsHelloForBusinessMethod', 
               'Reset-MgPolicyCrossTenantAccessPolicyDefaultToSystemDefault', 
               'Reset-MgUserAuthenticationMethodPassword', 
               'Set-MgIdentityB2XUserFlowLanguageDefaultPageContent', 
               'Set-MgIdentityB2XUserFlowLanguageOverridePageContent', 
               'Set-MgIdentityB2XUserFlowPostAttributeCollectionByRef', 
               'Set-MgIdentityB2XUserFlowPostFederationSignupByRef', 
               'Set-MgIdentityB2XUserFlowUserAttributeAssignmentOrder', 
               'Set-MgPolicyCrossTenantAccessPolicyPartnerIdentitySynchronization', 
               'Test-MgIdentityCustomAuthenticationExtensionAuthenticationConfiguration', 
               'Update-MgDataPolicyOperation', 'Update-MgIdentityApiConnector', 
               'Update-MgIdentityAuthenticationEventFlow', 
               'Update-MgIdentityAuthenticationEventFlowAsExternalUserSelfServiceSignUpEventFlowIncludeApplication', 
               'Update-MgIdentityAuthenticationEventFlowIncludeApplication', 
               'Update-MgIdentityAuthenticationEventListener', 
               'Update-MgIdentityB2XUserFlow', 
               'Update-MgIdentityB2XUserFlowLanguage', 
               'Update-MgIdentityB2XUserFlowLanguageDefaultPage', 
               'Update-MgIdentityB2XUserFlowLanguageOverridePage', 
               'Update-MgIdentityB2XUserFlowPostAttributeCollection', 
               'Update-MgIdentityB2XUserFlowPostFederationSignup', 
               'Update-MgIdentityB2XUserFlowUserAttributeAssignment', 
               'Update-MgIdentityConditionalAccessAuthenticationContextClassReference', 
               'Update-MgIdentityConditionalAccessNamedLocation', 
               'Update-MgIdentityConditionalAccessPolicy', 
               'Update-MgIdentityCustomAuthenticationExtension', 
               'Update-MgIdentityProvider', 'Update-MgIdentityUserFlowAttribute', 
               'Update-MgInformationProtection', 
               'Update-MgInformationProtectionThreatAssessmentRequest', 
               'Update-MgInformationProtectionThreatAssessmentRequestResult', 
               'Update-MgInvitationInvitedUserMailboxSetting', 
               'Update-MgOauth2PermissionGrant', 
               'Update-MgPolicyActivityBasedTimeoutPolicy', 
               'Update-MgPolicyAdminConsentRequestPolicy', 
               'Update-MgPolicyAppManagementPolicy', 
               'Update-MgPolicyAuthenticationFlowPolicy', 
               'Update-MgPolicyAuthenticationMethodPolicy', 
               'Update-MgPolicyAuthenticationMethodPolicyAuthenticationMethodConfiguration', 
               'Update-MgPolicyAuthenticationStrengthPolicy', 
               'Update-MgPolicyAuthenticationStrengthPolicyAllowedCombination', 
               'Update-MgPolicyAuthenticationStrengthPolicyCombinationConfiguration', 
               'Update-MgPolicyAuthorizationPolicy', 
               'Update-MgPolicyClaimMappingPolicy', 
               'Update-MgPolicyCrossTenantAccessPolicy', 
               'Update-MgPolicyCrossTenantAccessPolicyDefault', 
               'Update-MgPolicyCrossTenantAccessPolicyPartner', 
               'Update-MgPolicyCrossTenantAccessPolicyTemplate', 
               'Update-MgPolicyCrossTenantAccessPolicyTemplateMultiTenantOrganizationIdentitySynchronization', 
               'Update-MgPolicyCrossTenantAccessPolicyTemplateMultiTenantOrganizationPartnerConfiguration', 
               'Update-MgPolicyDefaultAppManagementPolicy', 
               'Update-MgPolicyFeatureRolloutPolicy', 
               'Update-MgPolicyHomeRealmDiscoveryPolicy', 
               'Update-MgPolicyIdentitySecurityDefaultEnforcementPolicy', 
               'Update-MgPolicyPermissionGrantPolicy', 
               'Update-MgPolicyPermissionGrantPolicyExclude', 
               'Update-MgPolicyPermissionGrantPolicyInclude', 
               'Update-MgPolicyRoleManagementPolicy', 
               'Update-MgPolicyRoleManagementPolicyAssignment', 
               'Update-MgPolicyRoleManagementPolicyEffectiveRule', 
               'Update-MgPolicyRoleManagementPolicyRule', 
               'Update-MgPolicyTokenIssuancePolicy', 
               'Update-MgPolicyTokenLifetimePolicy', 'Update-MgRiskDetection', 
               'Update-MgRiskyServicePrincipal', 
               'Update-MgRiskyServicePrincipalHistory', 'Update-MgRiskyUser', 
               'Update-MgRiskyUserHistory', 
               'Update-MgServicePrincipalRiskDetection', 
               'Update-MgTenantRelationshipMultiTenantOrganization', 
               'Update-MgTenantRelationshipMultiTenantOrganizationJoinRequest', 
               'Update-MgTenantRelationshipMultiTenantOrganizationTenant', 
               'Update-MgUserAuthenticationEmailMethod', 
               'Update-MgUserAuthenticationMethod', 
               'Update-MgUserAuthenticationOperation', 
               'Update-MgUserAuthenticationPhoneMethod'

# Cmdlets to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no cmdlets to export.
CmdletsToExport = @()

# Variables to export from this module
# VariablesToExport = @()

# Aliases to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no aliases to export.
AliasesToExport = 'Remove-MgIdentityB2XUserFlowIdentityProviderByRef', 
               'Remove-MgPolicyFeatureRolloutPolicyApplyToByRef'

# DSC resources to export from this module
# DscResourcesToExport = @()

# List of all modules packaged with this module
# ModuleList = @()

# List of all files packaged with this module
# FileList = @()

# Private data to pass to the module specified in RootModule/ModuleToProcess. This may also contain a PSData hashtable with additional module metadata used by PowerShell.
PrivateData = @{

    PSData = @{

        # Tags applied to this module. These help with module discovery in online galleries.
        Tags = 'Microsoft','Office365','Graph','PowerShell','PSModule','PSIncludes_Cmdlet'

        # A URL to the license for this module.
        LicenseUri = 'https://aka.ms/devservicesagreement'

        # A URL to the main website for this project.
        ProjectUri = 'https://github.com/microsoftgraph/msgraph-sdk-powershell'

        # A URL to an icon representing this module.
        IconUri = 'https://raw.githubusercontent.com/microsoftgraph/msgraph-sdk-powershell/dev/docs/images/graph_color256.png'

        # ReleaseNotes of this module
        ReleaseNotes = 'See https://aka.ms/GraphPowerShell-Release.'

        # Prerelease string of this module
        # Prerelease = ''

        # Flag to indicate whether the module requires explicit user acceptance for install/update/save
        # RequireLicenseAcceptance = $false

        # External dependent modules of this module
        # ExternalModuleDependencies = @()

    } # End of PSData hashtable

 } # End of PrivateData hashtable

# HelpInfo URI of this module
# HelpInfoURI = ''

# Default prefix for commands exported from this module. Override the default prefix using Import-Module -Prefix.
# DefaultCommandPrefix = ''

}


# SIG # Begin signature block
# MIIoKgYJKoZIhvcNAQcCoIIoGzCCKBcCAQExDzANBglghkgBZQMEAgEFADB5Bgor
# BgEEAYI3AgEEoGswaTA0BgorBgEEAYI3AgEeMCYCAwEAAAQQH8w7YFlLCE63JNLG
# KX7zUQIBAAIBAAIBAAIBAAIBADAxMA0GCWCGSAFlAwQCAQUABCDIAdbQnN8m3/GZ
# q6Y6jkPIAzjPjoY/l1hsVDvokIQa+aCCDXYwggX0MIID3KADAgECAhMzAAAEBGx0
# Bv9XKydyAAAAAAQEMA0GCSqGSIb3DQEBCwUAMH4xCzAJBgNVBAYTAlVTMRMwEQYD
# VQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNy
# b3NvZnQgQ29ycG9yYXRpb24xKDAmBgNVBAMTH01pY3Jvc29mdCBDb2RlIFNpZ25p
# bmcgUENBIDIwMTEwHhcNMjQwOTEyMjAxMTE0WhcNMjUwOTExMjAxMTE0WjB0MQsw
# CQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9u
# ZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMR4wHAYDVQQDExVNaWNy
# b3NvZnQgQ29ycG9yYXRpb24wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIB
# AQC0KDfaY50MDqsEGdlIzDHBd6CqIMRQWW9Af1LHDDTuFjfDsvna0nEuDSYJmNyz
# NB10jpbg0lhvkT1AzfX2TLITSXwS8D+mBzGCWMM/wTpciWBV/pbjSazbzoKvRrNo
# DV/u9omOM2Eawyo5JJJdNkM2d8qzkQ0bRuRd4HarmGunSouyb9NY7egWN5E5lUc3
# a2AROzAdHdYpObpCOdeAY2P5XqtJkk79aROpzw16wCjdSn8qMzCBzR7rvH2WVkvF
# HLIxZQET1yhPb6lRmpgBQNnzidHV2Ocxjc8wNiIDzgbDkmlx54QPfw7RwQi8p1fy
# 4byhBrTjv568x8NGv3gwb0RbAgMBAAGjggFzMIIBbzAfBgNVHSUEGDAWBgorBgEE
# AYI3TAgBBggrBgEFBQcDAzAdBgNVHQ4EFgQU8huhNbETDU+ZWllL4DNMPCijEU4w
# RQYDVR0RBD4wPKQ6MDgxHjAcBgNVBAsTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEW
# MBQGA1UEBRMNMjMwMDEyKzUwMjkyMzAfBgNVHSMEGDAWgBRIbmTlUAXTgqoXNzci
# tW2oynUClTBUBgNVHR8ETTBLMEmgR6BFhkNodHRwOi8vd3d3Lm1pY3Jvc29mdC5j
# b20vcGtpb3BzL2NybC9NaWNDb2RTaWdQQ0EyMDExXzIwMTEtMDctMDguY3JsMGEG
# CCsGAQUFBwEBBFUwUzBRBggrBgEFBQcwAoZFaHR0cDovL3d3dy5taWNyb3NvZnQu
# Y29tL3BraW9wcy9jZXJ0cy9NaWNDb2RTaWdQQ0EyMDExXzIwMTEtMDctMDguY3J0
# MAwGA1UdEwEB/wQCMAAwDQYJKoZIhvcNAQELBQADggIBAIjmD9IpQVvfB1QehvpC
# Ge7QeTQkKQ7j3bmDMjwSqFL4ri6ae9IFTdpywn5smmtSIyKYDn3/nHtaEn0X1NBj
# L5oP0BjAy1sqxD+uy35B+V8wv5GrxhMDJP8l2QjLtH/UglSTIhLqyt8bUAqVfyfp
# h4COMRvwwjTvChtCnUXXACuCXYHWalOoc0OU2oGN+mPJIJJxaNQc1sjBsMbGIWv3
# cmgSHkCEmrMv7yaidpePt6V+yPMik+eXw3IfZ5eNOiNgL1rZzgSJfTnvUqiaEQ0X
# dG1HbkDv9fv6CTq6m4Ty3IzLiwGSXYxRIXTxT4TYs5VxHy2uFjFXWVSL0J2ARTYL
# E4Oyl1wXDF1PX4bxg1yDMfKPHcE1Ijic5lx1KdK1SkaEJdto4hd++05J9Bf9TAmi
# u6EK6C9Oe5vRadroJCK26uCUI4zIjL/qG7mswW+qT0CW0gnR9JHkXCWNbo8ccMk1
# sJatmRoSAifbgzaYbUz8+lv+IXy5GFuAmLnNbGjacB3IMGpa+lbFgih57/fIhamq
# 5VhxgaEmn/UjWyr+cPiAFWuTVIpfsOjbEAww75wURNM1Imp9NJKye1O24EspEHmb
# DmqCUcq7NqkOKIG4PVm3hDDED/WQpzJDkvu4FrIbvyTGVU01vKsg4UfcdiZ0fQ+/
# V0hf8yrtq9CkB8iIuk5bBxuPMIIHejCCBWKgAwIBAgIKYQ6Q0gAAAAAAAzANBgkq
# hkiG9w0BAQsFADCBiDELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldhc2hpbmd0b24x
# EDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlv
# bjEyMDAGA1UEAxMpTWljcm9zb2Z0IFJvb3QgQ2VydGlmaWNhdGUgQXV0aG9yaXR5
# IDIwMTEwHhcNMTEwNzA4MjA1OTA5WhcNMjYwNzA4MjEwOTA5WjB+MQswCQYDVQQG
# EwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwG
# A1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSgwJgYDVQQDEx9NaWNyb3NvZnQg
# Q29kZSBTaWduaW5nIFBDQSAyMDExMIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIIC
# CgKCAgEAq/D6chAcLq3YbqqCEE00uvK2WCGfQhsqa+laUKq4BjgaBEm6f8MMHt03
# a8YS2AvwOMKZBrDIOdUBFDFC04kNeWSHfpRgJGyvnkmc6Whe0t+bU7IKLMOv2akr
# rnoJr9eWWcpgGgXpZnboMlImEi/nqwhQz7NEt13YxC4Ddato88tt8zpcoRb0Rrrg
# OGSsbmQ1eKagYw8t00CT+OPeBw3VXHmlSSnnDb6gE3e+lD3v++MrWhAfTVYoonpy
# 4BI6t0le2O3tQ5GD2Xuye4Yb2T6xjF3oiU+EGvKhL1nkkDstrjNYxbc+/jLTswM9
# sbKvkjh+0p2ALPVOVpEhNSXDOW5kf1O6nA+tGSOEy/S6A4aN91/w0FK/jJSHvMAh
# dCVfGCi2zCcoOCWYOUo2z3yxkq4cI6epZuxhH2rhKEmdX4jiJV3TIUs+UsS1Vz8k
# A/DRelsv1SPjcF0PUUZ3s/gA4bysAoJf28AVs70b1FVL5zmhD+kjSbwYuER8ReTB
# w3J64HLnJN+/RpnF78IcV9uDjexNSTCnq47f7Fufr/zdsGbiwZeBe+3W7UvnSSmn
# Eyimp31ngOaKYnhfsi+E11ecXL93KCjx7W3DKI8sj0A3T8HhhUSJxAlMxdSlQy90
# lfdu+HggWCwTXWCVmj5PM4TasIgX3p5O9JawvEagbJjS4NaIjAsCAwEAAaOCAe0w
# ggHpMBAGCSsGAQQBgjcVAQQDAgEAMB0GA1UdDgQWBBRIbmTlUAXTgqoXNzcitW2o
# ynUClTAZBgkrBgEEAYI3FAIEDB4KAFMAdQBiAEMAQTALBgNVHQ8EBAMCAYYwDwYD
# VR0TAQH/BAUwAwEB/zAfBgNVHSMEGDAWgBRyLToCMZBDuRQFTuHqp8cx0SOJNDBa
# BgNVHR8EUzBRME+gTaBLhklodHRwOi8vY3JsLm1pY3Jvc29mdC5jb20vcGtpL2Ny
# bC9wcm9kdWN0cy9NaWNSb29DZXJBdXQyMDExXzIwMTFfMDNfMjIuY3JsMF4GCCsG
# AQUFBwEBBFIwUDBOBggrBgEFBQcwAoZCaHR0cDovL3d3dy5taWNyb3NvZnQuY29t
# L3BraS9jZXJ0cy9NaWNSb29DZXJBdXQyMDExXzIwMTFfMDNfMjIuY3J0MIGfBgNV
# HSAEgZcwgZQwgZEGCSsGAQQBgjcuAzCBgzA/BggrBgEFBQcCARYzaHR0cDovL3d3
# dy5taWNyb3NvZnQuY29tL3BraW9wcy9kb2NzL3ByaW1hcnljcHMuaHRtMEAGCCsG
# AQUFBwICMDQeMiAdAEwAZQBnAGEAbABfAHAAbwBsAGkAYwB5AF8AcwB0AGEAdABl
# AG0AZQBuAHQALiAdMA0GCSqGSIb3DQEBCwUAA4ICAQBn8oalmOBUeRou09h0ZyKb
# C5YR4WOSmUKWfdJ5DJDBZV8uLD74w3LRbYP+vj/oCso7v0epo/Np22O/IjWll11l
# hJB9i0ZQVdgMknzSGksc8zxCi1LQsP1r4z4HLimb5j0bpdS1HXeUOeLpZMlEPXh6
# I/MTfaaQdION9MsmAkYqwooQu6SpBQyb7Wj6aC6VoCo/KmtYSWMfCWluWpiW5IP0
# wI/zRive/DvQvTXvbiWu5a8n7dDd8w6vmSiXmE0OPQvyCInWH8MyGOLwxS3OW560
# STkKxgrCxq2u5bLZ2xWIUUVYODJxJxp/sfQn+N4sOiBpmLJZiWhub6e3dMNABQam
# ASooPoI/E01mC8CzTfXhj38cbxV9Rad25UAqZaPDXVJihsMdYzaXht/a8/jyFqGa
# J+HNpZfQ7l1jQeNbB5yHPgZ3BtEGsXUfFL5hYbXw3MYbBL7fQccOKO7eZS/sl/ah
# XJbYANahRr1Z85elCUtIEJmAH9AAKcWxm6U/RXceNcbSoqKfenoi+kiVH6v7RyOA
# 9Z74v2u3S5fi63V4GuzqN5l5GEv/1rMjaHXmr/r8i+sLgOppO6/8MO0ETI7f33Vt
# Y5E90Z1WTk+/gFcioXgRMiF670EKsT/7qMykXcGhiJtXcVZOSEXAQsmbdlsKgEhr
# /Xmfwb1tbWrJUnMTDXpQzTGCGgowghoGAgEBMIGVMH4xCzAJBgNVBAYTAlVTMRMw
# EQYDVQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVN
# aWNyb3NvZnQgQ29ycG9yYXRpb24xKDAmBgNVBAMTH01pY3Jvc29mdCBDb2RlIFNp
# Z25pbmcgUENBIDIwMTECEzMAAAQEbHQG/1crJ3IAAAAABAQwDQYJYIZIAWUDBAIB
# BQCgga4wGQYJKoZIhvcNAQkDMQwGCisGAQQBgjcCAQQwHAYKKwYBBAGCNwIBCzEO
# MAwGCisGAQQBgjcCARUwLwYJKoZIhvcNAQkEMSIEIPm+8pvYged0LHeVeHsvorB4
# LxuGVLbfI0nrB3OpNkYAMEIGCisGAQQBgjcCAQwxNDAyoBSAEgBNAGkAYwByAG8A
# cwBvAGYAdKEagBhodHRwOi8vd3d3Lm1pY3Jvc29mdC5jb20wDQYJKoZIhvcNAQEB
# BQAEggEACzLkuUVrkw/SqXcL4a2axDuT7Fx87Cr/9jjl/9GVLvI5e+Gnggw8RIlw
# 4pNh5U6v/Iw8RAdYR6sTBANVMYt+uBmlL58oc6YB/WTk6mPHriBRsBK9yWv1dlno
# xlZ3aAQKVKql1B69JFSrJzdfS8OJiPkxS2bBUrRmoJ7RgvAUShnDgbNEU0tjqHo5
# FbvmKQsaPwW8/swT0y+0JF3+rO39BMdnRGnXN7qRAAUQTUZskQlczI7X4pZBFXbn
# wmF9mYf7MfGWTPnJfEjmtPNYDWknAEmyQc1sktda4fW25IK21kwYDKVUguEwvpkH
# Q1PAtR+slD9/EJ5iQ8IOMJ6EPYXAUKGCF5QwgheQBgorBgEEAYI3AwMBMYIXgDCC
# F3wGCSqGSIb3DQEHAqCCF20wghdpAgEDMQ8wDQYJYIZIAWUDBAIBBQAwggFSBgsq
# hkiG9w0BCRABBKCCAUEEggE9MIIBOQIBAQYKKwYBBAGEWQoDATAxMA0GCWCGSAFl
# AwQCAQUABCDkHxkY6UlgNddNuYNUyZArNF6GQmHxTIKqPkbkpfDX7AIGaEsRScZT
# GBMyMDI1MDcwOTExMDcyNS44NDJaMASAAgH0oIHRpIHOMIHLMQswCQYDVQQGEwJV
# UzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwGA1UE
# ChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSUwIwYDVQQLExxNaWNyb3NvZnQgQW1l
# cmljYSBPcGVyYXRpb25zMScwJQYDVQQLEx5uU2hpZWxkIFRTUyBFU046N0YwMC0w
# NUUwLUQ5NDcxJTAjBgNVBAMTHE1pY3Jvc29mdCBUaW1lLVN0YW1wIFNlcnZpY2Wg
# ghHqMIIHIDCCBQigAwIBAgITMwAAAgbXvFE4mCPsLAABAAACBjANBgkqhkiG9w0B
# AQsFADB8MQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UE
# BxMHUmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSYwJAYD
# VQQDEx1NaWNyb3NvZnQgVGltZS1TdGFtcCBQQ0EgMjAxMDAeFw0yNTAxMzAxOTQy
# NTBaFw0yNjA0MjIxOTQyNTBaMIHLMQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2Fz
# aGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENv
# cnBvcmF0aW9uMSUwIwYDVQQLExxNaWNyb3NvZnQgQW1lcmljYSBPcGVyYXRpb25z
# MScwJQYDVQQLEx5uU2hpZWxkIFRTUyBFU046N0YwMC0wNUUwLUQ5NDcxJTAjBgNV
# BAMTHE1pY3Jvc29mdCBUaW1lLVN0YW1wIFNlcnZpY2UwggIiMA0GCSqGSIb3DQEB
# AQUAA4ICDwAwggIKAoICAQDpRIWbIM3Rlr397cjHaYx85l7I+ZVWGMCBCM911BpU
# 6+IGWCqksqgqefZFEjKzNVDYC9YcgITAz276NGgvECm4ZfNv/FPwcaSDz7xbDbsO
# oxbwQoHUNRro+x5ubZhT6WJeU97F06+vDjAw/Yt1vWOgRTqmP/dNr9oqIbE5oCLY
# dH3wI/noYmsJVc7966n+B7UAGAWU2se3Lz+xdxnNsNX4CR6zIMVJTSezP/2STNcx
# JTu9k2sl7/vzOhxJhCQ38rdaEoqhGHrXrmVkEhSv+S00DMJc1OIXxqfbwPjMqEVp
# 7K3kmczCkbum1BOIJ2wuDAbKuJelpteNZj/S58NSQw6khfuJAluqHK3igkS/Oux4
# 9qTP+rU+PQeNuD+GtrCopFucRmanQvxISGNoxnBq3UeDTqphm6aI7GMHtFD6DOjJ
# lllH1gVWXPTyivf+4tN8TmO6yIgB4uP00bH9jn/dyyxSjxPQ2nGvZtgtqnvq3h3T
# RjRnkc+e1XB1uatDa1zUcS7r3iodTpyATe2hgkVX3m4DhRzI6A4SJ6fbJM9isLH8
# AGKcymisKzYupAeFSTJ10JEFa6MjHQYYohoCF77R0CCwMNjvE4XfLHu+qKPY8GQf
# sZdigQ9clUAiydFmVt61hytoxZP7LmXbzjD0VecyzZoL4Equ1XszBsulAr5Ld2Kw
# cwIDAQABo4IBSTCCAUUwHQYDVR0OBBYEFO0wsLKdDGpT97cx3Iymyo/SBm4SMB8G
# A1UdIwQYMBaAFJ+nFV0AXmJdg/Tl0mWnG1M1GelyMF8GA1UdHwRYMFYwVKBSoFCG
# Tmh0dHA6Ly93d3cubWljcm9zb2Z0LmNvbS9wa2lvcHMvY3JsL01pY3Jvc29mdCUy
# MFRpbWUtU3RhbXAlMjBQQ0ElMjAyMDEwKDEpLmNybDBsBggrBgEFBQcBAQRgMF4w
# XAYIKwYBBQUHMAKGUGh0dHA6Ly93d3cubWljcm9zb2Z0LmNvbS9wa2lvcHMvY2Vy
# dHMvTWljcm9zb2Z0JTIwVGltZS1TdGFtcCUyMFBDQSUyMDIwMTAoMSkuY3J0MAwG
# A1UdEwEB/wQCMAAwFgYDVR0lAQH/BAwwCgYIKwYBBQUHAwgwDgYDVR0PAQH/BAQD
# AgeAMA0GCSqGSIb3DQEBCwUAA4ICAQB23GZOfe9ThTUvD29i4t6lDpxJhpVRMme+
# UbyZhBFCZhoGTtjDdphAArU2Q61WYg3YVcl2RdJm5PUbZ2bA77zk+qtLxC+3dNxV
# sTcdtxPDSSWgwBHxTj6pCmoDNXolAYsWpvHQFCHDqEfAiBxX1dmaXbiTP1d0Xffv
# gR6dshUcqaH/mFfjDZAxLU1s6HcVgCvBQJlJ7xEG5jFKdtqapKWcbUHwTVqXQGbI
# lHVClNJ3yqW6Z3UJH/CFcYiLV/e68urTmGtiZxGSYb4SBSPArTrTYeHOlQIj/7lo
# VWmfWX2y4AGV/D+MzyZMyvFw4VyL0Vgq96EzQKyteiVeBaVEjxQKo3AcPULRF4Uz
# z98P2tCM5XbFZ3Qoj9PLg3rgFXr0oJEhfh2tqUrhTJd13+i4/fek9zWicoshlwXg
# Fu002ZWBVzASEFuqED48qyulZ/2jGJBcta+Fdk2loP2K3oSj4PQQe1MzzVZO52AX
# O42MHlhm3SHo3/RhQ+I1A0Ny+9uAehkQH6LrxkrVNvZG4f0PAKMbqUcXG7xznKJ0
# x0HYr5ayWGbHKZRcObU+/34ZpL9NrXOedVDXmSd2ylKSl/vvi1QwNJqXJl/+gJkQ
# EetqmHAUFQkFtemi8MUXQG2w/RDHXXwWAjE+qIDZLQ/k4z2Z216tWaR6RDKHGkwe
# CoDtQtzkHTCCB3EwggVZoAMCAQICEzMAAAAVxedrngKbSZkAAAAAABUwDQYJKoZI
# hvcNAQELBQAwgYgxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNoaW5ndG9uMRAw
# DgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNyb3NvZnQgQ29ycG9yYXRpb24x
# MjAwBgNVBAMTKU1pY3Jvc29mdCBSb290IENlcnRpZmljYXRlIEF1dGhvcml0eSAy
# MDEwMB4XDTIxMDkzMDE4MjIyNVoXDTMwMDkzMDE4MzIyNVowfDELMAkGA1UEBhMC
# VVMxEzARBgNVBAgTCldhc2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNV
# BAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEmMCQGA1UEAxMdTWljcm9zb2Z0IFRp
# bWUtU3RhbXAgUENBIDIwMTAwggIiMA0GCSqGSIb3DQEBAQUAA4ICDwAwggIKAoIC
# AQDk4aZM57RyIQt5osvXJHm9DtWC0/3unAcH0qlsTnXIyjVX9gF/bErg4r25Phdg
# M/9cT8dm95VTcVrifkpa/rg2Z4VGIwy1jRPPdzLAEBjoYH1qUoNEt6aORmsHFPPF
# dvWGUNzBRMhxXFExN6AKOG6N7dcP2CZTfDlhAnrEqv1yaa8dq6z2Nr41JmTamDu6
# GnszrYBbfowQHJ1S/rboYiXcag/PXfT+jlPP1uyFVk3v3byNpOORj7I5LFGc6XBp
# Dco2LXCOMcg1KL3jtIckw+DJj361VI/c+gVVmG1oO5pGve2krnopN6zL64NF50Zu
# yjLVwIYwXE8s4mKyzbnijYjklqwBSru+cakXW2dg3viSkR4dPf0gz3N9QZpGdc3E
# XzTdEonW/aUgfX782Z5F37ZyL9t9X4C626p+Nuw2TPYrbqgSUei/BQOj0XOmTTd0
# lBw0gg/wEPK3Rxjtp+iZfD9M269ewvPV2HM9Q07BMzlMjgK8QmguEOqEUUbi0b1q
# GFphAXPKZ6Je1yh2AuIzGHLXpyDwwvoSCtdjbwzJNmSLW6CmgyFdXzB0kZSU2LlQ
# +QuJYfM2BjUYhEfb3BvR/bLUHMVr9lxSUV0S2yW6r1AFemzFER1y7435UsSFF5PA
# PBXbGjfHCBUYP3irRbb1Hode2o+eFnJpxq57t7c+auIurQIDAQABo4IB3TCCAdkw
# EgYJKwYBBAGCNxUBBAUCAwEAATAjBgkrBgEEAYI3FQIEFgQUKqdS/mTEmr6CkTxG
# NSnPEP8vBO4wHQYDVR0OBBYEFJ+nFV0AXmJdg/Tl0mWnG1M1GelyMFwGA1UdIARV
# MFMwUQYMKwYBBAGCN0yDfQEBMEEwPwYIKwYBBQUHAgEWM2h0dHA6Ly93d3cubWlj
# cm9zb2Z0LmNvbS9wa2lvcHMvRG9jcy9SZXBvc2l0b3J5Lmh0bTATBgNVHSUEDDAK
# BggrBgEFBQcDCDAZBgkrBgEEAYI3FAIEDB4KAFMAdQBiAEMAQTALBgNVHQ8EBAMC
# AYYwDwYDVR0TAQH/BAUwAwEB/zAfBgNVHSMEGDAWgBTV9lbLj+iiXGJo0T2UkFvX
# zpoYxDBWBgNVHR8ETzBNMEugSaBHhkVodHRwOi8vY3JsLm1pY3Jvc29mdC5jb20v
# cGtpL2NybC9wcm9kdWN0cy9NaWNSb29DZXJBdXRfMjAxMC0wNi0yMy5jcmwwWgYI
# KwYBBQUHAQEETjBMMEoGCCsGAQUFBzAChj5odHRwOi8vd3d3Lm1pY3Jvc29mdC5j
# b20vcGtpL2NlcnRzL01pY1Jvb0NlckF1dF8yMDEwLTA2LTIzLmNydDANBgkqhkiG
# 9w0BAQsFAAOCAgEAnVV9/Cqt4SwfZwExJFvhnnJL/Klv6lwUtj5OR2R4sQaTlz0x
# M7U518JxNj/aZGx80HU5bbsPMeTCj/ts0aGUGCLu6WZnOlNN3Zi6th542DYunKmC
# VgADsAW+iehp4LoJ7nvfam++Kctu2D9IdQHZGN5tggz1bSNU5HhTdSRXud2f8449
# xvNo32X2pFaq95W2KFUn0CS9QKC/GbYSEhFdPSfgQJY4rPf5KYnDvBewVIVCs/wM
# nosZiefwC2qBwoEZQhlSdYo2wh3DYXMuLGt7bj8sCXgU6ZGyqVvfSaN0DLzskYDS
# PeZKPmY7T7uG+jIa2Zb0j/aRAfbOxnT99kxybxCrdTDFNLB62FD+CljdQDzHVG2d
# Y3RILLFORy3BFARxv2T5JL5zbcqOCb2zAVdJVGTZc9d/HltEAY5aGZFrDZ+kKNxn
# GSgkujhLmm77IVRrakURR6nxt67I6IleT53S0Ex2tVdUCbFpAUR+fKFhbHP+Crvs
# QWY9af3LwUFJfn6Tvsv4O+S3Fb+0zj6lMVGEvL8CwYKiexcdFYmNcP7ntdAoGokL
# jzbaukz5m/8K6TT4JDVnK+ANuOaMmdbhIurwJ0I9JZTmdHRbatGePu1+oDEzfbzL
# 6Xu/OHBE0ZDxyKs6ijoIYn/ZcGNTTY3ugm2lBRDBcQZqELQdVTNYs6FwZvKhggNN
# MIICNQIBATCB+aGB0aSBzjCByzELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldhc2hp
# bmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBDb3Jw
# b3JhdGlvbjElMCMGA1UECxMcTWljcm9zb2Z0IEFtZXJpY2EgT3BlcmF0aW9uczEn
# MCUGA1UECxMeblNoaWVsZCBUU1MgRVNOOjdGMDAtMDVFMC1EOTQ3MSUwIwYDVQQD
# ExxNaWNyb3NvZnQgVGltZS1TdGFtcCBTZXJ2aWNloiMKAQEwBwYFKw4DAhoDFQAE
# a0f118XHM/VNdqKBs4QXxNnN96CBgzCBgKR+MHwxCzAJBgNVBAYTAlVTMRMwEQYD
# VQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNy
# b3NvZnQgQ29ycG9yYXRpb24xJjAkBgNVBAMTHU1pY3Jvc29mdCBUaW1lLVN0YW1w
# IFBDQSAyMDEwMA0GCSqGSIb3DQEBCwUAAgUA7Bh9QjAiGA8yMDI1MDcwOTA1MzE0
# NloYDzIwMjUwNzEwMDUzMTQ2WjB0MDoGCisGAQQBhFkKBAExLDAqMAoCBQDsGH1C
# AgEAMAcCAQACAin7MAcCAQACAhOrMAoCBQDsGc7CAgEAMDYGCisGAQQBhFkKBAIx
# KDAmMAwGCisGAQQBhFkKAwKgCjAIAgEAAgMHoSChCjAIAgEAAgMBhqAwDQYJKoZI
# hvcNAQELBQADggEBAChiucSv2xFTuI2DXCEbr4WDI/QnyPFDKoNNQijL5EIKZs73
# rhM8jlGA0MPnhnT5rynFhHJeC0mOfocmgKf+g/i1cbD3ol/LsTxvvk0/cVpW4VXC
# dvOhrsxrFLzy4ilRDZ2jTHUeXv6hVTnwWmfgUSHJ+EHzHlJBodEN7LKNrLHM+Vrz
# s8zYnDVGzCxjG5V/ptY0PnjKes6qckrcGkNauylskM6ks+IA3y3QejEjxZwy4QYw
# +i6U4bH6lqiIzA+7AO9A95Q/yNbZwN5OyMC/kKE8ilGjG+A5aIHl0XWJcfTe///+
# 3+eISsX279oJ61g8kJ8DeRZyTLU18TjnOXox2scxggQNMIIECQIBATCBkzB8MQsw
# CQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9u
# ZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSYwJAYDVQQDEx1NaWNy
# b3NvZnQgVGltZS1TdGFtcCBQQ0EgMjAxMAITMwAAAgbXvFE4mCPsLAABAAACBjAN
# BglghkgBZQMEAgEFAKCCAUowGgYJKoZIhvcNAQkDMQ0GCyqGSIb3DQEJEAEEMC8G
# CSqGSIb3DQEJBDEiBCAEmsHG9HQsylBIEg6tPXNg0j+94rY2cbXTMiW1/hLRwzCB
# +gYLKoZIhvcNAQkQAi8xgeowgecwgeQwgb0EIODo9ZSIkZ6dVtKT+E/uZx2WAy7K
# iXM5R1JIOhNJf0vSMIGYMIGApH4wfDELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldh
# c2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBD
# b3Jwb3JhdGlvbjEmMCQGA1UEAxMdTWljcm9zb2Z0IFRpbWUtU3RhbXAgUENBIDIw
# MTACEzMAAAIG17xROJgj7CwAAQAAAgYwIgQgpZrAF23xvfE13FfXgxFae+PBDfel
# 7lk/GABByKeqxBEwDQYJKoZIhvcNAQELBQAEggIAADgMNLp3cdOskou3gO3pAJDC
# NaVo1eLi+POIc6CaL8Zed8Lvb7ynH+DyVBorhY1HtVe9UNftpN4nEnu8CRYnuedS
# KdRpqHBnfC1jIjAWdt7+wiVLidAvw1dnlMc1Nu7lyseAozcxkutqp8riiRhQjY0U
# Eu8uEH6ZwzgZbKtwG0V80v1sv3UcMY0nowVdZglzct+WUL95LU8mI+W5A36Fx5wU
# IEWF3IS7COGRIzR9gDM6RxMyjL5JmjxE9D2gXUehZr6kxqMrUd//cRqDbb1ncLkE
# /lN/2LI6Yo0CtsoAc4r17oVoQ2Y9J8PY2VYJhuSDermW0B/RgRYgb/FpAzfF9MFB
# 00kbV6ex+NV3WRTBUG6ip+R0f3tb0ItYjORGTxzjfNu27ugWoaZrUQGns0gAZJf+
# bC/fak2ZQTeM2roWVQyoei09TW9N8keVFCR9jZIJ6MaSUZh8ZhH786VWAC5QyZLV
# Go06IpqCHRyFDfUNipVW+X9gxDHDpLu3cveoCDa00Ubw8De/ObihGIoxRoWiTG2I
# Ot7j2VWYF1hY0HuMcfkTk277lThasbNTEp0wnUyLUg+M0EuRSZ9EenJ84BoViSgu
# CX8JZ65tZz+6ppzB0TWYIYAPfd6M81ePhrRmEqdiAh3oC65lI2rH/AOU2kJdOTqI
# 1S5m6LOdcXH4oWg2qnA=
# SIG # End signature block
