﻿<?xml version="1.0" encoding="utf-8"?>
<helpItems schema="maml" xmlns="http://msh">
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Add-MgEnvironment</command:name>
      <command:verb>Add</command:verb>
      <command:noun>MgEnvironment</command:noun>
      <maml:description>
        <maml:para>Adds Microsoft Graph environment to the settings file.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>Adds Microsoft Graph environment to the settings file.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Add-MgEnvironment</maml:name>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName)" position="0" aliases="none">
          <maml:name>Name</maml:name>
          <maml:description>
            <maml:para>The environment name.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName)" position="1" aliases="AzureADUrl">
          <maml:name>AzureADEndpoint</maml:name>
          <maml:description>
            <maml:para>The base URL for Azure AD endpoint to get access tokens for Microsoft Graph endpoint.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName)" position="2" aliases="GraphUrl">
          <maml:name>GraphEndpoint</maml:name>
          <maml:description>
            <maml:para>The service root endpoint for Microsoft Graph.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="proga">
          <maml:name>ProgressAction</maml:name>
          <maml:description>
            <maml:para>{{ Fill ProgressAction Description }}</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
          <dev:type>
            <maml:name>ActionPreference</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="cf">
          <maml:name>Confirm</maml:name>
          <maml:description>
            <maml:para>Prompts you for confirmation before running the cmdlet.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="wi">
          <maml:name>WhatIf</maml:name>
          <maml:description>
            <maml:para>Shows what would happen if the cmdlet runs. The cmdlet is not run.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName)" position="1" aliases="AzureADUrl">
        <maml:name>AzureADEndpoint</maml:name>
        <maml:description>
          <maml:para>The base URL for Azure AD endpoint to get access tokens for Microsoft Graph endpoint.</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName)" position="2" aliases="GraphUrl">
        <maml:name>GraphEndpoint</maml:name>
        <maml:description>
          <maml:para>The service root endpoint for Microsoft Graph.</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName)" position="0" aliases="none">
        <maml:name>Name</maml:name>
        <maml:description>
          <maml:para>The environment name.</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="proga">
        <maml:name>ProgressAction</maml:name>
        <maml:description>
          <maml:para>{{ Fill ProgressAction Description }}</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
        <dev:type>
          <maml:name>ActionPreference</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="cf">
        <maml:name>Confirm</maml:name>
        <maml:description>
          <maml:para>Prompts you for confirmation before running the cmdlet.</maml:para>
        </maml:description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="wi">
        <maml:name>WhatIf</maml:name>
        <maml:description>
          <maml:para>Shows what would happen if the cmdlet runs. The cmdlet is not run.</maml:para>
        </maml:description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>System.String</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>Microsoft.Graph.PowerShell.Authentication.Models.GraphEnvironment</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>----------- Example 1: Add user defined environment -----------</maml:title>
        <dev:code>PS C:\&gt; Add-MgEnvironment -Name "Canary" -GraphEndpoint "https://canary.graph.microsoft.com" -AzureADEndpoint "https://login.microsoftonline.com"
Name     AzureADEndpoint                      GraphEndpoint                 Type
----     ---------------                      -------------                 ----
Canary    https://login.microsoftonline.com   https://microsoftgraph.com User-defined</dev:code>
        <dev:remarks>
          <maml:para>Adds user defined environment.</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks>
      <maml:navigationLink>
        <maml:linkText>https://learn.microsoft.com/en-us/powershell/module/microsoft.graph.authentication/add-mgenvironment</maml:linkText>
        <maml:uri>https://learn.microsoft.com/en-us/powershell/module/microsoft.graph.authentication/add-mgenvironment</maml:uri>
      </maml:navigationLink>
    </command:relatedLinks>
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Connect-MgGraph</command:name>
      <command:verb>Connect</command:verb>
      <command:noun>MgGraph</command:noun>
      <maml:description>
        <maml:para>Microsoft Graph PowerShell supports two types of authentication: delegated and app-only access. There are a number of cmdlets that can be used to manage the different parameters required during authentication, for example, environment, application ID, and certificate.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>You must invoke Connect-MgGraph before any commands that access Microsoft Graph. This cmdlet gets the access token using the Microsoft Authentication Library</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Connect-MgGraph</maml:name>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="1" aliases="none">
          <maml:name>AccessToken</maml:name>
          <maml:description>
            <maml:para>Specifies a bearer token for Microsoft Graph service. Access tokens do timeout and you'll have to handle their refresh.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">SecureString</command:parameterValue>
          <dev:type>
            <maml:name>SecureString</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ClientTimeout</maml:name>
          <maml:description>
            <maml:para>Sets the HTTP client timeout in seconds.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">Double</command:parameterValue>
          <dev:type>
            <maml:name>Double</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="EnvironmentName, NationalCloud">
          <maml:name>Environment</maml:name>
          <maml:description>
            <maml:para>The name of the national cloud environment to connect to. By default global cloud is used.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>NoWelcome</maml:name>
          <maml:description>
            <maml:para>Hides the welcome message.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="proga">
          <maml:name>ProgressAction</maml:name>
          <maml:description>
            <maml:para>{{ Fill ProgressAction Description }}</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
          <dev:type>
            <maml:name>ActionPreference</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
      <command:syntaxItem>
        <maml:name>Connect-MgGraph</maml:name>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="1" aliases="AppId, ApplicationId">
          <maml:name>ClientId</maml:name>
          <maml:description>
            <maml:para>The client id of your application.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="2" aliases="CertificateSubject, CertificateName">
          <maml:name>CertificateSubjectName</maml:name>
          <maml:description>
            <maml:para>The subject distinguished name of a certificate. The Certificate will be retrieved from the current user's certificate store.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="3" aliases="none">
          <maml:name>CertificateThumbprint</maml:name>
          <maml:description>
            <maml:para>The thumbprint of your certificate. The Certificate will be retrieved from the current user's certificate store.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Certificate</maml:name>
          <maml:description>
            <maml:para>An X.509 certificate supplied during invocation.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">X509Certificate2</command:parameterValue>
          <dev:type>
            <maml:name>X509Certificate2</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ClientTimeout</maml:name>
          <maml:description>
            <maml:para>Sets the HTTP client timeout in seconds.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">Double</command:parameterValue>
          <dev:type>
            <maml:name>Double</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ContextScope</maml:name>
          <maml:description>
            <maml:para>Determines the scope of authentication context. This accepts `Process` for the current process, or `CurrentUser` for all sessions started by user.</maml:para>
          </maml:description>
          <command:parameterValueGroup>
            <command:parameterValue required="false" command:variableLength="false">Process</command:parameterValue>
            <command:parameterValue required="false" command:variableLength="false">CurrentUser</command:parameterValue>
          </command:parameterValueGroup>
          <command:parameterValue required="true" variableLength="false">ContextScope</command:parameterValue>
          <dev:type>
            <maml:name>ContextScope</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="EnvironmentName, NationalCloud">
          <maml:name>Environment</maml:name>
          <maml:description>
            <maml:para>The name of the national cloud environment to connect to. By default global cloud is used.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>NoWelcome</maml:name>
          <maml:description>
            <maml:para>Hides the welcome message.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="proga">
          <maml:name>ProgressAction</maml:name>
          <maml:description>
            <maml:para>{{ Fill ProgressAction Description }}</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
          <dev:type>
            <maml:name>ActionPreference</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>SendCertificateChain</maml:name>
          <maml:description>
            <maml:para>Include x5c header in client claims when acquiring a token to enable subject name / issuer based authentication using given certificate.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
          <dev:type>
            <maml:name>Boolean</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="Audience, Tenant">
          <maml:name>TenantId</maml:name>
          <maml:description>
            <maml:para>The id of the tenant to connect to. You can also use this parameter to specify your sign-in audience. i.e., common, organizations, or consumers. See https://docs.microsoft.com/en-us/azure/active-directory/develop/msal-client-application-configuration#authority.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
      <command:syntaxItem>
        <maml:name>Connect-MgGraph</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="1" aliases="AppId, ApplicationId">
          <maml:name>ClientId</maml:name>
          <maml:description>
            <maml:para>The client id of your application.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="1" aliases="none">
          <maml:name>Scopes</maml:name>
          <maml:description>
            <maml:para>An array of delegated permissions to consent to.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
          <dev:type>
            <maml:name>String[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ClientTimeout</maml:name>
          <maml:description>
            <maml:para>Sets the HTTP client timeout in seconds.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">Double</command:parameterValue>
          <dev:type>
            <maml:name>Double</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ContextScope</maml:name>
          <maml:description>
            <maml:para>Determines the scope of authentication context. This accepts `Process` for the current process, or `CurrentUser` for all sessions started by user.</maml:para>
          </maml:description>
          <command:parameterValueGroup>
            <command:parameterValue required="false" command:variableLength="false">Process</command:parameterValue>
            <command:parameterValue required="false" command:variableLength="false">CurrentUser</command:parameterValue>
          </command:parameterValueGroup>
          <command:parameterValue required="true" variableLength="false">ContextScope</command:parameterValue>
          <dev:type>
            <maml:name>ContextScope</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="EnvironmentName, NationalCloud">
          <maml:name>Environment</maml:name>
          <maml:description>
            <maml:para>The name of the national cloud environment to connect to. By default global cloud is used.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>NoWelcome</maml:name>
          <maml:description>
            <maml:para>Hides the welcome message.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="proga">
          <maml:name>ProgressAction</maml:name>
          <maml:description>
            <maml:para>{{ Fill ProgressAction Description }}</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
          <dev:type>
            <maml:name>ActionPreference</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="Audience, Tenant">
          <maml:name>TenantId</maml:name>
          <maml:description>
            <maml:para>The id of the tenant to connect to. You can also use this parameter to specify your sign-in audience. i.e., common, organizations, or consumers. See https://docs.microsoft.com/en-us/azure/active-directory/develop/msal-client-application-configuration#authority.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="UseDeviceAuthentication, DeviceCode, DeviceAuth, Device">
          <maml:name>UseDeviceCode</maml:name>
          <maml:description>
            <maml:para>Use device code authentication instead of a browser control.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
      <command:syntaxItem>
        <maml:name>Connect-MgGraph</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="1" aliases="AppId, ApplicationId">
          <maml:name>ClientId</maml:name>
          <maml:description>
            <maml:para>The client id of your application.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="1" aliases="ManagedIdentity, ManagedServiceIdentity, MSI">
          <maml:name>Identity</maml:name>
          <maml:description>
            <maml:para>Login using a Managed Identity.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ClientTimeout</maml:name>
          <maml:description>
            <maml:para>Sets the HTTP client timeout in seconds.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">Double</command:parameterValue>
          <dev:type>
            <maml:name>Double</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ContextScope</maml:name>
          <maml:description>
            <maml:para>Determines the scope of authentication context. This accepts `Process` for the current process, or `CurrentUser` for all sessions started by user.</maml:para>
          </maml:description>
          <command:parameterValueGroup>
            <command:parameterValue required="false" command:variableLength="false">Process</command:parameterValue>
            <command:parameterValue required="false" command:variableLength="false">CurrentUser</command:parameterValue>
          </command:parameterValueGroup>
          <command:parameterValue required="true" variableLength="false">ContextScope</command:parameterValue>
          <dev:type>
            <maml:name>ContextScope</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="EnvironmentName, NationalCloud">
          <maml:name>Environment</maml:name>
          <maml:description>
            <maml:para>The name of the national cloud environment to connect to. By default global cloud is used.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>NoWelcome</maml:name>
          <maml:description>
            <maml:para>Hides the welcome message.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="proga">
          <maml:name>ProgressAction</maml:name>
          <maml:description>
            <maml:para>{{ Fill ProgressAction Description }}</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
          <dev:type>
            <maml:name>ActionPreference</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
      <command:syntaxItem>
        <maml:name>Connect-MgGraph</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="SecretCredential, Credential">
          <maml:name>ClientSecretCredential</maml:name>
          <maml:description>
            <maml:para>The PSCredential object provides the application ID and client secret for service principal credentials. For more information about the PSCredential object, type Get-Help Get-Credential.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">PSCredential</command:parameterValue>
          <dev:type>
            <maml:name>PSCredential</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ClientTimeout</maml:name>
          <maml:description>
            <maml:para>Sets the HTTP client timeout in seconds.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">Double</command:parameterValue>
          <dev:type>
            <maml:name>Double</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ContextScope</maml:name>
          <maml:description>
            <maml:para>Determines the scope of authentication context. This accepts `Process` for the current process, or `CurrentUser` for all sessions started by user.</maml:para>
          </maml:description>
          <command:parameterValueGroup>
            <command:parameterValue required="false" command:variableLength="false">Process</command:parameterValue>
            <command:parameterValue required="false" command:variableLength="false">CurrentUser</command:parameterValue>
          </command:parameterValueGroup>
          <command:parameterValue required="true" variableLength="false">ContextScope</command:parameterValue>
          <dev:type>
            <maml:name>ContextScope</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="EnvironmentName, NationalCloud">
          <maml:name>Environment</maml:name>
          <maml:description>
            <maml:para>The name of the national cloud environment to connect to. By default global cloud is used.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>NoWelcome</maml:name>
          <maml:description>
            <maml:para>Hides the welcome message.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="proga">
          <maml:name>ProgressAction</maml:name>
          <maml:description>
            <maml:para>{{ Fill ProgressAction Description }}</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
          <dev:type>
            <maml:name>ActionPreference</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="Audience, Tenant">
          <maml:name>TenantId</maml:name>
          <maml:description>
            <maml:para>The id of the tenant to connect to. You can also use this parameter to specify your sign-in audience. i.e., common, organizations, or consumers. See https://docs.microsoft.com/en-us/azure/active-directory/develop/msal-client-application-configuration#authority.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
      <command:syntaxItem>
        <maml:name>Connect-MgGraph</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ClientTimeout</maml:name>
          <maml:description>
            <maml:para>Sets the HTTP client timeout in seconds.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">Double</command:parameterValue>
          <dev:type>
            <maml:name>Double</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ContextScope</maml:name>
          <maml:description>
            <maml:para>Determines the scope of authentication context. This accepts `Process` for the current process, or `CurrentUser` for all sessions started by user.</maml:para>
          </maml:description>
          <command:parameterValueGroup>
            <command:parameterValue required="false" command:variableLength="false">Process</command:parameterValue>
            <command:parameterValue required="false" command:variableLength="false">CurrentUser</command:parameterValue>
          </command:parameterValueGroup>
          <command:parameterValue required="true" variableLength="false">ContextScope</command:parameterValue>
          <dev:type>
            <maml:name>ContextScope</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="EnvironmentName, NationalCloud">
          <maml:name>Environment</maml:name>
          <maml:description>
            <maml:para>The name of the national cloud environment to connect to. By default global cloud is used.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>EnvironmentVariable</maml:name>
          <maml:description>
            <maml:para>Allows for authentication using environment variables configured on the host machine. See https://github.com/Azure/azure-sdk-for-net/tree/main/sdk/identity/Azure.Identity#environment-variables.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>NoWelcome</maml:name>
          <maml:description>
            <maml:para>Hides the welcome message.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="proga">
          <maml:name>ProgressAction</maml:name>
          <maml:description>
            <maml:para>{{ Fill ProgressAction Description }}</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
          <dev:type>
            <maml:name>ActionPreference</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="1" aliases="none">
        <maml:name>AccessToken</maml:name>
        <maml:description>
          <maml:para>Specifies a bearer token for Microsoft Graph service. Access tokens do timeout and you'll have to handle their refresh.</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">SecureString</command:parameterValue>
        <dev:type>
          <maml:name>SecureString</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Certificate</maml:name>
        <maml:description>
          <maml:para>An X.509 certificate supplied during invocation.</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">X509Certificate2</command:parameterValue>
        <dev:type>
          <maml:name>X509Certificate2</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="2" aliases="CertificateSubject, CertificateName">
        <maml:name>CertificateSubjectName</maml:name>
        <maml:description>
          <maml:para>The subject distinguished name of a certificate. The Certificate will be retrieved from the current user's certificate store.</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="3" aliases="none">
        <maml:name>CertificateThumbprint</maml:name>
        <maml:description>
          <maml:para>The thumbprint of your certificate. The Certificate will be retrieved from the current user's certificate store.</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="1" aliases="AppId, ApplicationId">
        <maml:name>ClientId</maml:name>
        <maml:description>
          <maml:para>The client id of your application.</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="SecretCredential, Credential">
        <maml:name>ClientSecretCredential</maml:name>
        <maml:description>
          <maml:para>The PSCredential object provides the application ID and client secret for service principal credentials. For more information about the PSCredential object, type Get-Help Get-Credential.</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">PSCredential</command:parameterValue>
        <dev:type>
          <maml:name>PSCredential</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>ClientTimeout</maml:name>
        <maml:description>
          <maml:para>Sets the HTTP client timeout in seconds.</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">Double</command:parameterValue>
        <dev:type>
          <maml:name>Double</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>ContextScope</maml:name>
        <maml:description>
          <maml:para>Determines the scope of authentication context. This accepts `Process` for the current process, or `CurrentUser` for all sessions started by user.</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">ContextScope</command:parameterValue>
        <dev:type>
          <maml:name>ContextScope</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="EnvironmentName, NationalCloud">
        <maml:name>Environment</maml:name>
        <maml:description>
          <maml:para>The name of the national cloud environment to connect to. By default global cloud is used.</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>EnvironmentVariable</maml:name>
        <maml:description>
          <maml:para>Allows for authentication using environment variables configured on the host machine. See https://github.com/Azure/azure-sdk-for-net/tree/main/sdk/identity/Azure.Identity#environment-variables.</maml:para>
        </maml:description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="1" aliases="ManagedIdentity, ManagedServiceIdentity, MSI">
        <maml:name>Identity</maml:name>
        <maml:description>
          <maml:para>Login using a Managed Identity.</maml:para>
        </maml:description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>NoWelcome</maml:name>
        <maml:description>
          <maml:para>Hides the welcome message.</maml:para>
        </maml:description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="proga">
        <maml:name>ProgressAction</maml:name>
        <maml:description>
          <maml:para>{{ Fill ProgressAction Description }}</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
        <dev:type>
          <maml:name>ActionPreference</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="1" aliases="none">
        <maml:name>Scopes</maml:name>
        <maml:description>
          <maml:para>An array of delegated permissions to consent to.</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
        <dev:type>
          <maml:name>String[]</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>SendCertificateChain</maml:name>
        <maml:description>
          <maml:para>Include x5c header in client claims when acquiring a token to enable subject name / issuer based authentication using given certificate.</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
        <dev:type>
          <maml:name>Boolean</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="Audience, Tenant">
        <maml:name>TenantId</maml:name>
        <maml:description>
          <maml:para>The id of the tenant to connect to. You can also use this parameter to specify your sign-in audience. i.e., common, organizations, or consumers. See https://docs.microsoft.com/en-us/azure/active-directory/develop/msal-client-application-configuration#authority.</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="UseDeviceAuthentication, DeviceCode, DeviceAuth, Device">
        <maml:name>UseDeviceCode</maml:name>
        <maml:description>
          <maml:para>Use device code authentication instead of a browser control.</maml:para>
        </maml:description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>None</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>System.Object</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>Example 1: Delegated access: Using interactive authentication, where you provide the scopes that you require during your session</maml:title>
        <dev:code>PS C:\&gt; Connect-MgGraph -Scopes "User.Read.All", "Group.ReadWrite.All"</dev:code>
        <dev:remarks>
          <maml:para>This example shows how to authenticate to graph with scopes.</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>----- Example 2: Delegated access: Using device code flow -----</maml:title>
        <dev:code>PS C:\&gt; Connect-MgGraph -Scopes "User.Read.All", "Group.ReadWrite.All" -UseDeviceAuthentication</dev:code>
        <dev:remarks>
          <maml:para>This example shows how to authenticate to graph with scopes and device.</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>--- Example 3: Delegated access: Using your own access token ---</maml:title>
        <dev:code>PS C:\&gt; Connect-MgGraph -AccessToken $AccessToken</dev:code>
        <dev:remarks>
          <maml:para>This example shows how to authenticate to graph using an access token.</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>Example 4: Delegated access: custom application for Microsoft Graph PowerShell</maml:title>
        <dev:code>PS C:\&gt; Connect-MgGraph -ClientId &lt;YOUR_NEW_APP_ID&gt; -TenantId &lt;YOUR_TENANT_ID&gt;</dev:code>
        <dev:remarks>
          <maml:para>Follow this link (https://learn.microsoft.com/en-us/powershell/microsoftgraph/authentication-commands?view=graph-powershell-1.0#using-connect-mggraph)for more information on the steps for creating custom applications.</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>Example 5: App-only access: Using client credential with a certificate - Certificate Thumbprint</maml:title>
        <dev:code>PS C:\&gt; Connect-MgGraph -ClientId "YOUR_APP_ID" -TenantId "YOUR_TENANT_ID" -CertificateThumbprint "YOUR_CERT_THUMBPRINT"</dev:code>
        <dev:remarks>
          <maml:para>Follow this link (https://learn.microsoft.com/en-us/powershell/microsoftgraph/authentication-commands?view=graph-powershell-1.0#using-connect-mggraph)for more information on how to load the certificate.</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>Example 6: App-only access: Using client credential with a certificate - Certificate name</maml:title>
        <dev:code>PS C:\&gt; Connect-MgGraph -ClientId "YOUR_APP_ID" -TenantId "YOUR_TENANT_ID" -CertificateName "YOUR_CERT_SUBJECT"</dev:code>
        <dev:remarks>
          <maml:para>Follow this link (https://learn.microsoft.com/en-us/powershell/microsoftgraph/authentication-commands?view=graph-powershell-1.0#using-connect-mggraph)for more information on how to load the certificate.</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>Example 7: App-only access: Using client credential with a certificate - Certificate</maml:title>
        <dev:code>PS C:\&gt; $Cert = Get-ChildItem Cert:\LocalMachine\My\$CertThumbprint
PS C:\&gt; Connect-MgGraph -ClientId "YOUR_APP_ID" -TenantId "YOUR_TENANT_ID" -Certificate $Cert</dev:code>
        <dev:remarks>
          <maml:para>Follow this link (https://learn.microsoft.com/en-us/powershell/microsoftgraph/authentication-commands?view=graph-powershell-1.0#using-connect-mggraph)for more information on how to load the certificate.</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>---------- Example 8: Using client secret credentials ----------</maml:title>
        <dev:code>PS C:\&gt; $ClientSecretCredential = Get-Credential -Credential "Client_Id"
# Enter client_secret in the password prompt.
PS C:\&gt; Connect-MgGraph -TenantId "Tenant_Id" -ClientSecretCredential $ClientSecretCredential</dev:code>
        <dev:remarks>
          <maml:para>This authentication method is ideal for background interactions. It does not require a user to physically sign in.</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>Example 9: Using managed identity: System-assigned managed identity</maml:title>
        <dev:code>PS C:\&gt; Connect-MgGraph -Identity</dev:code>
        <dev:remarks>
          <maml:para>Uses an automatically managed identity on a service instance. The identity is tied to the lifecycle of a service instance.</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>Example 10: Using managed identity: User-assigned managed identity</maml:title>
        <dev:code>PS C:\&gt; Connect-MgGraph -Identity -ClientId "User_Assigned_Managed_identity_Client_Id"</dev:code>
        <dev:remarks>
          <maml:para>Uses a user created managed identity as a standalone Azure resource.</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>------ Example 11: Connecting to an environment or cloud ------</maml:title>
        <dev:code>PS C:\&gt; Get-MgEnvironment
Name     AzureADEndpoint                   GraphEndpoint                           Type
----     ---------------                   -------------                           ----
China    https://login.chinacloudapi.cn    https://microsoftgraph.chinacloudapi.cn Built-in
Global   https://login.microsoftonline.com https://graph.microsoft.com             Built-in
USGov    https://login.microsoftonline.us  https://graph.microsoft.us              Built-in
USGovDoD https://login.microsoftonline.us  https://dod-graph.microsoft.us          Built-in
PS C:\&gt; Connect-MgGraph -Environment USGov</dev:code>
        <dev:remarks>
          <maml:para>When you use Connect-MgGraph, you can choose to target other environments. By default, Connect-MgGraph targets the global public cloud.</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>Example 12: Connecting to an environment as a different identity</maml:title>
        <dev:code>PS C:\&gt; Connect-MgGraph -ContextScope Process</dev:code>
        <dev:remarks>
          <maml:para>To connect as a different identity other than CurrentUser, specify the -ContextScope parameter with the value Process.</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks>
      <maml:navigationLink>
        <maml:linkText>https://learn.microsoft.com/en-us/powershell/module/microsoft.graph.authentication/connect-mggraph</maml:linkText>
        <maml:uri>https://learn.microsoft.com/en-us/powershell/module/microsoft.graph.authentication/connect-mggraph</maml:uri>
      </maml:navigationLink>
    </command:relatedLinks>
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Disconnect-MgGraph</command:name>
      <command:verb>Disconnect</command:verb>
      <command:noun>MgGraph</command:noun>
      <maml:description>
        <maml:para>Once you're signed in, you'll remain signed in until you invoke Disconnect-MgGraph. Microsoft Graph PowerShell automatically refreshes the access token for you and sign-in persists across PowerShell sessions because Microsoft Graph PowerShell securely caches the token.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>Use Disconnect-MgGraph to sign out.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Disconnect-MgGraph</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="proga">
          <maml:name>ProgressAction</maml:name>
          <maml:description>
            <maml:para>{{ Fill ProgressAction Description }}</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
          <dev:type>
            <maml:name>ActionPreference</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="proga">
        <maml:name>ProgressAction</maml:name>
        <maml:description>
          <maml:para>{{ Fill ProgressAction Description }}</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
        <dev:type>
          <maml:name>ActionPreference</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>None</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>Microsoft.Graph.PowerShell.Authentication.IAuthContext</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>------------- Example 1: Using Disconnect-MgGraph -------------</maml:title>
        <dev:code>PS C:\&gt; Disconnect-MgGraph</dev:code>
        <dev:remarks>
          <maml:para>Use Disconnect-MgGraph to sign out.</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks>
      <maml:navigationLink>
        <maml:linkText>https://learn.microsoft.com/en-us/powershell/module/microsoft.graph.authentication/disconnect-graph</maml:linkText>
        <maml:uri>https://learn.microsoft.com/en-us/powershell/module/microsoft.graph.authentication/disconnect-graph</maml:uri>
      </maml:navigationLink>
    </command:relatedLinks>
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Get-MgContext</command:name>
      <command:verb>Get</command:verb>
      <command:noun>MgContext</command:noun>
      <maml:description>
        <maml:para>Get-MgContext is used to retrieve the details about your current session, which include:  - ClientID - TenantID - Certificate Thumbprint - Scopes consented to - AuthType: Delegated or app-only - AuthProviderType - CertificateName - Account - AppName - ContextScope - Certificate - PSHostVersion - ClientTimeOut.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>Retrieves session details.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Get-MgContext</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="proga">
          <maml:name>ProgressAction</maml:name>
          <maml:description>
            <maml:para>{{ Fill ProgressAction Description }}</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
          <dev:type>
            <maml:name>ActionPreference</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="proga">
        <maml:name>ProgressAction</maml:name>
        <maml:description>
          <maml:para>{{ Fill ProgressAction Description }}</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
        <dev:type>
          <maml:name>ActionPreference</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>None</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>Microsoft.Graph.PowerShell.Authentication.IAuthContext</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>---------------- Example 1: Using Get-MgContext ----------------</maml:title>
        <dev:code>PS C:\&gt; Get-MgContext
ClientId              : 615e6e7c-aa11-4402-91a1-6234967405d5
TenantId              : 9f32a42e-6782-4b96-a4d3-e0828a292569
CertificateThumbprint :
Scopes                : {AppRoleAssignment.ReadWrite.All, Directory.AccessAsUser.All, Directory.ReadWrite.All, EntitlementManagement.ReadWrite.All...}
AuthType              : Delegated
AuthProviderType      : InteractiveAuthenticationProvider
CertificateName       :
Account               : <EMAIL>
AppName               : Microsoft Graph PowerShell
ContextScope          : CurrentUser
Certificate           :
PSHostVersion         : 5.1.17763.1
ClientTimeout         : 00:05:00</dev:code>
        <dev:remarks>
          <maml:para>Retrieves session details.</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>Example 2: Using Get-MgContext: Retrieve all scopes you've consented to</maml:title>
        <dev:code>PS C:\&gt; Get-MgContext | Select -ExpandProperty Scopes
AppRoleAssignment.ReadWrite.All
Directory.AccessAsUser.All
Directory.ReadWrite.All
EntitlementManagement.ReadWrite.All
Group.ReadWrite.All
openid
Organization.Read.All
profile
RoleManagement.ReadWrite.Directory
User.Read
User.ReadWrite.All</dev:code>
        <dev:remarks>
          <maml:para>Retrieves all scopes.</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks>
      <maml:navigationLink>
        <maml:linkText>https://learn.microsoft.com/en-us/powershell/module/microsoft.graph.authentication/get-mgcontext</maml:linkText>
        <maml:uri>https://learn.microsoft.com/en-us/powershell/module/microsoft.graph.authentication/get-mgcontext</maml:uri>
      </maml:navigationLink>
    </command:relatedLinks>
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Get-MgEnvironment</command:name>
      <command:verb>Get</command:verb>
      <command:noun>MgEnvironment</command:noun>
      <maml:description>
        <maml:para>When you use Connect-MgGraph, you can choose to target other environments. By default, Connect-MgGraph targets the global public cloud.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>When you use Connect-MgGraph, you can choose to target other environments. By default, Connect-MgGraph targets the global public cloud.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Get-MgEnvironment</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName)" position="0" aliases="none">
          <maml:name>Name</maml:name>
          <maml:description>
            <maml:para>The environment name.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="proga">
          <maml:name>ProgressAction</maml:name>
          <maml:description>
            <maml:para>{{ Fill ProgressAction Description }}</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
          <dev:type>
            <maml:name>ActionPreference</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName)" position="0" aliases="none">
        <maml:name>Name</maml:name>
        <maml:description>
          <maml:para>The environment name.</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="proga">
        <maml:name>ProgressAction</maml:name>
        <maml:description>
          <maml:para>{{ Fill ProgressAction Description }}</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
        <dev:type>
          <maml:name>ActionPreference</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>System.String</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>Microsoft.Graph.PowerShell.Authentication.Models.GraphEnvironment</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>----------------- Example 1: Get-MgEnvironment -----------------</maml:title>
        <dev:code>PS C:\&gt; Get-MgEnvironment
Name     AzureADEndpoint                   GraphEndpoint                           Type
----     ---------------                   -------------                           ----
China    https://login.chinacloudapi.cn    https://microsoftgraph.chinacloudapi.cn Built-in
Global   https://login.microsoftonline.com https://graph.microsoft.com             Built-in
USGov    https://login.microsoftonline.us  https://graph.microsoft.us              Built-in
USGovDoD https://login.microsoftonline.us  https://dod-graph.microsoft.us          Built-in</dev:code>
        <dev:remarks>
          <maml:para>This command gets list of all environments.</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks>
      <maml:navigationLink>
        <maml:linkText>https://learn.microsoft.com/en-us/powershell/module/microsoft.graph.authentication/get-mgenvironment</maml:linkText>
        <maml:uri>https://learn.microsoft.com/en-us/powershell/module/microsoft.graph.authentication/get-mgenvironment</maml:uri>
      </maml:navigationLink>
    </command:relatedLinks>
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Get-MgGraphOption</command:name>
      <command:verb>Get</command:verb>
      <command:noun>MgGraphOption</command:noun>
      <maml:description>
        <maml:para>Gets global configurations that apply to the SDK. For example, check if Web Account Manager (WAM) support has been enabled.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>Gets global configurations that apply to the SDK. For example, check if Web Account Manager (WAM) support has been enabled.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Get-MgGraphOption</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="proga">
          <maml:name>ProgressAction</maml:name>
          <maml:description>
            <maml:para>{{ Fill ProgressAction Description }}</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
          <dev:type>
            <maml:name>ActionPreference</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="proga">
        <maml:name>ProgressAction</maml:name>
        <maml:description>
          <maml:para>{{ Fill ProgressAction Description }}</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
        <dev:type>
          <maml:name>ActionPreference</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>None</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>Microsoft.Graph.PowerShell.Authentication.IGraphOption</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>------- Example 1: Check if WAM support has been enabled -------</maml:title>
        <dev:code>PS C:\&gt; Get-MgGraphOption
EnableWAMForMSGraph 
--------------------
                True</dev:code>
        <dev:remarks>
          <maml:para>Confirms if WAM support has been enabled.</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks>
      <maml:navigationLink>
        <maml:linkText>Online Version:</maml:linkText>
        <maml:uri>https://learn.microsoft.com/en-us/powershell/module/microsoft.graph.authentication/get-mgenvironment</maml:uri>
      </maml:navigationLink>
    </command:relatedLinks>
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Get-MgRequestContext</command:name>
      <command:verb>Get</command:verb>
      <command:noun>MgRequestContext</command:noun>
      <maml:description>
        <maml:para>Gets graph request context details</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>Gets graph request context details</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Get-MgRequestContext</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="proga">
          <maml:name>ProgressAction</maml:name>
          <maml:description>
            <maml:para>{{ Fill ProgressAction Description }}</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
          <dev:type>
            <maml:name>ActionPreference</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="cf">
          <maml:name>Confirm</maml:name>
          <maml:description>
            <maml:para>Prompts you for confirmation before running the cmdlet.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="wi">
          <maml:name>WhatIf</maml:name>
          <maml:description>
            <maml:para>Shows what would happen if the cmdlet runs. The cmdlet is not run.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="proga">
        <maml:name>ProgressAction</maml:name>
        <maml:description>
          <maml:para>{{ Fill ProgressAction Description }}</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
        <dev:type>
          <maml:name>ActionPreference</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="cf">
        <maml:name>Confirm</maml:name>
        <maml:description>
          <maml:para>Prompts you for confirmation before running the cmdlet.</maml:para>
        </maml:description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="wi">
        <maml:name>WhatIf</maml:name>
        <maml:description>
          <maml:para>Shows what would happen if the cmdlet runs. The cmdlet is not run.</maml:para>
        </maml:description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>None</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>Microsoft.Graph.PowerShell.Authentication.Core.Interfaces.IRequestContext</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>---------------- Example 1: Get request context ----------------</maml:title>
        <dev:code>PS C:\&gt; Get-MgRequestContext
ClientTimeout     RetryDelay                      MaxRetry                 RetriesTimeLimit
-------------     ----------                      --------                 ----------------
00:00:05                   3                             2                         00:00:02</dev:code>
        <dev:remarks>
          <maml:para>Gets graph request context details</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks>
      <maml:navigationLink>
        <maml:linkText>Online Version:</maml:linkText>
        <maml:uri>https://learn.microsoft.com/en-us/powershell/module/microsoft.graph.authentication/get-mgenvironment</maml:uri>
      </maml:navigationLink>
    </command:relatedLinks>
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Invoke-MgGraphRequest</command:name>
      <command:verb>Invoke</command:verb>
      <command:noun>MgGraphRequest</command:noun>
      <maml:description>
        <maml:para>Invoke-MgGraphRequest issues REST API requests to the Graph API. It works for any Graph API if you know the REST URI, method, and optional body parameter. This command is especially useful for accessing APIs for which there isn't an equivalent cmdlet yet.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>This command issues REST API requests.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Invoke-MgGraphRequest</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="1" aliases="none">
          <maml:name>Method</maml:name>
          <maml:description>
            <maml:para>HTTP Method</maml:para>
          </maml:description>
          <command:parameterValueGroup>
            <command:parameterValue required="false" command:variableLength="false">GET</command:parameterValue>
            <command:parameterValue required="false" command:variableLength="false">POST</command:parameterValue>
            <command:parameterValue required="false" command:variableLength="false">PUT</command:parameterValue>
            <command:parameterValue required="false" command:variableLength="false">PATCH</command:parameterValue>
            <command:parameterValue required="false" command:variableLength="false">DELETE</command:parameterValue>
          </command:parameterValueGroup>
          <command:parameterValue required="true" variableLength="false">GraphRequestMethod</command:parameterValue>
          <dev:type>
            <maml:name>GraphRequestMethod</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="10" aliases="none">
          <maml:name>SkipHeaderValidation</maml:name>
          <maml:description>
            <maml:para>Add headers to Request Header collection without validation</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="11" aliases="none">
          <maml:name>ContentType</maml:name>
          <maml:description>
            <maml:para>Custom Content Type</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="13" aliases="SV">
          <maml:name>SessionVariable</maml:name>
          <maml:description>
            <maml:para>Specifies a web request session. Enter the variable name, including the dollar sign ($).You can't use the SessionVariable and GraphRequestSession parameters in the same command.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="14" aliases="RHV">
          <maml:name>ResponseHeadersVariable</maml:name>
          <maml:description>
            <maml:para>Response Headers Variable</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="15" aliases="none">
          <maml:name>StatusCodeVariable</maml:name>
          <maml:description>
            <maml:para>Response Status Code Variable</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="16" aliases="none">
          <maml:name>SkipHttpErrorCheck</maml:name>
          <maml:description>
            <maml:para>Skip Checking HTTP Errors</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="17" aliases="none">
          <maml:name>GraphRequestSession</maml:name>
          <maml:description>
            <maml:para>Custom Graph Request Session</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">GraphRequestSession</command:parameterValue>
          <dev:type>
            <maml:name>GraphRequestSession</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="18" aliases="none">
          <maml:name>UserAgent</maml:name>
          <maml:description>
            <maml:para>Custom User Specified User Agent</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="19" aliases="none">
          <maml:name>OutputType</maml:name>
          <maml:description>
            <maml:para>Output Type to return to the caller</maml:para>
          </maml:description>
          <command:parameterValueGroup>
            <command:parameterValue required="false" command:variableLength="false">HashTable</command:parameterValue>
            <command:parameterValue required="false" command:variableLength="false">PSObject</command:parameterValue>
            <command:parameterValue required="false" command:variableLength="false">HttpResponseMessage</command:parameterValue>
            <command:parameterValue required="false" command:variableLength="false">Json</command:parameterValue>
          </command:parameterValueGroup>
          <command:parameterValue required="true" variableLength="false">OutputType</command:parameterValue>
          <dev:type>
            <maml:name>OutputType</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="2" aliases="none">
          <maml:name>Uri</maml:name>
          <maml:description>
            <maml:para>Uri to call can be segments such as /beta/me or fully qualified https://graph.microsoft.com/beta/me</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">Uri</command:parameterValue>
          <dev:type>
            <maml:name>Uri</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByValue)" position="3" aliases="none">
          <maml:name>Body</maml:name>
          <maml:description>
            <maml:para>Request Body. Required when Method is Post or Patch</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">Object</command:parameterValue>
          <dev:type>
            <maml:name>Object</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="4" aliases="none">
          <maml:name>Headers</maml:name>
          <maml:description>
            <maml:para>Optional Custom Headers</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">IDictionary</command:parameterValue>
          <dev:type>
            <maml:name>IDictionary</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="5" aliases="none">
          <maml:name>OutputFilePath</maml:name>
          <maml:description>
            <maml:para>Output file where the response body will be saved</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="6" aliases="none">
          <maml:name>InferOutputFileName</maml:name>
          <maml:description>
            <maml:para>Infer output filename</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="7" aliases="none">
          <maml:name>InputFilePath</maml:name>
          <maml:description>
            <maml:para>Input file to send in the request</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="8" aliases="none">
          <maml:name>PassThru</maml:name>
          <maml:description>
            <maml:para>Indicates that the cmdlet returns the results, in addition to writing them to a file. Only valid when the OutFile parameter is also used.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="proga">
          <maml:name>ProgressAction</maml:name>
          <maml:description>
            <maml:para>{{ Fill ProgressAction Description }}</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
          <dev:type>
            <maml:name>ActionPreference</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByValue)" position="3" aliases="none">
        <maml:name>Body</maml:name>
        <maml:description>
          <maml:para>Request Body. Required when Method is Post or Patch</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">Object</command:parameterValue>
        <dev:type>
          <maml:name>Object</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="11" aliases="none">
        <maml:name>ContentType</maml:name>
        <maml:description>
          <maml:para>Custom Content Type</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="17" aliases="none">
        <maml:name>GraphRequestSession</maml:name>
        <maml:description>
          <maml:para>Custom Graph Request Session</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">GraphRequestSession</command:parameterValue>
        <dev:type>
          <maml:name>GraphRequestSession</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="4" aliases="none">
        <maml:name>Headers</maml:name>
        <maml:description>
          <maml:para>Optional Custom Headers</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">IDictionary</command:parameterValue>
        <dev:type>
          <maml:name>IDictionary</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="6" aliases="none">
        <maml:name>InferOutputFileName</maml:name>
        <maml:description>
          <maml:para>Infer output filename</maml:para>
        </maml:description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="7" aliases="none">
        <maml:name>InputFilePath</maml:name>
        <maml:description>
          <maml:para>Input file to send in the request</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="1" aliases="none">
        <maml:name>Method</maml:name>
        <maml:description>
          <maml:para>HTTP Method</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">GraphRequestMethod</command:parameterValue>
        <dev:type>
          <maml:name>GraphRequestMethod</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="5" aliases="none">
        <maml:name>OutputFilePath</maml:name>
        <maml:description>
          <maml:para>Output file where the response body will be saved</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="19" aliases="none">
        <maml:name>OutputType</maml:name>
        <maml:description>
          <maml:para>Output Type to return to the caller</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">OutputType</command:parameterValue>
        <dev:type>
          <maml:name>OutputType</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="8" aliases="none">
        <maml:name>PassThru</maml:name>
        <maml:description>
          <maml:para>Indicates that the cmdlet returns the results, in addition to writing them to a file. Only valid when the OutFile parameter is also used.</maml:para>
        </maml:description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="proga">
        <maml:name>ProgressAction</maml:name>
        <maml:description>
          <maml:para>{{ Fill ProgressAction Description }}</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
        <dev:type>
          <maml:name>ActionPreference</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="14" aliases="RHV">
        <maml:name>ResponseHeadersVariable</maml:name>
        <maml:description>
          <maml:para>Response Headers Variable</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="13" aliases="SV">
        <maml:name>SessionVariable</maml:name>
        <maml:description>
          <maml:para>Specifies a web request session. Enter the variable name, including the dollar sign ($).You can't use the SessionVariable and GraphRequestSession parameters in the same command.</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="10" aliases="none">
        <maml:name>SkipHeaderValidation</maml:name>
        <maml:description>
          <maml:para>Add headers to Request Header collection without validation</maml:para>
        </maml:description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="16" aliases="none">
        <maml:name>SkipHttpErrorCheck</maml:name>
        <maml:description>
          <maml:para>Skip Checking HTTP Errors</maml:para>
        </maml:description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="15" aliases="none">
        <maml:name>StatusCodeVariable</maml:name>
        <maml:description>
          <maml:para>Response Status Code Variable</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="2" aliases="none">
        <maml:name>Uri</maml:name>
        <maml:description>
          <maml:para>Uri to call can be segments such as /beta/me or fully qualified https://graph.microsoft.com/beta/me</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">Uri</command:parameterValue>
        <dev:type>
          <maml:name>Uri</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="18" aliases="none">
        <maml:name>UserAgent</maml:name>
        <maml:description>
          <maml:para>Custom User Specified User Agent</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>System.Object</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>System.Object</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>------------ Example 1: Using Invoke-MgGraphRequest ------------</maml:title>
        <dev:code>PS C:\&gt; Invoke-MgGraphRequest -Method GET https://graph.microsoft.com/v1.0/me
Name                           Value
----                           -----
userPrincipalName              <EMAIL>
preferredLanguage              en-US
mobilePhone                    ************
displayName                    MOD Administrator
givenName                      MOD
mail                           <EMAIL>
@odata.context                 https://graph.microsoft.com/v1.0/$metadata#users/$entity
id                             694bab60-392a-4f64-9430-c1dea2951f50
jobTitle
officeLocation
businessPhones                 {************}
surname                        Administrator</dev:code>
        <dev:remarks>
          <maml:para>Issues REST API requests.</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks>
      <maml:navigationLink>
        <maml:linkText>https://learn.microsoft.com/en-us/powershell/module/microsoft.graph.authentication/invoke-mggraphrequest</maml:linkText>
        <maml:uri>https://learn.microsoft.com/en-us/powershell/module/microsoft.graph.authentication/invoke-mggraphrequest</maml:uri>
      </maml:navigationLink>
    </command:relatedLinks>
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Remove-MgEnvironment</command:name>
      <command:verb>Remove</command:verb>
      <command:noun>MgEnvironment</command:noun>
      <maml:description>
        <maml:para>Removes Microsoft Graph environment to the settings file</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>Removes Microsoft Graph environment to the settings file</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Remove-MgEnvironment</maml:name>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName)" position="0" aliases="none">
          <maml:name>Name</maml:name>
          <maml:description>
            <maml:para>The environment name.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="proga">
          <maml:name>ProgressAction</maml:name>
          <maml:description>
            <maml:para>{{ Fill ProgressAction Description }}</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
          <dev:type>
            <maml:name>ActionPreference</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="cf">
          <maml:name>Confirm</maml:name>
          <maml:description>
            <maml:para>Prompts you for confirmation before running the cmdlet.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="wi">
          <maml:name>WhatIf</maml:name>
          <maml:description>
            <maml:para>Shows what would happen if the cmdlet runs. The cmdlet is not run.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName)" position="0" aliases="none">
        <maml:name>Name</maml:name>
        <maml:description>
          <maml:para>The environment name.</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="proga">
        <maml:name>ProgressAction</maml:name>
        <maml:description>
          <maml:para>{{ Fill ProgressAction Description }}</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
        <dev:type>
          <maml:name>ActionPreference</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="cf">
        <maml:name>Confirm</maml:name>
        <maml:description>
          <maml:para>Prompts you for confirmation before running the cmdlet.</maml:para>
        </maml:description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="wi">
        <maml:name>WhatIf</maml:name>
        <maml:description>
          <maml:para>Shows what would happen if the cmdlet runs. The cmdlet is not run.</maml:para>
        </maml:description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>System.String</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>Microsoft.Graph.PowerShell.Authentication.Models.GraphEnvironment</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>---------- Example 1: Remove user-defined environment ----------</maml:title>
        <dev:code>PS C:\&gt; Remove-MgEnvironment Canary
Name     AzureADEndpoint                      GraphEndpoint                 Type
----     ---------------                      -------------                 ----
Canary    https://login.microsoftonline.com   https://microsoftgraph.com User-defined</dev:code>
        <dev:remarks>
          <maml:para>Removes user-defined environment.</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks>
      <maml:navigationLink>
        <maml:linkText>https://learn.microsoft.com/en-us/powershell/module/microsoft.graph.authentication/remove-mgenvironment</maml:linkText>
        <maml:uri>https://learn.microsoft.com/en-us/powershell/module/microsoft.graph.authentication/remove-mgenvironment</maml:uri>
      </maml:navigationLink>
    </command:relatedLinks>
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Set-MgEnvironment</command:name>
      <command:verb>Set</command:verb>
      <command:noun>MgEnvironment</command:noun>
      <maml:description>
        <maml:para>Sets a Microsoft Graph environment to the settings file.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>Sets a Microsoft Graph environment to the settings file.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Set-MgEnvironment</maml:name>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName)" position="0" aliases="none">
          <maml:name>Name</maml:name>
          <maml:description>
            <maml:para>The environment name.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName)" position="1" aliases="AzureADUrl">
          <maml:name>AzureADEndpoint</maml:name>
          <maml:description>
            <maml:para>The base URL for Azure AD endpoint to get access tokens for Microsoft Graph endpoint.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName)" position="2" aliases="GraphUrl">
          <maml:name>GraphEndpoint</maml:name>
          <maml:description>
            <maml:para>The service root endpoint for Microsoft Graph.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="proga">
          <maml:name>ProgressAction</maml:name>
          <maml:description>
            <maml:para>{{ Fill ProgressAction Description }}</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
          <dev:type>
            <maml:name>ActionPreference</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="cf">
          <maml:name>Confirm</maml:name>
          <maml:description>
            <maml:para>Prompts you for confirmation before running the cmdlet.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="wi">
          <maml:name>WhatIf</maml:name>
          <maml:description>
            <maml:para>Shows what would happen if the cmdlet runs. The cmdlet is not run.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName)" position="1" aliases="AzureADUrl">
        <maml:name>AzureADEndpoint</maml:name>
        <maml:description>
          <maml:para>The base URL for Azure AD endpoint to get access tokens for Microsoft Graph endpoint.</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName)" position="2" aliases="GraphUrl">
        <maml:name>GraphEndpoint</maml:name>
        <maml:description>
          <maml:para>The service root endpoint for Microsoft Graph.</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName)" position="0" aliases="none">
        <maml:name>Name</maml:name>
        <maml:description>
          <maml:para>The environment name.</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="proga">
        <maml:name>ProgressAction</maml:name>
        <maml:description>
          <maml:para>{{ Fill ProgressAction Description }}</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
        <dev:type>
          <maml:name>ActionPreference</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="cf">
        <maml:name>Confirm</maml:name>
        <maml:description>
          <maml:para>Prompts you for confirmation before running the cmdlet.</maml:para>
        </maml:description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="wi">
        <maml:name>WhatIf</maml:name>
        <maml:description>
          <maml:para>Shows what would happen if the cmdlet runs. The cmdlet is not run.</maml:para>
        </maml:description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>System.String</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>Microsoft.Graph.PowerShell.Authentication.Models.GraphEnvironment</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>------ Example 1: Set environment for the current session ------</maml:title>
        <dev:code>PS C:\&gt; Set-MgEnvironment -GraphEndpoint "https://canary.graph.microsoft.com" -AzureADEndpoint "https://login.microsoftonline.com"</dev:code>
        <dev:remarks>
          <maml:para>Sets the environment for the current session.</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks>
      <maml:navigationLink>
        <maml:linkText>https://learn.microsoft.com/en-us/powershell/module/microsoft.graph.authentication/set-mgenvironment</maml:linkText>
        <maml:uri>https://learn.microsoft.com/en-us/powershell/module/microsoft.graph.authentication/set-mgenvironment</maml:uri>
      </maml:navigationLink>
    </command:relatedLinks>
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Set-MgGraphOption</command:name>
      <command:verb>Set</command:verb>
      <command:noun>MgGraphOption</command:noun>
      <maml:description>
        <maml:para>Sets global configurations that apply to the SDK. For example, toggle Web Account Manager (WAM) support.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>Sets global configurations that apply to the SDK. For example, toggle Web Account Manager (WAM) support.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Set-MgGraphOption</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>EnableLoginByWAM</maml:name>
          <maml:description>
            <maml:para>{{ Fill EnableLoginByWAM Description }}</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
          <dev:type>
            <maml:name>Boolean</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="proga">
          <maml:name>ProgressAction</maml:name>
          <maml:description>
            <maml:para>{{ Fill ProgressAction Description }}</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
          <dev:type>
            <maml:name>ActionPreference</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>EnableLoginByWAM</maml:name>
        <maml:description>
          <maml:para>{{ Fill EnableLoginByWAM Description }}</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
        <dev:type>
          <maml:name>Boolean</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="proga">
        <maml:name>ProgressAction</maml:name>
        <maml:description>
          <maml:para>{{ Fill ProgressAction Description }}</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
        <dev:type>
          <maml:name>ActionPreference</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>None</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>System.Object</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>---------- Example 1: Set web account manager support ----------</maml:title>
        <dev:code>PS C:\&gt; Set-MgGraphOption -EnableLoginByWAM $True</dev:code>
        <dev:remarks>
          <maml:para>Sets web account manager support</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks>
      <maml:navigationLink>
        <maml:linkText>Online Version:</maml:linkText>
        <maml:uri>https://learn.microsoft.com/en-us/powershell/module/microsoft.graph.authentication/set-mgenvironment</maml:uri>
      </maml:navigationLink>
    </command:relatedLinks>
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Set-MgRequestContext</command:name>
      <command:verb>Set</command:verb>
      <command:noun>MgRequestContext</command:noun>
      <maml:description>
        <maml:para>Sets request context for Microsoft Graph invocations</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>Sets request context for Microsoft Graph invocations</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Set-MgRequestContext</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName)" position="named" aliases="none">
          <maml:name>ClientTimeout</maml:name>
          <maml:description>
            <maml:para>Sets the HTTP client timeout in seconds.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">Int32</command:parameterValue>
          <dev:type>
            <maml:name>Int32</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName)" position="named" aliases="none">
          <maml:name>MaxRetry</maml:name>
          <maml:description>
            <maml:para>Sets the maximum number of retries for a request with a maximum value of 10. This defaults to 3.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">Int32</command:parameterValue>
          <dev:type>
            <maml:name>Int32</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="proga">
          <maml:name>ProgressAction</maml:name>
          <maml:description>
            <maml:para>{{ Fill ProgressAction Description }}</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
          <dev:type>
            <maml:name>ActionPreference</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName)" position="named" aliases="none">
          <maml:name>RetriesTimeLimit</maml:name>
          <maml:description>
            <maml:para>Sets the maximum time in seconds allowed for request retries.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">Int32</command:parameterValue>
          <dev:type>
            <maml:name>Int32</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName)" position="named" aliases="none">
          <maml:name>RetryDelay</maml:name>
          <maml:description>
            <maml:para>Sets the waiting time in seconds before retrying a request with a maximum value of 180 seconds. This defaults to 3 seconds.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">Int32</command:parameterValue>
          <dev:type>
            <maml:name>Int32</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="cf">
          <maml:name>Confirm</maml:name>
          <maml:description>
            <maml:para>Prompts you for confirmation before running the cmdlet.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="wi">
          <maml:name>WhatIf</maml:name>
          <maml:description>
            <maml:para>Shows what would happen if the cmdlet runs. The cmdlet is not run.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName)" position="named" aliases="none">
        <maml:name>ClientTimeout</maml:name>
        <maml:description>
          <maml:para>Sets the HTTP client timeout in seconds.</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">Int32</command:parameterValue>
        <dev:type>
          <maml:name>Int32</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName)" position="named" aliases="none">
        <maml:name>MaxRetry</maml:name>
        <maml:description>
          <maml:para>Sets the maximum number of retries for a request with a maximum value of 10. This defaults to 3.</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">Int32</command:parameterValue>
        <dev:type>
          <maml:name>Int32</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="proga">
        <maml:name>ProgressAction</maml:name>
        <maml:description>
          <maml:para>{{ Fill ProgressAction Description }}</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">ActionPreference</command:parameterValue>
        <dev:type>
          <maml:name>ActionPreference</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName)" position="named" aliases="none">
        <maml:name>RetriesTimeLimit</maml:name>
        <maml:description>
          <maml:para>Sets the maximum time in seconds allowed for request retries.</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">Int32</command:parameterValue>
        <dev:type>
          <maml:name>Int32</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName)" position="named" aliases="none">
        <maml:name>RetryDelay</maml:name>
        <maml:description>
          <maml:para>Sets the waiting time in seconds before retrying a request with a maximum value of 180 seconds. This defaults to 3 seconds.</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">Int32</command:parameterValue>
        <dev:type>
          <maml:name>Int32</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="cf">
        <maml:name>Confirm</maml:name>
        <maml:description>
          <maml:para>Prompts you for confirmation before running the cmdlet.</maml:para>
        </maml:description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="wi">
        <maml:name>WhatIf</maml:name>
        <maml:description>
          <maml:para>Shows what would happen if the cmdlet runs. The cmdlet is not run.</maml:para>
        </maml:description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>System.Int32</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>Microsoft.Graph.PowerShell.Authentication.Core.Interfaces.IRequestContext</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------- Example 1: Set Http client timeout in seconds --------</maml:title>
        <dev:code>PS C:\&gt; Set-MgRequestContext -ClientTimeout 5
ClientTimeout     RetryDelay                      MaxRetry                 RetriesTimeLimit
-------------     ----------                      --------                 ----------------
00:00:05                   3                             2                         00:00:00</dev:code>
        <dev:remarks>
          <maml:para>Sets Http client timeout in seconds.</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>Example 2: Set the maximum time in seconds allowed for request retries</maml:title>
        <dev:code>PS C:\&gt; Set-MgRequestContext -RetriesTimeLimit 2
ClientTimeout     RetryDelay                      MaxRetry                 RetriesTimeLimit
-------------     ----------                      --------                 ----------------
00:00:05                   3                             2                         00:00:02</dev:code>
        <dev:remarks>
          <maml:para>Sets the maximum time in seconds allowed for request retries.</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>-- Example 3: Set the maximum number of retries for a request --</maml:title>
        <dev:code>PS C:\&gt; Set-MgRequestContext -MaxRetry 2
ClientTimeout     RetryDelay                      MaxRetry                 RetriesTimeLimit
-------------     ----------                      --------                 ----------------
00:00:05                   3                             2                         00:00:02</dev:code>
        <dev:remarks>
          <maml:para>Sets the maximum number of retries for a request.</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>Example 4: Set the waiting time in seconds before retrying a request</maml:title>
        <dev:code>PS C:\&gt; Set-MgRequestContext -RetryDelay 3
ClientTimeout     RetryDelay                      MaxRetry                 RetriesTimeLimit
-------------     ----------                      --------                 ----------------
00:00:05                   3                             2                         00:00:02</dev:code>
        <dev:remarks>
          <maml:para>Sets the waiting time in seconds before retrying a request.</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks>
      <maml:navigationLink>
        <maml:linkText>Online Version:</maml:linkText>
        <maml:uri>https://learn.microsoft.com/en-us/powershell/module/microsoft.graph.authentication/set-mgenvironment</maml:uri>
      </maml:navigationLink>
    </command:relatedLinks>
  </command:command>
</helpItems>