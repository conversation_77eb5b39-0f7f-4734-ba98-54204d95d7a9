#
# Module manifest for module 'Microsoft.Graph.Reports'
#
# Generated by: Microsoft Corporation
#
# Generated on: 7/9/2025
#

@{

# Script module or binary module file associated with this manifest.
RootModule = './Microsoft.Graph.Reports.psm1'

# Version number of this module.
ModuleVersion = '2.29.0'

# Supported PSEditions
CompatiblePSEditions = 'Core', 'Desktop'

# ID used to uniquely identify this module
GUID = '0bfc88b7-a8ad-471a-8c86-5f0aa3c84217'

# Author of this module
Author = 'Microsoft Corporation'

# Company or vendor of this module
CompanyName = 'Microsoft Corporation'

# Copyright statement for this module
Copyright = 'Microsoft Corporation. All rights reserved.'

# Description of the functionality provided by this module
Description = 'Microsoft Graph PowerShell Cmdlets'

# Minimum version of the PowerShell engine required by this module
PowerShellVersion = '5.1'

# Name of the PowerShell host required by this module
# PowerShellHostName = ''

# Minimum version of the PowerShell host required by this module
# PowerShellHostVersion = ''

# Minimum version of Microsoft .NET Framework required by this module. This prerequisite is valid for the PowerShell Desktop edition only.
DotNetFrameworkVersion = '4.7.2'

# Minimum version of the common language runtime (CLR) required by this module. This prerequisite is valid for the PowerShell Desktop edition only.
# ClrVersion = ''

# Processor architecture (None, X86, Amd64) required by this module
# ProcessorArchitecture = ''

# Modules that must be imported into the global environment prior to importing this module
RequiredModules = @(@{ModuleName = 'Microsoft.Graph.Authentication'; RequiredVersion = '2.29.0'; })

# Assemblies that must be loaded prior to importing this module
RequiredAssemblies = './bin/Microsoft.Graph.Reports.private.dll'

# Script files (.ps1) that are run in the caller's environment prior to importing this module.
# ScriptsToProcess = @()

# Type files (.ps1xml) to be loaded when importing this module
# TypesToProcess = @()

# Format files (.ps1xml) to be loaded when importing this module
FormatsToProcess = './Microsoft.Graph.Reports.format.ps1xml'

# Modules to import as nested modules of the module specified in RootModule/ModuleToProcess
# NestedModules = @()

# Functions to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no functions to export.
FunctionsToExport = 'Confirm-MgAuditLogSignInCompromised', 
               'Confirm-MgAuditLogSignInSafe', 
               'Export-MgReportPartnerBillingReconciliationBilled', 
               'Export-MgReportPartnerBillingReconciliationUnbilled', 
               'Export-MgReportPartnerBillingUsageBilled', 
               'Export-MgReportPartnerBillingUsageUnbilled', 
               'Get-MgAdminReportSetting', 'Get-MgAuditLogDirectoryAudit', 
               'Get-MgAuditLogDirectoryAuditCount', 'Get-MgAuditLogProvisioning', 
               'Get-MgAuditLogProvisioningCount', 'Get-MgAuditLogSignIn', 
               'Get-MgAuditLogSignInCount', 'Get-MgDeviceManagementReport', 
               'Get-MgDeviceManagementReportCachedReport', 
               'Get-MgDeviceManagementReportCompliancePolicyNonComplianceReport', 
               'Get-MgDeviceManagementReportCompliancePolicyNonComplianceSummaryReport', 
               'Get-MgDeviceManagementReportComplianceSettingNonComplianceReport', 
               'Get-MgDeviceManagementReportConfigurationPolicyNonComplianceReport', 
               'Get-MgDeviceManagementReportConfigurationPolicyNonComplianceSummaryReport', 
               'Get-MgDeviceManagementReportConfigurationSettingNonComplianceReport', 
               'Get-MgDeviceManagementReportDeviceAppInstallationStatusReport', 
               'Get-MgDeviceManagementReportDeviceManagementIntentPerSettingContributingProfile', 
               'Get-MgDeviceManagementReportDeviceManagementIntentSettingReport', 
               'Get-MgDeviceManagementReportDeviceNonComplianceReport', 
               'Get-MgDeviceManagementReportDeviceWithoutCompliancePolicyReport', 
               'Get-MgDeviceManagementReportExportJob', 
               'Get-MgDeviceManagementReportExportJobCount', 
               'Get-MgDeviceManagementReportFilter', 
               'Get-MgDeviceManagementReportHistoricalReport', 
               'Get-MgDeviceManagementReportNoncompliantDeviceAndSettingReport', 
               'Get-MgDeviceManagementReportPolicyNonComplianceMetadata', 
               'Get-MgDeviceManagementReportPolicyNonComplianceReport', 
               'Get-MgDeviceManagementReportPolicyNonComplianceSummaryReport', 
               'Get-MgDeviceManagementReportSettingNonComplianceReport', 
               'Get-MgReportAuthenticationMethod', 
               'Get-MgReportAuthenticationMethodUserRegistrationDetail', 
               'Get-MgReportAuthenticationMethodUserRegistrationDetailCount', 
               'Get-MgReportDailyPrintUsageByPrinter', 
               'Get-MgReportDailyPrintUsageByPrinterCount', 
               'Get-MgReportDailyPrintUsageByUser', 
               'Get-MgReportDailyPrintUsageByUserCount', 
               'Get-MgReportDeviceConfigurationDeviceActivity', 
               'Get-MgReportDeviceConfigurationUserActivity', 
               'Get-MgReportEmailActivityCount', 
               'Get-MgReportEmailActivityUserCount', 
               'Get-MgReportEmailActivityUserDetail', 
               'Get-MgReportEmailAppUsageAppUserCount', 
               'Get-MgReportEmailAppUsageUserCount', 
               'Get-MgReportEmailAppUsageUserDetail', 
               'Get-MgReportEmailAppUsageVersionUserCount', 
               'Get-MgReportGroupArchivedPrintJob', 
               'Get-MgReportM365AppPlatformUserCount', 
               'Get-MgReportM365AppUserCount', 'Get-MgReportM365AppUserDetail', 
               'Get-MgReportMailboxUsageDetail', 
               'Get-MgReportMailboxUsageMailboxCount', 
               'Get-MgReportMailboxUsageQuotaStatusMailboxCount', 
               'Get-MgReportMailboxUsageStorage', 
               'Get-MgReportManagedDeviceEnrollmentFailureDetail', 
               'Get-MgReportManagedDeviceEnrollmentTopFailure', 
               'Get-MgReportMonthlyPrintUsageByPrinter', 
               'Get-MgReportMonthlyPrintUsageByPrinterCount', 
               'Get-MgReportMonthlyPrintUsageByUser', 
               'Get-MgReportMonthlyPrintUsageByUserCount', 
               'Get-MgReportOffice365ActivationCount', 
               'Get-MgReportOffice365ActivationUserCount', 
               'Get-MgReportOffice365ActivationUserDetail', 
               'Get-MgReportOffice365ActiveUserCount', 
               'Get-MgReportOffice365ActiveUserDetail', 
               'Get-MgReportOffice365GroupActivityCount', 
               'Get-MgReportOffice365GroupActivityDetail', 
               'Get-MgReportOffice365GroupActivityFileCount', 
               'Get-MgReportOffice365GroupActivityGroupCount', 
               'Get-MgReportOffice365GroupActivityStorage', 
               'Get-MgReportOffice365ServiceUserCount', 
               'Get-MgReportOneDriveActivityFileCount', 
               'Get-MgReportOneDriveActivityUserCount', 
               'Get-MgReportOneDriveActivityUserDetail', 
               'Get-MgReportOneDriveUsageAccountCount', 
               'Get-MgReportOneDriveUsageAccountDetail', 
               'Get-MgReportOneDriveUsageFileCount', 
               'Get-MgReportOneDriveUsageStorage', 'Get-MgReportPartner', 
               'Get-MgReportPartnerBilling', 'Get-MgReportPartnerBillingManifest', 
               'Get-MgReportPartnerBillingManifestCount', 
               'Get-MgReportPartnerBillingOperation', 
               'Get-MgReportPartnerBillingOperationCount', 
               'Get-MgReportPartnerBillingReconciliation', 
               'Get-MgReportPartnerBillingReconciliationBilled', 
               'Get-MgReportPartnerBillingReconciliationUnbilled', 
               'Get-MgReportPartnerBillingUsage', 
               'Get-MgReportPartnerBillingUsageBilled', 
               'Get-MgReportPartnerBillingUsageUnbilled', 
               'Get-MgReportPrinterArchivedPrintJob', 
               'Get-MgReportRelyingPartyDetailedSummary', 'Get-MgReportSecurity', 
               'Get-MgReportSecurityAttackSimulationRepeatOffender', 
               'Get-MgReportSecurityAttackSimulationTrainingUserCoverage', 
               'Get-MgReportSecurityAttackSimulationUserCoverage', 
               'Get-MgReportSharePointActivityFileCount', 
               'Get-MgReportSharePointActivityPage', 
               'Get-MgReportSharePointActivityUserCount', 
               'Get-MgReportSharePointActivityUserDetail', 
               'Get-MgReportSharePointSiteUsageDetail', 
               'Get-MgReportSharePointSiteUsageFileCount', 
               'Get-MgReportSharePointSiteUsagePage', 
               'Get-MgReportSharePointSiteUsageSiteCount', 
               'Get-MgReportSharePointSiteUsageStorage', 
               'Get-MgReportSkypeForBusinessActivityCount', 
               'Get-MgReportSkypeForBusinessActivityUserCount', 
               'Get-MgReportSkypeForBusinessActivityUserDetail', 
               'Get-MgReportSkypeForBusinessDeviceUsageDistributionUserCount', 
               'Get-MgReportSkypeForBusinessDeviceUsageUserCount', 
               'Get-MgReportSkypeForBusinessDeviceUsageUserDetail', 
               'Get-MgReportSkypeForBusinessOrganizerActivityCount', 
               'Get-MgReportSkypeForBusinessOrganizerActivityMinuteCount', 
               'Get-MgReportSkypeForBusinessOrganizerActivityUserCount', 
               'Get-MgReportSkypeForBusinessParticipantActivityCount', 
               'Get-MgReportSkypeForBusinessParticipantActivityMinuteCount', 
               'Get-MgReportSkypeForBusinessParticipantActivityUserCount', 
               'Get-MgReportSkypeForBusinessPeerToPeerActivityCount', 
               'Get-MgReportSkypeForBusinessPeerToPeerActivityMinuteCount', 
               'Get-MgReportSkypeForBusinessPeerToPeerActivityUserCount', 
               'Get-MgReportTeamActivityCount', 'Get-MgReportTeamActivityDetail', 
               'Get-MgReportTeamActivityDistributionCount', 
               'Get-MgReportTeamCount', 
               'Get-MgReportTeamDeviceUsageDistributionUserCount', 
               'Get-MgReportTeamDeviceUsageUserCount', 
               'Get-MgReportTeamDeviceUsageUserDetail', 
               'Get-MgReportTeamUserActivityCount', 
               'Get-MgReportTeamUserActivityUserCount', 
               'Get-MgReportTeamUserActivityUserDetail', 
               'Get-MgReportUserArchivedPrintJob', 
               'Get-MgReportYammerActivityCount', 
               'Get-MgReportYammerActivityUserCount', 
               'Get-MgReportYammerActivityUserDetail', 
               'Get-MgReportYammerDeviceUsageDistributionUserCount', 
               'Get-MgReportYammerDeviceUsageUserCount', 
               'Get-MgReportYammerDeviceUsageUserDetail', 
               'Get-MgReportYammerGroupActivityCount', 
               'Get-MgReportYammerGroupActivityDetail', 
               'Get-MgReportYammerGroupActivityGroupCount', 
               'Invoke-MgDismissAuditLogSignIn', 
               'Invoke-MgGraphReportAuthenticationMethod', 
               'New-MgReportAuthenticationMethodUserRegistrationDetail', 
               'New-MgReportPartnerBillingManifest', 
               'New-MgReportPartnerBillingOperation', 
               'Remove-MgAdminReportSetting', 'Remove-MgDeviceManagementReport', 
               'Remove-MgReportAuthenticationMethodUserRegistrationDetail', 
               'Remove-MgReportPartnerBilling', 
               'Remove-MgReportPartnerBillingManifest', 
               'Remove-MgReportPartnerBillingOperation', 
               'Remove-MgReportPartnerBillingReconciliation', 
               'Remove-MgReportPartnerBillingReconciliationBilled', 
               'Remove-MgReportPartnerBillingReconciliationUnbilled', 
               'Remove-MgReportPartnerBillingUsage', 
               'Remove-MgReportPartnerBillingUsageBilled', 
               'Remove-MgReportPartnerBillingUsageUnbilled', 
               'Update-MgAdminReportSetting', 'Update-MgDeviceManagementReport', 
               'Update-MgReportAuthenticationMethodUserRegistrationDetail', 
               'Update-MgReportPartnerBilling', 
               'Update-MgReportPartnerBillingManifest', 
               'Update-MgReportPartnerBillingOperation', 
               'Update-MgReportPartnerBillingReconciliation', 
               'Update-MgReportPartnerBillingReconciliationBilled', 
               'Update-MgReportPartnerBillingReconciliationUnbilled', 
               'Update-MgReportPartnerBillingUsage', 
               'Update-MgReportPartnerBillingUsageBilled', 
               'Update-MgReportPartnerBillingUsageUnbilled'

# Cmdlets to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no cmdlets to export.
CmdletsToExport = @()

# Variables to export from this module
# VariablesToExport = @()

# Aliases to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no aliases to export.
AliasesToExport = '*'

# DSC resources to export from this module
# DscResourcesToExport = @()

# List of all modules packaged with this module
# ModuleList = @()

# List of all files packaged with this module
# FileList = @()

# Private data to pass to the module specified in RootModule/ModuleToProcess. This may also contain a PSData hashtable with additional module metadata used by PowerShell.
PrivateData = @{

    PSData = @{

        # Tags applied to this module. These help with module discovery in online galleries.
        Tags = 'Microsoft','Office365','Graph','PowerShell','PSModule','PSIncludes_Cmdlet'

        # A URL to the license for this module.
        LicenseUri = 'https://aka.ms/devservicesagreement'

        # A URL to the main website for this project.
        ProjectUri = 'https://github.com/microsoftgraph/msgraph-sdk-powershell'

        # A URL to an icon representing this module.
        IconUri = 'https://raw.githubusercontent.com/microsoftgraph/msgraph-sdk-powershell/dev/docs/images/graph_color256.png'

        # ReleaseNotes of this module
        ReleaseNotes = 'See https://aka.ms/GraphPowerShell-Release.'

        # Prerelease string of this module
        # Prerelease = ''

        # Flag to indicate whether the module requires explicit user acceptance for install/update/save
        # RequireLicenseAcceptance = $false

        # External dependent modules of this module
        # ExternalModuleDependencies = @()

    } # End of PSData hashtable

 } # End of PrivateData hashtable

# HelpInfo URI of this module
# HelpInfoURI = ''

# Default prefix for commands exported from this module. Override the default prefix using Import-Module -Prefix.
# DefaultCommandPrefix = ''

}


# SIG # Begin signature block
# MIIoVQYJKoZIhvcNAQcCoIIoRjCCKEICAQExDzANBglghkgBZQMEAgEFADB5Bgor
# BgEEAYI3AgEEoGswaTA0BgorBgEEAYI3AgEeMCYCAwEAAAQQH8w7YFlLCE63JNLG
# KX7zUQIBAAIBAAIBAAIBAAIBADAxMA0GCWCGSAFlAwQCAQUABCBuCJhzcD3U2KuH
# +GWxW8jVa9YoBk1xielT01kR0MIZm6CCDYUwggYDMIID66ADAgECAhMzAAAEA73V
# lV0POxitAAAAAAQDMA0GCSqGSIb3DQEBCwUAMH4xCzAJBgNVBAYTAlVTMRMwEQYD
# VQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNy
# b3NvZnQgQ29ycG9yYXRpb24xKDAmBgNVBAMTH01pY3Jvc29mdCBDb2RlIFNpZ25p
# bmcgUENBIDIwMTEwHhcNMjQwOTEyMjAxMTEzWhcNMjUwOTExMjAxMTEzWjB0MQsw
# CQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9u
# ZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMR4wHAYDVQQDExVNaWNy
# b3NvZnQgQ29ycG9yYXRpb24wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIB
# AQCfdGddwIOnbRYUyg03O3iz19XXZPmuhEmW/5uyEN+8mgxl+HJGeLGBR8YButGV
# LVK38RxcVcPYyFGQXcKcxgih4w4y4zJi3GvawLYHlsNExQwz+v0jgY/aejBS2EJY
# oUhLVE+UzRihV8ooxoftsmKLb2xb7BoFS6UAo3Zz4afnOdqI7FGoi7g4vx/0MIdi
# kwTn5N56TdIv3mwfkZCFmrsKpN0zR8HD8WYsvH3xKkG7u/xdqmhPPqMmnI2jOFw/
# /n2aL8W7i1Pasja8PnRXH/QaVH0M1nanL+LI9TsMb/enWfXOW65Gne5cqMN9Uofv
# ENtdwwEmJ3bZrcI9u4LZAkujAgMBAAGjggGCMIIBfjAfBgNVHSUEGDAWBgorBgEE
# AYI3TAgBBggrBgEFBQcDAzAdBgNVHQ4EFgQU6m4qAkpz4641iK2irF8eWsSBcBkw
# VAYDVR0RBE0wS6RJMEcxLTArBgNVBAsTJE1pY3Jvc29mdCBJcmVsYW5kIE9wZXJh
# dGlvbnMgTGltaXRlZDEWMBQGA1UEBRMNMjMwMDEyKzUwMjkyNjAfBgNVHSMEGDAW
# gBRIbmTlUAXTgqoXNzcitW2oynUClTBUBgNVHR8ETTBLMEmgR6BFhkNodHRwOi8v
# d3d3Lm1pY3Jvc29mdC5jb20vcGtpb3BzL2NybC9NaWNDb2RTaWdQQ0EyMDExXzIw
# MTEtMDctMDguY3JsMGEGCCsGAQUFBwEBBFUwUzBRBggrBgEFBQcwAoZFaHR0cDov
# L3d3dy5taWNyb3NvZnQuY29tL3BraW9wcy9jZXJ0cy9NaWNDb2RTaWdQQ0EyMDEx
# XzIwMTEtMDctMDguY3J0MAwGA1UdEwEB/wQCMAAwDQYJKoZIhvcNAQELBQADggIB
# AFFo/6E4LX51IqFuoKvUsi80QytGI5ASQ9zsPpBa0z78hutiJd6w154JkcIx/f7r
# EBK4NhD4DIFNfRiVdI7EacEs7OAS6QHF7Nt+eFRNOTtgHb9PExRy4EI/jnMwzQJV
# NokTxu2WgHr/fBsWs6G9AcIgvHjWNN3qRSrhsgEdqHc0bRDUf8UILAdEZOMBvKLC
# rmf+kJPEvPldgK7hFO/L9kmcVe67BnKejDKO73Sa56AJOhM7CkeATrJFxO9GLXos
# oKvrwBvynxAg18W+pagTAkJefzneuWSmniTurPCUE2JnvW7DalvONDOtG01sIVAB
# +ahO2wcUPa2Zm9AiDVBWTMz9XUoKMcvngi2oqbsDLhbK+pYrRUgRpNt0y1sxZsXO
# raGRF8lM2cWvtEkV5UL+TQM1ppv5unDHkW8JS+QnfPbB8dZVRyRmMQ4aY/tx5x5+
# sX6semJ//FbiclSMxSI+zINu1jYerdUwuCi+P6p7SmQmClhDM+6Q+btE2FtpsU0W
# +r6RdYFf/P+nK6j2otl9Nvr3tWLu+WXmz8MGM+18ynJ+lYbSmFWcAj7SYziAfT0s
# IwlQRFkyC71tsIZUhBHtxPliGUu362lIO0Lpe0DOrg8lspnEWOkHnCT5JEnWCbzu
# iVt8RX1IV07uIveNZuOBWLVCzWJjEGa+HhaEtavjy6i7MIIHejCCBWKgAwIBAgIK
# YQ6Q0gAAAAAAAzANBgkqhkiG9w0BAQsFADCBiDELMAkGA1UEBhMCVVMxEzARBgNV
# BAgTCldhc2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jv
# c29mdCBDb3Jwb3JhdGlvbjEyMDAGA1UEAxMpTWljcm9zb2Z0IFJvb3QgQ2VydGlm
# aWNhdGUgQXV0aG9yaXR5IDIwMTEwHhcNMTEwNzA4MjA1OTA5WhcNMjYwNzA4MjEw
# OTA5WjB+MQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UE
# BxMHUmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSgwJgYD
# VQQDEx9NaWNyb3NvZnQgQ29kZSBTaWduaW5nIFBDQSAyMDExMIICIjANBgkqhkiG
# 9w0BAQEFAAOCAg8AMIICCgKCAgEAq/D6chAcLq3YbqqCEE00uvK2WCGfQhsqa+la
# UKq4BjgaBEm6f8MMHt03a8YS2AvwOMKZBrDIOdUBFDFC04kNeWSHfpRgJGyvnkmc
# 6Whe0t+bU7IKLMOv2akrrnoJr9eWWcpgGgXpZnboMlImEi/nqwhQz7NEt13YxC4D
# dato88tt8zpcoRb0RrrgOGSsbmQ1eKagYw8t00CT+OPeBw3VXHmlSSnnDb6gE3e+
# lD3v++MrWhAfTVYoonpy4BI6t0le2O3tQ5GD2Xuye4Yb2T6xjF3oiU+EGvKhL1nk
# kDstrjNYxbc+/jLTswM9sbKvkjh+0p2ALPVOVpEhNSXDOW5kf1O6nA+tGSOEy/S6
# A4aN91/w0FK/jJSHvMAhdCVfGCi2zCcoOCWYOUo2z3yxkq4cI6epZuxhH2rhKEmd
# X4jiJV3TIUs+UsS1Vz8kA/DRelsv1SPjcF0PUUZ3s/gA4bysAoJf28AVs70b1FVL
# 5zmhD+kjSbwYuER8ReTBw3J64HLnJN+/RpnF78IcV9uDjexNSTCnq47f7Fufr/zd
# sGbiwZeBe+3W7UvnSSmnEyimp31ngOaKYnhfsi+E11ecXL93KCjx7W3DKI8sj0A3
# T8HhhUSJxAlMxdSlQy90lfdu+HggWCwTXWCVmj5PM4TasIgX3p5O9JawvEagbJjS
# 4NaIjAsCAwEAAaOCAe0wggHpMBAGCSsGAQQBgjcVAQQDAgEAMB0GA1UdDgQWBBRI
# bmTlUAXTgqoXNzcitW2oynUClTAZBgkrBgEEAYI3FAIEDB4KAFMAdQBiAEMAQTAL
# BgNVHQ8EBAMCAYYwDwYDVR0TAQH/BAUwAwEB/zAfBgNVHSMEGDAWgBRyLToCMZBD
# uRQFTuHqp8cx0SOJNDBaBgNVHR8EUzBRME+gTaBLhklodHRwOi8vY3JsLm1pY3Jv
# c29mdC5jb20vcGtpL2NybC9wcm9kdWN0cy9NaWNSb29DZXJBdXQyMDExXzIwMTFf
# MDNfMjIuY3JsMF4GCCsGAQUFBwEBBFIwUDBOBggrBgEFBQcwAoZCaHR0cDovL3d3
# dy5taWNyb3NvZnQuY29tL3BraS9jZXJ0cy9NaWNSb29DZXJBdXQyMDExXzIwMTFf
# MDNfMjIuY3J0MIGfBgNVHSAEgZcwgZQwgZEGCSsGAQQBgjcuAzCBgzA/BggrBgEF
# BQcCARYzaHR0cDovL3d3dy5taWNyb3NvZnQuY29tL3BraW9wcy9kb2NzL3ByaW1h
# cnljcHMuaHRtMEAGCCsGAQUFBwICMDQeMiAdAEwAZQBnAGEAbABfAHAAbwBsAGkA
# YwB5AF8AcwB0AGEAdABlAG0AZQBuAHQALiAdMA0GCSqGSIb3DQEBCwUAA4ICAQBn
# 8oalmOBUeRou09h0ZyKbC5YR4WOSmUKWfdJ5DJDBZV8uLD74w3LRbYP+vj/oCso7
# v0epo/Np22O/IjWll11lhJB9i0ZQVdgMknzSGksc8zxCi1LQsP1r4z4HLimb5j0b
# pdS1HXeUOeLpZMlEPXh6I/MTfaaQdION9MsmAkYqwooQu6SpBQyb7Wj6aC6VoCo/
# KmtYSWMfCWluWpiW5IP0wI/zRive/DvQvTXvbiWu5a8n7dDd8w6vmSiXmE0OPQvy
# CInWH8MyGOLwxS3OW560STkKxgrCxq2u5bLZ2xWIUUVYODJxJxp/sfQn+N4sOiBp
# mLJZiWhub6e3dMNABQamASooPoI/E01mC8CzTfXhj38cbxV9Rad25UAqZaPDXVJi
# hsMdYzaXht/a8/jyFqGaJ+HNpZfQ7l1jQeNbB5yHPgZ3BtEGsXUfFL5hYbXw3MYb
# BL7fQccOKO7eZS/sl/ahXJbYANahRr1Z85elCUtIEJmAH9AAKcWxm6U/RXceNcbS
# oqKfenoi+kiVH6v7RyOA9Z74v2u3S5fi63V4GuzqN5l5GEv/1rMjaHXmr/r8i+sL
# gOppO6/8MO0ETI7f33VtY5E90Z1WTk+/gFcioXgRMiF670EKsT/7qMykXcGhiJtX
# cVZOSEXAQsmbdlsKgEhr/Xmfwb1tbWrJUnMTDXpQzTGCGiYwghoiAgEBMIGVMH4x
# CzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRt
# b25kMR4wHAYDVQQKExVNaWNyb3NvZnQgQ29ycG9yYXRpb24xKDAmBgNVBAMTH01p
# Y3Jvc29mdCBDb2RlIFNpZ25pbmcgUENBIDIwMTECEzMAAAQDvdWVXQ87GK0AAAAA
# BAMwDQYJYIZIAWUDBAIBBQCgga4wGQYJKoZIhvcNAQkDMQwGCisGAQQBgjcCAQQw
# HAYKKwYBBAGCNwIBCzEOMAwGCisGAQQBgjcCARUwLwYJKoZIhvcNAQkEMSIEIE2l
# HJg+f87Gj272Tbf7ooDt03rXlBdZ3eUa8VfRCIW3MEIGCisGAQQBgjcCAQwxNDAy
# oBSAEgBNAGkAYwByAG8AcwBvAGYAdKEagBhodHRwOi8vd3d3Lm1pY3Jvc29mdC5j
# b20wDQYJKoZIhvcNAQEBBQAEggEAXQiJ8HH8PX1ZpUROK7bl2YN6xbDe+mlVRkIQ
# 7Pxh0n17LW1/g6E49LPqLmQel6N2900pspGl11p9Yx7hZ/0kHP1opLlfFGndTOrY
# fcJ1njx/zMs/8JvQ1AnLI8tVqAczNEYfHzdISDbQBfCI8JL5CJiLv+Q/zOkjoPkC
# ecvGSST9Sc8ULyq8btU4hsb+2oFik/E5TbZ2ySEfcKswU31Nu9IAiOdxUSP3GopV
# VgQUOYutH8h2DRC57ZlN2ujJyAsEUQ4hoYNENUpvAerpHN/MoehNnLfg8uYBerK9
# PCCE952ktH5umHtwHWhKFYOGvwnnC1uyVOpo8MOyLIqa5WOv/aGCF7AwghesBgor
# BgEEAYI3AwMBMYIXnDCCF5gGCSqGSIb3DQEHAqCCF4kwgheFAgEDMQ8wDQYJYIZI
# AWUDBAIBBQAwggFaBgsqhkiG9w0BCRABBKCCAUkEggFFMIIBQQIBAQYKKwYBBAGE
# WQoDATAxMA0GCWCGSAFlAwQCAQUABCC+MQVMHUWQYj1cVPIRiIKB8esZ239y3fj0
# wFRiBraUVAIGaFKvE9j1GBMyMDI1MDcwOTExMDcyOC4yNjdaMASAAgH0oIHZpIHW
# MIHTMQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMH
# UmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMS0wKwYDVQQL
# EyRNaWNyb3NvZnQgSXJlbGFuZCBPcGVyYXRpb25zIExpbWl0ZWQxJzAlBgNVBAsT
# Hm5TaGllbGQgVFNTIEVTTjozMjFBLTA1RTAtRDk0NzElMCMGA1UEAxMcTWljcm9z
# b2Z0IFRpbWUtU3RhbXAgU2VydmljZaCCEf4wggcoMIIFEKADAgECAhMzAAAB+KOh
# JgwMQEj+AAEAAAH4MA0GCSqGSIb3DQEBCwUAMHwxCzAJBgNVBAYTAlVTMRMwEQYD
# VQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNy
# b3NvZnQgQ29ycG9yYXRpb24xJjAkBgNVBAMTHU1pY3Jvc29mdCBUaW1lLVN0YW1w
# IFBDQSAyMDEwMB4XDTI0MDcyNTE4MzEwOFoXDTI1MTAyMjE4MzEwOFowgdMxCzAJ
# BgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25k
# MR4wHAYDVQQKExVNaWNyb3NvZnQgQ29ycG9yYXRpb24xLTArBgNVBAsTJE1pY3Jv
# c29mdCBJcmVsYW5kIE9wZXJhdGlvbnMgTGltaXRlZDEnMCUGA1UECxMeblNoaWVs
# ZCBUU1MgRVNOOjMyMUEtMDVFMC1EOTQ3MSUwIwYDVQQDExxNaWNyb3NvZnQgVGlt
# ZS1TdGFtcCBTZXJ2aWNlMIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEA
# xR23pXYnD2BuODdeXs2Cu/T5kKI+bAw8cbtN50Cm/FArjXyL4RTqMe6laQ/CqeMT
# xgckvZr1JrW0Mi4F15rx/VveGhKBmob45DmOcV5xyx7h9Tk59NAl5PNMAWKAIWf2
# 70SWAAWxQbpVIhhPWCnVV3otVvahEad8pMmoSXrT5Z7Nk1RnB70A2bq9Hk8wIeC3
# vBuxEX2E8X50IgAHsyaR9roFq3ErzUEHlS8YnSq33ui5uBcrFOcFOCZILuVFVTgE
# qSrX4UiX0etqi7jUtKypgIflaZcV5cI5XI/eCxY8wDNmBprhYMNlYxdmQ9aLRDcT
# KWtddWpnJtyl5e3gHuYoj8xuDQ0XZNy7ESRwJIK03+rTZqfaYyM4XSK1s0aa+mO6
# 9vo/NmJ4R/f1+KucBPJ4yUdbqJWM3xMvBwLYycvigI/WK4kgPog0UBNczaQwDVXp
# cU+TMcOvWP8HBWmWJQImTZInAFivXqUaBbo3wAfPNbsQpvNNGu/12pg0F8O/CdRf
# gPHfOhIWQ0D8ALCY+LsiwbzcejbrVl4N9fn2wOg2sDa8RfNoD614I0pFjy/lq1Ns
# Bo9V4GZBikzX7ZjWCRgd1FCBXGpfpDikHjQ05YOkAakdWDT2bGSaUZJGVYtepIpP
# TAs1gd/vUogcdiL51o7shuHIlB6QSUiQ24XYhRbbQCECAwEAAaOCAUkwggFFMB0G
# A1UdDgQWBBS9zsZzz57QlT5nrt/oitLv1OQ7tjAfBgNVHSMEGDAWgBSfpxVdAF5i
# XYP05dJlpxtTNRnpcjBfBgNVHR8EWDBWMFSgUqBQhk5odHRwOi8vd3d3Lm1pY3Jv
# c29mdC5jb20vcGtpb3BzL2NybC9NaWNyb3NvZnQlMjBUaW1lLVN0YW1wJTIwUENB
# JTIwMjAxMCgxKS5jcmwwbAYIKwYBBQUHAQEEYDBeMFwGCCsGAQUFBzAChlBodHRw
# Oi8vd3d3Lm1pY3Jvc29mdC5jb20vcGtpb3BzL2NlcnRzL01pY3Jvc29mdCUyMFRp
# bWUtU3RhbXAlMjBQQ0ElMjAyMDEwKDEpLmNydDAMBgNVHRMBAf8EAjAAMBYGA1Ud
# JQEB/wQMMAoGCCsGAQUFBwMIMA4GA1UdDwEB/wQEAwIHgDANBgkqhkiG9w0BAQsF
# AAOCAgEAYfk8GzzpEVnGl7y6oXoytCb42Hx6TOA0+dkaBI36ftDE9tLubUa/xMbH
# B5rcNiRhFHZ93RefdPpc4+FF0DAl5lP8xKAO+293RWPKDFOFIxgtZY08t8D9cSQp
# gGUzyw3lETZebNLEA17A/CTpA2F9uh8j84KygeEbj+bidWDiEfayoH2A5/5ywJJx
# IuLzFVHacvWxSCKoF9hlSrZSG5fXWS3namf4tt690UT6AGyWLFWe895coFPxm/m0
# UIMjjp9VRFH7nb3Ng2Q4gPS9E5ZTMZ6nAlmUicDj0NXAs2wQuQrnYnbRAJ/DQW35
# qLo7Daw9AsItqjFhbMcG68gDc4j74L2KYe/2goBHLwzSn5UDftS1HZI0ZRsqmNHI
# 0TZvvUWX9ajm6SfLBTEtoTo6gLOX0UD/9rrhGjdkiCw4SwU5osClgqgiNMK5ndk2
# gxFlDXHCyLp5qB6BoPpc82RhO0yCzoP9gv7zv2EocAWEsqE5+0Wmu5uarmfvcziL
# fU1SY240OZW8ld4sS8fnybn/jDMmFAhazV1zH0QERWEsfLSpwkOXaImWNFJ5lmcn
# f1VTm6cmfasScYtElpjqZ9GooCmk1XFApORPs/PO43IcFmPRwagt00iQSw+rBeIH
# 00KQq+FJT/62SB70g9g/R8TS6k6b/wt2UWhqrW+Q8lw6Xzgex/YwggdxMIIFWaAD
# AgECAhMzAAAAFcXna54Cm0mZAAAAAAAVMA0GCSqGSIb3DQEBCwUAMIGIMQswCQYD
# VQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEe
# MBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMTIwMAYDVQQDEylNaWNyb3Nv
# ZnQgUm9vdCBDZXJ0aWZpY2F0ZSBBdXRob3JpdHkgMjAxMDAeFw0yMTA5MzAxODIy
# MjVaFw0zMDA5MzAxODMyMjVaMHwxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNo
# aW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNyb3NvZnQgQ29y
# cG9yYXRpb24xJjAkBgNVBAMTHU1pY3Jvc29mdCBUaW1lLVN0YW1wIFBDQSAyMDEw
# MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEA5OGmTOe0ciELeaLL1yR5
# vQ7VgtP97pwHB9KpbE51yMo1V/YBf2xK4OK9uT4XYDP/XE/HZveVU3Fa4n5KWv64
# NmeFRiMMtY0Tz3cywBAY6GB9alKDRLemjkZrBxTzxXb1hlDcwUTIcVxRMTegCjhu
# je3XD9gmU3w5YQJ6xKr9cmmvHaus9ja+NSZk2pg7uhp7M62AW36MEBydUv626GIl
# 3GoPz130/o5Tz9bshVZN7928jaTjkY+yOSxRnOlwaQ3KNi1wjjHINSi947SHJMPg
# yY9+tVSP3PoFVZhtaDuaRr3tpK56KTesy+uDRedGbsoy1cCGMFxPLOJiss254o2I
# 5JasAUq7vnGpF1tnYN74kpEeHT39IM9zfUGaRnXNxF803RKJ1v2lIH1+/NmeRd+2
# ci/bfV+AutuqfjbsNkz2K26oElHovwUDo9Fzpk03dJQcNIIP8BDyt0cY7afomXw/
# TNuvXsLz1dhzPUNOwTM5TI4CvEJoLhDqhFFG4tG9ahhaYQFzymeiXtcodgLiMxhy
# 16cg8ML6EgrXY28MyTZki1ugpoMhXV8wdJGUlNi5UPkLiWHzNgY1GIRH29wb0f2y
# 1BzFa/ZcUlFdEtsluq9QBXpsxREdcu+N+VLEhReTwDwV2xo3xwgVGD94q0W29R6H
# XtqPnhZyacaue7e3PmriLq0CAwEAAaOCAd0wggHZMBIGCSsGAQQBgjcVAQQFAgMB
# AAEwIwYJKwYBBAGCNxUCBBYEFCqnUv5kxJq+gpE8RjUpzxD/LwTuMB0GA1UdDgQW
# BBSfpxVdAF5iXYP05dJlpxtTNRnpcjBcBgNVHSAEVTBTMFEGDCsGAQQBgjdMg30B
# ATBBMD8GCCsGAQUFBwIBFjNodHRwOi8vd3d3Lm1pY3Jvc29mdC5jb20vcGtpb3Bz
# L0RvY3MvUmVwb3NpdG9yeS5odG0wEwYDVR0lBAwwCgYIKwYBBQUHAwgwGQYJKwYB
# BAGCNxQCBAweCgBTAHUAYgBDAEEwCwYDVR0PBAQDAgGGMA8GA1UdEwEB/wQFMAMB
# Af8wHwYDVR0jBBgwFoAU1fZWy4/oolxiaNE9lJBb186aGMQwVgYDVR0fBE8wTTBL
# oEmgR4ZFaHR0cDovL2NybC5taWNyb3NvZnQuY29tL3BraS9jcmwvcHJvZHVjdHMv
# TWljUm9vQ2VyQXV0XzIwMTAtMDYtMjMuY3JsMFoGCCsGAQUFBwEBBE4wTDBKBggr
# BgEFBQcwAoY+aHR0cDovL3d3dy5taWNyb3NvZnQuY29tL3BraS9jZXJ0cy9NaWNS
# b29DZXJBdXRfMjAxMC0wNi0yMy5jcnQwDQYJKoZIhvcNAQELBQADggIBAJ1Vffwq
# reEsH2cBMSRb4Z5yS/ypb+pcFLY+TkdkeLEGk5c9MTO1OdfCcTY/2mRsfNB1OW27
# DzHkwo/7bNGhlBgi7ulmZzpTTd2YurYeeNg2LpypglYAA7AFvonoaeC6Ce5732pv
# vinLbtg/SHUB2RjebYIM9W0jVOR4U3UkV7ndn/OOPcbzaN9l9qRWqveVtihVJ9Ak
# vUCgvxm2EhIRXT0n4ECWOKz3+SmJw7wXsFSFQrP8DJ6LGYnn8AtqgcKBGUIZUnWK
# NsIdw2FzLixre24/LAl4FOmRsqlb30mjdAy87JGA0j3mSj5mO0+7hvoyGtmW9I/2
# kQH2zsZ0/fZMcm8Qq3UwxTSwethQ/gpY3UA8x1RtnWN0SCyxTkctwRQEcb9k+SS+
# c23Kjgm9swFXSVRk2XPXfx5bRAGOWhmRaw2fpCjcZxkoJLo4S5pu+yFUa2pFEUep
# 8beuyOiJXk+d0tBMdrVXVAmxaQFEfnyhYWxz/gq77EFmPWn9y8FBSX5+k77L+Dvk
# txW/tM4+pTFRhLy/AsGConsXHRWJjXD+57XQKBqJC4822rpM+Zv/Cuk0+CQ1Zyvg
# DbjmjJnW4SLq8CdCPSWU5nR0W2rRnj7tfqAxM328y+l7vzhwRNGQ8cirOoo6CGJ/
# 2XBjU02N7oJtpQUQwXEGahC0HVUzWLOhcGbyoYIDWTCCAkECAQEwggEBoYHZpIHW
# MIHTMQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMH
# UmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMS0wKwYDVQQL
# EyRNaWNyb3NvZnQgSXJlbGFuZCBPcGVyYXRpb25zIExpbWl0ZWQxJzAlBgNVBAsT
# Hm5TaGllbGQgVFNTIEVTTjozMjFBLTA1RTAtRDk0NzElMCMGA1UEAxMcTWljcm9z
# b2Z0IFRpbWUtU3RhbXAgU2VydmljZaIjCgEBMAcGBSsOAwIaAxUAtkQt/ebWSQ5D
# nG+aKRzPELCFE9GggYMwgYCkfjB8MQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2Fz
# aGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENv
# cnBvcmF0aW9uMSYwJAYDVQQDEx1NaWNyb3NvZnQgVGltZS1TdGFtcCBQQ0EgMjAx
# MDANBgkqhkiG9w0BAQsFAAIFAOwYMuAwIhgPMjAyNTA3MDkwMDE0MjRaGA8yMDI1
# MDcxMDAwMTQyNFowdzA9BgorBgEEAYRZCgQBMS8wLTAKAgUA7Bgy4AIBADAKAgEA
# AgISkQIB/zAHAgEAAgISmjAKAgUA7BmEYAIBADA2BgorBgEEAYRZCgQCMSgwJjAM
# BgorBgEEAYRZCgMCoAowCAIBAAIDB6EgoQowCAIBAAIDAYagMA0GCSqGSIb3DQEB
# CwUAA4IBAQB18cY8chWDdnbxunZYar7ssWUGb1kq5L1pjts5HkWmzcA7Pj0qoaiV
# oHWj0SaO/XJlUYbt9ji3ZeITb1XZnOK0dhJGnrGfZRr2ESYPh10zP7A9kdI7J+U9
# ijE41sGeNOieZVyMWZFgnGBaBuUCx3+jhbSAv7XN+naRKrK9gSmiHdSjiQjgdqmm
# p+h7zR3to0Rqqp9ZU7lxi856E9C6NUOHGacO8abCUNuGY/qpqu8Q6Io8+8PHkKwW
# R6Dckg+TDC4uQxkphXRYRtvxnFx3JwtR75QtC2+R+HN8izV7b4wERwm8sfxaCs3W
# ErUjpf2lrMzjtvkydj29juc6ivU7GmukMYIEDTCCBAkCAQEwgZMwfDELMAkGA1UE
# BhMCVVMxEzARBgNVBAgTCldhc2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAc
# BgNVBAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEmMCQGA1UEAxMdTWljcm9zb2Z0
# IFRpbWUtU3RhbXAgUENBIDIwMTACEzMAAAH4o6EmDAxASP4AAQAAAfgwDQYJYIZI
# AWUDBAIBBQCgggFKMBoGCSqGSIb3DQEJAzENBgsqhkiG9w0BCRABBDAvBgkqhkiG
# 9w0BCQQxIgQgUPi/9vMp6LAebBy2Efy9YXpgiXugG3X4RJ0W3KkPULcwgfoGCyqG
# SIb3DQEJEAIvMYHqMIHnMIHkMIG9BCDvzDPyXw1UkAUFYt8bR4UdjM90Qv5xnVai
# KD3I0Zz3WjCBmDCBgKR+MHwxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNoaW5n
# dG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNyb3NvZnQgQ29ycG9y
# YXRpb24xJjAkBgNVBAMTHU1pY3Jvc29mdCBUaW1lLVN0YW1wIFBDQSAyMDEwAhMz
# AAAB+KOhJgwMQEj+AAEAAAH4MCIEIHofihpynocQvTmHYhZGaJSer5A7cAJS22DC
# jxQ/NDdsMA0GCSqGSIb3DQEBCwUABIICAGXsUyH8U9PCsm9fK+ozvIg7yreAyWgN
# p4oSW3nze93BqiiorC5B+W0COqJTDcAbSknhTnoL/rJPExAC2hZW9NTgA/UVRNqq
# Vwl5E/lQ9rzeDrPZFJYDtXf4b8oTYWRgoLaDwDKjRGsHgfqqOsND1ichvZqnles8
# zRPIksYKJdFTo9xbMwgfxj/Pn2IF3vJw7iRwG0wsGSh5m7vMmq/BrkFgtZoy82Ob
# 5rk3hqULdvWPbSK3ESg8wUosgVM5QWDPHxVTAh4ySQ66J/qmDhzpbR0wfvGiLDHs
# /4tvyuaX0NGZoJb5vzuRmCKvgawuRa0ENI0GyxWxkbosX9oMRUSw1Fv3MRug0+vj
# HcJTlAXwEZcytislmJaKHQRnqtx9t0KJ6UqjtR5wLtOP3ViG3tUKLmcEDDmCOMBX
# nmt3jeOTHOwuPBZZu62ToEYbKmxX2RimIr5xnmYRvc/9zoss47Yo9lomyCd8x/rW
# BaT/X+Czj/w47Mz5KGlv8LCbNwM+RMlKKkzDHUFL6toGQp719auf3HwCIxNjQ9IH
# 4h2uGTFw8PpIr61TYe7ThqUOS5SwRG5rSdbkw90iXwFEdTUxa2A/KauyLMXi1Raz
# Ww5WeIIUzwvpaSDYjRwhyWZvz10OsMgqlnmjPsT/e+FlYjX+uHaNu4Q175Q/eOov
# Ht9Zq4K48Njp
# SIG # End signature block
