#pragma namespace("\\\\.\\root\\default")
instance of __namespace{ name="MS_409";};
#pragma namespace("\\\\.\\root\\default\\MS_409")

[AMENDMENT, LOCALE("MS_409")] 
class MSFT_PSModule : OMI_BaseResource
{
  [Key,Description("Name of the module\n") : Amended] String Name;
  [Description("Whether the module is to be installed or uninstalled.\nPresent {default}  \nAbsent   \n") : Amended] String Ensure;
  [Description("The name of the module source where the module can be found.\n") : Amended] String Repository;
  [Description("Whether the package is trusted or untrusted.\nTrusted {default}  \nUntrusted   \n") : Amended] String InstallationPolicy;
  [Description("The required version of the module.\n") : Amended] String RequiredVersion;
  [Description("The minimum version of the module.\n") : Amended] String MinimumVersion;
  [Description("The maximum version of the module.\n") : Amended] String MaximumVersion;
  [Description("Forces the installation of the module.\n" : Amended] Boolean Force;
  [Description("Allows installation when existing cmdlets of the same name exist.\n" : Amended] Boolean AllowClobber;
  [Description("Allows installation when module is not signed.\n" : Amended] Boolean SkipPublisherCheck;
  [Description("The brief description of the module.\n") : Amended] string Description;
  [Description("The version of the module that is installed.\n") : Amended] String InstalledVersion;
  [Description("The identifier of the module.\n") : Amended] String Guid;
  [Description("The base location where the module is installed.\n") : Amended] String ModuleBase;
  [Description("The type of the module.\n") : Amended] String ModuleType;
  [Description("The author of the module.\n") : Amended] String Author;
};
